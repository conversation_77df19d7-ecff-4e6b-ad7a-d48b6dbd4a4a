/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.integracao.bpa;

import br.com.celk.util.Coalesce;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.MensagemValidacao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.Bpa;
import br.com.ksisolucoes.vo.atendimento.BpaProcesso;
import br.com.ksisolucoes.vo.basico.ParametroAtendimento;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoDetalheCadastro;
import com.amazonaws.util.StringUtils;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MontarArquivoBpa extends AbstractCommandTransaction {

    private String PREFIXO_LOG_TIPO = "";
    private Long codigoBpaProcesso;
    private RetornoValidacao retornoValidacao = new RetornoValidacao();
    private boolean fimProcessoComErro = false;

    public MontarArquivoBpa(Long codigoBpaProcesso) {
        this.codigoBpaProcesso = codigoBpaProcesso;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            ParametroAtendimento parametroAtendimento = CargaBasicoPadrao.getInstance().getParametroAtendimento();

            BpaProcesso bpaProcesso = (BpaProcesso) getSession().get(BpaProcesso.class, this.codigoBpaProcesso);

            PREFIXO_LOG_TIPO = ProcedimentoDetalheCadastro.REGISTRO_SISCAN.equals(bpaProcesso.getTipoBpa()) ? "SISCAN" : "BPA";
            Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - INICIO MONTAR ARQUIVO, PROCESSO N°-").concat(String.valueOf(this.codigoBpaProcesso)));
            List<Bpa> bpaList = getSession().createCriteria(Bpa.class)
                    .add(Restrictions.eq(Bpa.PROP_BPA_PROCESSO, bpaProcesso))
                    .addOrder(Order.asc(Bpa.PROP_TIPO_BPA))
                    .addOrder(Order.asc(Bpa.PROP_CODIGO))
                    .list();

            /**
             * CABECALHO
             */
            StringBuilder texto = new StringBuilder();
            texto.insert(0, "01");
            if (!ProcedimentoDetalheCadastro.REGISTRO_SISCAN.equals(bpaProcesso.getTipoBpa())) {
                texto.insert(2, "#BPA#");
            } else {
                texto.insert(2, "#C435");
            }
            texto.insert(7, new DecimalFormat("0000").format(bpaProcesso.getAno()) + new DecimalFormat("00").format(bpaProcesso.getMes()));

            int numeroLinhas = bpaList.size() + 1;
            texto.insert(13, new DecimalFormat("000000").format(numeroLinhas));

            int qtdadeFolhas = bpaList.size() % 20 == 0 ? bpaList.size() / 20 : (bpaList.size() / 20) + 1;
            texto.insert(19, new DecimalFormat("000000").format(qtdadeFolhas));
            texto.insert(25, new DecimalFormat("0000").format(bpaProcesso.getControleDadosArquivo()));

            String nomeOrgaoOrigem = parametroAtendimento.getBpaNomeOrigem();
            texto.insert(29, String.format("%-30.30s", Coalesce.asString(nomeOrgaoOrigem)));
            texto.insert(59, String.format("%-6.6s", Coalesce.asString(parametroAtendimento.getBpaSiglaOrigem())));
            texto.insert(65, new DecimalFormat("00000000000000").format(Coalesce.asLong(Coalesce.asString(parametroAtendimento.getBpaCnpjCpfOrigem()).replaceAll("[^0-9]", ""))));
            texto.insert(79, String.format("%-40.40s", Coalesce.asString(parametroAtendimento.getBpaNomeDestino())));
            texto.insert(119, Coalesce.asString(parametroAtendimento.getBpaFlagDestino()));
            texto.insert(120, "CELK-SAUDE");
            texto.insert(130, new char[]{(char) 13, (char) 10});//Quebra de Linha
            //**** FIM CABECALHO *******

            if (!retornoValidacao.isValido()) {
                throw new ValidacaoException(retornoValidacao);
            }

            int contador = 0;
            int sizeLog = Math.min(bpaList.size(), 1000);
            for (Bpa bpa : bpaList) {
                if (contador % sizeLog == 0) {
                    Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - Gerando Arquivo - registro N° ").concat(String.valueOf(contador)).concat("/").concat(String.valueOf(bpaList.size())).concat(" processado."));
                }
                contador++;

                if (Bpa.TIPO_BPA.CONSOLIDADO.value().equals(bpa.getTipoBpa())) {
                    texto.append(textoBpaConsolidado(bpa));
                } else if (Bpa.TIPO_BPA.INDIVIDUAL.value().equals(bpa.getTipoBpa())) {
                    texto.append(textoBpaIndividual(bpa));
                }
            }

            BpaProcesso processo = (BpaProcesso) getSession().get(BpaProcesso.class, bpaProcesso.getCodigo());

            processo.setTexto(texto.toString());

            bpaProcesso.setStatus(BpaProcesso.STATUS_ARQUIVO_GERADO);

        } catch (ValidacaoException e) {
            String msg = "";
            if (!retornoValidacao.isVazio()) {
                for (MensagemValidacao mv : retornoValidacao.getMensagemValidacaoMap().values()) {
                    msg += mv.getMensagem() + "\n";
                }
            } else {
                msg = StringUtil.montarMensagemErro(e);
            }
            BpaProcesso processo = (BpaProcesso) getSession().get(BpaProcesso.class, this.codigoBpaProcesso);
            processo.setMensagemErro(msg);
            processo.setStatus(BpaProcesso.STATUS_ERRO);
            getSession().saveOrUpdate(processo);
            fimProcessoComErro = true;
        } catch (Exception e) {
            BpaProcesso processo = (BpaProcesso) getSession().get(BpaProcesso.class, this.codigoBpaProcesso);
            processo.setMensagemErro(StringUtil.montarMensagemErro(e));
            processo.setStatus(BpaProcesso.STATUS_ERRO);
            getSession().saveOrUpdate(processo);
            Loggable.log.error(e.getMessage(), e);
            fimProcessoComErro = true;
        } finally {
            if (fimProcessoComErro) {
                Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - FIM MONTAR ARQUIVO (COM ERROS) - Processo Nº-").concat(String.valueOf(this.codigoBpaProcesso)));
            } else {
                Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - FIM MONTAR ARQUIVO (SUCESSO) - Processo Nº-").concat(String.valueOf(this.codigoBpaProcesso)));
            }
        }
    }

    private StringBuilder textoBpaConsolidado(Bpa bpa) {
        StringBuilder linhas = new StringBuilder();
        String cnes = Coalesce.asString(bpa.getEmpresa().getCnes());
        linhas.insert(0, "02");
        linhas.insert(2, String.format("%-7.7s", cnes));
        linhas.insert(9, new SimpleDateFormat("yyyyMM").format(bpa.getCompetenciaProcedimento()));
        linhas.insert(15, String.format("%-6.6s", bpa.getTabelaCbo() != null ? bpa.getTabelaCbo().getCbo() : ""));
        linhas.insert(21, new DecimalFormat("000").format(bpa.getFolha()));
        linhas.insert(24, new DecimalFormat("00").format(bpa.getSequencia()));
        linhas.insert(26, new DecimalFormat("0000000000").format(Long.parseLong(bpa.getProcedimento().getReferencia())));
        linhas.insert(36, new DecimalFormat("000").format(Coalesce.asLong(bpa.getIdade())));
        linhas.insert(39, new DecimalFormat("000000").format(bpa.getQuantidade()));
        linhas.insert(45, "EXT");
        linhas.insert(48, new char[]{(char) 13, (char) 10});
        return linhas;
    }

    private StringBuilder textoBpaIndividual(Bpa bpa) {
        StringBuilder linhas = new StringBuilder();
        String cnes = Coalesce.asString(bpa.getEmpresa().getCnes());
        linhas.insert(0, "03");
        linhas.insert(2, String.format("%-7.7s", cnes));
        linhas.insert(9, new SimpleDateFormat("yyyyMM").format(bpa.getCompetenciaProcedimento()));
        linhas.insert(15, String.format("%-15.15s", bpa.getCnsProfissional()));
        linhas.insert(30, String.format("%-6.6s", bpa.getTabelaCbo() != null ? bpa.getTabelaCbo().getCbo() : ""));
        linhas.insert(36, bpa.getDataAtendimento() != null ? new SimpleDateFormat("yyyyMMdd").format(bpa.getDataAtendimento()) : "        ");
        linhas.insert(44, new DecimalFormat("000").format(bpa.getFolha()));
        linhas.insert(47, new DecimalFormat("00").format(bpa.getSequencia()));
        linhas.insert(49, new DecimalFormat("0000000000").format(Long.parseLong(bpa.getProcedimento().getReferencia())));
        linhas.insert(59, String.format("%-15.15s", bpa.getCns() != null  && StringUtils.isNullOrEmpty(bpa.getUsuarioCadsus().getCpf()) ? bpa.getCns() : ""));
        linhas.insert(74, UsuarioCadsus.SEXO_MASCULINO.equals(bpa.getUsuarioCadsus().getSexo()) ? "M" : "F");
        Long codigo = bpa.getCidade() != null && bpa.getCidade().getCodigo() != null ? bpa.getCidade().getCodigo() : 0L;
        linhas.insert(75, new DecimalFormat("000000").format(codigo));
        linhas.insert(81, String.format("%-4.4s", bpa.getCid() != null ? bpa.getCid().getCodigo() : "    "));
        linhas.insert(85, new DecimalFormat("000").format(Coalesce.asLong(bpa.getIdade())));
        linhas.insert(88, new DecimalFormat("000000").format(bpa.getQuantidade()));
        linhas.insert(94, new DecimalFormat("00").format(bpa.getCaraterAtendimento() != null ? bpa.getCaraterAtendimento() : Bpa.CaraterAtendimento.ELETIVO.value()));
        linhas.insert(96, String.format("%-13.13s", bpa.getNumeroAutorizacao() != null ? bpa.getNumeroAutorizacao() : "             "));
        linhas.insert(109, "EXT");
        linhas.insert(112, String.format("%-30.30s", StringUtilKsi.normalize(bpa.getUsuarioCadsus().getNome())));
        linhas.insert(142, String.format("%-8.8s", new SimpleDateFormat("yyyyMMdd").format(bpa.getUsuarioCadsus().getDataNascimento())));
        linhas.insert(150, new DecimalFormat("00").format(bpa.getUsuarioCadsus().getRaca() != null ? bpa.getUsuarioCadsus().getRaca().getCodigo() : 99L));
        linhas.insert(152, (bpa.getUsuarioCadsus().getEtniaIndigena() != null && bpa.getUsuarioCadsus().getEtniaIndigena().getCodigoSus() != null) ? bpa.getUsuarioCadsus().getEtniaIndigena().getCodigoSus() : String.format("%-4.41s", ""));
        linhas.insert(156, new DecimalFormat("000").format((bpa.getUsuarioCadsus().getPaisNascimento() != null ? bpa.getUsuarioCadsus().getPaisNascimento().getCodigo() : 10L)));
        linhas.insert(159, bpa.getServico() != null ? new DecimalFormat("000").format(bpa.getServico()) : String.format("%-3.3s", ""));
        linhas.insert(162, bpa.getClassificacao() != null ? new DecimalFormat("000").format(bpa.getClassificacao()) : String.format("%-3.3s", ""));
        linhas.insert(165, String.format("%-12.12s", ""));
        linhas.insert(177, String.format("%-14.14s", ""));
        linhas.insert(191, String.format("%-8.8s", bpa.getEnderecoUsuarioCadsus() != null ? Coalesce.asString(bpa.getEnderecoUsuarioCadsus().getCep()).replaceAll("[^0-9]", "") : ""));
        linhas.insert(199, bpa.getEnderecoUsuarioCadsus() != null && bpa.getEnderecoUsuarioCadsus().getTipoLogradouro() != null ? new DecimalFormat("000").format(bpa.getEnderecoUsuarioCadsus().getTipoLogradouro().getCodigo()) : "081");
        linhas.insert(202, String.format("%-30.30s", bpa.getEnderecoUsuarioCadsus() != null ? StringUtilKsi.normalize(Coalesce.asString(bpa.getEnderecoUsuarioCadsus().getLogradouro())) : ""));
        linhas.insert(232, String.format("%-10.10s", bpa.getEnderecoUsuarioCadsus() != null ? StringUtilKsi.normalize(Coalesce.asString(bpa.getEnderecoUsuarioCadsus().getComplementoLogradouro())) : ""));
        linhas.insert(242, String.format("%-5.5s", bpa.getEnderecoUsuarioCadsus() != null ? Coalesce.asString(bpa.getEnderecoUsuarioCadsus().getNumeroLogradouro()) : ""));
        linhas.insert(247, String.format("%-30.30s", bpa.getEnderecoUsuarioCadsus() != null ? StringUtilKsi.normalize(Coalesce.asString(bpa.getEnderecoUsuarioCadsus().getBairro())) : ""));
        linhas.insert(277, String.format("%-11.11s", bpa.getUsuarioCadsus() != null ? Coalesce.asString(bpa.getUsuarioCadsus().getCelular()) : ""));
        linhas.insert(288, String.format("%-40.40s", ""));
        linhas.insert(328, String.format("%-10.10s", bpa.getCnesEquipe() != null ? Coalesce.asString(bpa.getCnesEquipe()) : ""));
        linhas.insert(338, String.format("%-11.11s", bpa.getUsuarioCadsus().getCpf() != null ? bpa.getUsuarioCadsus().getCpf() : ""));
        linhas.insert(349, (bpa.getUsuarioCadsus().getUsuarioCadsusEsus() != null && bpa.getUsuarioCadsus().getUsuarioCadsusEsus().getSituacaoRua() != null && RepositoryComponentDefault.SIM_LONG.equals(bpa.getUsuarioCadsus().getUsuarioCadsusEsus().getSituacaoRua())) ? "S": "N");
        linhas.insert(350, new char[]{(char) 13, (char) 10});
        return linhas;
    }
}
