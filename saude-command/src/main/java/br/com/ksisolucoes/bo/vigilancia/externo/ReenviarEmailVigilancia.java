package br.com.ksisolucoes.bo.vigilancia.externo;

import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Email;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.externo.UsuarioVigilancia;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class ReenviarEmailVigilancia extends AbstractCommandTransaction{

    private String cpf;
    private String origem;
    private String email;

    public ReenviarEmailVigilancia(String origem, String cpf) {
        this.cpf = StringUtil.getDigits(cpf);
        this.origem = origem;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(cpf.isEmpty()){
            throw new ValidacaoException(Bundle.getStringApplication("informeCpfLocalizarConta"));
        }

        UsuarioVigilancia uv = (UsuarioVigilancia) getSession().createCriteria(UsuarioVigilancia.class)
                .add(Restrictions.eq(VOUtils.montarPath(UsuarioVigilancia.PROP_CPF), cpf))
                .uniqueResult();
                
        // Sempre retorna mensagem genérica para prevenir user enumeration
        // Simula o processo completo mesmo quando usuário não existe
        if(uv == null){
            // Simula delay para evitar timing attacks
            simulateProcessingDelay();
            throw new ValidacaoException("Se os dados informados estiverem corretos, você receberá um email com instruções.");
        }

        email = uv.getEmail();

        String chaveValidacao = StringUtil.randomStringOfLength(10);

        uv.setDataSolicitacao(DataUtil.getDataAtual());
        uv.setChaveVerificacao(chaveValidacao);
        BOFactory.save(uv);

        getSession().flush();//Utilizado para nao enviar o email em caso de erro devido a concorrencia.
        try {
            Email.create()
                    .assunto("Reenvio de e-mail")
                    .para(email)
                    .mensagem("Foi solicitado o reenvio do e-mail de confirmação.\n\n"
                            + "Para utilizar os serviços da Vigilância Sanitária, você deve liberar sua conta através link abaixo\n\n"
                            + ""
                            + "" + origem + "?USUP=" + uv.getCodigo() + "&SENP=" + chaveValidacao)
                    .send();

        } catch (Throwable ex) {
            Loggable.log.warn("Erro ao reenviar email de confirmação", ex);
            throw new ValidacaoException("Se os dados informados estiverem corretos, você receberá um email com instruções.");
        }
    }

    public String getEmail() {
        return email;
    }

    /**
     * Simula delay de processamento para evitar timing attacks
     * que poderiam ser usados para enumerar usuários válidos
     */
    private void simulateProcessingDelay() {
        try {
            // Delay aleatório entre 500ms e 1500ms para simular processamento real
            Thread.sleep(500 + (long)(Math.random() * 1000));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

}
