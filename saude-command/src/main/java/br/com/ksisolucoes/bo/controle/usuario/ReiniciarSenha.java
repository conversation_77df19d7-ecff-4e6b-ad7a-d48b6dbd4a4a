package br.com.ksisolucoes.bo.controle.usuario;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.EnviarEmailDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.dto.EventoSistemaDTO;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EventoSistema;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.vo.controle.Usuario.STATUS_ATIVO;
import static br.com.ksisolucoes.vo.controle.Usuario.STATUS_BLOQUEADO;

public class ReiniciarSenha extends AbstractCommandTransaction {

    private static final long serialVersionUID = -4509208972982666661L;

    private static final int QTDE_CARACTERES = 3;
    private static final int COD_INICIO_MAISCULA = 65;
    private static final int COD_FIM_MAISCULA = 90;
    private static final int COD_INICIO_MINUSCULA = 97;
    private static final int COD_FIM_MINUSCULA = 122;
    private static final String VIRGULA_STRING = ",";
    private static final String ARROBA_STRING = "@";

    private String urlVigilancia;
    private Usuario usuario;
    private Usuario usuarioLogin;

    public ReiniciarSenha(Usuario usuarioLogin, String urlVigilancia) {
        this.usuarioLogin = usuarioLogin;
        this.urlVigilancia = urlVigilancia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (StringUtils.trimToNull(usuarioLogin.getLogin()) == null && StringUtils.trimToNull(usuarioLogin.getCpf()) == null) {
            throw new ValidacaoException("Informe o login ou CPF do usuário.");
        }

        usuario = LoadManager.getInstance(Usuario.class)
                .addInterceptor(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        if (StringUtils.trimToNull(usuarioLogin.getLogin()) != null) {
                            hql.addToWhereWhithAnd(alias + ".login = '" + usuarioLogin.getLogin() + "'");
                        } else if (StringUtils.trimToNull(usuarioLogin.getCpf()) != null) {
                            hql.addToWhereWhithAnd(alias + ".cpf = '" + usuarioLogin.getCpf() + "'");
                        }
                    }
                }).start().getVO();

        if (usuario == null) {
            throw new ValidacaoException("Login não encontrado, tente novamente.");
        }

        if (isUsuarioVigilancia(usuario)) {
            BOFactory.getBO(VigilanciaFacade.class).redefinirSenhaVigilancia(urlVigilancia, usuario.getLogin());
        } else if (isSalvarNovaSenha()) {
            usuario.setSenha(Util.criptografarSenha(usuarioLogin.getSenha()));

            if (STATUS_BLOQUEADO.equals(usuario.getStatus())) {
                usuario.setStatus(STATUS_ATIVO);
            }

            BOFactory.getBO(CadastroFacade.class).save(usuario);
        } else if (StringUtils.isNotEmpty(usuario.getEmail())) {
            String novaSenha = gerarNovaSenha();

            usuario.setSenha(Util.criptografarSenha(novaSenha));

            if (STATUS_BLOQUEADO.equals(usuario.getStatus())) {
                usuario.setStatus(STATUS_ATIVO);
            }

            BOFactory.getBO(CadastroFacade.class).save(usuario);
            enviarEmail(usuario.getSenha(), usuario.getLogin());
            salvarEventoRecuperarSenha();
        } else {
            throw new ValidacaoException("Usuário não possui email cadastrado, entre em contato com o suporte para solicitar alteração de senha.");
        }
    }

    private boolean isSalvarNovaSenha() {
        return urlVigilancia == null && usuario != null;
    }

    public boolean isUsuarioVigilancia(Usuario usuario) {
        if (usuario == null) return false;
        return Usuario.TipoUsuario.USUARIO_VIGILANCIA.value().equals(usuario.getTipoUsuario());
    }

    public String getEmailOfuscado() {
        return ofuscarTexto(this.usuario.getEmail(), ARROBA_STRING);
    }

    private String gerarNovaSenha() {
        String letrasMaiusculas = RandomStringUtils.random(QTDE_CARACTERES, COD_INICIO_MAISCULA, COD_FIM_MAISCULA, true, true);
        String letrasMinusculas = RandomStringUtils.random(QTDE_CARACTERES, COD_INICIO_MINUSCULA, COD_FIM_MINUSCULA, true, true);
        String numeros = RandomStringUtils.randomNumeric(QTDE_CARACTERES);
        String novaSenha = letrasMaiusculas.concat(letrasMinusculas).concat(numeros);
        List<Character> senhaEmbaralhada = embaralharCaracteres(novaSenha);
        return converterParaString(senhaEmbaralhada);
    }

    private String converterParaString(List<Character> senhaEmbaralhada) {
        StringBuilder builder = new StringBuilder();
        for (Character c : senhaEmbaralhada) {
            builder.append(c);
        }
        return builder.toString();
    }

    private List<Character> embaralharCaracteres(String stringParaEmbaralhar) {
        char[] caracteres = stringParaEmbaralhar.toCharArray();
        List<Character> stringParaEmbalharList = new ArrayList<>();
        for (char c : caracteres) {
            stringParaEmbalharList.add(c);
        }
        Collections.shuffle(stringParaEmbalharList);
        return stringParaEmbalharList;
    }

    private void enviarEmail(String novaSenha, String login) throws ValidacaoException, DAOException {
        EnviarEmailDTO dto = new EnviarEmailDTO();
        dto.setEmailDestino(usuario.getEmail());
        dto.setAssunto("Recuperação de senha");
        dto.setMensagem(getCorpoEmail(novaSenha, login));
        BOFactory.getBO(ComunicacaoFacade.class).enviarEmail(dto);
    }

    private void salvarEventoRecuperarSenha() throws DAOException, ValidacaoException {
        if (usuario.getSenha() != null) {
            EventoSistemaDTO eventoSistemaDTO = criarEventoSistemaDTO();

            String msg = Bundle.getStringApplication("msg_alteracao_senha", "( " + "(" + usuario.getLogin() + ")" + usuario.getDescricaoFormatado() + " )");
            eventoSistemaDTO.setDescricao(msg);
            BOFactory.getBO(CommomFacade.class).gerarEventoSistema(eventoSistemaDTO);
        }
    }

    private EventoSistemaDTO criarEventoSistemaDTO() {
        Date dataAtual = DataUtil.getDataAtual();
        EventoSistemaDTO eventoSistemaDTO = new EventoSistemaDTO();
        eventoSistemaDTO.setNivelCriticidade(EventoSistema.NivelCriticidade.INFORMACAO.value());
        eventoSistemaDTO.setKeyword(Bundle.getStringApplication("key_troca_senha"));
        eventoSistemaDTO.setTipoEvento(EventoSistema.TipoEvento.PROGRAMA.value());
        eventoSistemaDTO.setDataRegistro(dataAtual);
        if (Usuario.TipoUsuario.USUARIO_SAUDE.value().equals(usuario.getTipoUsuario())) {
            eventoSistemaDTO.setIdentificacaoEvento(EventoSistema.IdentificacaoEvento.BLOQUEIO_DESBLOQUEIO.value());
        }
        eventoSistemaDTO.setFonteEvento("Recuperação de senha");
        return eventoSistemaDTO;
    }

    private String getCorpoEmail(String novaSenha, String login) {
        String loginOfuscado = ofuscarTexto(login, VIRGULA_STRING);

        return "<h2 style=\"background: #0A8A91; color: white; width: 250px; padding-left: 10px\">Usuário Celk Sistemas</h2>" +
                "<p>Use link para redefinir a senha do usuário " + login + "</p>" +
                "<a href=\"" + urlVigilancia + "/" + novaSenha + "/" + login + "\">Redefinir senha</a>" +
                "<br/>" +
                "<p>Obrigado,</p>" +
                "<p>Equipe - Celk Sistemas.</p>";
    }

    @NotNull
    private static String ofuscarTexto(String texto, String caracterSplit) {
        char[] loginArray = texto.toCharArray();
        int fim = texto.indexOf(caracterSplit);
        if (fim == -1) {
            fim = texto.length();
        }
        if (fim < 2) {
            return texto;
        }
        for (int i = 2; i < fim; i++) {
            loginArray[i] = '*';
        }
        return String.valueOf(loginArray);
    }
}
