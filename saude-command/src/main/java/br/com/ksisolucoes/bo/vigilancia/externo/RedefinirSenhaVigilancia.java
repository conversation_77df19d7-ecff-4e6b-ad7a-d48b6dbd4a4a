package br.com.ksisolucoes.bo.vigilancia.externo;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Order;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Email;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.externo.UsuarioVigilancia;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class RedefinirSenhaVigilancia extends AbstractCommandTransaction {

    private String cpf;
    private String origem;
    private String email;

    public RedefinirSenhaVigilancia(String origem, String cpf) {
        this.cpf = StringUtil.getDigits(cpf);
        this.origem = origem;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (cpf.isEmpty()) {
            throw new ValidacaoException(Bundle.getStringApplication("informeCpfLocalizarConta"));
        }

        UsuarioVigilancia up = (UsuarioVigilancia) getSession().createCriteria(UsuarioVigilancia.class)
                .addOrder(Order.asc(UsuarioVigilancia.PROP_CODIGO))
                .createCriteria(UsuarioVigilancia.PROP_USUARIO)
                .add(Restrictions.eq(Usuario.PROP_LOGIN, cpf))
                .add(Restrictions.eq(Usuario.PROP_TIPO_USUARIO, Usuario.TipoUsuario.USUARIO_VIGILANCIA.value()))
                .setMaxResults(1)
                .uniqueResult();

        // Sempre retorna mensagem genérica para prevenir user enumeration
        // Simula o processo completo mesmo quando usuário não existe
        if (up == null) {
            // Simula delay para evitar timing attacks
            simulateProcessingDelay();
            throw new ValidacaoException("Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha.");
        }

        email = up.getUsuario().getEmail();

        String chaveNovaSenha = StringUtil.randomStringOfLength(50);

        up.setChaveNovaSenha(chaveNovaSenha);
        BOFactory.save(up);

        getSession().flush();//Utilizado para nao enviar o email em caso de erro devido a concorrencia.

        try {
            Email.create()
                    .assunto("Redefinir Senha")
                    .para(email)
                    .mensagem("Foi solicitado a recuperação de senha da Vigilância.\n\n"
                            + "Se você não fez essa solicitação, por favor desconsidere este e-mail.\n\n"
                            + ""
                            + "Para criar uma nova senha, acesse o link abaixo.\n\n"
                            + ""
                            + "" + origem + "?USUP=" + up.getCodigo() + "&SENP=" + chaveNovaSenha)
                    .send();
        } catch (Throwable ex) {
            Loggable.vigilancia.warn("Erro ao enviar email de recuperação de senha", ex);
            throw new ValidacaoException("Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha.");
        }
    }

    public String getEmail() {
        return email;
    }

    /**
     * Simula delay de processamento para evitar timing attacks
     * que poderiam ser usados para enumerar usuários válidos
     */
    private void simulateProcessingDelay() {
        try {
            // Delay aleatório entre 500ms e 1500ms para simular processamento real
            Thread.sleep(500 + (long)(Math.random() * 1000));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

}
