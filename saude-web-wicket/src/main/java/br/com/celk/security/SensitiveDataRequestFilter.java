package br.com.celk.security;

import org.apache.wicket.protocol.http.WebApplication;
import org.apache.wicket.request.IRequestHandler;
import org.apache.wicket.request.cycle.AbstractRequestCycleListener;
import org.apache.wicket.request.cycle.RequestCycle;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Filtro para interceptar e limpar dados sensíveis das requisições HTTP
 * Previne exposição de dados como login, CPF, etc. nos logs e ferramentas de debug
 */
public class SensitiveDataRequestFilter implements Filter {
    
    // Padrões para identificar parâmetros sensíveis
    private static final Set<Pattern> SENSITIVE_PATTERNS = new HashSet<>();
    
    static {
        SENSITIVE_PATTERNS.add(Pattern.compile(".*login.*", Pattern.CASE_INSENSITIVE));
        SENSITIVE_PATTERNS.add(Pattern.compile(".*cpf.*", Pattern.CASE_INSENSITIVE));
        SENSITIVE_PATTERNS.add(Pattern.compile(".*senha.*", Pattern.CASE_INSENSITIVE));
        SENSITIVE_PATTERNS.add(Pattern.compile(".*password.*", Pattern.CASE_INSENSITIVE));
        SENSITIVE_PATTERNS.add(Pattern.compile(".*usuario.*", Pattern.CASE_INSENSITIVE));
        SENSITIVE_PATTERNS.add(Pattern.compile(".*user.*", Pattern.CASE_INSENSITIVE));
    }
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // Inicialização do filtro
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            
            // Verifica se é uma requisição de recuperação de senha
            if (isPasswordRecoveryRequest(httpRequest)) {
                // Envolve a requisição para interceptar parâmetros
                SensitiveDataRequestWrapper wrappedRequest = new SensitiveDataRequestWrapper(httpRequest);
                
                // Adiciona headers de segurança
                addSecurityHeaders(httpResponse);
                
                chain.doFilter(wrappedRequest, response);
            } else {
                chain.doFilter(request, response);
            }
        } else {
            chain.doFilter(request, response);
        }
    }
    
    @Override
    public void destroy() {
        // Limpeza do filtro
    }
    
    /**
     * Verifica se é uma requisição de recuperação de senha
     */
    private boolean isPasswordRecoveryRequest(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        
        // Verifica pela URI
        if (uri != null && (uri.contains("recuperar") || uri.contains("senha"))) {
            return true;
        }
        
        // Verifica pelos parâmetros
        if (queryString != null) {
            return queryString.contains("recuperar") || 
                   queryString.contains("senha") ||
                   queryString.contains("login");
        }
        
        return false;
    }
    
    /**
     * Adiciona headers de segurança à resposta
     */
    private void addSecurityHeaders(HttpServletResponse response) {
        // Previne cache de páginas sensíveis
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate, private");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        
        // Headers de segurança adicionais
        response.setHeader("X-Content-Type-Options", "nosniff");
        response.setHeader("X-Frame-Options", "DENY");
        response.setHeader("Referrer-Policy", "no-referrer");
    }
    
    /**
     * Wrapper para interceptar e modificar parâmetros sensíveis
     */
    private static class SensitiveDataRequestWrapper extends HttpServletRequestWrapper {
        
        private final Map<String, String[]> modifiedParameters;
        
        public SensitiveDataRequestWrapper(HttpServletRequest request) {
            super(request);
            this.modifiedParameters = new HashMap<>();
            
            // Processa parâmetros originais
            Map<String, String[]> originalParams = request.getParameterMap();
            for (Map.Entry<String, String[]> entry : originalParams.entrySet()) {
                String paramName = entry.getKey();
                String[] paramValues = entry.getValue();
                
                if (isSensitiveParameter(paramName)) {
                    // Substitui valores sensíveis por placeholders
                    String[] maskedValues = new String[paramValues.length];
                    for (int i = 0; i < paramValues.length; i++) {
                        maskedValues[i] = maskSensitiveValue(paramValues[i]);
                    }
                    modifiedParameters.put(paramName, maskedValues);
                } else {
                    modifiedParameters.put(paramName, paramValues);
                }
            }
        }
        
        @Override
        public String getParameter(String name) {
            String[] values = getParameterValues(name);
            return (values != null && values.length > 0) ? values[0] : null;
        }
        
        @Override
        public String[] getParameterValues(String name) {
            return modifiedParameters.get(name);
        }
        
        @Override
        public Map<String, String[]> getParameterMap() {
            return Collections.unmodifiableMap(modifiedParameters);
        }
        
        @Override
        public Enumeration<String> getParameterNames() {
            return Collections.enumeration(modifiedParameters.keySet());
        }
        
        /**
         * Verifica se um parâmetro é sensível
         */
        private boolean isSensitiveParameter(String paramName) {
            if (paramName == null) return false;
            
            for (Pattern pattern : SENSITIVE_PATTERNS) {
                if (pattern.matcher(paramName).matches()) {
                    return true;
                }
            }
            return false;
        }
        
        /**
         * Mascara valores sensíveis
         */
        private String maskSensitiveValue(String value) {
            if (value == null || value.trim().isEmpty()) {
                return value;
            }
            
            // Para valores muito curtos, retorna apenas asteriscos
            if (value.length() <= 2) {
                return "***";
            }
            
            // Para valores maiores, mantém primeiro e último caractere
            return value.charAt(0) + "***" + value.charAt(value.length() - 1);
        }
    }
    
    /**
     * Listener para Wicket que adiciona proteções adicionais
     */
    public static class WicketSecurityListener extends AbstractRequestCycleListener {
        
        @Override
        public IRequestHandler onException(RequestCycle cycle, Exception ex) {
            // Remove dados sensíveis de exceções que podem ser logadas
            if (ex != null && ex.getMessage() != null) {
                String message = ex.getMessage();
                // Remove possíveis dados sensíveis da mensagem de erro
                message = message.replaceAll("\\b\\d{11}\\b", "***CPF***"); // CPF
                message = message.replaceAll("\\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}\\b", "***EMAIL***"); // Email
            }
            
            return super.onException(cycle, ex);
        }
        
        /**
         * Registra o listener na aplicação Wicket
         */
        public static void register(WebApplication application) {
            application.getRequestCycleListeners().add(new WicketSecurityListener());
        }
    }
}
