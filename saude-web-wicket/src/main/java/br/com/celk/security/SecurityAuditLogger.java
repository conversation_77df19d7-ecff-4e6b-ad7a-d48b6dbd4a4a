package br.com.celk.security;

import br.com.ksisolucoes.util.log.Loggable;

/**
 * Utilitário para logs de auditoria de segurança
 * Garante que dados sensíveis não sejam logados
 */
public class SecurityAuditLogger {
    
    /**
     * Registra tentativa de recuperação de senha sem expor dados sensíveis
     * @param clientIP IP do cliente (pode ser logado)
     * @param success se a operação foi bem-sucedida
     * @param userExists se o usuário existe (não deve ser exposto)
     */
    public static void logPasswordRecoveryAttempt(String clientIP, boolean success, boolean userExists) {
        if (success) {
            Loggable.log.info("Password recovery attempt from IP: {} - Process completed", 
                sanitizeIP(clientIP));
        } else {
            Loggable.log.warn("Password recovery attempt from IP: {} - Process failed", 
                sanitizeIP(clientIP));
        }
        
        // Log interno mais detalhado (apenas para auditoria, não para usuário)
        Loggable.log.debug("Password recovery audit - IP: {}, Success: {}, UserExists: {}", 
            sanitizeIP(clientIP), success, userExists);
    }
    
    /**
     * Registra tentativa de rate limiting
     * @param clientIP IP do cliente
     * @param attemptsCount número de tentativas
     */
    public static void logRateLimitingTriggered(String clientIP, int attemptsCount) {
        Loggable.log.warn("Rate limiting triggered for IP: {} - Attempts: {}", 
            sanitizeIP(clientIP), attemptsCount);
    }
    
    /**
     * Registra acesso suspeito (muitas tentativas em pouco tempo)
     * @param clientIP IP do cliente
     * @param timeWindow janela de tempo em minutos
     * @param attempts número de tentativas
     */
    public static void logSuspiciousActivity(String clientIP, int timeWindow, int attempts) {
        Loggable.log.error("SECURITY ALERT - Suspicious password recovery activity from IP: {} - {} attempts in {} minutes", 
            sanitizeIP(clientIP), attempts, timeWindow);
    }
    
    /**
     * Sanitiza o IP para logging seguro
     * @param ip IP original
     * @return IP sanitizado
     */
    private static String sanitizeIP(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return "unknown";
        }
        
        // Remove caracteres potencialmente perigosos para logs
        return ip.replaceAll("[^0-9a-fA-F:.\\-]", "").substring(0, Math.min(ip.length(), 45));
    }
    
    /**
     * Ofusca dados sensíveis para logging
     * @param sensitiveData dado sensível (login, CPF, etc.)
     * @return dado ofuscado
     */
    public static String obfuscateSensitiveData(String sensitiveData) {
        if (sensitiveData == null || sensitiveData.length() <= 2) {
            return "***";
        }
        
        StringBuilder obfuscated = new StringBuilder();
        obfuscated.append(sensitiveData.charAt(0));
        for (int i = 1; i < sensitiveData.length() - 1; i++) {
            obfuscated.append('*');
        }
        obfuscated.append(sensitiveData.charAt(sensitiveData.length() - 1));
        
        return obfuscated.toString();
    }
}
