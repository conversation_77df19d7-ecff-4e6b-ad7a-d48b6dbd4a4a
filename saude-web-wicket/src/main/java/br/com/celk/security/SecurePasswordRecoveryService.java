package br.com.celk.security;

import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Serviço seguro para recuperação de senha que minimiza exposição de dados
 * nas requisições HTTP através de tokens temporários e criptografia
 */
public class SecurePasswordRecoveryService {

    private static final SecurePasswordRecoveryService INSTANCE = new SecurePasswordRecoveryService();
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES";

    // Cache de tokens temporários (em produção, usar Redis ou similar)
    private final ConcurrentHashMap<String, TokenData> tokenCache = new ConcurrentHashMap<>();

    // Chave para criptografia (em produção, usar HSM ou configuração externa)
    private final SecretKey secretKey;

    private SecurePasswordRecoveryService() {
        this.secretKey = generateSecretKey();
    }

    public static SecurePasswordRecoveryService getInstance() {
        return INSTANCE;
    }

    /**
     * Cria um token temporário para os dados de recuperação
     *
     * @param loginData dados de login (login ou CPF)
     * @param clientIP  IP do cliente
     * @return token temporário
     */
    public String createRecoveryToken(String loginData, String clientIP) {
        if (StringUtils.isBlank(loginData)) {
            return null;
        }

        try {
            // Gera um token único
            String tokenId = generateTokenId();

            // Criptografa os dados sensíveis
            String encryptedData = encrypt(loginData);

            // Armazena no cache com expiração
            TokenData tokenData = new TokenData(encryptedData, clientIP, System.currentTimeMillis());
            tokenCache.put(tokenId, tokenData);

            return tokenId;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Processa recuperação de senha usando token
     *
     * @param token         token temporário
     * @param clientIP      IP do cliente
     * @param urlVigilancia URL para vigilância
     * @return email mascarado ou null
     */
    public String processPasswordRecovery(String token, String clientIP, String urlVigilancia)
            throws ValidacaoException, DAOException {

        if (StringUtils.isBlank(token)) {
            throw new ValidacaoException("Token inválido.");
        }

        TokenData tokenData = tokenCache.get(token);
        if (tokenData == null) {
            throw new ValidacaoException("Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha.");
        }

        // Verifica se o token expirou (5 minutos)
        if (System.currentTimeMillis() - tokenData.timestamp > 5 * 60 * 1000) {
            tokenCache.remove(token);
            throw new ValidacaoException("Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha.");
        }

        // Verifica se o IP é o mesmo
        if (!clientIP.equals(tokenData.clientIP)) {
            tokenCache.remove(token);
            throw new ValidacaoException("Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha.");
        }

        try {
            // Descriptografa os dados
            String loginData = decrypt(tokenData.encryptedData);

            // Remove o token do cache (uso único)
            tokenCache.remove(token);

            // Processa a recuperação
            Usuario usuario = new Usuario();
            if (loginData.matches("\\d+")) {
                usuario.setCpf(loginData);
            } else {
                usuario.setLogin(loginData);
            }

            return BOFactoryWicket.getBO(UsuarioFacade.class).gerarNovaSenha(usuario, urlVigilancia);

        } catch (Exception e) {
            tokenCache.remove(token);
            throw new ValidacaoException("Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha.");
        }
    }

    /**
     * Gera um token único
     */
    private String generateTokenId() {
        return UUID.fromString(String.valueOf(System.currentTimeMillis())).toString();
    }

    /**
     * Criptografa dados sensíveis
     */
    private String encrypt(String data) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * Descriptografa dados
     */
    private String decrypt(String encryptedData) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    /**
     * Gera chave secreta para criptografia
     */
    private SecretKey generateSecretKey() {
        try {
            // Em produção, usar chave fixa configurada externamente
            String keyString = "MySecretKey12345"; // 16 bytes para AES-128
            return new SecretKeySpec(keyString.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        } catch (Exception e) {
            throw new RuntimeException("Erro ao gerar chave secreta", e);
        }
    }

    /**
     * Limpa tokens expirados
     */
    public void cleanupExpiredTokens() {
        long currentTime = System.currentTimeMillis();
        tokenCache.entrySet().removeIf(entry ->
                currentTime - entry.getValue().timestamp > 10 * 60 * 1000); // 10 minutos
    }

    /**
     * Classe para armazenar dados do token
     */
    private static class TokenData {
        final String encryptedData;
        final String clientIP;
        final long timestamp;

        TokenData(String encryptedData, String clientIP, long timestamp) {
            this.encryptedData = encryptedData;
            this.clientIP = clientIP;
            this.timestamp = timestamp;
        }
    }
}
