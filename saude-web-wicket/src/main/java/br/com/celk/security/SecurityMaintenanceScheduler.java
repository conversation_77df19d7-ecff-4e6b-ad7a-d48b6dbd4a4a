package br.com.celk.security;

import br.com.ksisolucoes.util.log.Loggable;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.ejb.Singleton;
import javax.ejb.Startup;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Scheduler para manutenção automática dos serviços de segurança
 * Executa limpeza de caches, tokens expirados e outras tarefas de manutenção
 */
@Singleton
@Startup
public class SecurityMaintenanceScheduler {
    
    private ScheduledExecutorService scheduler;
    
    @PostConstruct
    public void init() {
        scheduler = Executors.newScheduledThreadPool(2);
        
        // Limpeza de rate limiting a cada 30 minutos
        scheduler.scheduleAtFixedRate(this::cleanupRateLimiting, 30, 30, TimeUnit.MINUTES);
        
        // Limpeza de tokens de recuperação a cada 10 minutos
        scheduler.scheduleAtFixedRate(this::cleanupRecoveryTokens, 10, 10, TimeUnit.MINUTES);
        
        Loggable.log.info("SecurityMaintenanceScheduler iniciado com sucesso");
    }
    
    @PreDestroy
    public void destroy() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        Loggable.log.info("SecurityMaintenanceScheduler finalizado");
    }
    
    /**
     * Limpa tentativas antigas do rate limiting
     */
    private void cleanupRateLimiting() {
        try {
            RateLimitingService.getInstance().cleanupOldAttempts();
            Loggable.log.debug("Limpeza de rate limiting executada com sucesso");
        } catch (Exception e) {
            Loggable.log.error("Erro na limpeza de rate limiting", e);
        }
    }
    
    /**
     * Limpa tokens expirados de recuperação de senha
     */
    private void cleanupRecoveryTokens() {
        try {
            SecurePasswordRecoveryService.getInstance().cleanupExpiredTokens();
            Loggable.log.debug("Limpeza de tokens de recuperação executada com sucesso");
        } catch (Exception e) {
            Loggable.log.error("Erro na limpeza de tokens de recuperação", e);
        }
    }
}
