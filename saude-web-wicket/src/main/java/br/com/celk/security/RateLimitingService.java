package br.com.celk.security;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Serviço para implementar rate limiting em operações sensíveis
 * como recuperação de senha para prevenir ataques de força bruta
 */
public class RateLimitingService {
    
    private static final RateLimitingService INSTANCE = new RateLimitingService();
    
    // Máximo de tentativas por IP em um período de tempo
    private static final int MAX_ATTEMPTS_PER_IP = 5;
    private static final long TIME_WINDOW_MS = 15 * 60 * 1000; // 15 minutos
    
    // Cache de tentativas por IP
    private final ConcurrentHashMap<String, AttemptInfo> attemptCache = new ConcurrentHashMap<>();
    
    private RateLimitingService() {}
    
    public static RateLimitingService getInstance() {
        return INSTANCE;
    }
    
    /**
     * Verifica se o IP pode fazer uma nova tentativa de recuperação de senha
     * @param clientIP IP do cliente
     * @return true se pode tentar, false se excedeu o limite
     */
    public boolean canAttemptPasswordRecovery(String clientIP) {
        if (clientIP == null || clientIP.trim().isEmpty()) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        AttemptInfo info = attemptCache.computeIfAbsent(clientIP, k -> new AttemptInfo());
        
        synchronized (info) {
            // Se passou do tempo limite, reseta o contador
            if (currentTime - info.firstAttemptTime.get() > TIME_WINDOW_MS) {
                info.attemptCount.set(0);
                info.firstAttemptTime.set(currentTime);
            }
            
            // Verifica se excedeu o limite
            if (info.attemptCount.get() >= MAX_ATTEMPTS_PER_IP) {
                return false;
            }
            
            return true;
        }
    }
    
    /**
     * Registra uma tentativa de recuperação de senha
     * @param clientIP IP do cliente
     */
    public void recordPasswordRecoveryAttempt(String clientIP) {
        if (clientIP == null || clientIP.trim().isEmpty()) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        AttemptInfo info = attemptCache.computeIfAbsent(clientIP, k -> new AttemptInfo());
        
        synchronized (info) {
            // Se passou do tempo limite, reseta o contador
            if (currentTime - info.firstAttemptTime.get() > TIME_WINDOW_MS) {
                info.attemptCount.set(1);
                info.firstAttemptTime.set(currentTime);
            } else {
                info.attemptCount.incrementAndGet();
            }
        }
    }
    
    /**
     * Retorna o tempo restante em minutos até poder tentar novamente
     * @param clientIP IP do cliente
     * @return minutos restantes, ou 0 se pode tentar
     */
    public long getMinutesUntilNextAttempt(String clientIP) {
        if (clientIP == null || clientIP.trim().isEmpty()) {
            return 0;
        }
        
        AttemptInfo info = attemptCache.get(clientIP);
        if (info == null) {
            return 0;
        }
        
        synchronized (info) {
            long currentTime = System.currentTimeMillis();
            long timeSinceFirst = currentTime - info.firstAttemptTime.get();
            
            if (timeSinceFirst > TIME_WINDOW_MS || info.attemptCount.get() < MAX_ATTEMPTS_PER_IP) {
                return 0;
            }
            
            long remainingTime = TIME_WINDOW_MS - timeSinceFirst;
            return (remainingTime / 1000 / 60) + 1; // Arredonda para cima
        }
    }
    
    /**
     * Limpa tentativas antigas do cache (deve ser chamado periodicamente)
     */
    public void cleanupOldAttempts() {
        long currentTime = System.currentTimeMillis();
        attemptCache.entrySet().removeIf(entry -> {
            AttemptInfo info = entry.getValue();
            synchronized (info) {
                return currentTime - info.firstAttemptTime.get() > TIME_WINDOW_MS * 2;
            }
        });
    }
    
    /**
     * Classe interna para armazenar informações de tentativas
     */
    private static class AttemptInfo {
        private final AtomicInteger attemptCount = new AtomicInteger(0);
        private final AtomicLong firstAttemptTime = new AtomicLong(System.currentTimeMillis());
    }
}
