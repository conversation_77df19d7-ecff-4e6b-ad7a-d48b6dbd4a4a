package br.com.celk.system;

import br.com.celk.cluster.interceptor.ClientInterceptorError;
import br.com.celk.component.resource.FtpReportResource;
import br.com.celk.component.resource.StaticReportResource;
import br.com.celk.resources.Resources;
import br.com.celk.system.authorization.AuthorizationStrategy;
import br.com.celk.system.converter.DateTimeConverter;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.properties.SystemPropertiesEnum;
import br.com.celk.system.request.CelkRequestListener;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.login.LoginPage;
import br.com.celk.view.sessaoexpirada.SessaoExpiradaPage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import com.googlecode.wickedcharts.wicket6.JavaScriptResourceRegistry;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.DoubleConverter;
import org.apache.commons.beanutils.converters.LongConverter;
import org.apache.wicket.ConverterLocator;
import org.apache.wicket.IConverterLocator;
import org.apache.wicket.Page;
import org.apache.wicket.Session;
import org.apache.wicket.core.request.mapper.IMapperContext;
import org.apache.wicket.markup.html.SecurePackageResourceGuard;
import org.apache.wicket.protocol.http.WebApplication;
import org.apache.wicket.request.Request;
import org.apache.wicket.request.Response;
import org.apache.wicket.request.cycle.AbstractRequestCycleListener;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.resource.SharedResourceReference;
import org.apache.wicket.protocol.http.servlet.ServletWebResponse;
import org.apache.wicket.request.resource.caching.FilenameWithVersionResourceCachingStrategy;
import org.apache.wicket.request.resource.caching.version.CachingResourceVersion;
import org.apache.wicket.request.resource.caching.version.LastModifiedResourceVersion;
import org.apache.wicket.util.lang.Bytes;
import org.apache.wicket.util.time.Duration;
import org.jboss.ejb.client.EJBClientContext;
import org.wicketstuff.annotation.scan.AnnotatedMountScanner;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.jar.Manifest;

public class Application extends WebApplication implements Serializable{

    private String urlPapaleguas;
    private Date ultimaConsulta;
    
    @Override
    public Class<? extends Page> getHomePage() {
        return LoginPage.class;
    }

    public static Application get() {
        return (Application) WebApplication.get();
    }

    @Override
    protected void init() {
        super.init();
        try{
            Manifest manifest = new Manifest(get().getServletContext().getResourceAsStream("/META-INF/MANIFEST.MF"));
            String componentPath = manifest.getMainAttributes().getValue("ComponentPath");
            if("development".equals(componentPath)){
                getDebugSettings().setOutputComponentPath(true);
                getResourceSettings().setResourcePollFrequency(Duration.ONE_SECOND);
            }
        } catch (IOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        //Resources
        getResourceSettings().setCachingStrategy(new FilenameWithVersionResourceCachingStrategy(new CachingResourceVersion(new LastModifiedResourceVersion())));

        //Security
        getApplicationSettings().setPageExpiredErrorPage(SessaoExpiradaPage.class);
        getApplicationSettings().setAccessDeniedPage(SessaoExpiradaPage.class);
        getSecuritySettings().setAuthorizationStrategy(new AuthorizationStrategy());
//        getSecuritySettings().setUnauthorizedComponentInstantiationListener(new UnauthorizationStrategy());

        // Configurações de segurança XSS
        configureXSSProtection();

        // Registra listener de segurança para dados sensíveis
        br.com.celk.security.SensitiveDataRequestFilter.WicketSecurityListener.register(this);

        getMarkupSettings().setDefaultMarkupEncoding("UTF-8");
        getMarkupSettings().setCompressWhitespace(true);
        
        
        getSharedResources().add("report", new FtpReportResource());
        mountResource("report", new SharedResourceReference("report"));
        getSharedResources().add("staticReport", new StaticReportResource());
        mountResource("staticReport", new SharedResourceReference("staticReport"));

        new AnnotatedMountScanner().scanPackage(SystemPropertiesEnum.PAGES_SCAN_PACKAGE.toString()).mount(this);
//        setRootRequestMapper(new CryptoMapper(getRootRequestMapper(), this));QUEBRA A TELA DE ATENDIMENTO DO PRONTUARIO QUANDO VAI INFORMAR O PROFISSIONAL NO DIALOG
        
        //Session Store
        getRequestCycleListeners().add(new CelkRequestListener());
//        setSessionStoreProvider(new IProvider<ISessionStore>() {
//
//            @Override
//            public ISessionStore get() {
//                return new CelkSessionStore(new HttpSessionStore());
//            }
//        });
        getStoreSettings().setInmemoryCacheSize(5000);
        getStoreSettings().setMaxSizePerSession(Bytes.kilobytes(50));
//        getStoreSettings().setAsynchronousQueueCapacity(100);
        
        ConvertUtils.register(new LongConverter(null), Long.class);
        ConvertUtils.register(new DoubleConverter(null), Double.class);
        ConvertUtils.register(new org.apache.commons.beanutils.converters.DateConverter(null), Date.class);
        
        SecurePackageResourceGuard guard = new SecurePackageResourceGuard();
        guard.addPattern("+*.htm");
        getResourceSettings().setPackageResourceGuard(guard);
        
        EJBClientContext.getCurrent().registerInterceptor(0, new ClientInterceptorError());
        JavaScriptResourceRegistry.getInstance().setHighchartsReference(Resources.JS_HIGHCHARTS);
        JavaScriptResourceRegistry.getInstance().setHighchartsMoreReference(Resources.JS_HIGHCHARTS_MORE);
        JavaScriptResourceRegistry.getInstance().setHighchartsExportingReference(Resources.JS_EXPORTING);
    }

    @Override
    protected IMapperContext newMapperContext() {
        return new CelkMapperContext();
    }
    
    @Override
    public Session newSession(Request request, Response response) {
        return new ApplicationSession(request);
    }

    @Override
    protected IConverterLocator newConverterLocator() {
        ConverterLocator converterLocator = new ConverterLocator();

        converterLocator.set(Date.class, new DateTimeConverter());
        converterLocator.set(Timestamp.class, new DateTimeConverter());
        converterLocator.set(java.sql.Date.class, new DateTimeConverter());

        return converterLocator;
    }

    @Override
    public void sessionUnbound(String sessionId) {
        super.sessionUnbound(sessionId);
    }

    public String getUrlPapaleguas(){
        if(ultimaConsulta == null || ultimaConsulta.before(Data.removeMinutos(DataUtil.getDataAtual(), 30))){
            try {
                urlPapaleguas = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("URL_Papaleguas");
            } catch (DAOException ex) {
                urlPapaleguas = "";
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return urlPapaleguas;
    }

    /**
     * Configura proteções contra XSS
     */
    private void configureXSSProtection() {
        // Configurar CSP (Content Security Policy) via RequestCycleListener
        getRequestCycleListeners().add(new AbstractRequestCycleListener() {
            @Override
            public void onBeginRequest(RequestCycle cycle) {
                if (cycle.getResponse() instanceof ServletWebResponse) {
                    ServletWebResponse response = (ServletWebResponse) cycle.getResponse();

                    HttpServletResponse httpResponse = response.getContainerResponse();

                    // Content Security Policy - Proteção robusta contra XSS
                    String csp = buildContentSecurityPolicy();
                    httpResponse.setHeader("Content-Security-Policy", csp);

                    // Outros headers de segurança são adicionados no URLParameterValidationFilter
                }
            }
        });

        // Configurar escape automático de markup
        getMarkupSettings().setStripWicketTags(true);
        getMarkupSettings().setDefaultMarkupEncoding("UTF-8");

        // Configurar validação de parâmetros
        getRequestCycleSettings().setResponseRequestEncoding("UTF-8");
    }

    /**
     * Constrói a política de segurança de conteúdo (CSP)
     */
    private String buildContentSecurityPolicy() {
        StringBuilder csp = new StringBuilder();

        // Política padrão - apenas recursos do próprio domínio
        csp.append("default-src 'self'; ");

        // Scripts - permitir próprio domínio + Google reCAPTCHA + inline necessário para Wicket
        csp.append("script-src 'self' 'unsafe-inline' 'unsafe-eval' ")
           .append("https://www.google.com/recaptcha/ ")
           .append("https://www.gstatic.com/recaptcha/ ")
           .append("https://apis.google.com; ");

        // Estilos - permitir próprio domínio + inline para componentes
        csp.append("style-src 'self' 'unsafe-inline' ")
           .append("https://fonts.googleapis.com; ");

        // Imagens - permitir próprio domínio + data URIs + HTTPS
        csp.append("img-src 'self' data: https:; ");

        // Fontes - permitir próprio domínio + Google Fonts
        csp.append("font-src 'self' ")
           .append("https://fonts.gstatic.com; ");

        // Conexões - permitir próprio domínio + APIs necessárias
        csp.append("connect-src 'self' ")
           .append("https://www.google.com/recaptcha/; ");

        // Frames - permitir apenas Google reCAPTCHA
        csp.append("frame-src 'self' ")
           .append("https://www.google.com/recaptcha/; ");

        // Objetos - bloquear completamente
        csp.append("object-src 'none'; ");

        // Base URI - apenas próprio domínio
        csp.append("base-uri 'self'; ");

        // Formulários - apenas próprio domínio
        csp.append("form-action 'self'; ");

        // Upgrade de conexões inseguras
        csp.append("upgrade-insecure-requests; ");

        return csp.toString();
    }
}
