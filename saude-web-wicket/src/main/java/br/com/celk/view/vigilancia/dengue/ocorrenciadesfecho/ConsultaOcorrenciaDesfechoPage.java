package br.com.celk.view.vigilancia.dengue.ocorrenciadesfecho;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.SalaUnidade;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueOcorrenciaDesfecho;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 639
 */
@Private
public class ConsultaOcorrenciaDesfechoPage extends ConsultaPage<DengueOcorrenciaDesfecho, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DengueOcorrenciaDesfecho proxy = on(DengueOcorrenciaDesfecho.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<DengueOcorrenciaDesfecho>() {
            @Override
            public void customizeColumn(final DengueOcorrenciaDesfecho rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<DengueOcorrenciaDesfecho>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueOcorrenciaDesfecho modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroOcorrenciaDesfechoPage(rowObject));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<DengueOcorrenciaDesfecho>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueOcorrenciaDesfecho modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return DengueOcorrenciaDesfecho.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(DengueOcorrenciaDesfecho.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(DengueOcorrenciaDesfecho.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(SalaUnidade.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroOcorrenciaDesfechoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaOcorrenciaDesfecho");
    }
}
