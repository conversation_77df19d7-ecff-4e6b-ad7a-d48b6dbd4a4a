package br.com.celk.view.atendimento.consulta.relatorio;

import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.report.RelatorioPage;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 140
 */
@Private
public class RelatorioPerfilAtendimentosPage extends RelatorioPage<RelatorioPerfilAtendimentoDTOParam> {

    private PnlChoicePeriod pnlDatePeriod;

    @Override
    public void init(Form form) {
        form.add(pnlDatePeriod = new RequiredPnlChoicePeriod("periodo"));
    }

    @Override
    public Class<RelatorioPerfilAtendimentoDTOParam> getDTOParamClass() {
        return RelatorioPerfilAtendimentoDTOParam.class;
    }

    @Override
    public void customDTOParam(RelatorioPerfilAtendimentoDTOParam param) {
        param.setFaixaEtaria((FaixaEtaria) LoadManager.getInstance(FaixaEtaria.class).addParameter(new QueryCustom.QueryCustomParameter(FaixaEtaria.PROP_CODIGO, FaixaEtaria.FAIXA_ETARIA_SIAB)).start().getVO());
    }

    @Override
    public DataReport getDataReport(RelatorioPerfilAtendimentoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioPerfilAtendimento(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("perfilAtendimentos");
    }
}
