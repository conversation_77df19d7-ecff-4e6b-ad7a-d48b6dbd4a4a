package br.com.celk.view.materiais.informacoesprofissional;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.materiais.informacoesprofissional.customize.CustomizeConsultaProfissional;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 71
 */
@Private
public class ConsultaInformacoesProfissionalPage extends ConsultaPage<Profissional, List<BuilderQueryCustom.QueryParameter>> {

    private String nome;
    private String numeroRegistro;
    private String referencia;

    public ConsultaInformacoesProfissionalPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        this.getControls().setEnabled(false);
        this.getControls().setVisible(false);
        form.add(new UpperField("referencia"));
        form.add(new InputField<String>("numeroRegistro"));
        form.add(new InputField<String>("nome"));

        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(Profissional.class);
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(Profissional.PROP_REFERENCIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome"), VOUtils.montarPath(Profissional.PROP_NOME)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("numeroRegistro"), VOUtils.montarPath(Profissional.PROP_NUMERO_REGISTRO)));

        return columns;
    }

    private CustomColumn<Profissional> getCustomColumn() {
        return new CustomColumn<Profissional>() {

            @Override
            public Component getComponent(String componentId, final Profissional rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroInformacoesProfissionalPage(rowObject,false,true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }
                                       
                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroInformacoesProfissionalPage(rowObject, true));
                    }

                    @Override
                    public boolean isExcluirVisible() {
                        return false;
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaProfissional()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Profissional.PROP_NOME, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Profissional.PROP_REFERENCIA), BuilderQueryCustom.QueryParameter.ILIKE, referencia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Profissional.PROP_NUMERO_REGISTRO), BuilderQueryCustom.QueryParameter.ILIKE, numeroRegistro));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Profissional.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nome));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroInformacoesProfissionalPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaInformacoesProfissional");
    }
}
