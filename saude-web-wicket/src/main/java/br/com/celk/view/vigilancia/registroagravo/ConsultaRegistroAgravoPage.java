package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.table.CustomColorTableRow;
import br.com.celk.component.table.TableColorEnum;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cid.autocomplete.AutoCompleteConsultaCidMulti;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.prontuario.basico.classificacaocid.autocomplete.AutoCompleteConsultaClassificacaoCid;
import br.com.celk.view.vigilancia.registroagravo.autocomplete.AutoCompleteConsultaTipoOcorrenciaAgravo;
import br.com.celk.view.vigilancia.registroagravo.dialog.DlgMotivoCancelamentoRegistroAgravo;
import br.com.celk.vigilancia.dto.ConsultaRegistroAgravoDTOParam;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.agravo.TipoOcorrenciaAgravo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import ch.lambdaj.function.convert.Converter;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.celk.util.CollectionUtils.TipoOrdenacao.ASC;
import static br.com.celk.util.CollectionUtils.TipoOrdenacao.DESC;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 545
 */
@SuppressWarnings("java:S110")
@Private
public class ConsultaRegistroAgravoPage extends ConsultaPage<RegistroAgravo, ConsultaRegistroAgravoDTOParam> {


    private String paciente;
    private String cpf;
    private String rg;
    private String nomeMaeUsuarioCadsus;
    private String telefone;
    private Date dataDeNascimento;
    private Empresa empresa;

    @SuppressWarnings("java:S1068")
    private Empresa unidadeReferencia;
    private DatePeriod periodo;

    private Long codigoNotificacao;
    private Long situacao;
    private Long gestante;
    private Long tipoOcorrencia;
    private Date dtPrimeiroSintoma;
    private DropDown<Long> dropDownSituacao;
    private DropDown<Long> dropDownGestante;
    private DropDown<Long> dropDownTipoOcorrencia;
    private DlgMotivoCancelamentoRegistroAgravo dlgMotivoCancelamentoRegistroAgravo;
    private RadioButtonGroup radioPrimeirosSintomas;

    private List<Cid> lstCid;
    private ClassificacaoCids classificacaoCids;
    private TipoOcorrenciaAgravo tipoOcorrenciaAgravo;
    private AutoCompleteConsultaTipoOcorrenciaAgravo autoCompleteTipoOcorrenciaAgravo;
    private List<RegistroAgravo> registroAgravo;
    private IPagerProvider<RegistroAgravo, ConsultaRegistroAgravoDTOParam> pagerProvider;
    private Set<QueryCustomSorter> sorters;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("paciente"));
        form.add(new InputField<String>("cpf"));
        form.add(new InputField<String>("rg"));
        form.add(new TelefoneField("telefone"));
        form.add(new DateChooser("dataDeNascimento"));
        form.add(new InputField<String>("nomeMaeUsuarioCadsus"));
        form.add(new AutoCompleteConsultaEmpresa("empresa"));
        form.add(new AutoCompleteConsultaEmpresa("unidadeReferencia"));
        form.add(new AutoCompleteConsultaClassificacaoCid("classificacaoCids"));
        form.add(new AutoCompleteConsultaCidMulti("lstCid"));
        form.add(getDropDownSituacao("situacao"));
        form.add(getDropDownGestante("gestante"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(new InputField<Long>("codigoNotificacao"));
        form.add(getRadiosContainerPrimeirosSintomas("radioPrimeirosSintomas"));
        form.add(getDropDownTipoOcorrencia("tipoOcorrencia"));
        form.add(autoCompleteTipoOcorrenciaAgravo = new AutoCompleteConsultaTipoOcorrenciaAgravo("tipoOcorrenciaAgravo"));

        add(form);

        addModal(dlgMotivoCancelamentoRegistroAgravo = new DlgMotivoCancelamentoRegistroAgravo(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, RegistroAgravo registroAgravo) throws ValidacaoException, DAOException {
                BOFactoryWicket.save(registroAgravo);
                getPageableTable().update(target);
            }
        });
    }

    public RadioButtonGroup getRadiosContainerPrimeirosSintomas(String id) {
        if (radioPrimeirosSintomas == null) {
            radioPrimeirosSintomas = new RadioButtonGroup(id, new PropertyModel(this, "dtPrimeiroSintoma"));
            radioPrimeirosSintomas.add(new AjaxRadio("todos", new Model(null)).setOutputMarkupId(true));
            radioPrimeirosSintomas.add(new AjaxRadio("tempoSintomasD3", new Model(subtraiDiasEmUmaData(RegistroAgravo.PrimeirosSintomas.D3.value()))).setOutputMarkupId(true));
            radioPrimeirosSintomas.add(new AjaxRadio("tempoSintomasD7", new Model(subtraiDiasEmUmaData(RegistroAgravo.PrimeirosSintomas.D7.value()))).setOutputMarkupId(true));
            radioPrimeirosSintomas.add(new AjaxRadio("tempoSintomasD10", new Model(subtraiDiasEmUmaData(RegistroAgravo.PrimeirosSintomas.D10.value()))).setOutputMarkupId(true));
            radioPrimeirosSintomas.add(new AjaxRadio("tempoSintomasD14", new Model(subtraiDiasEmUmaData(RegistroAgravo.PrimeirosSintomas.D14.value()))).setOutputMarkupId(true));

        }
        return radioPrimeirosSintomas;
    }

    public DropDown<Long> getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<>(id);
            dropDownSituacao.addChoice(null, BundleManager.getString("todos"));
            dropDownSituacao.addChoice(RegistroAgravo.Status.PENDENTE.value(), bundle("pendente"));
            dropDownSituacao.addChoice(RegistroAgravo.Status.MONITORAMENTO.value(), bundle("monitoramento"));
            dropDownSituacao.addChoice(RegistroAgravo.Status.CONCLUIDO.value(), bundle("monitoramento_concluido"));
            dropDownSituacao.addChoice(RegistroAgravo.Status.CANCELADO.value(), bundle("cancelado"));
            dropDownSituacao.addChoice(RegistroAgravo.Status.EM_INVESTIGACAO.value(), bundle("emInvestigacao"));
            dropDownSituacao.addChoice(RegistroAgravo.Status.INVESTIGACAO_CONCLUIDA.value(), bundle("investigacaoConcluida"));
        }
        return dropDownSituacao;
    }

    public DropDown<Long> getDropDownTipoOcorrencia(String id) {
        if (dropDownTipoOcorrencia == null) {
            dropDownTipoOcorrencia = new DropDown<>(id);
            dropDownTipoOcorrencia.addChoice(null, BundleManager.getString("rotulo_possui"));
            dropDownTipoOcorrencia.addChoice(RepositoryComponentDefault.SIM_LONG, bundle("rotulo_nao_possui"));
        }
        return dropDownTipoOcorrencia;
    }

    public DropDown<Long> getDropDownGestante(String id) {
        if (dropDownGestante == null) {
            dropDownGestante = new DropDown<>(id);
            dropDownGestante.addChoice(null, BundleManager.getString("ambos"));
            dropDownGestante.addChoice(RepositoryComponentDefault.SIM_LONG, bundle("sim"));
            dropDownGestante.addChoice(RepositoryComponentDefault.NAO_LONG, bundle("nao"));
        }
        return dropDownGestante;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        RegistroAgravo proxy = on(RegistroAgravo.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("codigoNotificacao"), proxy.getCodigoNotificacao()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getUsuarioCadsus().getNome(), proxy.getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("dataRegistro"), proxy.getDataRegistro()));
        columns.add(createSortableColumn(bundle("dataPrimeirosSintomas"), proxy.getDataPrimeirosSintomas()));
        columns.add(createSortableColumn(bundle("cidAgravo"), proxy.getCid().getDescricao()));
        columns.add(createSortableColumn(bundle("gestante"), proxy.getGestante(), proxy.getDescricaoGestante()));
        columns.add(createColumn(bundle("unidadeNotificadora"), proxy.getUnidadeNotificadora()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        columns.add(createColumn(bundle("dataLimiteParaEncerramento"), proxy.getDataLimiteEncerramento()));
        columns.add(createColumn(bundle("dataEncerramento"), proxy.getDataEncerramento()));
        return columns;
    }

    @Override
    public Item getCustomColorTable(String id, int index, IModel model) {
        return new CustomColorTableRow(id, index, model) {
            @Override
            public TableColorEnum getColor() {
                RegistroAgravo ra = ((RegistroAgravo) getRowObject());
                if (ra != null && ra.getDataLimiteEncerramento() != null && (RegistroAgravo.Status.EM_INVESTIGACAO.value().equals(ra.getStatus()) || RegistroAgravo.Status.PENDENTE.value().equals(ra.getStatus())) && DataUtil.dataCompareTo(DataUtil.getDataAtualSemHora(), ra.getDataLimiteEncerramento()) > 0) {
                    return TableColorEnum.VERMELHA_LIGHT;
                }
                return TableColorEnum.PADRAO;
            }
        };
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<RegistroAgravo>() {
            @Override
            public void customizeColumn(RegistroAgravo rowObject) {
                teste(rowObject);
            }

            private void teste(final RegistroAgravo rowObject) {
                boolean possuiFichaInvestigacao = isPossuiFichaInvestigacao(rowObject);
                boolean possuiRegAgOcorrencias = !getRegistroAgravoOcorrencias(rowObject).isEmpty();

                addAction(ActionType.EDITAR, rowObject, new IModelAction<RegistroAgravo>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroAgravo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroAgravoPage(modelObject));
                    }
                }).setEnabled(RegistroAgravo.Status.PENDENTE.value().equals(rowObject.getStatus()));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<RegistroAgravo>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroAgravo modelObject) throws ValidacaoException, DAOException {
                        dlgMotivoCancelamentoRegistroAgravo.show(target, modelObject);
                    }
                }).setQuestionDialogBundleKey(null)
                        .setEnabled(getStatusCancelar(rowObject));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<RegistroAgravo>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroAgravo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesRegistroAgravoPage(modelObject));
                    }
                }).setTitleBundleKey("detalhes");

                addAction(ActionType.EVOLUIR, rowObject, new IModelAction<RegistroAgravo>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroAgravo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new MonitoracaoRegistroAgravoPage(modelObject));
                    }
                }).setTitleBundleKey("monitorar")
                        .setIcon(Icon.MONITOR)
                        .setEnabled(getStatusMonitoramento(rowObject, possuiFichaInvestigacao, possuiRegAgOcorrencias));

                addAction(ActionType.INVESTIGAR, rowObject, new IModelAction<RegistroAgravo>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroAgravo modelObject) throws ValidacaoException, DAOException {
                        investigarAction(modelObject);
                    }
                }).setTitleBundleKey("investigar")
                        .setEnabled(getStatusInvestigar(rowObject, possuiFichaInvestigacao));

                addAction(ActionType.ADICIONAR, rowObject, new IModelAction<RegistroAgravo>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroAgravo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new MonitoracaoRegistroAgravoPage(modelObject));
                    }
                }).setTitleBundleKey("ocorrencia")
                        .setIcon(Icon.NOTEPAD)
                        .setEnabled(getStatusOcorrencia(rowObject, possuiFichaInvestigacao, possuiRegAgOcorrencias));

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<RegistroAgravo>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroAgravo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new MonitoracaoRegistroAgravoPage(modelObject, true));
                    }
                }).setTitleBundleKey("concluir")
                        .setEnabled(getStatusConcluir(rowObject, possuiFichaInvestigacao, possuiRegAgOcorrencias));
            }
        };
    }

    private void investigarAction(RegistroAgravo modelObject) {
        boolean modoLeitura = RegistroAgravo.Status.INVESTIGACAO_CONCLUIDA.value().equals(modelObject.getStatus())
                && !modelObject.isPermiteAlterar()
                || (isActionPermitted(Permissions.CONSULTAR, ConsultaRegistroAgravoPage.class) && !isActionPermitted(Permissions.REGISTRAR, ConsultaRegistroAgravoPage.class));

        RegistroAgravo.TipoFichaInvestigacaoAgravo tipoFichaInvestigacaoAgravo = modelObject.getTipoFichaInvesticao();

        try {
            switch (tipoFichaInvestigacaoAgravo) {
                case EVENTOS_ADVERSOS_POSVACINACAO:
                    setResponsePage(new FichaInvestigacaoAgravoPage(modelObject));
                    break;
                case TRATAMENTO_ANTIRRABICO:
                    setResponsePage(new FichaInvestigacaoAgravoTratamentoAntirabicoPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case HIV_GESTANTE:
                    setResponsePage(new FichaInvestigacaoAgravoHivGestantePage(modelObject));
                    break;
                case SIFILIS_CONGENITA:
                    setResponsePage(new FichaInvestigacaoAgravoSifilisCongenitaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case AIDS_ADULTO:
                    setResponsePage(new FichaInvestigacaoAgravoAidsAdultoPage(modelObject));
                    break;
                case COVID_19:
                    setResponsePage(new FichaInvestigacaoAgravoCovid19Page(modelObject.getCodigo(), modoLeitura));
                    break;
                case ACIDENTE_GRAVE_TRABALHO:
                    setResponsePage(new FichaInvestigacaoAgravoAcidenteTrabalhoGravePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case SIFILIS_GESTANTE:
                    setResponsePage(new FichaInvestigacaoAgravoSifilisGestantePage(modelObject));
                    break;
                case DENGUE_ZIKA_CHIKUNGUNYA:
                    setResponsePage(new FichaInvestigacaoAgravoChikungunyaZikaDenguePage(modelObject.getCodigo(),modoLeitura));
                    break;
                case SARS:
                    setResponsePage(new FichaInvestigacaoAgravoSarsPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case VIOLENCIA:
                    setResponsePage(new FichaInvestigacaoAgravoViolenciaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case SIFILIS_ADQUIRIDA:
                    setResponsePage(new FichaInvestigacaoAgravoSifilisAdquiridaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case CRIANCA_EXPOSTA_HIV:
                    setResponsePage(new FichaInvestigacaoAgravoCriancaExpostaHivPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case MALARIA:
                    setResponsePage(new FichaInvestigacaoAgravoMalariaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case LEPTOSPIROSE:
                    setResponsePage(new FichaInvestigacaoAgravoLeptospirosePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case HEPATITE_VIRAL:
                    setResponsePage(new FichaInvestigacaoAgravoHepatiteViralPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case FEBRE_AMARELA:
                    setResponsePage(new FichaInvestigacaoAgravoFebreAmarelaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case ACIDENTE_POR_ANIMAL_PECONHENTO:
                    setResponsePage(new FichaInvestigacaoAgravoAcidenteAnimalPeconhentoPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case LEISHMANIOSE_VISCERAL:
                    setResponsePage(new FichaInvestigacaoAgravoLeishmanioseVisceralPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case INTOXICACAO_EXOGENA:
                    setResponsePage(new FichaInvestigacaoAgravoIntoxicacaoExogenaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case COQUELUCHE:
                    setResponsePage(new FichaInvestigacaoAgravoCoqueluchePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case LEISHMANIOSE_TEGUMENTAR_AMERICANA:
                    setResponsePage(new FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case ACIDENTE_TRABALHO_MATERIAL_BIOLOGICO:
                    setResponsePage(new FichaInvestigacaoAgravoAcidenteDeTrabalhoComMaterialBiologicoPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case SINDROME_CORRIMENTO_URETRAL_MASCULINO:
                    setResponsePage(new FichaInvestigacaoAgravoSindromeCorrimentoUretralMasculinoPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case HANSENIASE:
                    setResponsePage(new FichaInvestigacaoAgravoHanseniasePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case MENINGITE:
                    setResponsePage(new FichaInvestigacaoAgravoMeningitePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case FEBRE_MACULOSA_OUTRAS_RICKETTSIOSES:
                    setResponsePage(new FichaInvestigacaoAgravoFebreMaculosaOutrasRickettsiosesPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case ACIDENTE_TRABALHO_DERMATOSE_OCUPACIONAL:
                    setResponsePage(new FichaInvestigacaoAgravoDermatoseOcupacionalPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case HANTAVIROSE:
                    setResponsePage(new FichaInvestigacaoAgravoHantavirosePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case DOENCA_RELACIOANADA_TRABALHO:
                    setResponsePage(new FichaInvestigacaoAgravoDoencaTrabalhoLerDortPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case DOENCAS_EXANTEMATICAS_RUBEOLA_SARAMPO:
                    setResponsePage(new FichaInvestigacaoAgravoRubeolaSarampoPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case DOENCA_RELACIOANADA_TRABALHO_CANCER:
                    setResponsePage(new FichaInvestigacaoAgravoDoencaTrabalhoCancerPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case DOENCA_RELACIONADA_TRABALHO_PNEUMONOCONIOSES:
                    setResponsePage(new FichaInvestigacaoAgravoDoencaTrabalhoPneumonoconiosesPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case DOENCA_RELACIONADA_TRABALHO_PAIR:
                    setResponsePage(new FichaInvestigacaoAgravoDoencaTrabalhoPairPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case ACIDENTE_TRABALHO_TRANSTORNO_MENTAL:
                    setResponsePage(new FichaInvestigacaoAgravoAcidenteDeTrabalhoTranstornoMentalPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case BOTULISMO:
                    setResponsePage(new FichaInvestigacaoAgravoBotulismoPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case CASOS_INUSITADOS:
                    setResponsePage(new FichaInvestigacaoAgravoCasosInusitadosPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case PESTE:
                    setResponsePage(new FichaInvestigacaoAgravoPestePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case AIDS_CRIANCA:
                    setResponsePage(new FichaInvestigacaoAgravoAidsCriancaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case EPIZOOTIA:
                    setResponsePage(new FichaInvestigacaoAgravoEpizootiaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case DOENCA_LYME:
                    setResponsePage(new FichaInvestigacaoAgravoDoencaLymePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case ROTAVIRUS:
                    setResponsePage(new FichaInvestigacaoAgravoRotavirusPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case BRUCELOSE:
                    setResponsePage(new FichaInvestigacaoAgravoBrucelosePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case TRACOMA:
                    setResponsePage(new FichaInvestigacaoAgravoTracomaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case DOENCA_CREUTZFELDT_JACOB:
                    setResponsePage(new FichaInvestigacaoAgravoDoencaCreutzfeldtJacobPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case PARALISIA_FLACIDA_AGUDA:
                    setResponsePage(new FichaInvestigacaoAgravoParalisiaFlacidaAgudaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case CISTICERCOSE:
                    setResponsePage(new FichaInvestigacaoAgravoCisticercosePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case DOENCA_CHAGAS:
                    setResponsePage(new FichaInvestigacaoAgravoDoencaDeChagasPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case TETANO_ACIDENTAL:
                    setResponsePage(new FichaInvestigacaoAgravoTetanoAcidentalPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case FEBRE_NILO_OCIDENTAL:
                    setResponsePage(new FichaInvestigacaoAgravoFebreNiloOcidentalPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case NOTIFICACAO_NEGATIVA:
                    setResponsePage(new FichaInvestigacaoAgravoNotificacaoNegativaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case RUBEOLA_CONGENITA:
                    setResponsePage(new FichaInvestigacaoAgravoRubeolaCongenitaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case MICROCEFALIA:
                    setResponsePage(new FichaInvestigacaoAgravoMicrocefaliaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case ESQUISTOSSOMOSE:
                    setResponsePage(new FichaInvestigacaoAgravoEsquistossomosePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case TUBERCULOSE:
                    setResponsePage(new FichaInvestigacaoAgravoTuberculosePage(modelObject.getCodigo(), modoLeitura));
                    break;

                case TETANO_NEONATAL:
                    setResponsePage(new FichaInvestigacaoAgravoTetanoNeonatalPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case SURTO:
                    setResponsePage(new FichaInvestigacaoAgravoSurtoPage(modelObject.getCodigo(), modoLeitura));
                    break;
                case FEBRE_TIFOIDE:
                    setResponsePage(new FichaInvestigacaoAgravoFebreTifoidePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case MENINGOCOCICA_MENINGITES:
                    setResponsePage(new FichaInvestigacaoAgravoMenigococicaMeningitePage(modelObject.getCodigo(), modoLeitura));
                    break;
                case VIOLENCIA_INTERPESSOAL_AUTOPROVOCADA:
                    setResponsePage(new FichaInvestigacaoAgravoViolenciaInterpessoalAutoprovocadaPage(modelObject.getCodigo(), modoLeitura));
                    break;
                default:
                    setResponsePage(new FichaInvestigacaoAgravoPage(modelObject));
                    break;
            }
        } catch (Exception e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }

    private boolean getStatusCancelar(RegistroAgravo rowObject) {
        if (VigilanciaHelper.isMonitoramentoContinuo(rowObject)) {
            if (RegistroAgravo.Status.PENDENTE.value().equals(rowObject.getStatus())
                    || RegistroAgravo.Status.MONITORAMENTO.value().equals(rowObject.getStatus())
                    || RegistroAgravo.Status.EM_INVESTIGACAO.value().equals(rowObject.getStatus())) {
                return true;
            }
        }

        return RegistroAgravo.Status.PENDENTE.value().equals(rowObject.getStatus())
                || RegistroAgravo.Status.MONITORAMENTO.value().equals(rowObject.getStatus());
    }

    private boolean getStatusInvestigar(RegistroAgravo rowObject, boolean possuiFichaInvestigacao) {
        if (!isActionPermitted(Permissions.REGISTRAR, ConsultaRegistroAgravoPage.class)
                && !isActionPermitted(Permissions.CONSULTAR, ConsultaRegistroAgravoPage.class)) {
            return false;
        }

        if (VigilanciaHelper.isMonitoramentoContinuo(rowObject)) {
            if (RegistroAgravo.Status.CANCELADO.value().equals(rowObject.getStatus())) {
                return false;
            }
            if (RegistroAgravo.Status.MONITORAMENTO.value().equals(rowObject.getStatus())) {
                return true;
            }
        }
        return possuiFichaInvestigacao;
    }

    private boolean getStatusConcluir(RegistroAgravo rowObject, boolean possuiFichaInvestigacao, boolean possuiRegAgOcorrencias) {
        if (VigilanciaHelper.isMonitoramentoContinuo(rowObject)) {
            if (RegistroAgravo.Status.MONITORAMENTO.value().equals(rowObject.getStatus())) {
                return true;
            }
            if (possuiRegAgOcorrencias && RegistroAgravo.Status.EM_INVESTIGACAO.value().equals(rowObject.getStatus())) {
                return true;
            }
        }
        return RegistroAgravo.Status.MONITORAMENTO.value().equals(rowObject.getStatus()) && !possuiFichaInvestigacao;
    }

    private boolean getStatusOcorrencia(RegistroAgravo rowObject, boolean possuiFichaInvestigacao, boolean possuiRegAgOcorrencias) {
        if (VigilanciaHelper.isMonitoramentoContinuo(rowObject)) {
            if (RegistroAgravo.Status.MONITORAMENTO.value().equals(rowObject.getStatus())) {
                return true;
            }
            if (possuiRegAgOcorrencias && RegistroAgravo.Status.EM_INVESTIGACAO.value().equals(rowObject.getStatus())) {
                return true;
            }
        }
        return RegistroAgravo.Status.MONITORAMENTO.value().equals(rowObject.getStatus()) && !possuiFichaInvestigacao;
    }

    private boolean getStatusMonitoramento(RegistroAgravo rowObject, boolean possuiFichaInvestigacao, boolean possuiRegAgOcorrencias) {
        if (VigilanciaHelper.isMonitoramentoContinuo(rowObject)) {
            if (RegistroAgravo.Status.PENDENTE.value().equals(rowObject.getStatus())) {
                return true;
            }
            if (possuiRegAgOcorrencias && RegistroAgravo.Status.EM_INVESTIGACAO.value().equals(rowObject.getStatus())) {
                return false;
            } else if (!possuiRegAgOcorrencias && RegistroAgravo.Status.EM_INVESTIGACAO.value().equals(rowObject.getStatus())) {
                return true;
            }
        }
        return RegistroAgravo.Status.PENDENTE.value().equals(rowObject.getStatus()) && !possuiFichaInvestigacao;
    }

    private List<RegistroAgravo> getRegistroAgravoOcorrencias(RegistroAgravo registroAgravo) {
        return LoadManager.getInstance(RegistroAgravoOcorrencia.class)
                .addProperties(new HQLProperties(RegistroAgravoOcorrencia.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RegistroAgravoOcorrencia.PROP_REGISTRO_AGRAVO, registroAgravo))
                .start().getList();
    }

    private boolean isPossuiFichaInvestigacao(RegistroAgravo rowObject) {
        boolean possuiFichaInvestigacao = false;
        if (rowObject.getCid() != null && rowObject.getCid().getCidClassificacao() != null && rowObject.getCid().getCidClassificacao().getFichaInvestigacaoAgravo() != null) {
            possuiFichaInvestigacao = true;
        }
        return possuiFichaInvestigacao;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {

        if (this.pagerProvider != null) {
            return this.pagerProvider;
        }

        this.pagerProvider = new QueryPagerProvider<RegistroAgravo, ConsultaRegistroAgravoDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaRegistroAgravoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarRegistroAgravo(dataPaging);
            }

            @Override
            public void customizeParam(ConsultaRegistroAgravoDTOParam param) {
                SingleSortState<String> sortState = (SingleSortState) getPagerProvider().getSortState();

                initSorter();

                applySorters(param, sortState);
            }

            private void applySorters(final ConsultaRegistroAgravoDTOParam param,
                                      final SingleSortState<String> sortState) {

                if (!ConsultaRegistroAgravoPage.this.sorters.isEmpty() && sortState.getSort() == null) {
                    return;
                }

                String campoOrdenacao = RegistroAgravo.PROP_DATA_REGISTRO;
                boolean isAscending = false;
                if (sortState.getSort() != null) {
                    campoOrdenacao = sortState.getSort().getProperty();
                    isAscending = sortState.getSort().isAscending();
                }

                ConsultaRegistroAgravoPage.this.sorters = getQueryCustomSorters(campoOrdenacao, isAscending);
                param.setSorters(ConsultaRegistroAgravoPage.this.sorters);
            }

            private Set<QueryCustomSorter> getQueryCustomSorters(final String campoOrdenacao, final boolean isAscending) {
                Set<QueryCustomSorter> customSorters = new LinkedHashSet<>();
                customSorters.add(
                        new QueryCustomSorter(campoOrdenacao, (isAscending ? ASC : DESC).name()));
                for (QueryCustomSorter queryCustomSorter : ConsultaRegistroAgravoPage.this.sorters) {
                    if (Lambda.filter(Matchers
                                    .hasProperty("prop", Matchers.equalTo(queryCustomSorter.getProp())),
                            customSorters).isEmpty()) {
                        customSorters.add(queryCustomSorter);
                    }
                }
                return customSorters;
            }
        };
        return this.pagerProvider;
    }

    @Override
    public ConsultaRegistroAgravoDTOParam getParameters() {

        ConsultaRegistroAgravoDTOParam param = new ConsultaRegistroAgravoDTOParam();

        param.setNomePaciente(paciente);
        param.setCpf(cpf);
        param.setRg(rg);
        param.setTelefone(telefone);
        param.setDataDeNascimento(dataDeNascimento);
        param.setNomeMaeUsuarioCadsus(nomeMaeUsuarioCadsus);
        param.setCodigoEmpresa(empresa != null ? empresa.getCodigo() : null);
        param.setSituacao(situacao);
        param.setGestante(gestante);
        param.setDtPrimeiroSintoma(radioPrimeirosSintomas != null ?
                (Date) radioPrimeirosSintomas.getConvertedInput() : null);
        param.setCodigoNotificacao(codigoNotificacao);
        param.setCodigoClassificacaoCids(classificacaoCids != null ? classificacaoCids.getCodigo() : null);

        param.setIncluiAgravo(dropDownTipoOcorrencia.getComponentValue() == null);
        param.setAgravos(autoCompleteTipoOcorrenciaAgravo.getComponentValue() != null ? getListaAgravos() : Collections.emptySet());
        param.setCids(getCidsCodes(lstCid));

        param.setPeriodo(periodo);
        param.setCodigoUnidadeReferencia(unidadeReferencia != null ? unidadeReferencia.getCodigo() : null);

        return param;
    }

    private void initSorter() {
        if (sorters == null) {
            sorters = new LinkedHashSet<>();
        }
    }

    private Set<String> getCidsCodes(List<Cid> lstCid) {
        if (lstCid == null) {
            return Collections.emptySet();
        }
        return new LinkedHashSet<>(
                Lambda.map(lstCid, new Converter<Cid, String>() {
                    @Override
                    public String convert(Cid from) {
                        return from != null ? from.getCodigo() : null;
                    }
                }).keySet()
        );
    }

    private LinkedHashSet<Long> getListaAgravos() {
        List<RegistroAgravoOcorrencia> tiposOcorrencia = LoadManager.getInstance(RegistroAgravoOcorrencia.class)
                .addProperties(new HQLProperties(RegistroAgravoOcorrencia.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RegistroAgravoOcorrencia.PROP_TIPO_OCORRENCIA_AGRAVO, autoCompleteTipoOcorrenciaAgravo.getComponentValue()))
                .start().getList();

        LinkedHashSet<Long> listaAgravos = new LinkedHashSet<>();

        for (RegistroAgravoOcorrencia registroAgravoOcorrencia : tiposOcorrencia) {
            listaAgravos.add(registroAgravoOcorrencia.getRegistroAgravo().getCodigo());
        }
        return listaAgravos;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRegistroAgravoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRegistroAgravo");
    }

    private Date subtraiDiasEmUmaData(Long dias) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -dias.intValue());
        return calendar.getTime();
    }
}
