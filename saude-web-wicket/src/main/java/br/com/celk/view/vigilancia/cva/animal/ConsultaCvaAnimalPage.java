package br.com.celk.view.vigilancia.cva.animal;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.cva.animal.dialog.DlgCvaAnimalRegistroOcorrencia;
import br.com.celk.view.vigilancia.cva.proprietarioresponsavel.autocomplete.AutoCompleteConsultaCvaProprietarioResponsavelAnimal;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.EspecieAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.CvaRacaAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.animal.CvaAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.animal.CvaAnimalOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.cva.proprietarioresponsavel.CvaProprietarioResponsavel;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 604
 */
@Private
public class ConsultaCvaAnimalPage extends ConsultaPage<CvaAnimal, List<BuilderQueryCustom.QueryParameter>> {

    private String nomeAnimal;
    private Long tipoAnimal;
    private Long numeroMicrochip;
    private String especieAnimal;
    private String proprietarioAnimal;
    private String microchip;
    private Long situacao;
    private DropDown<Long> dropDownSituacao;
    private DropDown<Long> dropDownTipoAnimal;
    private CvaProprietarioResponsavel cvaProprietarioResponsavel;
    private DlgCvaAnimalRegistroOcorrencia dlgCvaAnimalRegistroOcorrencia;
    private CvaAnimal cvaAnimal;

    public ConsultaCvaAnimalPage() {
    }

    public ConsultaCvaAnimalPage(CvaAnimal cvaAnimal) {
        this.cvaAnimal = cvaAnimal;
        setProcurarAoAbrir(true);
        getPageableTable().populate();
        dropDownSituacao.setComponentValue(null);
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("nomeAnimal"));
        form.add(getDropDownSituacao("situacao"));
        form.add(new AutoCompleteConsultaCvaProprietarioResponsavelAnimal("cvaProprietarioResponsavel"));
        form.add(getTipoAnimalDropDown("tipoAnimal"));
        form.add(new InputField<String>("numeroMicrochip"));
        add(form);
        dropDownSituacao.setComponentValue(CvaAnimal.Status.VIVO.value());
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        CvaAnimal proxy = on(CvaAnimal.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("nomeAnimal"), proxy.getNomeAnimal()));
        columns.add(createSortableColumn(BundleManager.getString("tipoAnimal"), proxy.getTipoAnimal(), proxy.getDescricaoTipoAnimal()));
        columns.add(createSortableColumn(BundleManager.getString("especie"), proxy.getCvaRacaAnimal().getEspecieAnimal().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("proprietarioResponsavel"), proxy.getCvaProprietarioResponsavel().getProprietarioResponsavel()));
        columns.add(createSortableColumn(BundleManager.getString("numeroMicrochip"), proxy.getNumeroMicrochip()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<CvaAnimal>() {
            @Override
            public void customizeColumn(final CvaAnimal rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<CvaAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaAnimal modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCvaAnimalPage(rowObject, false, true));
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<CvaAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaAnimal modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalheCvaAnimalPage(rowObject));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<CvaAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaAnimal modelObject) throws ValidacaoException, DAOException {
                        if (existeOcorrencia(modelObject.getCodigo())) {
                            throw new ValidacaoException(BundleManager.getString("msgExisteOcorrenciaAnimal"));
                        }
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });

                addAction(ActionType.OCORRENCIA, rowObject, new IModelAction<CvaAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaAnimal modelObject) throws ValidacaoException, DAOException {
                        viewDlgOcorrencia(target);
                        dlgCvaAnimalRegistroOcorrencia.show(target, modelObject);
                    }
                });
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<CvaAnimal>() {
                    @Override
                    public DataReport action(CvaAnimal modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoFichaCvaAnimal(modelObject);
                    }
                });
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return CvaAnimal.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(CvaAnimal.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(CvaAnimal.PROP_NOME_ANIMAL),
                            VOUtils.montarPath(CvaAnimal.PROP_TIPO_ANIMAL),
                            VOUtils.montarPath(CvaAnimal.PROP_NUMERO_MICROCHIP),
                            VOUtils.montarPath(CvaAnimal.PROP_PROFISSIONAL_CASTRACAO, Profissional.PROP_NOME),
                            VOUtils.montarPath(CvaAnimal.PROP_FOTO, GerenciadorArquivo.PROP_CODIGO),
                            VOUtils.montarPath(CvaAnimal.PROP_FOTO, GerenciadorArquivo.PROP_CAMINHO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_RACA_ANIMAL, CvaRacaAnimal.PROP_ESPECIE_ANIMAL, EspecieAnimal.PROP_DESCRICAO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_RACA_ANIMAL, CvaRacaAnimal.PROP_ESPECIE_ANIMAL, EspecieAnimal.PROP_CODIGO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_PROPRIETARIO_RESPONSAVEL),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_LOGRADOURO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_BAIRRO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_COMPLEMENTO_LOGRADOURO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_NUMERO_LOGRADOURO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_TELEFONE1),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_TELEFONE2),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_TELEFONE3),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_FLAG_ESTRANGEIRO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_PAIS_NASCIMENTO, Pais.PROP_CODIGO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_PAIS_NASCIMENTO, Pais.PROP_DESCRICAO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CEP),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_DESCRICAO),
                            VOUtils.montarPath(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA),
                        });
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(CvaAnimal.PROP_NOME_ANIMAL, true);
            }
        };
    }

    public DropDown<Long> getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>(id);
            dropDownSituacao.addChoice(null, BundleManager.getString("todos"));
            dropDownSituacao.addChoice(CvaAnimal.Status.VIVO.value(), BundleManager.getString("rotuloVivo"));
            dropDownSituacao.addChoice(CvaAnimal.Status.MORTO.value(), BundleManager.getString("rotuloMorto"));
            dropDownSituacao.addChoice(CvaAnimal.Status.DESAPARECIDO.value(), BundleManager.getString("rotuloDesaparecido"));
        }
        return dropDownSituacao;
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CvaAnimal.PROP_NOME_ANIMAL), BuilderQueryCustom.QueryParameter.ILIKE, nomeAnimal));
        parameters.add(new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_STATUS, situacao));
        if (cvaProprietarioResponsavel != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, cvaProprietarioResponsavel));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_TIPO_ANIMAL, tipoAnimal));
        parameters.add(new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_NUMERO_MICROCHIP, numeroMicrochip));
        if (cvaAnimal != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_CODIGO, cvaAnimal.getCodigo()));
        }
        cvaAnimal = null;
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroCvaAnimalPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaAnimais");
    }

    public DropDown<Long> getTipoAnimalDropDown(String id) {
        if (dropDownTipoAnimal == null) {
            dropDownTipoAnimal = new DropDown<Long>(id);
            dropDownTipoAnimal.addChoice(null, BundleManager.getString("todos"));
            dropDownTipoAnimal.addChoice(CvaAnimal.TipoAnimal.DE_PROPRIETARIO.value(), BundleManager.getString("tpAnimalDeProprietario"));
            dropDownTipoAnimal.addChoice(CvaAnimal.TipoAnimal.ERRANTE.value(), BundleManager.getString("tpAnimalErrante"));
            dropDownTipoAnimal.addChoice(CvaAnimal.TipoAnimal.COMUNITARIO.value(), BundleManager.getString("tpAnimalComunitario"));
        }
        return dropDownTipoAnimal;
    }

    private boolean existeOcorrencia(Long cvaAnimal) {
        CvaAnimalOcorrencia proxy = on(CvaAnimalOcorrencia.class);
        return LoadManager.getInstance(CvaAnimalOcorrencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCvaAnimal().getCodigo()), cvaAnimal))
                .setMaxResults(1)
                .start().getVO() != null;
    }

    private void viewDlgOcorrencia(AjaxRequestTarget target) {
        if (dlgCvaAnimalRegistroOcorrencia == null) {
            addModal(target, dlgCvaAnimalRegistroOcorrencia = new DlgCvaAnimalRegistroOcorrencia(newModalId()));
        }
    }

}
