package br.com.celk.view.vigilancia.solicitacaousuario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaSolicitacaoUsuario;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 734
 */
@Private
public class CadastroSolicitacaoUsuarioVigilanciaPage extends BasePage {

    private AutoCompleteConsultaUsuario autoCompleteConsultaUsuario;
    private Table<VigilanciaSolicitacaoUsuario> table;
    private List<IColumn> columns;
    private CollectionProvider collectionProvider;
    private WebMarkupContainer container;
    private DropDown dropDownTipoDocumento;
    
    private List<VigilanciaSolicitacaoUsuario> lstUsuSetor = new ArrayList<>();
    private VigilanciaSolicitacaoUsuario vigilanciaSolicitacaoUsuario;
    private Long tipoDocumento;
    private AbstractAjaxButton btnSalvar;
    private CompoundPropertyModel<VigilanciaSolicitacaoUsuario> model;
    private AbstractAjaxButton btnAdicionar;
    
    public CadastroSolicitacaoUsuarioVigilanciaPage() {
        init();
    }
    
    private void init() {
        Form form = new Form("form",new CompoundPropertyModel(this));
        form.add(dropDownTipoDocumento = DropDownUtil.getIEnumDropDown("tipoDocumento", TipoSolicitacao.TipoDocumento.values(), true, "", false, false, true));
        
        container = new WebMarkupContainer("container", model = new CompoundPropertyModel(vigilanciaSolicitacaoUsuario == null ? new VigilanciaSolicitacaoUsuario() : vigilanciaSolicitacaoUsuario));
        
        container.add(autoCompleteConsultaUsuario = new AutoCompleteConsultaUsuario(VigilanciaSolicitacaoUsuario.PROP_USUARIO));
        container.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
        
        dropDownTipoDocumento.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (tipoDocumento != null) {
                    carregaRegistros();
                    setEnable(target,true);
                    target.focusComponent(autoCompleteConsultaUsuario.getTxtDescricao().getTextField());
                } else {
                    setEnable(target, false);
                    lstUsuSetor.clear();
                }
                table.update(target);
            }
        });
        container.setOutputMarkupId(true);
        container.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        
        form.add(btnSalvar = new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));
        
        btnSalvar.setEnabled(false);
        container.setEnabled(false);
        form.add(container);
        add(form);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (tipoDocumento == null) {
            target.focusComponent(dropDownTipoDocumento);
            throw new ValidacaoException(BundleManager.getString("selecioneSolicitacao"));
        }
        
        if (model.getObject().getUsuario() == null) {
            target.focusComponent(autoCompleteConsultaUsuario);
            throw new ValidacaoException(BundleManager.getString("informeUsuario"));
        }
        
        if (!jaExiste()) {
            this.lstUsuSetor.add(model.getObject());
            this.table.update(target);
        } else {
            target.focusComponent(autoCompleteConsultaUsuario);
            throw new ValidacaoException(BundleManager.getString("usuarioJaInformado"));
        }
        limpar(target);
    }
    
    private boolean jaExiste() {
        for (VigilanciaSolicitacaoUsuario item : lstUsuSetor) {
            if (item.getUsuario().getCodigo().equals(this.model.getObject().getUsuario().getCodigo())) {
                return true;
            }
        }
        return false;
    }

    private CollectionProvider getCollectionProvider() {
        if (this.collectionProvider == null) {
            this.collectionProvider = new CollectionProvider() {
                @Override
                public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                    return lstUsuSetor;
                }
            };
        }
        return this.collectionProvider;
    }
    
    private List<IColumn> getColumns() {
        if (this.columns == null) {
            this.columns = new ArrayList<>();

            ColumnFactory columnFactory = new ColumnFactory(VigilanciaSolicitacaoUsuario.class);
            
            this.columns.add(getCustomColumn());
            this.columns.add(columnFactory.createSortableColumn(BundleManager.getString("usuario"), VOUtils.montarPath(VigilanciaSolicitacaoUsuario.PROP_USUARIO, Usuario.PROP_NOME)));
        }
        return this.columns;
    }
    
    private CustomColumn<VigilanciaSolicitacaoUsuario> getCustomColumn() {
        return new CustomColumn<VigilanciaSolicitacaoUsuario>() {

            @Override
            public Component getComponent(String componentId, final VigilanciaSolicitacaoUsuario rowObject) {
                return new RemoverActionColumnPanel(componentId) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException {
                        remover(target, rowObject);
                    }
                };
            }
        };
    }
    
    private void remover(AjaxRequestTarget target, VigilanciaSolicitacaoUsuario vigilanciaSolicitacaoUsuario) {
        for (int i = 0; i < lstUsuSetor.size(); i++) {
            if (lstUsuSetor.get(i).getUsuario().getCodigo().equals(vigilanciaSolicitacaoUsuario.getUsuario().getCodigo())) {
                lstUsuSetor.remove(i);
                break;
            }
        }
        table.update(target);
    }

    private void limpar(AjaxRequestTarget target) {
        model.setObject(new VigilanciaSolicitacaoUsuario());
        autoCompleteConsultaUsuario.limpar(target);
        target.focusComponent(autoCompleteConsultaUsuario.getTxtDescricao().getTextField());
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("solicitacaoXUsuario");
    }
    
    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (tipoDocumento == null) {
            target.focusComponent(dropDownTipoDocumento);
            throw new ValidacaoException(BundleManager.getString("selecioneSolicitacao"));
        }
        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarVigilanciaSolicitacaoUsuario(lstUsuSetor, tipoDocumento);
        limparTudo(target);
    }
    
    private void carregaRegistros() {
            this.lstUsuSetor = LoadManager.getInstance(VigilanciaSolicitacaoUsuario.class)
                    .addProperties(new HQLProperties(VigilanciaSolicitacaoUsuario.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaSolicitacaoUsuario.PROP_TIPO_DOCUMENTO, tipoDocumento))
                    .start().getList();
    }
    
    private void limparTudo(AjaxRequestTarget target){
        limpar(target);
        this.lstUsuSetor.clear();
        table.update(target);
        dropDownTipoDocumento.limpar(target);
        setEnable(target, false);
        target.focusComponent(dropDownTipoDocumento);
    }
    
    public void setEnable(AjaxRequestTarget target, boolean enable) {
        container.setEnabled(enable);
        btnSalvar.setEnabled(enable);
        target.add(autoCompleteConsultaUsuario);
        target.add(btnAdicionar);
        target.add(btnSalvar);
    }
}
