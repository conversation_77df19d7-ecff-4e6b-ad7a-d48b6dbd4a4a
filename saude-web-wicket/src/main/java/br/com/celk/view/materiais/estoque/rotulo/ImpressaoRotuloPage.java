package br.com.celk.view.materiais.estoque.rotulo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AjaxReportLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.lote.saida.PnlSaidaLote;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.estoque.ImpressaoRotuloDTOParam;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

/**
 * <AUTHOR> S. Schmoeller
 * Programa - 941
 */
@Private
public class ImpressaoRotuloPage extends BasePage {

    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private Form form;
    private Produto produto;
    private String grupoEstoque;
    private Long quantidadeRotulos;
    private Double quantidadeProduto;
    private LongField txtQuantidadeRotulos;
    private DoubleField txtQuantidadeProduto;
    private AjaxReportLink btnImprimir;
    private AbstractAjaxButton btnImprimirZebra;

    private Long tipoImpressora;

    private PnlSaidaLote pnlSaidaLote;

    public ImpressaoRotuloPage() throws DAOException {
        tipoImpressora = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("tamanhoImpressaoRotulo");
        init();
    }

    private void init() {

        form = new Form("form", new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                pnlSaidaLote.limpar(target);
            }
        });
        autoCompleteConsultaProduto.setIncluirInativos(false);

        form.add(pnlSaidaLote = new PnlSaidaLote("grupoEstoque"));
        pnlSaidaLote.setAutoCompleteConsultaProduto(autoCompleteConsultaProduto);
        pnlSaidaLote.setValidarLotesVencidos(true);
        pnlSaidaLote.registerEvents();

        form.add(txtQuantidadeRotulos = new LongField("quantidadeRotulos"));
        txtQuantidadeRotulos.setVMax(999L);
        txtQuantidadeRotulos.addAjaxUpdateValue();

        form.add(txtQuantidadeProduto = new DoubleField("quantidadeProduto"));
        txtQuantidadeProduto.setVMax(99999D);
        txtQuantidadeProduto.addAjaxUpdateValue();
        txtQuantidadeProduto.add(new Tooltip().setText("quantidadeReferenteProdutoImpressaRotulo"));


        form.add(btnImprimir = new AjaxReportLink("btnImprimir") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                MovimentoGrupoEstoqueItemDTO dto;

                if (produto == null) {
                    throw new ValidacaoException(BundleManager.getString("informeProduto"));
                }

                if (RepositoryComponentDefault.SIM.equals(produto.getSubGrupo().getFlagControlaGrupoEstoque())) {
                    if (pnlSaidaLote.getLoteSelecionado() != null) {
                        dto = pnlSaidaLote.getLoteSelecionado();
                        dto.setProduto(produto);
                    } else {
                        throw new ValidacaoException(BundleManager.getString("informeLote"));
                    }
                } else {
                    dto = new MovimentoGrupoEstoqueItemDTO();
                    dto.setProduto(produto);
                    dto.setGrupoEstoque("0");
                }

                if (quantidadeProduto == null) {
                    throw new ValidacaoException(BundleManager.getString("informeQuantidadeProdutos"));
                }
                if (quantidadeRotulos == null) {
                    throw new ValidacaoException(BundleManager.getString("informeQuantidadeRotulos"));
                }

                List<Long> lstCodigoBarrasProduto = BOFactoryWicket.getBO(ProdutoFacade.class).gerarCodigoBarrasProduto(dto, quantidadeRotulos, quantidadeProduto);

                ImpressaoRotuloDTOParam param = new ImpressaoRotuloDTOParam();
                param.setLstCodigoBarrasProduto(lstCodigoBarrasProduto);
                param.setLote(dto.getGrupoEstoque());
                param.setValidade(dto.getDataValidade());
                param.setQuantidadeRotulos(quantidadeRotulos);
                param.setProduto(produto);
                if (pnlSaidaLote != null && pnlSaidaLote.getLoteSelecionado() != null && pnlSaidaLote.getLoteSelecionado().getFabricante() != null) {
                    param.setFabricante(pnlSaidaLote.getLoteSelecionado().getFabricante());
                }
                param.setTipoImpressora(tipoImpressora);
                param.setQuantidadeProduto(quantidadeProduto);

                return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioImpressaoRotulo(param);
            }
        });

        btnImprimir.setOutputMarkupPlaceholderTag(true);
        btnImprimir.setVisible(true);

        add(form);

    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("impressaoRotulo");
    }

}
