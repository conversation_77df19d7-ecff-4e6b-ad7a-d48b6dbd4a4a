package br.com.celk.view.materiais.estoque.volume;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AjaxReportLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.ImpressaoEtiquetaVolumeDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoEntrega;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 694
 */
@Private
public class ImpressaoEtiquetaVolumePage extends BasePage {

    private Form form;

    private Long quantidadeEtiquetas;
    private LongField txtQuantidadeEtiquetas;

    private String numeroGuia;
    private InputField txtNumeroGuia;

    @Override
    protected void postConstruct() {
        super.postConstruct();

        form = new Form("form", new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(txtNumeroGuia = new InputField("numeroGuia"));
        txtNumeroGuia.addAjaxUpdateValue();
        form.add(txtQuantidadeEtiquetas = new LongField("quantidadeEtiquetas"));
        txtQuantidadeEtiquetas.setVMax(999L);
        txtQuantidadeEtiquetas.addAjaxUpdateValue();

        form.add(new AjaxReportLink("btnImprimir") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                if (numeroGuia == null || numeroGuia.trim().isEmpty()) {
                    throw new ValidacaoException(BundleManager.getString("msgInformeNumeroGuia"));
                }

                if (quantidadeEtiquetas == null) {
                    throw new ValidacaoException(BundleManager.getString("informeQuantidade"));
                }

                boolean existsGuia = LoadManager.getInstance(PedidoTransferenciaLicitacaoEntrega.class)
                        .setId(Long.valueOf(numeroGuia))
                        .exists();
                if (!existsGuia) {
                    throw new ValidacaoException(BundleManager.getString("msgGuiaNaoEncontradaParaEsteNumero"));
                }

                PedidoTransferenciaLicitacaoEntrega pedidoTransferenciaLicitacaoEntrega = LoadManager.getInstance(PedidoTransferenciaLicitacaoEntrega.class)
                        .addProperty(VOUtils.montarPath(PedidoTransferenciaLicitacaoEntrega.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_CODIGO))
                        .setId(Long.valueOf(numeroGuia))
                        .start().getVO();

                if (pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao() == null || pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().getCodigo() == null) {
                    throw new ValidacaoException(BundleManager.getString("msgPedidoParaEstaGuia"));
                }

                ImpressaoEtiquetaVolumeDTOParam param = new ImpressaoEtiquetaVolumeDTOParam();
                param.setNumeroGuia(numeroGuia);
                param.setNumeroPedido(pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().getCodigo().toString());
                param.setQuantidadeEtiquetas(quantidadeEtiquetas);

                return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioImpressaoEtiquetaVolume(param);
            }
        });

        form.add(new AbstractAjaxButton("btnConfiguracoes") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConfiguracoesImpressaoEtiquetaVolumePage());
            }
        });

        add(form);

    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("impressaoEtiquetasEmbarque");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtNumeroGuia;
    }

}
