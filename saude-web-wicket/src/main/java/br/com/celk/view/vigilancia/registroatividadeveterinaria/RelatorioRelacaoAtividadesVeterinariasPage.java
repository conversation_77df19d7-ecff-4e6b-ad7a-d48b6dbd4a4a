package br.com.celk.view.vigilancia.registroatividadeveterinaria;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaAtividadeVeterinaria;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaEspecieAnimal;
import br.com.celk.vigilancia.dto.RelatorioRelacaoAtividadeVeterinariaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 570
 */
@Private
public class RelatorioRelacaoAtividadesVeterinariasPage extends RelatorioPage<RelatorioRelacaoAtividadeVeterinariaDTOParam>{

    @Override
    public void init(Form<RelatorioRelacaoAtividadeVeterinariaDTOParam> form) {
        
        RelatorioRelacaoAtividadeVeterinariaDTOParam proxy = on(RelatorioRelacaoAtividadeVeterinariaDTOParam.class);
        
        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidade())));
        form.add(new AutoCompleteConsultaAtividadeVeterinaria(path(proxy.getAtividadeVeterinaria())));
        form.add(new AutoCompleteConsultaEspecieAnimal(path(proxy.getEspecieAnimal())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSexo()), RelatorioRelacaoAtividadeVeterinariaDTOParam.Sexo.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioRelacaoAtividadeVeterinariaDTOParam.FormaApresentacao.values()));
        form.add(new PnlDatePeriod(path(proxy.getPeriodo())));
        
    }

    @Override
    public Class<RelatorioRelacaoAtividadeVeterinariaDTOParam> getDTOParamClass() {
        return RelatorioRelacaoAtividadeVeterinariaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoAtividadeVeterinariaDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRelacaoAtividadeVeterinaria(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoRegistroAtividade");
    }
    
}
