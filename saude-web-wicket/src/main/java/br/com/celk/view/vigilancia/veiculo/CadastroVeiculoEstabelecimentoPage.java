package br.com.celk.view.vigilancia.veiculo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.placafield.PlacaField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.RequerimentoLicencaTransporteHelper;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 247
 */
@Private
public class CadastroVeiculoEstabelecimentoPage extends BasePage {

    private InputField txtPlaca;
    private InputField txtRenavam;
    private DropDown<Long> dropDownTercerizada;
    private Table<VeiculoEstabelecimento> table;
    private List<IColumn> columns;
    private CollectionProvider collectionProvider;
    private InputField txtRestricoes;
    private InputField txtEspecificacao;
    private InputField txtTipoVeiculo;
    private WebMarkupContainer containerVeiculo;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;

    private List<VeiculoEstabelecimento> lstVeiculos = new ArrayList<VeiculoEstabelecimento>();
    private VeiculoEstabelecimento veiculoEstabelecimento;
    private Estabelecimento estabelecimento;
    private AbstractAjaxButton btnSalvar;
    private CompoundPropertyModel<VeiculoEstabelecimento> model;

    public CadastroVeiculoEstabelecimentoPage() {
        init();
    }

    public void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento("estabelecimento"));

        containerVeiculo = new WebMarkupContainer("containerVeiculo", model = new CompoundPropertyModel(veiculoEstabelecimento == null ? new VeiculoEstabelecimento() : veiculoEstabelecimento));

        containerVeiculo.add(txtPlaca = new PlacaField(VeiculoEstabelecimento.PROP_PLACA));
        containerVeiculo.add(txtRenavam = new InputField(VeiculoEstabelecimento.PROP_RENAVAM));
        containerVeiculo.add(txtTipoVeiculo = new InputField(VeiculoEstabelecimento.PROP_TIPO_VEICULO));
        containerVeiculo.add(txtEspecificacao = new InputField(VeiculoEstabelecimento.PROP_ESPECIFICACAO));
        containerVeiculo.add(txtRestricoes = new InputField(VeiculoEstabelecimento.PROP_RESTRICOES));
        containerVeiculo.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();

        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                if (object != null) {
                    carregaVeiculos(object);
                    setEnableVeiculos(target, true);
                    target.focusComponent(txtPlaca);
                } else {
                    setEnableVeiculos(target, false);
                    lstVeiculos.clear();
                }
                table.update(target);
            }
        });
        containerVeiculo.setOutputMarkupId(true);
        containerVeiculo.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        form.add(btnSalvar = new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        btnSalvar.setEnabled(false);
        containerVeiculo.setEnabled(false);
        form.add(containerVeiculo);
        add(form);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (estabelecimento == null) {
            throw new ValidacaoException(BundleManager.getString("informeEstabelecimento"));
        }

        VeiculoEstabelecimento veiculoEstabelecimento = this.model.getObject();

        if (RepositoryComponentDefault.SIM_LONG.equals(VigilanciaHelper.getConfiguracaoVigilancia().getFlagObrigaDadosVeiculo())) {
            if (veiculoEstabelecimento.getPlaca() == null) {
                target.focusComponent(txtPlaca);
                throw new ValidacaoException(BundleManager.getString("informePlaca"));
            }

            if (veiculoEstabelecimento.getEspecificacao() == null) {
                target.focusComponent(txtEspecificacao);
                throw new ValidacaoException(BundleManager.getString("informeEspecificacao"));
            }
        }

        if (veiculoEstabelecimento.getTipoVeiculo() == null) {
            target.focusComponent(txtTipoVeiculo);
            throw new ValidacaoException(BundleManager.getString("informeTipoVeiculo"));
        }

        validaCarroJaInformado(target, veiculoEstabelecimento);

        this.lstVeiculos.add(veiculoEstabelecimento);
        this.table.update(target);
        limpar(target);
    }

    private void validaCarroJaInformado(AjaxRequestTarget target, VeiculoEstabelecimento veiculoEstabelecimento) throws ValidacaoException {
        if (RequerimentoLicencaTransporteHelper.existsVeiculoEstabelecimentoIn(lstVeiculos, veiculoEstabelecimento)) {
            if (veiculoEstabelecimento.getPlaca() == null) {
                target.focusComponent(txtTipoVeiculo);
                throw new ValidacaoException(BundleManager.getString("tipoVeiculoSemPlacaJaInformado"));
            } else {
                target.focusComponent(txtPlaca);
                throw new ValidacaoException(BundleManager.getString("placaJaInformada"));
            }
        }
    }

    private CollectionProvider getCollectionProvider() {
        if (this.collectionProvider == null) {
            this.collectionProvider = new CollectionProvider() {
                @Override
                public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                    return lstVeiculos;
                }
            };
        }
        return this.collectionProvider;
    }

    private List<IColumn> getColumns() {
        if (this.columns == null) {
            this.columns = new ArrayList<IColumn>();

            ColumnFactory columnFactory = new ColumnFactory(VeiculoEstabelecimento.class);

            this.columns.add(getCustomColumn());
            this.columns.add(columnFactory.createSortableColumn(BundleManager.getString("placa"), VOUtils.montarPath(VeiculoEstabelecimento.PROP_PLACA), VOUtils.montarPath(VeiculoEstabelecimento.PROP_PLACA_FORMATADA)));
            this.columns.add(columnFactory.createSortableColumn(BundleManager.getString("especificacao"), VOUtils.montarPath(VeiculoEstabelecimento.PROP_ESPECIFICACAO)));
            this.columns.add(columnFactory.createSortableColumn(BundleManager.getString("observacao"), VOUtils.montarPath(VeiculoEstabelecimento.PROP_RESTRICOES)));
        }
        return this.columns;
    }

    private CustomColumn<VeiculoEstabelecimento> getCustomColumn() {
        return new CustomColumn<VeiculoEstabelecimento>() {

            @Override
            public Component getComponent(String componentId, final VeiculoEstabelecimento rowObject) {
                return new RemoverActionColumnPanel(componentId) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException {
                        remover(target, rowObject);
                    }
                };
            }
        };
    }

    private void remover(AjaxRequestTarget target, VeiculoEstabelecimento _veiculoEstabelecimento) {
        RequerimentoLicencaTransporteHelper.excluirVeiculoEstabelecimentoIn(lstVeiculos, _veiculoEstabelecimento);
        table.update(target);
    }

    public DropDown getDropDownTercerizada() {
        if (dropDownTercerizada == null) {
            dropDownTercerizada = new DropDown<Long>("terceirizada", new PropertyModel<Long>(this, "terceirizada"));
            dropDownTercerizada.addChoice(RepositoryComponentDefault.NAO_LONG, BundleManager.getString("nao"));
            dropDownTercerizada.addChoice(RepositoryComponentDefault.SIM_LONG, BundleManager.getString("sim"));
        }
        return dropDownTercerizada;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField();
    }

    private void limpar(AjaxRequestTarget target) {
        model.setObject(new VeiculoEstabelecimento());
        txtPlaca.limpar(target);
        txtEspecificacao.limpar(target);
        txtRestricoes.limpar(target);
        txtTipoVeiculo.limpar(target);
        target.focusComponent(txtPlaca);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("veiculos");
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (estabelecimento == null) {
            target.focusComponent(autoCompleteConsultaEstabelecimento);
            throw new ValidacaoException(BundleManager.getString("informeEstabelecimento"));
        }
        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarVeiculosEstabelecimento(lstVeiculos, estabelecimento);
        limparTudo(target);
    }

    private void carregaVeiculos(Estabelecimento object) {
        this.lstVeiculos = LoadManager.getInstance(VeiculoEstabelecimento.class)
                .addProperties(new HQLProperties(VeiculoEstabelecimento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VeiculoEstabelecimento.PROP_ESTABELECIMENTO, object))
                .start().getList();
    }

    private void limparTudo(AjaxRequestTarget target) {
        limpar(target);
        this.lstVeiculos.clear();
        table.update(target);
        autoCompleteConsultaEstabelecimento.limpar(target);
        setEnableVeiculos(target, false);
        target.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField());
    }

    public void setEnableVeiculos(AjaxRequestTarget target, boolean enable) {
        containerVeiculo.setEnabled(enable);
        btnSalvar.setEnabled(enable);
        target.add(containerVeiculo);
        target.add(btnSalvar);
        target.appendJavaScript(JScript.initMasks());
    }
}
