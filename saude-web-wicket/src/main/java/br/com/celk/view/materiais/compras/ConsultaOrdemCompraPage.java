package br.com.celk.view.materiais.compras;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.laboratorio.exames.dialog.DlgInformarMotivoCancelamentoExame;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioImpressaoOrdemCompraDTOParam;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 353
 */
@Private
public class ConsultaOrdemCompraPage extends ConsultaPage<OrdemCompra, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa empresaLogada;
    private Empresa empresaConsorciada;
    private Pessoa fornecedor;
    private Long situacao;
    private DatePeriod periodo;
    private Long ordemCompra;
    private Long pedidoLicitacao;
    private DropDown<Long> dropDownSituacao;
    private DlgInformarMotivoCancelamentoExame dlgInformarMotivoCancelamentoExame;
    private String existePregao;
    private String utilizaAutorizacaoFornecimentoDescritivo;
    private String exibeFiltroPedidoConsorcio;
    private WebMarkupContainer containerFiltros;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new AutoCompleteConsultaPessoa("fornecedor"));

        form.add(getDropDownSituacao());
        form.add(new InputField<Long>("ordemCompra", new PropertyModel(this, "ordemCompra")));
        form.add(new PnlDatePeriod("periodo"));

        containerFiltros = new WebMarkupContainer("containerFiltros");
        containerFiltros.setOutputMarkupId(true);

        containerFiltros.add(new AutoCompleteConsultaEmpresa("empresaConsorciada").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        containerFiltros.add(new InputField<Long>("pedidoLicitacao"));

        form.add(containerFiltros);

        if (!RepositoryComponentDefault.SIM.equals(exibeFiltroPedidoConsorcio)) {
            containerFiltros.setVisible(false);
        }

        empresaLogada = SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa();
        try {
            utilizaAutorizacaoFornecimentoDescritivo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaAutorizacaoFornecimentoDescritivo");
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }


        setExibeExpandir(true);
    }
    
    private DropDown<Long> getDropDownSituacao() {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>("situacao");
            
            dropDownSituacao.addChoice(null, bundle("todas"));
            dropDownSituacao.addChoice(OrdemCompra.Status.PENDENTE.value(), bundle("pendente"));
            dropDownSituacao.addChoice(OrdemCompra.Status.RECEBIDA.value(), bundle("recebida"));
            dropDownSituacao.addChoice(OrdemCompra.Status.CANCELADA.value(), bundle("cancelada"));
        }
        return dropDownSituacao;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        OrdemCompra proxy = on(OrdemCompra.class);
        try {
            exibeFiltroPedidoConsorcio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("visualizarFiltroPedidoConsorcio");
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("ordemCompra"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("fornecedor"), proxy.getPessoa().getDescricao()));
        if (RepositoryComponentDefault.SIM.equals(exibeFiltroPedidoConsorcio)) {
            columns.add(createSortableColumn(bundle("consorciado"), proxy.getEmpresaConsorciado().getDescricao()));
            columns.add(createSortableColumn(bundle("pedido"), proxy.getPedidoTransferenciaLicitacao().getCodigo()));
        }
        columns.add(createSortableColumn(bundle("data"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        columns.add(createSortableColumn(bundle("nrPregao"), proxy.getNumeroPregao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<OrdemCompra>() {
            @Override
            public void customizeColumn(OrdemCompra rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<OrdemCompra>() {

                    @Override
                    public void action(AjaxRequestTarget target, OrdemCompra modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroOrdemCompraStep1Page(modelObject));
                    }

                }).setEnabled(OrdemCompra.Status.PARCIAL.value().equals(rowObject.getStatus()) || OrdemCompra.Status.PENDENTE.value().equals(rowObject.getStatus()));
                
                addAction(ActionType.CANCELAR, rowObject, new IModelAction<OrdemCompra>() {

                    @Override
                    public void action(AjaxRequestTarget target, OrdemCompra modelObject) throws ValidacaoException, DAOException {
                        cancelarOrdemCompra(target, modelObject);
                    }

                }).setQuestionDialogBundleKey("desejaRealmenteCancelarOrdemCompra")
                        .setIcon(Icon.DOC_DELETE)
                        .setEnabled(OrdemCompra.Status.PARCIAL.value().equals(rowObject.getStatus()) || OrdemCompra.Status.PENDENTE.value().equals(rowObject.getStatus()));
                
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<OrdemCompra>() {
                    @Override
                    public DataReport action(OrdemCompra ordemCompra) throws ReportException {
                        try {
                            existePregao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaAutorizacaoFornecimentoDescritivo");
                        } catch (DAOException e) {
                             br.com.ksisolucoes.util.log.Loggable.log.error(e);
                        }
                        RelatorioImpressaoOrdemCompraDTOParam param = new RelatorioImpressaoOrdemCompraDTOParam();
                        if (RepositoryComponentDefault.SIM.equals(existePregao)){
                            param.setRelatorioPregao(true);
                        }
                        param.setOrdemCompra(ordemCompra);

                        return BOFactoryWicket.getBO(MaterialBasicoFacade.class).relatorioImpressaoOrdemCompra(param);
                    }
                }).setEnabled(verificaAF(rowObject));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<OrdemCompra>() {
                    @Override
                    public void action(AjaxRequestTarget target, OrdemCompra modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesOrdemCompraPage(modelObject));
                    }
                });
                try {
                    String utilizaAutorizacaoFornecimentoDescritivo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaAutorizacaoFornecimentoDescritivo");
                    if (RepositoryComponentDefault.SIM.equals(utilizaAutorizacaoFornecimentoDescritivo)) {
                        addAction(ActionType.CADASTRAR_AUTORIZACAO_FORNECIMENTO, rowObject, new IModelAction<OrdemCompra>() {
                            @Override
                            public void action(AjaxRequestTarget target, OrdemCompra modelObject) throws ValidacaoException, DAOException {
                                setResponsePage(new CadastroAutorizacaoFornecimentoPage(modelObject));
                            }
                        }).setEnabled(OrdemCompra.Status.PARCIAL.value().equals(rowObject.getStatus()) || OrdemCompra.Status.PENDENTE.value().equals(rowObject.getStatus()));
                        addAction(ActionType.MANUTENCAO, rowObject, new IModelAction<OrdemCompra>() {
                            @Override
                            public void action(AjaxRequestTarget target, OrdemCompra modelObject) throws ValidacaoException, DAOException {
                                setResponsePage(new CadastroAutorizacaoFornecimentoPage(modelObject, true));
                            }
                        }).setTitleBundleKey("manutencaoAutorizacaoFornecimento").setIcon(Icon.WRENCH).setEnabled(verificaAF(rowObject) && (OrdemCompra.Status.PARCIAL.value().equals(rowObject.getStatus()) || OrdemCompra.Status.PENDENTE.value().equals(rowObject.getStatus())));
                    }
                } catch (DAOException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        };
    }

    private boolean verificaAF(OrdemCompra obj) {
        if  (utilizaAutorizacaoFornecimentoDescritivo.equals(RepositoryComponentDefault.SIM)){
            if(obj.getAutorizacaoFornecimento() != null) {
                AutorizacaoFornecimentoItem proxy = on(AutorizacaoFornecimentoItem.class);
                List<AutorizacaoFornecimentoItem> list = LoadManager.getInstance(AutorizacaoFornecimentoItem.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_AUTORIZACAO_FORNECIMENTO), obj.getAutorizacaoFornecimento()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_AUTORIZACAO_FORNECIMENTO, AutorizacaoFornecimento.PROP_ORDEM_COMPRA), ordemCompra))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.DIFERENTE, AutorizacaoFornecimentoItem.Situacao.CANCELADA.value()))
                        .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getOrdemCompraItem().getProduto().getDescricao())))
                        .start().getList();
                if (CollectionUtils.isNotNullEmpty(list)) {
                    return true;
                }
            } else {
                return false;
            }
        } else {
            if  (! OrdemCompra.Status.CANCELADA.value().equals(obj.getStatus())){
                return true;
            }
        }
        return false;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return OrdemCompra.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(OrdemCompra.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(OrdemCompra.PROP_CODIGO), false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompra.PROP_EMPRESA), empresaLogada));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompra.PROP_EMPRESA_CONSORCIADO), empresaConsorciada));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompra.PROP_PESSOA), fornecedor));
        
        if (situacao == null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompra.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE, OrdemCompra.Status.CANCELADA.value()));
        } else if (OrdemCompra.Status.PENDENTE.value().equals(situacao)) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompra.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, 
                    Arrays.asList(OrdemCompra.Status.PENDENTE.value(), OrdemCompra.Status.PARCIAL.value())));
        } else{
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompra.PROP_STATUS), BuilderQueryCustom.QueryParameter.IGUAL, situacao));
        }
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompra.PROP_DATA_CADASTRO), periodo));
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompra.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, ordemCompra));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompra.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, pedidoLicitacao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroOrdemCompraStep1Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaOrdemCompra");
    }
    
    private void cancelarOrdemCompra(AjaxRequestTarget target, OrdemCompra modelObject) throws DAOException, ValidacaoException {
        OrdemCompraItem proxy = on(OrdemCompraItem.class);
        
        List<OrdemCompraItem> list = LoadManager.getInstance(OrdemCompraItem.class)
                .addProperties(new HQLProperties(OrdemCompraItem.class).getProperties())
                .addProperties(new HQLProperties(Produto.class, path(proxy.getProduto())).getProperties())
                .addProperties(new HQLProperties(Unidade.class, path(proxy.getProduto().getUnidade())).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(OrdemCompraItem.PROP_ORDEM_COMPRA, modelObject))
                .addParameter(new QueryCustom.QueryCustomParameter(OrdemCompraItem.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, OrdemCompraItem.Status.CANCELADA.value()))
                .start().getList();
        
        for(OrdemCompraItem item : list) {
            if (OrdemCompraItem.Status.PENDENTE.value().equals(item.getStatus()) && item.getQuantidadeRecebida() == null) {
                item.setStatus(OrdemCompraItem.Status.CANCELADA.value());
                item.setQuantidadeCancelada(item.getQuantidadeCompra());
                item.setQuantidadeCompra(new BigDecimal(0));
                item.setDataCancelamento(DataUtil.getDataAtual());
                if(item.getQuantidadePregao() != null) {
                    item.setQuantidadeCancelada(item.getQuantidadePregao());
                }
                item.setUsuarioCancelamento(ApplicationSession.get().getSession().<Usuario>getUsuario());

                BOFactoryWicket.save(item);
            } else if (OrdemCompraItem.Status.PENDENTE.value().equals(item.getStatus()) && item.getQuantidadeRecebida() != null && item.getQuantidadePregao() == null) {
                item.setStatus(OrdemCompraItem.Status.RECEBIDA.value());
                item.setQuantidadeCancelada(item.getQuantidadeCompra().subtract(item.getQuantidadeRecebida()));
                item.setQuantidadeCompra(item.getQuantidadeRecebida());
                item.setDataCancelamento(DataUtil.getDataAtual());
                item.setUsuarioCancelamento(ApplicationSession.get().getSession().<Usuario>getUsuario());

                BOFactoryWicket.save(item);
            } else if (OrdemCompraItem.Status.PENDENTE.value().equals(item.getStatus()) && item.getQuantidadePregao() != null) {
                item.setStatus(OrdemCompraItem.Status.RECEBIDA.value());
                item.setQuantidadeCancelada(item.getQuantidadePregao().subtract(item.getQuantidadeCompra()));
                item.setQuantidadeCompra(item.getQuantidadeCompra());
                item.setDataCancelamento(DataUtil.getDataAtual());
                item.setQuantidadePregao(item.getQuantidadePregao().subtract(item.getQuantidadeCancelada()));
                item.setUsuarioCancelamento(ApplicationSession.get().getSession().<Usuario>getUsuario());

                BOFactoryWicket.save(item);
            }
        }
        
        getPageableTable().update(target);
    }

}
