package br.com.celk.view.vigilancia.dengue.localidade;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 637
 */
public class ConsultaDengueLocalidadePage extends ConsultaPage<DengueLocalidade, List<BuilderQueryCustom.QueryParameter>> {

    private String localidade;
    private DengueLocalidade dengueLocalidade;
    private Long situacao = (Long) DengueLocalidade.Situacao.ATIVO.value();

    public ConsultaDengueLocalidadePage() {
    }

    public ConsultaDengueLocalidadePage(DengueLocalidade dengueLocalidade) {
        this.dengueLocalidade = dengueLocalidade;
        situacao = null;
        setProcurarAoAbrir(true);
        getPageableTable().populate();
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField(DengueLocalidade.PROP_LOCALIDADE));
        form.add(DropDownUtil.getIEnumDropDown(DengueLocalidade.PROP_SITUACAO, DengueLocalidade.Situacao.values()));

        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DengueLocalidade proxy = on(DengueLocalidade.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("dataRegistro"), proxy.getDataRegistro()));
        columns.add(createSortableColumn(BundleManager.getString("codigo"), proxy.getCodigo()));
        columns.add(createSortableColumn(BundleManager.getString("localidade"), proxy.getLocalidade()));
        columns.add(createSortableColumn(BundleManager.getString("categoria"), proxy.getCategoria()));
        columns.add(createSortableColumn(BundleManager.getString("zona"), proxy.getZona(), proxy.getZonaFormatado()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getSituacao(), proxy.getSituacaoFormatado()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<DengueLocalidade>() {
            @Override
            public void customizeColumn(final DengueLocalidade rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<DengueLocalidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueLocalidade modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroDengueLocalidadePage(rowObject, false, true));
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DengueLocalidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueLocalidade modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroDengueLocalidadePage(modelObject, true, true));
                    }
                }).setTitleBundleKey("detalhes");
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return DengueLocalidade.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(DengueLocalidade.PROP_LOCALIDADE, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueLocalidade.PROP_LOCALIDADE), BuilderQueryCustom.QueryParameter.ILIKE, localidade));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueLocalidade.PROP_SITUACAO), situacao));
        if (dengueLocalidade != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueLocalidade.PROP_CODIGO), dengueLocalidade.getCodigo()));
        }
        dengueLocalidade = null;
        situacao = (Long) DengueLocalidade.Situacao.ATIVO.value();
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroDengueLocalidadePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDengueLocalidade");
    }
}
