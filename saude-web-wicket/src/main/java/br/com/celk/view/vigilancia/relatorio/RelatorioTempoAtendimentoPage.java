package br.com.celk.view.vigilancia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.report.vigilancia.tempoatendimento.dto.RelatorioTempoAtendimentoDTOParam;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import org.apache.wicket.markup.html.form.Form;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 962
 */
@Private
public class RelatorioTempoAtendimentoPage extends RelatorioPage<RelatorioTempoAtendimentoDTOParam> {

    private DropDown<TipoSolicitacao> dropDownTipoSolicitacao;
    private RequiredPnlDatePeriod pnlDatePeriod;

    @Override
    public void init(final Form<RelatorioTempoAtendimentoDTOParam> form) {
        RelatorioTempoAtendimentoDTOParam proxy = on(RelatorioTempoAtendimentoDTOParam.class);

        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoDocumento()), TipoSolicitacao.TipoDocumento.values(), true, false, true));
        form.add(pnlDatePeriod = new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        pnlDatePeriod.getDataInicial().setRequired(true);
    }

    private DropDown<TipoSolicitacao> getDropDownTipoSolicitacao(String id) {
        if (dropDownTipoSolicitacao == null) {
            dropDownTipoSolicitacao = new DropDown(id);
            dropDownTipoSolicitacao.addChoice(null, "");

            List<TipoSolicitacao> list = LoadManager.getInstance(TipoSolicitacao.class)
                    .addProperty(TipoSolicitacao.PROP_CODIGO)
                    .addProperty(TipoSolicitacao.PROP_DESCRICAO)
                    .start().getList();

            for (TipoSolicitacao tipoSolicitacao : list) {
                dropDownTipoSolicitacao.addChoice(tipoSolicitacao, tipoSolicitacao.getDescricao());
            }
        }

        return dropDownTipoSolicitacao;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioTempoAtendimento");
    }

    @Override
    public Class<RelatorioTempoAtendimentoDTOParam> getDTOParamClass() {
        return RelatorioTempoAtendimentoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioTempoAtendimentoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioTempoAtendimento(param);
    }

}
