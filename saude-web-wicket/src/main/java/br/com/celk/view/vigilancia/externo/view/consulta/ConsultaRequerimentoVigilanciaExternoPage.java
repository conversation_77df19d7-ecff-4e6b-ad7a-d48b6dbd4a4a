package br.com.celk.view.vigilancia.externo.view.consulta;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AjaxActionMultiReportLink;
import br.com.celk.component.table.TableColorEnum;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaVigilanciaPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.externo.util.sessao.VigilanciaRecursosPageHelper;
import br.com.celk.view.vigilancia.externo.view.components.dialog.DlgEscolherAutoImpressao;
import br.com.celk.view.vigilancia.externo.view.components.dialog.DlgEscolherParecerResposta;
import br.com.celk.view.vigilancia.externo.view.consulta.dlg.DlgAnexosObservacao;
import br.com.celk.view.vigilancia.externo.view.home.VigilanciaHomePage;
import br.com.celk.view.vigilancia.externo.view.servicos.*;
import br.com.celk.view.vigilancia.externo.view.servicos.declaratorio.RequerimentoHabiteseDeclaratorioExternoPage;
import br.com.celk.view.vigilancia.externo.view.servicos.declaratorio.RequerimentoHidrossanitarioDeclaratorioExternoPage;
import br.com.celk.view.vigilancia.externo.view.servicos.parecer.*;
import br.com.celk.view.vigilancia.externo.view.servicos.parecer.declaratorio.CadastroRespostaParecerHabiteseDeclaratorioPage;
import br.com.celk.view.vigilancia.externo.view.servicos.parecer.declaratorio.CadastroRespostaParecerHidroDeclaratorioPage;
import br.com.celk.view.vigilancia.externo.view.servicos.parecer.projetoArquitetonico.CadastroRequerimentoProjetoArquitetonicoParecerRespostaPage;
import br.com.celk.view.vigilancia.financeiro.DlgEscolherVigilanciaFinanceiro;
import br.com.celk.view.vigilancia.financeiro.dialog.DlgAnexoComprovantePagamentoRequerimento;
import br.com.celk.view.vigilancia.processoadministrativo.DetalhesProcessoAdministrativoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoEnderecoExternoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoRazaoSocialExternoPage;
import br.com.celk.view.vigilancia.requerimentovigilancia.dialog.DlgCancelamentoFinalizacaoRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentovigilancia.dialog.DlgImpressaoConsultaRequerimentoVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.financeiro.IntegracaoFinanceiroSmartHelper;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;
import org.hamcrest.Matchers;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao.TipoDocumento.*;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 717
 */
@Private
public class ConsultaRequerimentoVigilanciaExternoPage extends ConsultaVigilanciaPage<RequerimentoVigilanciaDTO, RequerimentoVigilanciaDTOParam> {

    private RequerimentoVigilanciaDTOParam param;

    private DropDown<Long> dropDownSituacao;
    private DlgCancelamentoFinalizacaoRequerimentoVigilancia dlgCancelamentoRequerimentoVigilancia;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private DlgAnexoComprovantePagamentoRequerimento dlgAnexoComprovantePagamentoRequerimento;
    private DlgAnexoComprovantePagamentoRequerimento dlgAnexoComprovantePagamentoRequerimentoChoice;
    private DropDown<Long> dropDownVisualizarCancelados;
    private ConfiguracaoVigilancia configuracaoVigilancia;
    private ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro;
    private DlgEscolherVigilanciaFinanceiro<RequerimentoVigilanciaDTO> dlgEscolherVigilanciaFinanceiro;
    private DlgImpressaoConsultaRequerimentoVigilancia dlgImpressaoConsultaRequerimentoVigilancia;

    public ConsultaRequerimentoVigilanciaExternoPage() {
        super(true);
    }

    @Override
    public void initForm(Form form) {
        if (configuracaoVigilancia == null) {
            try {
                configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            } catch (ValidacaoException e) {
                Logger.getLogger(ConsultaRequerimentoVigilanciaExternoPage.class.getName()).log(Level.SEVERE, null, e);
            }
        }
        if (configuracaoVigilanciaFinanceiro == null) {
            try {
                configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
            } catch (ValidacaoException e) {
                Logger.getLogger(ConsultaRequerimentoVigilanciaExternoPage.class.getName()).log(Level.SEVERE, null, e);
            }
        }
        form.setDefaultModel(new CompoundPropertyModel<>(param = new RequerimentoVigilanciaDTOParam()));


        RequerimentoVigilanciaDTOParam proxy = on(RequerimentoVigilanciaDTOParam.class);
        form.add(dropDownVisualizarCancelados = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVisualizarCancelados())));
        form.add(getTipoDocumentoDropDown((path(proxy.getTipoDocumento()))));
        form.add(new InputField<String>(path(proxy.getNumeroProtocolo())));
        form.add(getDropDownSituacao());

        add(form);
        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());
        setExibeBtnVoltar(true);

    }

    @Override
    protected TableColorEnum getColorLine(Serializable rowObject) {
        RequerimentoVigilanciaDTO dto = (RequerimentoVigilanciaDTO) rowObject;
        if (visibleAcaoAutos(dto)) {
            return TableColorEnum.VERMELHA_LIGHT;
        } else if (visiblePareceres(dto)) {
            return TableColorEnum.AMARELA;
        } else {
            return TableColorEnum.PADRAO;
        }
    }

    public DropDown<Long> getDropDownSituacao() {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<>("situacao");
            dropDownSituacao.addChoice(null, BundleManager.getString("todos"));
            for (RequerimentoVigilancia.Situacao situacao : RequerimentoVigilancia.Situacao.values()) {
                dropDownSituacao.addChoice(situacao.value(), situacao.descricao());
            }

            AjaxFormComponentUpdatingBehavior ddSituacao = new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownVisualizarCancelados.limpar(target);
                    if (param.getSituacao() != null) {
                        dropDownVisualizarCancelados.setEnabled(false);
                        if (RequerimentoVigilancia.Situacao.CANCELADO.value().equals(dropDownSituacao.getComponentValue())) {
                            dropDownVisualizarCancelados.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                        }
                    } else {
                        dropDownVisualizarCancelados.setEnabled(true);
                    }
                    target.add(dropDownVisualizarCancelados);
                }
            };
            dropDownSituacao.add(ddSituacao);
        }
        return dropDownSituacao;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        RequerimentoVigilanciaDTO proxy = on(RequerimentoVigilanciaDTO.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("dataRequerimento"), proxy.getRequerimentoVigilancia().getDataRequerimento(), proxy.getRequerimentoVigilancia().getDataRequerimento()));
        columns.add(createSortableColumn(BundleManager.getString("protocolo"), proxy.getRequerimentoVigilancia().getProtocolo(), proxy.getRequerimentoVigilancia().getProtocoloFormatado()));
        columns.add(createColumn(BundleManager.getString("estabelecimentoPessoaProfissional"), proxy.getRequerimentoVigilancia().getEstabelecimentoPessoaProfissionalFormatado()));
        columns.add(createSortableColumn(BundleManager.getString("solicitante"), proxy.getRequerimentoVigilancia().getNomeSolicitante()));
        columns.add(createSortableColumn(BundleManager.getString("solicitacao"), proxy.getRequerimentoVigilancia().getTipoDocumento(), proxy.getRequerimentoVigilancia().getDescricaoTipoDocumentoComPlaca()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getRequerimentoVigilancia().getSituacao(), proxy.getRequerimentoVigilancia().getDescricaoSituacao()));

        return columns;
    }

    private boolean regrasSituacaoAprovacao(RequerimentoVigilanciaDTO rowObject) {
        boolean gestaoSetor = VigilanciaHelper.isGestaoRequerimentoSetor();
        Long situacaoAprovacao = rowObject.getRequerimentoVigilancia().getSituacaoAprovacao();

        List<VigilanciaFinanceiro> isento = new ArrayList<>();
        List<VigilanciaFinanceiro> naoIsento = new ArrayList<>();

        if (CollectionUtils.isNotNullEmpty(rowObject.getVigilanciaFinanceiroList())) {
            naoIsento = Lambda.select(rowObject.getVigilanciaFinanceiroList(), Lambda.having(on(VigilanciaFinanceiro.class).getIsento(), Matchers.equalTo(RepositoryComponentDefault.NAO_LONG)));
            isento = Lambda.select(rowObject.getVigilanciaFinanceiroList(), Lambda.having(on(VigilanciaFinanceiro.class).getIsento(), Matchers.equalTo(RepositoryComponentDefault.SIM_LONG)));
        }

        if (!gestaoSetor) { // gestão por fiscal
            if (CollectionUtils.isNotNullEmpty(rowObject.getVigilanciaFinanceiroList())) {
                if (CollectionUtils.isNotNullEmpty(naoIsento))  // possui pelo menos um registro de boleto
                {
                    return RequerimentoVigilancia.SituacaoAprovacao.EMISAO_BOLETO.value().equals(situacaoAprovacao);
                }

                if (CollectionUtils.isNotNullEmpty(isento) && VigilanciaHelper.isRequerimentoAnaliseProjeto(rowObject.getRequerimentoVigilancia())) { // req isento
                    return RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value().equals(situacaoAprovacao);
                } else {
                    return RequerimentoVigilancia.SituacaoAprovacao.AGUARDANDO_INFORMAR_FISCAL.value().equals(situacaoAprovacao);
                }
            } else { // não cobrado
                if (VigilanciaHelper.isRequerimentoAnaliseProjeto(rowObject.getRequerimentoVigilancia())) {
                    return RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value().equals(situacaoAprovacao);
                } else {
                    return RequerimentoVigilancia.SituacaoAprovacao.AGUARDANDO_INFORMAR_FISCAL.value().equals(situacaoAprovacao);
                }
            }
        } else {
            return CollectionUtils.isEmpty(rowObject.getVigilanciaFinanceiroList());
        }
    }

    private MultipleActionCustomColumn<RequerimentoVigilanciaDTO> getActionColumn() {
//    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<RequerimentoVigilanciaDTO>() {
            @Override
            public void customizeColumn(final RequerimentoVigilanciaDTO rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) {
                        responsePage(modelObject.getRequerimentoVigilancia(), true);
                    }
                }).setEnabled(isActionPermitted(Permissions.EDITAR) && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao()) && rowObject.getRequerimentoVigilancia().getSituacaoAprovacao() == null)
                        || (RequerimentoVigilancia.Situacao.ANALISE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao()) && RequerimentoVigilancia.SituacaoAprovacao.DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao()))
                        || (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao()) && RequerimentoVigilancia.SituacaoAprovacao.DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao())));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) {
                        responseCancel(target, modelObject.getRequerimentoVigilancia());
                    }
                }).setQuestionDialogBundleKey(null)
                        .setEnabled(isActionPermitted(Permissions.CANCELAR)
                                && enableCancel(rowObject.getRequerimentoVigilancia())
                                && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                                && regrasSituacaoAprovacao(rowObject)
                        );

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) {
                        responsePage(modelObject.getRequerimentoVigilancia(), false);
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new AjaxActionMultiReportLink<RequerimentoVigilanciaDTO>() {
                    @Override
                    public List<DataReport> getDataReports(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) throws ReportException {
                        return responseImpression(target, rowObject.getRequerimentoVigilancia());
                    }
                }).setVisible(isVisibleImpressao(rowObject));


                addAction(ActionType.ANEXAR, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) {
                        redirecionarComprovantePagamento(target, modelObject);
                    }
                }).setIcon(Icon.MONEY).setTitleBundleKey("comprovantePagamento")
                        .setEnabled(enableComprovante(rowObject))
                        .setVisible(visibleComprovante());


                addAction(ActionType.IMPRIMIR, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) throws ValidacaoException {
                        IntegracaoFinanceiroSmartHelper.redirecionarPaginaConsultaBoletoMensagem(target);

                        if (isCobrancaBoleto()) {
                            imprimirBoleto(target, modelObject.getRequerimentoVigilancia());
                        } else if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.MEMORANDO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
                            imprimirMemorando(target, modelObject.getRequerimentoVigilancia());
                        }

                    }
                }).setIcon(Icon.CODE_BAR).setTitleBundleKey("imprimirBoleto")
                        .setEnabled(enableBoleto(rowObject));

                addAction(ActionType.WARN, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) {
                        rowObject.getRequerimentoVigilancia().getTipoSolicitacao().setTipoDocumento(rowObject.getRequerimentoVigilancia().getTipoDocumento());
                        DlgAnexosObservacao dlgAnexosObservacao = initDlgAnexosObservacao(target, rowObject);
                        dlgAnexosObservacao.show(target);
                    }
                }).setTitleBundleKey("anexosPendencia")
                        .setVisible(isVisibleAnexosPendencia(rowObject));


                addAction(ActionType.RETORNO, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).reenviarRequerimentoVigilancia(modelObject.getRequerimentoVigilancia());
                        getPageableTable().update(target);
                    }
                }).setIcon(Icon.REDO).setTitleBundleKey("reenviar").setQuestionDialogBundleKey("msgConfirmaReenvioRequerimentoProtocoloX", rowObject.getRequerimentoVigilancia().getProtocoloFormatado())
                        .setEnabled(isEnabledReenviar(rowObject))
                        .setVisible(ConfiguracaoVigilanciaEnum.TipoFluxoExterno.DOCUMENTACAO_PAGAMENTO.value().equals(configuracaoVigilancia.getTipoFluxoExterno()));

                addAction(ActionType.RETORNO, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).devolverRequerimentoVigilancia(modelObject.getRequerimentoVigilancia());
                        getPageableTable().update(target);
                    }
                }).setIcon(Icon.REDO).setTitleBundleKey("rotulo_devolver").setQuestionDialogBundleKey("msgConfirmaReenvioRequerimentoProtocoloX", rowObject.getRequerimentoVigilancia().getProtocoloFormatado())
                        .setEnabled(isEnabledDevolver(rowObject))
                        .setVisible(ConfiguracaoVigilanciaEnum.TipoFluxoExterno.PAGAMENTO_DOCUMENTACAO.value().equals(configuracaoVigilancia.getTipoFluxoExterno()));

                addAction(ActionType.DOCUMENTO, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) {
                        actionAutos(target, modelObject);
                    }
                }).setTitleBundleKey("consultaAutuacao")
                        .setVisible(visibleAcaoAutos(rowObject));

                addAction(ActionType.RETORNO, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) {
                        actionPareceres(target, modelObject);
                    }
                }).setIcon(Icon.WARN).setTitleBundleKey("parecerComPendencia")
                        .setVisible(visiblePareceres(rowObject));

            }
        };
    }

    private DlgAnexosObservacao initDlgAnexosObservacao(AjaxRequestTarget target, final RequerimentoVigilanciaDTO rowObject) {
        DlgAnexosObservacao dlgAnexosObservacao;
        addModal(target, dlgAnexosObservacao = new DlgAnexosObservacao(newModalId(), rowObject.getRequerimentoVigilancia(), null, true) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList, List<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarRequerimentosAnexosVeiculosAnexos(rowObject.getRequerimentoVigilancia(), requerimentoVigilanciaAnexoDTOList, requerimentoVigilanciaAnexoDTOExcluidoList, veiculoEstabelecimentoDTOList, false);
                getPageableTable().update(target);
            }

            @Override
            public void onConfirmarReenviar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList, List<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarRequerimentosAnexosVeiculosAnexos(rowObject.getRequerimentoVigilancia(), requerimentoVigilanciaAnexoDTOList, requerimentoVigilanciaAnexoDTOExcluidoList, veiculoEstabelecimentoDTOList, true);
                getPageableTable().update(target);
            }
        });
        return dlgAnexosObservacao;
    }

    private boolean isCobrancaBoleto() {
        return ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca());
    }

    private void initDlgEscolherVigilanciaFinanceiro() {
        dlgEscolherVigilanciaFinanceiro = new DlgEscolherVigilanciaFinanceiro<RequerimentoVigilanciaDTO>(newModalId()) {
            @Override
            public void onEscolherVigilanciaFinanceiro(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) {
                if (dlgAnexoComprovantePagamentoRequerimentoChoice == null) {
                    addModal(target, dlgAnexoComprovantePagamentoRequerimentoChoice = new DlgAnexoComprovantePagamentoRequerimento(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) {
                            dlgEscolherVigilanciaFinanceiro.close(target);
                            redirecionarComprovantePagamento(target, dlgEscolherVigilanciaFinanceiro.getObject());
                        }
                    });
                }

                dlgAnexoComprovantePagamentoRequerimentoChoice.show(target, getObject().getRequerimentoVigilancia(), vigilanciaFinanceiro);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) {
                super.onFechar(target);
                getPageableTable().update(target);
            }
        };
    }

    private boolean isVisibleImpressao(RequerimentoVigilanciaDTO rowObject) {
        return isActionPermitted(Permissions.IMPRIMIR) &&
                (habilitarAcaoAutorizacaoExumacao(rowObject.getRequerimentoVigilancia(), true)
                        || habilitarAcaoAlvara(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoCredenciamento(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoLicencaTransporte(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoLivro(rowObject.getRequerimentoVigilancia(), true)
                        || habilitarAcaoNadaConsta(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoDeclaracaoVisa(rowObject.getRequerimentoVigilancia())
                        || habilitarAcaoAlteracaoRepresentanteLegalAlteracaoAtividade(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoReceituario(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoReceituarioExterno(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoEntradaBaixaResponsabilidadeTecnica(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarImprimirAlteracaoEndereco((rowObject.getRequerimentoVigilancia()))
                        || habilitarImprimirAlteracaoRazaoSocial((rowObject.getRequerimentoVigilancia()))
                        || habilitarImprimirDeclaracaoVeracidadeDiversas((rowObject.getRequerimentoVigilancia()))
                        || habilitarImprimirProrrogacaoPrazo((rowObject.getRequerimentoVigilancia()))
                        || habilitarImprimirInspecaoSanitaria((rowObject.getRequerimentoVigilancia()))
                        || habilitarAcaoPedidoDocumento(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarVacinacaoExtramuro(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoBaixaVeiculo(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoAnaliseProjeto(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoDispensa(rowObject.getRequerimentoVigilancia(), true, false)
                        || habilitarAcaoDispensaAtividadesBaixoRisco(rowObject.getRequerimentoVigilancia(), true, false)
                );
    }

    private void actionAutos(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) {
        DlgEscolherAutoImpressao dlgEscolherAuto = new DlgEscolherAutoImpressao(newModalId(), VigilanciaHomePage.class, modelObject.getAutoExternoDTOList()) {

            @Override
            public void onAction(AjaxRequestTarget target, ImpressaoAutoExternoDTO dto) throws ValidacaoException, DAOException {
                redirecionarRecurso(dto);
            }

            @Override
            public void onPrint(AjaxRequestTarget target, ImpressaoAutoExternoDTO dto) throws ValidacaoException, DAOException {
                imprimirAuto(target, dto);
            }
        };
        addModal(target, dlgEscolherAuto);
        dlgEscolherAuto.show(target);
    }

    private void actionPareceres(AjaxRequestTarget target, final RequerimentoVigilanciaDTO modelObject) {
        DlgEscolherParecerResposta dlgEscolherParecer = new DlgEscolherParecerResposta(newModalId(), VigilanciaHomePage.class, modelObject.getParecerExternoDTO()) {
            @Override
            public void onAction(AjaxRequestTarget target, RespostaParecerViewDTO dto) {
                if (RespostaParecerViewDTO.Tipo.PBA.equals(dto.getTipo())) {
                    setResponsePage(new CadastroRespostaParecerPBAPage(modelObject.getRequerimentoVigilancia(), dto.getRequerimentoAnaliseProjetoParecer(), getPageParameters(), ConsultaRequerimentoVigilanciaExternoPage.class));
                } else if (RespostaParecerViewDTO.Tipo.VISTORIA_PBA.equals(dto.getTipo())) {
                    setResponsePage(new CadastroRespostaParecerVistoriaPBAPage(modelObject.getRequerimentoVigilancia(), dto.getRequerimentoVistoriaPBAConformidadeTecnica(), getPageParameters(), ConsultaRequerimentoVigilanciaExternoPage.class));
                } else if (RespostaParecerViewDTO.Tipo.HIDRO.equals(dto.getTipo())) {
                    setResponsePage(new CadastroRespostaParecerHidroPage(modelObject.getRequerimentoVigilancia(), dto.getRequerimentoProjetoHidrossanitarioParecer(), getPageParameters(), ConsultaRequerimentoVigilanciaExternoPage.class));
                } else if (RespostaParecerViewDTO.Tipo.HABITESE.equals(dto.getTipo())) {
                    setResponsePage(new CadastroRespostaParecerHabitesePage(modelObject.getRequerimentoVigilancia(), dto.getRequerimentoVistoriaHidrossanitarioParecer(), getPageParameters(), ConsultaRequerimentoVigilanciaExternoPage.class));
                } else if (RespostaParecerViewDTO.Tipo.HABITESE_DECLARATORIO.equals(dto.getTipo())) {
                    setResponsePage(new CadastroRespostaParecerHabiteseDeclaratorioPage(modelObject.getRequerimentoVigilancia(), dto.getRequerimentoVistoriaHidrossanitarioDeclaratorioParecer(), getPageParameters(), ConsultaRequerimentoVigilanciaExternoPage.class));
                } else if (RespostaParecerViewDTO.Tipo.REQUERIMENTO.equals(dto.getTipo())) {
                    setResponsePage(new CadastroRespostaParecerRequerimentoPage(modelObject.getRequerimentoVigilancia(), dto.getRequerimentoVigilanciaParecer(), getPageParameters(), ConsultaRequerimentoVigilanciaExternoPage.class));
                } else if (RespostaParecerViewDTO.Tipo.HIDRO_DECLARATORIO.equals(dto.getTipo())) {
                    setResponsePage(new CadastroRespostaParecerHidroDeclaratorioPage(modelObject.getRequerimentoVigilancia(), dto.getRequerimentoProjetoHidrossanitarioDeclaratorioParecer(), getPageParameters(), ConsultaRequerimentoVigilanciaExternoPage.class));
                } else if (RespostaParecerViewDTO.Tipo.PROJETO_ARQUITETONICO_SANITARIO.equals(dto.getTipo())) {
                    setResponsePage(new CadastroRequerimentoProjetoArquitetonicoParecerRespostaPage(modelObject.getRequerimentoVigilancia(), dto.getRequerimentoProjetoArquitetonicoParecer(), getPageParameters(), ConsultaRequerimentoVigilanciaExternoPage.class));
                }
            }
        };
        addModal(target, dlgEscolherParecer);
        dlgEscolherParecer.show(target);
    }

    private void redirecionarRecurso(ImpressaoAutoExternoDTO dto) throws ValidacaoException, DAOException {
        final String msgAutoIndisponivel = "msgAutoIndisponivel";
        Object auto = BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarDataRecebimentoAuto(dto);
        Page page = null;
        if (auto instanceof AutoIntimacao) {
            AutoIntimacao autoIntimacao = (AutoIntimacao) auto;
            if (autoIntimacao.getDataRecebimento() == null) {
                throw new ValidacaoException(BundleManager.getString(msgAutoIndisponivel));
            }
            page = VigilanciaRecursosPageHelper.getResponsePageRecursoAutoIntimacao(autoIntimacao, ConsultaRequerimentoVigilanciaExternoPage.class);
        } else if (auto instanceof AutoInfracao) {
            AutoInfracao autoInfracao = (AutoInfracao) auto;
            if (autoInfracao.getDataRecebimento() == null) {
                throw new ValidacaoException(BundleManager.getString(msgAutoIndisponivel));
            }
            page = VigilanciaRecursosPageHelper.getResponsePageRecursoAutoInfracao(autoInfracao, ConsultaRequerimentoVigilanciaExternoPage.class);
        } else if (auto instanceof AutoMulta) {
            AutoMulta autoMulta = (AutoMulta) auto;
            if (autoMulta.getDataRecebimento() == null) {
                throw new ValidacaoException(BundleManager.getString(msgAutoIndisponivel));
            }
            page = VigilanciaRecursosPageHelper.getResponsePageRecursoAutoMulta(autoMulta, ConsultaRequerimentoVigilanciaExternoPage.class);
        } else if (auto instanceof AutoPenalidade) {
            AutoPenalidade autoPenalidade = (AutoPenalidade) auto;
            if (autoPenalidade.getDataRecebimento() == null) {
                throw new ValidacaoException(BundleManager.getString(msgAutoIndisponivel));
            }
            page = VigilanciaRecursosPageHelper.getResponsePageRecursoAutoPenalidade(autoPenalidade, ConsultaRequerimentoVigilanciaExternoPage.class);
        }
        setResponsePage(page);
    }

    private void imprimirAuto(AjaxRequestTarget target, ImpressaoAutoExternoDTO dto) throws ValidacaoException, DAOException {
        Object auto = BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarDataRecebimentoAuto(dto);
        DataReport dataReport = null;
        final String msgAutoIndisponivel = "msgAutoIndisponivel";
        try {
            if (auto instanceof AutoIntimacao) {
                AutoIntimacao autoIntimacao = (AutoIntimacao) auto;
                if (autoIntimacao.getDataRecebimento() == null) {
                    throw new ValidacaoException(BundleManager.getString(msgAutoIndisponivel));
                }
                dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoIntimacao(autoIntimacao.getCodigo(), autoIntimacao.getNumeroFormatado());
            } else if (auto instanceof AutoInfracao) {
                AutoInfracao autoInfracao = (AutoInfracao) auto;
                if (autoInfracao.getDataRecebimento() == null) {
                    throw new ValidacaoException(BundleManager.getString(msgAutoIndisponivel));
                }
                dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoInfracao(autoInfracao.getCodigo(), autoInfracao.getNumeroFormatado(), autoInfracao.getSituacao());
            } else if (auto instanceof AutoMulta) {
                AutoMulta autoMulta = (AutoMulta) auto;
                if (autoMulta.getDataRecebimento() == null) {
                    throw new ValidacaoException(BundleManager.getString(msgAutoIndisponivel));
                }
                dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoMulta(autoMulta.getCodigo(), autoMulta.getNumeroFormatado());
            } else if (auto instanceof AutoPenalidade) {
                AutoPenalidade autoPenalidade = (AutoPenalidade) auto;
                if (autoPenalidade.getDataRecebimento() == null) {
                    throw new ValidacaoException(BundleManager.getString(msgAutoIndisponivel));
                }
                dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoPenalidade(autoPenalidade.getCodigo(), autoPenalidade.getNumeroFormatado());
            }

            if (dataReport != null) {
                File file = File.createTempFile("autuacao", ".pdf");
                JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
                IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(file));
                ajaxPreviewBlank.initiate(target, "autuacao.pdf", resourceStream);
            }
        } catch (ReportException | JRException | IOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }

    private boolean isVisibleAnexosPendencia(RequerimentoVigilanciaDTO rowObject) {
        if (isEnabledReenviar(rowObject)) {
            return false;
        }

        if (ConfiguracaoVigilanciaEnum.TipoFluxoExterno.DOCUMENTACAO_PAGAMENTO.value().equals(configuracaoVigilancia.getTipoFluxoExterno())) {
            return existsAnexoObservacoes(rowObject.getRequerimentoVigilancia()) && RequerimentoVigilancia.SituacaoAprovacao.DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao())
                    || RequerimentoVigilancia.SituacaoAprovacao.PAGAMENTO_DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao());
        } else {
            return StringUtils.trimToNull(rowObject.getRequerimentoVigilancia().getMotivoAprovacao()) != null && RequerimentoVigilancia.SituacaoOcorrencia.PARADO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoOcorrencia());
        }
    }

    private boolean isEnabledReenviar(RequerimentoVigilanciaDTO rowObject) {
        if (ConfiguracaoVigilanciaEnum.TipoFluxoExterno.DOCUMENTACAO_PAGAMENTO.value().equals(configuracaoVigilancia.getTipoFluxoExterno())) {
            return RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                    && (RequerimentoVigilancia.SituacaoAprovacao.DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao())
                    || RequerimentoVigilancia.SituacaoAprovacao.PAGAMENTO_DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao()));
        } else {
            return false;
        }
    }

    private boolean isEnabledDevolver(RequerimentoVigilanciaDTO rowObject) {
        if (ConfiguracaoVigilanciaEnum.TipoFluxoExterno.PAGAMENTO_DOCUMENTACAO.value().equals(configuracaoVigilancia.getTipoFluxoExterno())) {
            return RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                    && (RequerimentoVigilancia.SituacaoAprovacao.DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao())
                    || RequerimentoVigilancia.SituacaoAprovacao.PAGAMENTO_DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao()));
        } else {
            return false;
        }
    }

    private boolean visibleAcaoAutos(RequerimentoVigilanciaDTO rowObject) {
        return CollectionUtils.isNotNullEmpty(rowObject.getAutoExternoDTOList());
    }

    private boolean visiblePareceres(RequerimentoVigilanciaDTO rowObject) {
        RespostaParecerExternoDTO dto = rowObject.getParecerExternoDTO();
        return CollectionUtils.isNotNullEmpty(dto.getRequerimentoAnaliseProjetoParecerList())
                || CollectionUtils.isNotNullEmpty(dto.getRequerimentoVistoriaPBAConformidadeTecnicaList())
                || CollectionUtils.isNotNullEmpty(dto.getRequerimentoProjetoHidrossanitarioParecerList())
                || CollectionUtils.isNotNullEmpty(dto.getRequerimentoProjetoHidrossanitarioDeclaratorioParecerList())
                || CollectionUtils.isNotNullEmpty(dto.getRequerimentoVistoriaHidrossanitarioDeclaratorioParecerList())
                || CollectionUtils.isNotNullEmpty(dto.getRequerimentoVistoriaHidrossanitarioParecerList())
                || CollectionUtils.isNotNullEmpty(dto.getRequerimentoProjetoArquitetonicoParecerList())
                || CollectionUtils.isNotNullEmpty(dto.getRequerimentoVigilanciaParecerList())
                ;
    }

    private boolean enableBoleto(RequerimentoVigilanciaDTO rowObject) {
        if (RequerimentoVigilancia.SituacaoFinanceiroComplementar.EMITIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoFinanceiroComplementar())) {
            return CollectionUtils.isNotNullEmpty(rowObject.getVigilanciaFinanceiroList())
                    && existsBoletoNaoPago(rowObject.getVigilanciaFinanceiroList());
        } else {
            if (ConfiguracaoVigilanciaEnum.TipoFluxoExterno.DOCUMENTACAO_PAGAMENTO.value().equals(configuracaoVigilancia.getTipoFluxoExterno())) {
                return RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                        && (RequerimentoVigilancia.SituacaoAprovacao.EMISAO_BOLETO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao())
                        || RequerimentoVigilancia.SituacaoAprovacao.COMPROVANTE_PAGAMENTO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao()))
                        || ((RequerimentoVigilancia.SituacaoAprovacao.DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao())
                        || RequerimentoVigilancia.SituacaoAprovacao.PAGAMENTO_DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao()))
                        && CollectionUtils.isNotNullEmpty(rowObject.getVigilanciaFinanceiroList())
                        && existsBoletoNaoPago(rowObject.getVigilanciaFinanceiroList())
                );
            } else {
                return RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                        && (RequerimentoVigilancia.SituacaoAprovacao.EMISAO_BOLETO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao()))
                        && CollectionUtils.isNotNullEmpty(rowObject.getVigilanciaFinanceiroList())
                        && existsBoletoNaoPago(rowObject.getVigilanciaFinanceiroList());
            }
        }
    }

    private boolean enableComprovante(RequerimentoVigilanciaDTO rowObject) {
        if (RequerimentoVigilancia.SituacaoFinanceiroComplementar.EMITIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoFinanceiroComplementar())) {
            return CollectionUtils.isNotNullEmpty(rowObject.getVigilanciaFinanceiroList())
                    && existsBoletoNaoPago(rowObject.getVigilanciaFinanceiroList());
        } else {
            if (ConfiguracaoVigilanciaEnum.TipoFluxoExterno.DOCUMENTACAO_PAGAMENTO.value().equals(configuracaoVigilancia.getTipoFluxoExterno())) {
                return RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                        && !existsBoletoPendenteImpressao(rowObject.getVigilanciaFinanceiroList())
                        && (RequerimentoVigilancia.SituacaoAprovacao.EMISAO_BOLETO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao()))
                        || ((RequerimentoVigilancia.SituacaoAprovacao.DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao())
                        || RequerimentoVigilancia.SituacaoAprovacao.PAGAMENTO_DEVOLVIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao()))
                        && CollectionUtils.isNotNullEmpty(rowObject.getVigilanciaFinanceiroList())
                        && existsBoletoNaoPago(rowObject.getVigilanciaFinanceiroList())
                );
            } else {
                return RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                        && CollectionUtils.isNotNullEmpty(rowObject.getVigilanciaFinanceiroList())
                        && !existsBoletoPendenteImpressao(rowObject.getVigilanciaFinanceiroList())
                        && existsBoletoNaoPago(rowObject.getVigilanciaFinanceiroList());
            }
        }
    }


    private boolean existsBoletoPendenteImpressao(List<VigilanciaFinanceiro> vigilanciaFinanceiroList) {
        if (!isCobrancaBoleto()) {
            return false;
        } else {
            if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
                return Lambda.exists(vigilanciaFinanceiroList, Lambda.having(Lambda.on(VigilanciaFinanceiro.class).getAnexoBoleto().getCodigo(), Matchers.nullValue()));
            } else {
                return true;
            }
        }
    }

    private boolean existsBoletoNaoPago(List<VigilanciaFinanceiro> vigilanciaFinanceiroList) {
        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
            return Lambda.exists(vigilanciaFinanceiroList, Lambda.having(Lambda.on(VigilanciaFinanceiro.class).getStatus(), Matchers.not(Matchers.equalTo(VigilanciaFinanceiro.Status.PAGO.value()))));
        } else {
            return false;
        }
    }


    private boolean visibleComprovante() {
        return new PermissoesWebUtil().isActionPermitted(Permissions.ANEXAR, ConsultaRequerimentoVigilanciaExternoPage.class);
    }

    private void redirecionarComprovantePagamento(AjaxRequestTarget target, RequerimentoVigilanciaDTO requerimentoVigilanciaDTO) {
        List<VigilanciaFinanceiro> vigilanciaFinanceiroList = VigilanciaHelper.getVigilanciaFinanceiroList(requerimentoVigilanciaDTO.getRequerimentoVigilancia());

        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {

            if (vigilanciaFinanceiroList.size() > 1 && dlgEscolherVigilanciaFinanceiro == null) {
                initDlgEscolherVigilanciaFinanceiro();
                addModal(target, dlgEscolherVigilanciaFinanceiro);

                dlgEscolherVigilanciaFinanceiro.setObject(requerimentoVigilanciaDTO);
                dlgEscolherVigilanciaFinanceiro.show(target, vigilanciaFinanceiroList);
            } else {
                if (dlgAnexoComprovantePagamentoRequerimento == null) {
                    dlgAnexoComprovantePagamentoRequerimento = new DlgAnexoComprovantePagamentoRequerimento(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) {
                            getPageableTable().update(target);
                        }
                    };
                    addModal(target, dlgAnexoComprovantePagamentoRequerimento);
                }

                dlgAnexoComprovantePagamentoRequerimento.show(target, requerimentoVigilanciaDTO.getRequerimentoVigilancia(), vigilanciaFinanceiroList.get(0));
            }
        }
    }

    private boolean habilitarAcaoAutorizacaoExumacao(RequerimentoVigilancia rv, boolean impressao) {
        return rv != null && TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.value().equals(rv.getTipoDocumento())
                && ((impressao && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(rv.getSituacao())))
                || !impressao && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoLivro(RequerimentoVigilancia rv, boolean impressao) {
        return rv != null && (TipoSolicitacao.TipoDocumento.ABERTURA_LIVRO_CONTROLE.value().equals(rv.getTipoDocumento()) || TipoSolicitacao.TipoDocumento.FECHAMENTO_LIVRO_CONTROLE.value().equals(rv.getTipoDocumento()))
                && ((impressao && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(rv.getSituacao())))
                || !impressao && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoNadaConsta(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && TipoSolicitacao.TipoDocumento.CERTIDAO_NADA_CONSTA.value().equals(rv.getTipoDocumento())
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoPedidoDocumento(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && TipoSolicitacao.TipoDocumento.PEDIDO_DOCUMENTO.value().equals(rv.getTipoDocumento())
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarVacinacaoExtramuro(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && TipoSolicitacao.TipoDocumento.VACINACAO_EXTRAMURO.value().equals(rv.getTipoDocumento())
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoReceituario(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && (TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_A.value().equals(rv.getTipoDocumento()) || TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_B.value().equals(rv.getTipoDocumento()))
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoReceituarioExterno(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && (TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_B.value().equals(rv.getTipoDocumento()) || TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_A.value().equals(rv.getTipoDocumento()))
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoDeclaracaoVisa(RequerimentoVigilancia rv) {
        return rv != null && (TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_PRODUTOS.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_ISENCAO_TAXAS.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_OUTROS.value().equals(rv.getTipoDocumento()))
                && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao());
    }

    private boolean habilitarAcaoAlvara(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && (ALVARA_PARTICIPANTE_EVENTO.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ALVARA_CADASTRO_EVENTO.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value().equals(rv.getTipoDocumento())
                || REVALIDACAO_LICENCA_SANITARIA.value().equals(rv.getTipoDocumento())
                || LICENCA_SANITARIA.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value().equals(rv.getTipoDocumento()))
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoDispensa(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && (LICENCA_SANITARIA.value().equals(rv.getTipoDocumento())) && habilitarDIspensa(rv);
    }

    private boolean habilitarDIspensa(RequerimentoVigilancia rv) {
        if (rv  != null) {
            ClassificacaoGrupoEstabelecimento cv = VigilanciaHelper.maiorGrauRiscoEstabelecimento(rv.getEstabelecimento());
           if(cv != null && ClassificacaoGrupoEstabelecimento.RiscoClassificacaoGrupo.MEDIO_RISCO.value().equals(cv.getCodigo())) {
               return !VigilanciaHelper.hasRespostasSimQuestionarioCondicao(Objects.requireNonNull(VigilanciaHelper.getQuestionarioRespostasList(rv)));
           }
        }
        return  false;
    }

    private boolean habilitarAcaoDispensaAtividadesBaixoRisco(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && habilitarDispensaAtividadesBaixoRisco(rv)
                && (LICENCA_SANITARIA.value().equals(rv.getTipoDocumento())
                || REVALIDACAO_LICENCA_SANITARIA.value().equals(rv.getTipoDocumento())
                || ALVARA_INICIAL.value().equals(rv.getTipoDocumento())
                || ALVARA_REVALIDACAO.value().equals(rv.getTipoDocumento()));
    }

    private boolean habilitarDispensaAtividadesBaixoRisco(RequerimentoVigilancia rv) {
        if (rv  != null) {
            ClassificacaoGrupoEstabelecimento cv = VigilanciaHelper.maiorGrauRiscoEstabelecimento(rv.getEstabelecimento());
            if(cv != null && ClassificacaoGrupoEstabelecimento.RiscoClassificacaoGrupo.BAIXO_RISCO.value().equals(cv.getCodigo())) {
                return VigilanciaHelper.usaDispensaLicencaSanitariaAtividadesBaixoRisco();
            }
        }
        return false;
    }

    private boolean habilitarAcaoLicencaTransporte(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value().equals(rv.getTipoDocumento())
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoBaixaVeiculo(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && TipoSolicitacao.TipoDocumento.BAIXA_VEICULO.value().equals(rv.getTipoDocumento())
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoAnaliseProjeto(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && (TipoSolicitacao.TipoDocumento.ANALISE_PROJETOS.value().equals(rv.getTipoDocumento()) || TipoSolicitacao.TipoDocumento.PROJETO_BASICO_ARQUITETURA.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.HABITE_SE_DECLARATORIO.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value().equals(rv.getTipoDocumento()))
                || TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value().equals(rv.getTipoDocumento())
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.EM_INSPECAO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.EM_REINSPECAO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoAlteracaoRepresentanteLegalAlteracaoAtividade(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && (TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value().equals(rv.getTipoDocumento()))
                && ((impressao && !analise && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao()) && !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao()))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarAcaoEntradaBaixaResponsabilidadeTecnica(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && (TipoSolicitacao.TipoDocumento.BAIXA_RESPONSABILIDADE_TECNICA.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ENTRADA_RESPONSABILIDADE_TECNICA.value().equals(rv.getTipoDocumento()))
                && ((impressao && !analise && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao()) && !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao()))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private boolean habilitarImprimirAlteracaoEndereco(RequerimentoVigilancia rv) {
        return rv != null && TipoSolicitacao.TipoDocumento.ALTERACAO_ENDERECO.value().equals(rv.getTipoDocumento()) && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao())
                && !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao());
    }

    private boolean habilitarImprimirAlteracaoRazaoSocial(RequerimentoVigilancia rv) {
        return rv != null && TipoSolicitacao.TipoDocumento.ALTERACAO_RAZAO_SOCIAL.value().equals(rv.getTipoDocumento()) && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao())
                && !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao());
    }

    private boolean habilitarImprimirDeclaracaoVeracidadeDiversas(RequerimentoVigilancia rv) {
        return rv != null && TipoSolicitacao.TipoDocumento.DECLARACAO_CARTORIO.value().equals(rv.getTipoDocumento()) && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao())
                && !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao());
    }

    private boolean habilitarImprimirProrrogacaoPrazo(RequerimentoVigilancia rv) {
        return rv != null && TipoSolicitacao.TipoDocumento.PRORROGACAO_PRAZO.value().equals(rv.getTipoDocumento()) && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao())
                && !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao());
    }

    private boolean habilitarImprimirInspecaoSanitaria(RequerimentoVigilancia rv) {
        return rv != null && (TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_COMUM.value().equals(rv.getTipoDocumento()) || TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_AFE_ANVISA.value().equals(rv.getTipoDocumento()))
                && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao())
                && !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao());
    }

    private boolean habilitarAcaoCredenciamento(RequerimentoVigilancia rv, boolean impressao, boolean analise) {
        return rv != null && TREINAMENTOS_ALIMENTO.value().equals(rv.getTipoDocumento())
                && ((impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rv.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao())))
                || !impressao && !analise && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rv.getSituacao()))
                || !impressao && analise && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rv.getSituacao()));
    }

    private void responsePage(RequerimentoVigilancia rv, boolean edicao) {
        Long td = rv.getTipoDocumento();
        rv.getTipoSolicitacao().setTipoDocumento(rv.getTipoDocumento());
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case EXUMACAO_RESTOS_MORTAIS:
                    setResponsePage(new RequerimentoExumacaoExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case ALVARA_PARTICIPANTE_EVENTO:
                    setResponsePage(new RequerimentoEventoExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case ALVARA_CADASTRO_EVENTO:
                    setResponsePage(new RequerimentoCadastroEventoExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class, false));
                    break;
                case ALVARA_INICIAL:
                case LICENCA_SANITARIA:
                    setResponsePage(new RequerimentoAlvaraInicialExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case REVALIDACAO_LICENCA_SANITARIA:
                case ALVARA_REVALIDACAO:
                    boolean devolvido = RequerimentoVigilancia.SituacaoAprovacao.DEVOLVIDO.value().equals(rv.getSituacaoAprovacao());
                    setResponsePage(new RequerimentoRevalidacaoAlvaraExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class, devolvido));
                    break;
                case LICENCA_TRANSPORTE:
                    setResponsePage(new RequerimentoLicencaTransporteExternoPage(rv, edicao, true, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case PRORROGACAO_PRAZO:
                    setResponsePage(new RequerimentoProrrogacaoPrazoExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case INSPECAO_SANITARIA_COMUM:
                    setResponsePage(new RequerimentoInspecaoSanitariaExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class, false));
                    break;
                case INSPECAO_SANITARIA_AFE_ANVISA:
                    setResponsePage(new RequerimentoInspecaoSanitariaExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class, true));
                    break;
                case DECLARACAO_CARTORIO:
                    setResponsePage(new RequerimentoDeclaracaoVeracidadeExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case ALTERACAO_RESPONSABILIDADE_LEGAL:
                    setResponsePage(new RequerimentoRepresentanteLegalExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case ALTERACAO_ATIVIDADE_ECONOMICA:
                    setResponsePage(new RequerimentoAtividadeEconomicaExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case ANALISE_PROJETOS:
                    setResponsePage(new RequerimentoAnaliseProjetosExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case PROJETO_BASICO_ARQUITETURA:
                    setResponsePage(new RequerimentoAnaliseProjetosExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case VISTORIA_LAUDO_CONFORMIDADE_PBA:
                    setResponsePage(new RequerimentoVistoriaProjetoBasicoArquiteturaExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case HABITE_SE_DECLARATORIO:
                    setResponsePage(new RequerimentoHabiteseDeclaratorioExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case REQUISICAO_RECEITUARIO_B:
                    setResponsePage(new RequerimentoReceitaBExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case BAIXA_RESPONSABILIDADE_TECNICA:
                    setResponsePage(new RequerimentoBaixaResponsabilidadeTecnicaExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case ENTRADA_RESPONSABILIDADE_TECNICA:
                    setResponsePage(new RequerimentoInclusaoResponsabilidadeExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case ALTERACAO_ENDERECO:
                    setResponsePage(new RequerimentoAlteracaoEnderecoExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case ALTERACAO_RAZAO_SOCIAL:
                    setResponsePage(new RequerimentoAlteracaoRazaoSocialExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case PEDIDO_DOCUMENTO:
                    setResponsePage(new RequerimentoPedidoDocumentoExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case VACINACAO_EXTRAMURO:
                    setResponsePage(new RequerimentoVacinacaoExtramuroExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case BAIXA_ESTABELECIMENTO:
                    setResponsePage(new RequerimentoBaixaEstabelecimentoExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case BAIXA_VEICULO:
                    setResponsePage(new RequerimentoBaixaVeiculoExternoPage(rv, edicao, true, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case TREINAMENTOS_ALIMENTO:
                    setResponsePage(new RequerimentoTreinamentoExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case RESTITUICAO_TAXA:
                    setResponsePage(new RequerimentoRestituicaoTaxaExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case AUTORIZACAO_SANITARIA:
                    setResponsePage(new RequerimentoAutorizacaoSanitariaExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO:
                    setResponsePage(new RequerimentoHidrossanitarioDeclaratorioExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;

                case ANALISE_PROJETO_HIDROSSANITARIO:
                    setResponsePage(new RequerimentoProjetoHidrossanitarioPadraoExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case VISTORIA_HABITESE_SANITARIO:
                    setResponsePage(new RequerimentoHabiteseExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;
                case PROJETO_ARQUITETONICO_SANITARIO:
                    setResponsePage(new RequerimentoProjetoArquitetonicoSanitarioExternoPage(rv, edicao, ConsultaRequerimentoVigilanciaExternoPage.class));
                    break;

                default:
                    break;
            }
        }
    }

    private List<DataReport> responseImpression(AjaxRequestTarget target, RequerimentoVigilancia rv) throws ReportException {
        Long td = rv.getTipoDocumento();
        List<DataReport> lstDataReport = new ArrayList<>();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case EXUMACAO_RESTOS_MORTAIS:
                    RelatorioAutorizacaoExumacaoDTOParam paramExumacao = new RelatorioAutorizacaoExumacaoDTOParam();
                    paramExumacao.setCodigoRequerimentoVigilancia(rv.getCodigo());
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRequerimentoExumacao(paramExumacao));
                    break;
                case DECLARACAO_VISA_PRODUTOS:
                    RelatorioRequerimentoVigilanciaComprovanteDTOParam paramVisaProdutos = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                    paramVisaProdutos.setRequerimentoVigilancia(rv);
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoDeclaracaoVisaProdutos(paramVisaProdutos));
                    break;
                case DECLARACAO_VISA_ISENCAO_TAXAS:
                    RelatorioRequerimentoVigilanciaComprovanteDTOParam paramVisaIsencaoTaxas = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                    paramVisaIsencaoTaxas.setRequerimentoVigilancia(rv);
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoDeclaracaoVisaIsencaoTaxas(paramVisaIsencaoTaxas));
                    break;
                case DECLARACAO_VISA_OUTROS:
                    RelatorioRequerimentoVigilanciaComprovanteDTOParam paramVisaOutros = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                    paramVisaOutros.setRequerimentoVigilancia(rv);
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoDeclaracaoVisaOutros(paramVisaOutros));
                    break;
                default:
                    initDlgImpressaoConsultaRequerimentoVigilancia(target, rv);
                    break;
            }

        }
        return lstDataReport;
    }

    private void initDlgImpressaoConsultaRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        if (dlgImpressaoConsultaRequerimentoVigilancia == null) {
            dlgImpressaoConsultaRequerimentoVigilancia = new DlgImpressaoConsultaRequerimentoVigilancia(newModalId(), false, true) {
                @Override
                public DataReport onImprimir(RequerimentoVigilancia requerimentoVigilancia) {
                    return null;
                }
            };
            addModal(target, dlgImpressaoConsultaRequerimentoVigilancia);
        }
        dlgImpressaoConsultaRequerimentoVigilancia.show(target, rv);
    }

    private void responseCancel(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case EXUMACAO_RESTOS_MORTAIS:
                case ALVARA_PARTICIPANTE_EVENTO:
                case ALVARA_CADASTRO_EVENTO:
                case ALVARA_INICIAL:
                case ALVARA_REVALIDACAO:
                case REVALIDACAO_LICENCA_SANITARIA:
                case LICENCA_TRANSPORTE:
                case ABERTURA_LIVRO_CONTROLE:
                case PRORROGACAO_PRAZO:
                case INSPECAO_SANITARIA_COMUM:
                case INSPECAO_SANITARIA_AFE_ANVISA:
                case ALTERACAO_RESPONSABILIDADE_LEGAL:
                case ALTERACAO_ATIVIDADE_ECONOMICA:
                case BAIXA_RESPONSABILIDADE_TECNICA:
                case ENTRADA_RESPONSABILIDADE_TECNICA:
                case PEDIDO_DOCUMENTO:
                case VACINACAO_EXTRAMURO:
                case LICENCA_SANITARIA:
                case BAIXA_ESTABELECIMENTO:
                case BAIXA_VEICULO:
                case ANALISE_PROJETOS:
                case PROJETO_BASICO_ARQUITETURA:
                case VISTORIA_LAUDO_CONFORMIDADE_PBA:
                case AUTORIZACAO_SANITARIA:
                case HABITE_SE_DECLARATORIO:
                case ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO:
                case PROJETO_ARQUITETONICO_SANITARIO:
                    initDlgCancelamentoRequerimentoVigilancia(target, rv);
                    break;
                default:
                    break;
            }
        }
    }

    private void initDlgCancelamentoRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        if (dlgCancelamentoRequerimentoVigilancia == null) {
            dlgCancelamentoRequerimentoVigilancia = new DlgCancelamentoFinalizacaoRequerimentoVigilancia(newModalId(), BundleManager.getString("cancelamentoRequerimento")) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) throws ValidacaoException, DAOException {
                    if (dto.getSituacao() == null) {
                        dto.setSituacao(RequerimentoVigilancia.Situacao.CANCELADO);
                    }
                    BOFactoryWicket.getBO(VigilanciaFacade.class).cancelarFinalizarRequerimentoVigilancia(dto);
                    getPageableTable().update(target);
                }
            };
            addModal(target, dlgCancelamentoRequerimentoVigilancia);
        }
        String validacaoBoletosPendentes = VigilanciaHelper.validacaoBoletosPendentes(rv);
        if (StringUtils.trimToNull(validacaoBoletosPendentes) != null) {
            initDlgConfirmacaoCancelamento(target, validacaoBoletosPendentes.concat(". Deseja realmente cancelar?"), rv);
        } else {
            dlgCancelamentoRequerimentoVigilancia.show(target, rv, false);
        }
    }

    private void initDlgConfirmacaoCancelamento(AjaxRequestTarget target, String mensagemErro, RequerimentoVigilancia requerimentoVigilancia) {
        DlgConfirmacaoSimNao<RequerimentoVigilancia> dlgConfirmacaoSimNaoCancelamento;
        dlgConfirmacaoSimNaoCancelamento = new DlgConfirmacaoSimNao<RequerimentoVigilancia>(newModalId(), mensagemErro) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) {
                dlgCancelamentoRequerimentoVigilancia.show(target, getObject(), false);
            }
        };
        addModal(target, dlgConfirmacaoSimNaoCancelamento);
        dlgConfirmacaoSimNaoCancelamento.setObject(requerimentoVigilancia);
        dlgConfirmacaoSimNaoCancelamento.show(target);
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<RequerimentoVigilanciaDTO, RequerimentoVigilanciaDTOParam>() {
            @Override
            public DataPagingResult<RequerimentoVigilanciaDTO> executeQueryPager(DataPaging<RequerimentoVigilanciaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setAscending(getSort().isAscending());
                dataPaging.getParam().setPropSort(getSort().getProperty());
                if (dataPaging.getParam().getVisualizarCancelados() == null) {
                    dataPaging.getParam().setVisualizarCancelados(RepositoryComponentDefault.NAO_LONG);
                }
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarRequerimentoVigilanciaExterno(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(RequerimentoVigilancia.PROP_DATA_REQUERIMENTO, false);
            }

        };
    }

    @Override
    public RequerimentoVigilanciaDTOParam getParameters() {
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return VigilanciaHomePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRequerimentosProtocolo");
    }

    private boolean enableCancel(RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            List<Long> tiposDocumentos = Arrays.asList(
                    TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_PARTICIPANTE_EVENTO.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_CADASTRO_EVENTO.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value(),
                    TipoSolicitacao.TipoDocumento.REVALIDACAO_LICENCA_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value(),
                    TipoSolicitacao.TipoDocumento.ABERTURA_LIVRO_CONTROLE.value(),
                    TipoSolicitacao.TipoDocumento.PRORROGACAO_PRAZO.value(),
                    TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_COMUM.value(),
                    TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_AFE_ANVISA.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_RESPONSABILIDADE_TECNICA.value(),
                    TipoSolicitacao.TipoDocumento.PEDIDO_DOCUMENTO.value(),
                    TipoSolicitacao.TipoDocumento.VACINACAO_EXTRAMURO.value(),
                    TipoSolicitacao.TipoDocumento.ENTRADA_RESPONSABILIDADE_TECNICA.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_VEICULO.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_ESTABELECIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.PROJETO_BASICO_ARQUITETURA.value(),
                    TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETOS.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.HABITE_SE_DECLARATORIO.value(),
                    TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value(),
                    TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value(),
                    LICENCA_SANITARIA.value()
            );
            return tiposDocumentos.contains(td);
        }
        return false;
    }

    public DropDown<Long> getTipoDocumentoDropDown(String id) {
        DropDown dropDown = new DropDown<Long>(id);
        dropDown.addChoice(null, "");
        if (isActionPermitted(Permissions.ALVARA_INICIAL, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value(), TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.descricao());
        }
        if (isActionPermitted(Permissions.EXUMACAO_RESTOS_MORTAIS, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.value(), TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.descricao());
        }
        if (isActionPermitted(Permissions.ALVARA_PARTICIPANTE_EVENTO, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.ALVARA_PARTICIPANTE_EVENTO.value(), TipoSolicitacao.TipoDocumento.ALVARA_PARTICIPANTE_EVENTO.descricao());
        }
        if (isActionPermitted(Permissions.ALVARA_REVALIDACAO, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value(), TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.descricao());
        }
        if (isActionPermitted(Permissions.LICENCA_TRANSPORTE, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value(), TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.descricao());
        }
        if (isActionPermitted(Permissions.PRORROGACAO_PRAZO, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.PRORROGACAO_PRAZO.value(), TipoSolicitacao.TipoDocumento.PRORROGACAO_PRAZO.descricao());
        }
        if (isActionPermitted(Permissions.INSPECAO_SANITARIA_ROTINA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_COMUM.value(), TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_COMUM.descricao());
        }
        if (isActionPermitted(Permissions.INSPECAO_SANITARIA_AFE_ANVISA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_AFE_ANVISA.value(), TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_AFE_ANVISA.descricao());
        }
        if (isActionPermitted(Permissions.ALTERACAO_RESPONSABILIDADE_LEGAL, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value(), TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.descricao());
        }
        if (isActionPermitted(Permissions.ALTERACAO_ATIVIDADE_ECONOMICA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value(), TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.descricao());
        }
        if (isActionPermitted(Permissions.BAIXA_RESPONSABILIDADE_TECNICA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.BAIXA_RESPONSABILIDADE_TECNICA.value(), TipoSolicitacao.TipoDocumento.BAIXA_RESPONSABILIDADE_TECNICA.descricao());
        }
        if (isActionPermitted(Permissions.ENTRADA_RESPONSABILIDADE_TECNICA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.ENTRADA_RESPONSABILIDADE_TECNICA.value(), TipoSolicitacao.TipoDocumento.ENTRADA_RESPONSABILIDADE_TECNICA.descricao());
        }
        if (isActionPermitted(Permissions.PEDIDO_DOCUMENTO, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.PEDIDO_DOCUMENTO.value(), TipoSolicitacao.TipoDocumento.PEDIDO_DOCUMENTO.descricao());
        }
        if (isActionPermitted(Permissions.VACINACAO_EXTRAMURO, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.VACINACAO_EXTRAMURO.value(), TipoSolicitacao.TipoDocumento.VACINACAO_EXTRAMURO.descricao());
        }
        if (isActionPermitted(Permissions.BAIXA_ESTABELECIMENTO, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.BAIXA_ESTABELECIMENTO.value(), TipoSolicitacao.TipoDocumento.BAIXA_ESTABELECIMENTO.descricao());
        }
        if (isActionPermitted(Permissions.BAIXA_VEICULO, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.BAIXA_VEICULO.value(), TipoSolicitacao.TipoDocumento.BAIXA_VEICULO.descricao());
        }
        if (isActionPermitted(Permissions.PROJETO_BASICO_ARQUITETURA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.PROJETO_BASICO_ARQUITETURA.value(), TipoSolicitacao.TipoDocumento.PROJETO_BASICO_ARQUITETURA.descricao());
        }
        if (isActionPermitted(Permissions.VISTORIA_LAUDO_CONFORMIDADE_PBA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.value(), TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.descricao());
        }
        if (isActionPermitted(Permissions.TREINAMENTOS_ALIMENTO, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value(), TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.descricao());
        }
        if (isActionPermitted(Permissions.RESTITUICAO_TAXA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.RESTITUICAO_TAXA.value(), TipoSolicitacao.TipoDocumento.RESTITUICAO_TAXA.descricao());
        }
        if (isActionPermitted(Permissions.AUTORIZACAO_SANITARIA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value(), TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.descricao());
        }
        if (isActionPermitted(Permissions.PROJETO_ARQUITETONICO_SANITARIO_EXTERNO, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value(), TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.descricao());
        }
        if (isActionPermitted(Permissions.LICENCA_SANITARIA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.value(), TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.descricao());
        }
        if (isActionPermitted(Permissions.REVALIDACAO_LICENCA_SANITARIA, VigilanciaHomePage.class)) {
            dropDown.addChoice(TipoSolicitacao.TipoDocumento.REVALIDACAO_LICENCA_SANITARIA.value(), TipoSolicitacao.TipoDocumento.REVALIDACAO_LICENCA_SANITARIA.descricao());
        }
        return dropDown;
    }

    private void imprimirBoleto(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException {
        List<VigilanciaFinanceiro> vigilanciaFinanceiroList = VigilanciaHelper.getVigilanciaFinanceiroList(requerimentoVigilancia);
        String boletoBase64 = FinanceiroVigilanciaHelper.getBoletoBase64(vigilanciaFinanceiroList);
        ajaxPreviewBlank.initiatePdfBase64(target, boletoBase64);
        getPageableTable().update(target);
    }

    private void imprimirMemorando(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        try {
            DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoMemorando(requerimentoVigilancia);

            File file = File.createTempFile("memorando", ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());

            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(file));
            ajaxPreviewBlank.initiate(target, "Memorando.pdf", resourceStream);
        } catch (ReportException | IOException | JRException ex) {
            Logger.getLogger(ConsultaRequerimentoVigilanciaExternoPage.class.getName()).log(Level.SEVERE, null, ex);
        }
        getPageableTable().update(target);
    }


    private boolean existsAnexoObservacoes(RequerimentoVigilancia requerimentoVigilancia) {
        if (TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value().equals(requerimentoVigilancia.getTipoDocumento())) {
            return VigilanciaHelper.existsAnexosComObservacaoRequerimentoLicencaTransporteVeiculo(requerimentoVigilancia);
        }

        return VigilanciaHelper.existsAnexoVigilanciaComObservacao(requerimentoVigilancia);
    }

    @Override
    public IPagerProvider getPagerProviderInstance2() {
        return new QueryPagerProvider<ProcessoAdministrativo, Usuario>() {
            @Override
            public DataPagingResult<ProcessoAdministrativo> executeQueryPager(DataPaging<Usuario> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.setParam(SessaoAplicacaoImp.getInstance().getUsuario());
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarProcessoAdministrativoExterno(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ProcessoAdministrativo.PROP_NUMERO_PROCESSO, false);
            }
        };
    }

    @Override
    public List<IColumn> getColumns2(List<IColumn> columns) {
        ProcessoAdministrativo proxy = on(ProcessoAdministrativo.class);

        columns.add(getActionColumn2());
        columns.add(createColumn(bundle("numeroProcesso"), proxy.getNumeroProcessoFormatado()));
        columns.add(createColumn(bundle("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createColumn(bundle("tipo"), proxy.getDescricaoTipo()));
        columns.add(createColumn(bundle("numeroDocumento"), proxy.getNumeroTipoProcessoFormatado()));
        columns.add(createColumn(bundle("dataInicio"), proxy.getDataInicio()));
        columns.add(createColumn(bundle("autuado"), proxy.getAutuado()));
        columns.add(createColumn(BundleManager.getString("situacaoFinanceira"), proxy.getDescricaoSituacaoFinanceira()));
        columns.add(createColumn(BundleManager.getString("situacao"), proxy.getDescricaoSituacao()));
        return columns;
    }

    private IColumn getActionColumn2() {
        return new MultipleActionCustomColumn<ProcessoAdministrativo>() {
            @Override
            public void customizeColumn(ProcessoAdministrativo rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ProcessoAdministrativo>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoAdministrativo modelObject) {
                        setResponsePage(new DetalhesProcessoAdministrativoPage(modelObject));
                    }
                }).setVisible(isActionPermitted(Permissions.CONSULTAR));

                addAction(ActionType.IMPRIMIR, rowObject, new IModelAction<ProcessoAdministrativo>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoAdministrativo modelObject) throws ValidacaoException, DAOException {
                        responseImpression(target, modelObject);
                    }
                }).setVisible(isActionPermitted(Permissions.IMPRIMIR));
            }
        };
    }

    private void responseImpression(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) {
        try {
            info(target, "O processo administrativo foi enviado para o email: " + ApplicationSession.get().getSessaoAplicacao().getUsuario().getEmail());
            BOFactoryWicket.getBO(VigilanciaReportFacade.class).enviarImpressaoDocumentosProcessoAdministrativoUsuarioExterno(processoAdministrativo);
        } catch (ValidacaoException e) {
            warn(target, e.getMessage());
        } catch (Exception e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }
}