package br.com.celk.view.consorcio.pedidotransferencialicitacao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioSaldoPedidosTransferenciaDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;

import java.util.Arrays;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 * Programa - 292
 */
@Private

public class RelatorioSaldoPedidosTransferenciaPage extends RelatorioPage<RelatorioSaldoPedidosTransferenciaDTOParam> {

    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        form.add(new InputField("codigo"));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(autoCompleteConsultaProduto =  new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new AutoCompleteConsultaPessoa("fornecedor"));
        form.add(new AutoCompleteConsultaTipoConta("tipoConta"));
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioSaldoPedidosTransferenciaDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoResumo", RelatorioSaldoPedidosTransferenciaDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioSaldoPedidosTransferenciaDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoOrdenacao", RelatorioSaldoPedidosTransferenciaDTOParam.TipoOrdenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("baseCalculo", RelatorioSaldoPedidosTransferenciaDTOParam.BaseCalculo.values()));
        form.add(DropDownUtil.getEnumDropDown("situacaoEstoque", RelatorioSaldoPedidosTransferenciaDTOParam.SituacaoEstoque.values()));
        form.add(DropDownUtil.getIEnumDropDown("situacaoFinanceira", PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.values(), true, bundle("todas"), false, false, true));

        form.add(new CheckBox("situacaoAberto"));
        form.add(new CheckBox("situacaoCancelado"));
        form.add(new CheckBox("situacaoEncaminhado"));
        form.add(new CheckBox("situacaoSeparado"));
        form.add(new CheckBox("situacaoFechado"));

        form.add(new CheckBox("situacaoItemAberto"));
        form.add(new CheckBox("situacaoItemCancelado"));
        form.add(new CheckBox("situacaoItemFechado"));

        form.add(DropDownUtil.getNaoSimLongDropDown("apresentarOrdemCompra"));

    }

    @Override
    public Class<RelatorioSaldoPedidosTransferenciaDTOParam> getDTOParamClass() {
        return RelatorioSaldoPedidosTransferenciaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioSaldoPedidosTransferenciaDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioSaldoPedidosTransferencia(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("saldoPedidosTransferencia");
    }
}
