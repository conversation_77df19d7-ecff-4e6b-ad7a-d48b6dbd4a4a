package br.com.celk.view.materiais.pedidotransferencia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaTipoVacina;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioPedidosDivergenciaEntregaDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import org.apache.wicket.markup.html.form.Form;


/**
 *
 * <AUTHOR>
 * Programa - 113
 */
@Private

public class RelatorioPedidosDivergenciaEntregaPage extends RelatorioPage<QueryRelatorioPedidosDivergenciaEntregaDTOParam> {
    private AutoCompleteConsultaTipoVacina autoCompleteConsultaTipoVacina;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaOrigem;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaDestino;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresaOrigem = new AutoCompleteConsultaEmpresa("unidadesOrigem"));
        form.add(autoCompleteConsultaEmpresaDestino = new AutoCompleteConsultaEmpresa("unidadesDestino"));
        form.add(autoCompleteConsultaTipoVacina = new AutoCompleteConsultaTipoVacina("vacina"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produtos"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getDropDownTipoPeriodo());
        form.add(getDropDownFormaApresentacao());

        autoCompleteConsultaEmpresaDestino.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresaOrigem.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setOperadorValor(true);

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresaDestino.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
    }

    @Override
    public Class getDTOParamClass() {
        return QueryRelatorioPedidosDivergenciaEntregaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(QueryRelatorioPedidosDivergenciaEntregaDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioPedidosDivergenciaEntrega(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("pedidosDivergenciaEntrega");
    }
    
    public DropDown getDropDownFormaApresentacao(){
        DropDown dropDown = new DropDown("formaApresentacao");
        DropDownUtil.setEnumChoices(dropDown, QueryRelatorioPedidosDivergenciaEntregaDTOParam.FormaApresentacao.values());
        
        return dropDown;
    }
    
    public DropDown getDropDownTipoPeriodo(){
        DropDown dropDown = new DropDown("tipoPeriodo");
        DropDownUtil.setEnumChoices(dropDown, QueryRelatorioPedidosDivergenciaEntregaDTOParam.TipoPeriodo.values());
        
        return dropDown;
    }
}
