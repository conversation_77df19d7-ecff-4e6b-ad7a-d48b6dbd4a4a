package br.com.celk.view.materiais.kitprodutos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.KitProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 449
 */
@Private
public class ConsultaKitProdutosPage extends ConsultaPage<KitProduto, List<BuilderQueryCustom.QueryParameter>> {

    private String codigo;
    private String descricao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField("codigo"));
        form.add(new UpperField("descricao"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(KitProduto.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(KitProduto.PROP_PRODUTO_PRINCIPAL)));

        return columns;
    }

    private CustomColumn<KitProduto> getCustomColumn() {
        return new CustomColumn<KitProduto>() {
            @Override
            public Component getComponent(String componentId, final KitProduto rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroKitProdutosPage(rowObject, false, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {

                        BOFactoryWicket.getBO(ProdutoFacade.class).ExcluiKitProduto(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroKitProdutosPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return KitProduto.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(KitProduto.class).getProperties(),
                        new String[]{
                    VOUtils.montarPath(KitProduto.PROP_PRODUTO_PRINCIPAL, Produto.PROP_CODIGO),
                    VOUtils.montarPath(KitProduto.PROP_PRODUTO_PRINCIPAL, Produto.PROP_REFERENCIA),
                    VOUtils.montarPath(KitProduto.PROP_PRODUTO_PRINCIPAL, Produto.PROP_DESCRICAO)
                });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(KitProduto.PROP_PRODUTO_PRINCIPAL, Produto.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(KitProduto.PROP_PRODUTO_PRINCIPAL, Produto.PROP_REFERENCIA), codigo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(KitProduto.PROP_PRODUTO_PRINCIPAL, Produto.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroKitProdutosPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaKitProdutos");
    }
}
