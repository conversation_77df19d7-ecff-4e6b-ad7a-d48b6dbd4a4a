package br.com.celk.view.vigilancia.registroatividadeveterinaria;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.checkgroup.CheckBoxGroup;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaAtividadeVeterinaria;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaEspecieAnimal;
import br.com.celk.vigilancia.dto.RelatorioResumoAtividadeVeterinariaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;

import java.util.Arrays;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 573
 */
@Private
public class RelatorioResumoAtividadesVeterinariasPage extends RelatorioPage<RelatorioResumoAtividadeVeterinariaDTOParam> {

    private CheckBoxGroup checkBoxGroup;

    @Override
    public void init(Form<RelatorioResumoAtividadeVeterinariaDTOParam> form) {

        RelatorioResumoAtividadeVeterinariaDTOParam proxy = on(RelatorioResumoAtividadeVeterinariaDTOParam.class);

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidade())));
        form.add(new AutoCompleteConsultaAtividadeVeterinaria(path(proxy.getAtividadeVeterinaria())));
        form.add(new AutoCompleteConsultaEspecieAnimal(path(proxy.getEspecieAnimal())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSexo()), RelatorioResumoAtividadeVeterinariaDTOParam.Sexo.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioResumoAtividadeVeterinariaDTOParam.FormaApresentacao.values()));
        form.add(checkBoxGroup = new CheckBoxGroup(path(proxy.getTipoResumo()), RelatorioResumoAtividadeVeterinariaDTOParam.TipoResumo.values(), true));
        form.add(new PnlDatePeriod(path(proxy.getPeriodo())));
        
        checkBoxGroup.removeSelectionList(Arrays.asList(RelatorioResumoAtividadeVeterinariaDTOParam.TipoResumo.ESPECIE_ANIMAL.value(), 
                RelatorioResumoAtividadeVeterinariaDTOParam.TipoResumo.SEXO.value()));
    }

    @Override
    public Class<RelatorioResumoAtividadeVeterinariaDTOParam> getDTOParamClass() {
        return RelatorioResumoAtividadeVeterinariaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoAtividadeVeterinariaDTOParam param) throws ReportException {
        if (checkBoxGroup.isSelectionEmpty()) {
            error(BundleManager.getString("msgSelecioneAoMenosUmTipoResumo"));
            return null;
        }
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioResumoAtividadeVeterinaria(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("resumoRegistroAtividade");
    }

}
