package br.com.celk.view.vigilancia.agravos.resumo.relatorio;

import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.cid.autocomplete.AutoCompleteConsultaCidMulti;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.basico.classificacaocid.autocomplete.AutoCompleteConsultaClassificacaoCid;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioAgravosDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioResumoAgravosDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 549
 */
public class RelatorioAgravoPage extends RelatorioPage<RelatorioAgravosDTOParam> {

    @Override
    public void init(Form<RelatorioAgravosDTOParam> form) {
        RelatorioAgravosDTOParam proxy = on(RelatorioAgravosDTOParam.class);

        form.add(new InputField(path(proxy.getCodigoNotificacao())));
        form.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus())));
        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidade())));
        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidadeReferencia())));
        form.add(new AutoCompleteConsultaCidMulti(path(proxy.getListCid())));
        form.add(new AutoCompleteConsultaClassificacaoCid(path(proxy.getClassificacaoCids())));
        form.add(new InputField(path(proxy.getBairro())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getStatus()), RelatorioResumoAgravosDTOParam.Status.values()));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getOrdenacao()), RelatorioAgravosDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoOrdenacao()), RelatorioAgravosDTOParam.TipoOrdenacao.values()));
        form.add(new PnlChoicePeriod(path(proxy.getPeriodo())));
        form.add(new DateChooser(path(proxy.getDataLimiteEncerramento())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioAgravosDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getGestante()), true));
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));
    }

    @Override
    public Class<RelatorioAgravosDTOParam> getDTOParamClass() {
        return RelatorioAgravosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioAgravosDTOParam param) throws ReportException {
        return BOFactory.getBO(VigilanciaReportFacade.class).relatorioAgravos(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioAgravos");
    }

}
