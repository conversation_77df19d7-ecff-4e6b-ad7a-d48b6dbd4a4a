package br.com.celk.view.consorcio.pedidotransferencialicitacaoentrega.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoEntregasDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Arrays;
import java.util.List;

import br.com.ksisolucoes.vo.consorcio.Licitacao;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import org.apache.wicket.markup.html.form.Form;


/**
 *
 * <AUTHOR>
 * Programa - 291
 */
@Private
 
public class RelatorioDetalhamentoEntregasPage extends RelatorioPage<RelatorioDetalhamentoEntregasDTOParam> {

    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private DropDown<TipoConta> dropDownTipoConta;
    private DropDown<Licitacao> dropDownLicitacao;

    @Override
    public void init(Form form) {
        form.add(new InputField("numeroGuia"));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioDetalhamentoEntregasDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoResumo", RelatorioDetalhamentoEntregasDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioDetalhamentoEntregasDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoOrdenacao", RelatorioDetalhamentoEntregasDTOParam.TipoOrdenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoRelatorio", RelatorioDetalhamentoEntregasDTOParam.TipoRelatorio.values()));
        form.add(dropDownTipoConta = populaDropDownTipoConta());
        form.add(dropDownLicitacao = populaDropDownLicitacao());
    }

    private DropDown populaDropDownTipoConta() {
        dropDownTipoConta = new DropDown<TipoConta>("tipoConta");

        List<TipoConta> tiposConta = LoadManager.getInstance(TipoConta.class)
                .addProperties(new HQLProperties(TipoConta.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(TipoConta.PROP_DESCRICAO), QueryCustom.QueryCustomSorter.DECRESCENTE))
                .start().getList();

        dropDownTipoConta.addChoice(null, "");
        for (TipoConta tipoConta : tiposConta) {
            dropDownTipoConta.addChoice(tipoConta, tipoConta.getDescricao());
        }
        return dropDownTipoConta;
    }

    private DropDown populaDropDownLicitacao() {
        dropDownLicitacao = new DropDown<Licitacao>("licitacao");

        List<Licitacao> licitacoes = LoadManager.getInstance(Licitacao.class)
                .addProperties(new HQLProperties(Licitacao.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Licitacao.PROP_CODIGO), QueryCustom.QueryCustomSorter.DECRESCENTE))
                .start().getList();

        dropDownLicitacao.addChoice(null, "");
        for (Licitacao licitacao : licitacoes) {
            dropDownLicitacao.addChoice(licitacao, licitacao.getDescricaoLicitacaoFormatado());
        }
        return dropDownLicitacao;
    }

    @Override
    public Class<RelatorioDetalhamentoEntregasDTOParam> getDTOParamClass() {
        return RelatorioDetalhamentoEntregasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioDetalhamentoEntregasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioDetalhamentoEntregas(param);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhamentoEntregas");
    }
}
