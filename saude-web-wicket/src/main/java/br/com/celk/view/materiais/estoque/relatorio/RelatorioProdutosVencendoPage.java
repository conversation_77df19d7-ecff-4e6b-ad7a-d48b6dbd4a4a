package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.materiais.grupoproduto.autocomplete.AutoCompleteConsultaGrupoProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioProdutosVencendoDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 * Programa - 146
 */
@Private

public class RelatorioProdutosVencendoPage extends RelatorioPage<QueryRelatorioProdutosVencendoDTOParam> {

    private AutoCompleteConsultaGrupoProduto autoCompleteConsultaGrupoProduto;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaGrupoProduto = new AutoCompleteConsultaGrupoProduto("grupoProduto"));
        form.add(new RequiredInputField("diasVencimento"));
        form.add(DropDownUtil.getSimNaoDropDown("separarUnidade"));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", QueryRelatorioProdutosVencendoDTOParam.Ordenacao.values()));
        
        autoCompleteConsultaGrupoProduto.setMultiplaSelecao(true);
        autoCompleteConsultaGrupoProduto.setOperadorValor(true);
    }

    @Override
    public void customDTOParam(QueryRelatorioProdutosVencendoDTOParam param) {
        param.setDiasVencimento(90);
    }
    
    @Override
    public Class<QueryRelatorioProdutosVencendoDTOParam> getDTOParamClass() {
        return QueryRelatorioProdutosVencendoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(QueryRelatorioProdutosVencendoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioProdutosVencendo(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("produtosVencendo");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaGrupoProduto.getTxtDescricao().getTextField();
    }

}
