package br.com.celk.view.vigilancia.sindrome;

import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.faixaetaria.autocomplete.AutoCompleteConsultaFaixaEtaria;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioSindromeGripalDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItem;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItemPK;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR> S. Schmoeller
 * Programa - 936
 */
public class RelatorioSindromeGripalPage extends RelatorioPage<RelatorioSindromeGripalDTOParam> {

    private LongField semana;
    private DropDown dropDownAno;
    private PnlDatePeriod pnlDatePeriod;
    private AutoCompleteConsultaFaixaEtaria acFaixaEtaria;

    private DropDown<FaixaEtariaItem> ddFaixaEtariaItem;

    @Override
    public void init(Form<RelatorioSindromeGripalDTOParam> form) {
        RelatorioSindromeGripalDTOParam proxy = on(RelatorioSindromeGripalDTOParam.class);

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidade())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioSindromeGripalDTOParam.FormaApresentacao.values()));


        semana = new LongField(path(proxy.getSemana()));
        semana.setVMin(1L);
        semana.setVMax(53L);
        semana.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                pnlDatePeriod.setModelObject(new DatePeriod(null, null));
                calcularDatas();

                target.add(pnlDatePeriod);
                target.appendJavaScript(JScript.initMasks());
            }
        });

        dropDownAno = DropDownUtil.getAnoDropDown("ano", false, false, false);
        dropDownAno.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                pnlDatePeriod.setModelObject(new DatePeriod(null, null));
                calcularDatas();

                target.add(pnlDatePeriod);
                target.appendJavaScript(JScript.initMasks());
            }
        });
        pnlDatePeriod = new PnlDatePeriod(path(proxy.getPeriodo()));

        pnlDatePeriod.getDataInicial().add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                semana.limpar(target);
                dropDownAno.limpar(target);
            }
        });
        pnlDatePeriod.getDataFinal().add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                semana.limpar(target);
                dropDownAno.limpar(target);
            }
        });

        acFaixaEtaria = new AutoCompleteConsultaFaixaEtaria(path(proxy.getFaixaEtaria()), true);
        acFaixaEtaria.add(new ConsultaListener<FaixaEtaria>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, FaixaEtaria faixaEtaria) {
                updateDropDownFaixaEtariaItem(target, faixaEtaria);
            }
        });
        acFaixaEtaria.add(new RemoveListener<FaixaEtaria>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, FaixaEtaria object) {
                updateDropDownFaixaEtariaItem(target, null);
            }
        });

        ddFaixaEtariaItem = new DropDown<>(path(proxy.getFaixaEtariaItem()));
        form.add(semana, dropDownAno, pnlDatePeriod, acFaixaEtaria, ddFaixaEtariaItem);

        FaixaEtaria faixaEtaria = LoadManager.getInstance(FaixaEtaria.class).setId(FaixaEtaria.FAIXA_ETARIA_CAMPANHA).start().getVO();
        acFaixaEtaria.setComponentValue(faixaEtaria);
        updateDropDownFaixaEtariaItem(null, faixaEtaria);

        try {
            dropDownAno.setModelObject(new Integer(Data.getAno(DataUtil.getDataAtual())).longValue());
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public void antesGerarRelatorio(AjaxRequestTarget target) throws ValidacaoException {
        if (param.getSemana() == null && param.getPeriodo() == null){
            throw new ValidacaoException(BundleManager.getString("obrigatorioPeloMenosUmFiltrodePeriodo"));
        }
        if (param.getPeriodo() != null){
            if (param.getPeriodo().getDataInicial() != null && param.getPeriodo().getDataFinal() != null){
                Long diffDate = Math.abs(param.getPeriodo().getDataFinal().getTime() - param.getPeriodo().getDataInicial().getTime());
                Long diff = TimeUnit.DAYS.convert(diffDate, TimeUnit.MILLISECONDS);
                if (diff > 366L){
                    throw new ValidacaoException(BundleManager.getString("diferencaPeriodoMaiorUmano"));
                }
            } else {
                throw new ValidacaoException(BundleManager.getString("informeDataInicialDataFinal"));
            }
        }
    }

    @Override
    public Class<RelatorioSindromeGripalDTOParam> getDTOParamClass() {
        return RelatorioSindromeGripalDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioSindromeGripalDTOParam param) throws ReportException {
        return BOFactory.getBO(VigilanciaReportFacade.class).relatorioSindromeGripal(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioSindromeGripal");
    }

    private void calcularDatas() {
        if (param.getSemana() != null && param.getAno() != null) {
            int semana = param.getSemana().intValue();
            Calendar c = GregorianCalendar.getInstance();
            c.setTimeZone(TimeZone.getTimeZone("America/Sao_Paulo"));

            //Criar o calendário com o primeiro dia do ano
            c.set(Calendar.YEAR, param.getAno().intValue());
            c.set(Calendar.MONTH, Calendar.JANUARY);
            c.set(Calendar.DAY_OF_YEAR, 1);

            c.set(Calendar.WEEK_OF_YEAR, semana);
            c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek());

            c.set(Calendar.HOUR_OF_DAY, 0);
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 1);
            c.set(Calendar.MILLISECOND, 0);

            Date initWeek = c.getTime();

            //Setar o calendário no último dia dessa semana
            int currentDay = c.get(Calendar.DAY_OF_WEEK);
            int leftDays = Calendar.SATURDAY - currentDay;

            c.add(Calendar.DATE, leftDays);
            c.set(Calendar.HOUR_OF_DAY, 23);
            c.set(Calendar.MINUTE, 59);
            c.set(Calendar.SECOND, 59);
            c.set(Calendar.MILLISECOND, 999);

            Date endWeek = c.getTime();

            pnlDatePeriod.setModelObject(new DatePeriod(initWeek, endWeek));
        }
    }

    private void updateDropDownFaixaEtariaItem(AjaxRequestTarget target, FaixaEtaria faixaEtaria) {
        ddFaixaEtariaItem.removeAllChoices();
        ddFaixaEtariaItem.setEnabled(faixaEtaria != null);
        if (faixaEtaria != null) {
            ddFaixaEtariaItem.addChoice(null, BundleManager.getString("todas"));

            List<FaixaEtariaItem> faixaEtariaItensList = LoadManager.getInstance(FaixaEtariaItem.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FaixaEtariaItem.PROP_ID, FaixaEtariaItemPK.PROP_FAIXA_ETARIA), faixaEtaria))
                    .start().getList();

            for (FaixaEtariaItem faixaEtariaItem : faixaEtariaItensList) {
                ddFaixaEtariaItem.addChoice(faixaEtariaItem, faixaEtariaItem.getDescricao());
            }

        }

        if (target != null) {
            target.add(ddFaixaEtariaItem);
        }
    }

}
