package br.com.celk.view.vigilancia.rotinas.autoinfracao;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.celk.view.vigilancia.requerimentos.CadastroRelatorioInspecaoPage;
import br.com.celk.view.vigilancia.requerimentos.ConsultaRelatorioInspecaoPage;
import br.com.celk.view.vigilancia.requerimentovigilancia.PendenciasFiscalPage;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.dialog.DlgAutoInfracaoReceber;
import br.com.celk.view.vigilancia.rotinas.autoinfracao.dlg.DlgConcluirInfracao;
import br.com.celk.view.vigilancia.rotinas.automulta.CadastroAutoMultaPage;
import br.com.celk.view.vigilancia.rotinas.automulta.ConsultaAutoMultaPage;
import br.com.celk.view.vigilancia.rotinas.cadastrosTac.CadastroTermoAjustamentoCondutaPage;
import br.com.celk.view.vigilancia.rotinas.cadastrosTac.ConsultaTermoAjustamentoCondutaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.util.string.StringValue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 612
 */
public class ConsultaAutoInfracaoPage extends ConsultaPage<AutoInfracao, List<BuilderQueryCustom.QueryParameter>> {

    private PageParameters parameters;
    private Class returnClass;

    private AbstractAjaxButton btnVoltar;
    private AbstractAjaxButton btnNovo;

    private Long numeroAutoInfracao;
    private String denunciado;
    private DatePeriod periodo;
    private Long situacao;
    private Long exibirOutrosFiscais;

    private DlgConcluirInfracao dlgConcluirInfracao;
    private DropDown cbxSituacao;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private DlgAutoInfracaoReceber dlgAutoInfracaoReceber;
    private DropDown<Long> dropDownExibirOutrosFiscais;

    private VigilanciaEndereco vigilanciaEndereco;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaEndereco;

    public ConsultaAutoInfracaoPage() {
        super();
    }

    public ConsultaAutoInfracaoPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField("numeroAutoInfracao"));
        form.add(new InputField("denunciado"));
        form.add(autoCompleteConsultaEndereco = new AutoCompleteConsultaVigilanciaEndereco("vigilanciaEndereco"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(cbxSituacao =DropDownUtil.getIEnumDropDown("situacao", AutoInfracao.Status.values(), true));
        cbxSituacao.addAjaxUpdateValue();
        cbxSituacao.setOutputMarkupId(true);

        form.add(dropDownExibirOutrosFiscais = DropDownUtil.getNaoSimLongDropDown("exibirOutrosFiscais"));
        dropDownExibirOutrosFiscais.addAjaxUpdateValue();
        dropDownExibirOutrosFiscais.setOutputMarkupPlaceholderTag(true);

        setExibeExpandir(true);

        getLinkNovo().setVisible(false);

        getControls().add(btnVoltar = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar();
            }
        });

        btnVoltar.setVisible(false);
        btnVoltar.add(new AttributeModifier("type", "button"));
        btnVoltar.add(new AttributeModifier("class", "arrow-left"));
        btnVoltar.add(new AttributeModifier("value", bundle("voltar")));

        getControls().add(btnNovo = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                CadastroAutoInfracaoPage cadastroAutoInfracaoPage = new CadastroAutoInfracaoPage(getPageParameters());
                setResponsePage(cadastroAutoInfracaoPage);
            }
        });

        btnNovo.add(new AttributeModifier("class", "doc-new"));
        btnNovo.add(new AttributeModifier("value", bundle("novo")));
        btnNovo.add(new AttributeModifier("style", "margin-left: 5px;"));

        setParameters(this.getPageParameters());

    }

    private void voltar() {
        setResponsePage(returnClass);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        AutoInfracao proxy = on(AutoInfracao.class);
        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("dataInfracao"), proxy.getDataInfracao()));
        columns.add(createColumn(bundle("numeroInfracaoAbv"), proxy.getNumeroFormatado()));
        columns.add(createSortableColumn(bundle("numeroRoteiroInspecaoAbv"), proxy.getRegistroInspecao().getCodigo()));
        columns.add(createSortableColumn(bundle("autuado"), proxy.getDenunciado()));
        columns.add(createSortableColumn(bundle("prazoDefesa"), proxy.getPrazoDefesa()));
        columns.add(createSortableColumn(bundle("situacao"),proxy.getSituacao(), proxy.getStatusDescricao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AutoInfracao>() {

            @Override
            public void customizeColumn(final AutoInfracao rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<AutoInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoInfracao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAutoInfracaoPage(modelObject, getPageParameters()));
                    }
                }).setEnabled(isProfissionalAuto(rowObject));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<AutoInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoInfracao modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).removerAutoInfracao(rowObject);
                        getPageableTable().populate();
                        getPageableTable().update(target);
                    }
                }).setEnabled(!AutoInfracao.Status.CONCLUIDO.value().equals(rowObject.getSituacao()) && isProfissionalAuto(rowObject));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AutoInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoInfracao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAutoInfracaoPage(modelObject, true, getPageParameters()));
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<AutoInfracao>() {
                    @Override
                    public DataReport action(AutoInfracao modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoInfracao(modelObject.getCodigo(),modelObject.getNumeroFormatado(),modelObject.getSituacao());
                    }
                });


                ModelActionLinkPanel acaoReceber = addAction(ActionType.ANEXO, rowObject, new IModelAction<AutoInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoInfracao modelObject) throws ValidacaoException, DAOException {
                        addModal(target, dlgAutoInfracaoReceber = new DlgAutoInfracaoReceber(newModalId(), modelObject) {
                            @Override
                            public void onRecebimentoAuto(AjaxRequestTarget target, AutoInfracao object) throws DAOException, ValidacaoException {
                                actionRecebimentoAutoInfracao(object);
                                getPageableTable().update(target);
                            }
                        });
                        dlgAutoInfracaoReceber.show(target);
                    }
                });
                acaoReceber.setTitleBundleKey("receberAuto");
                acaoReceber.setVisible(isActionPermitted(Permissions.RECEBER));
                acaoReceber.setEnabled(AutoInfracao.Status.AGUARDANDO_RECEBIMENTO.value().equals(rowObject.getSituacao()) && isProfissionalAuto(rowObject));
                acaoReceber.setIcon(Icon.CALENDAR_EDIT);


                ModelActionLinkPanel acaoConcluir = addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<AutoInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoInfracao modelObject) throws ValidacaoException, DAOException {
                    if (dlgConcluirInfracao == null) {
                        addModal(target, dlgConcluirInfracao = new DlgConcluirInfracao(newModalId()) {
                            @Override
                            public void onConfirmar(AjaxRequestTarget target, AutoInfracao autoInfracao) throws ValidacaoException, DAOException {
                                autoInfracao = BOFactoryWicket.getBO(VigilanciaFacade.class).finalizarAutoInfracao(autoInfracao);
                                getPageableTable().populate();
                                getPageableTable().update(target);
                            }
                        });
                    }
                    dlgConcluirInfracao.show(target, modelObject);
                    }
                });
                acaoConcluir.setEnabled(AutoInfracao.Status.PENDENTE.value().equals(rowObject.getSituacao()) && isProfissionalAuto(rowObject));
                acaoConcluir.setTitleBundleKey("finalizarArquivar");


                ModelActionLinkPanel acaoRelatorioInspecao = addAction(ActionType.LAUDAR, rowObject, new IModelAction<AutoInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoInfracao modelObject) throws ValidacaoException, DAOException {
                        CadastroRelatorioInspecaoPage relatorioClass = new CadastroRelatorioInspecaoPage();
                        relatorioClass.setClassReturn(ConsultaRelatorioInspecaoPage.class);
                        relatorioClass.setAutoInfracao(modelObject);
                        relatorioClass.buscarAutoInfracao(target, modelObject.getCodigo());
                        setResponsePage(relatorioClass);
                    }
                });
                acaoRelatorioInspecao.setTitleBundleKey("tituloNumRelatorioInspecao");
                acaoRelatorioInspecao.setEnabled(isProfissionalAuto(rowObject));
                acaoRelatorioInspecao.setVisible(new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaRelatorioInspecaoPage.class.getName()));

                ModelActionLinkPanel acaoAutoMulta = addAction(ActionType.MONEY, rowObject, new IModelAction<AutoInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoInfracao modelObject) throws ValidacaoException, DAOException {
                        CadastroAutoMultaPage autoMultaPage = new CadastroAutoMultaPage(){
                            @Override
                            public Class getResponsePage() {
                                return ConsultaAutoInfracaoPage.class;
                            }
                        };

                        autoMultaPage.instanceFromInfracao(target, modelObject);
                        setResponsePage(autoMultaPage);
                    }
                });
                acaoAutoMulta.setTitleBundleKey("autoDeMulta");
                acaoAutoMulta.setEnabled(!existsAutoMulta(rowObject) && isProfissionalAuto(rowObject));
                acaoAutoMulta.setVisible(isActionPermitted(Permissions.AUTO_MULTA) && new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaAutoMultaPage.class.getName()));

                ModelActionLinkPanel acaoAutoInfracao = addAction(ActionType.NEW_DOC, rowObject, new IModelAction<AutoInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoInfracao modelObject) throws ValidacaoException, DAOException {

                        CadastroTermoAjustamentoCondutaPage termoAjustamentoCondutaPage = new CadastroTermoAjustamentoCondutaPage() {
                            @Override
                            public Class getResponsePage() {
                                return ConsultaAutoInfracaoPage.class;
                            }
                        };

                        termoAjustamentoCondutaPage.instanceFromInfracao(target, modelObject);
                        setResponsePage(termoAjustamentoCondutaPage);
                    }
                });
                acaoAutoInfracao.setTitleBundleKey("cadastrar_tac");
                acaoAutoInfracao.setEnabled(isProfissionalAuto(rowObject));
                acaoAutoInfracao.setVisible(new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaTermoAjustamentoCondutaPage.class.getName()));
            }
        };
    }

    private void actionRecebimentoAutoInfracao(AutoInfracao object) throws ValidacaoException, DAOException {
        if(object.getDataRecebimento() == null) {
            throw new ValidacaoException(bundle("msgInformeData"));
        }
        ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();

        if (ConfiguracaoVigilanciaEnum.TipoDatabaseCalculoDefesa.RECEBIMENTO.value().equals(configuracaoVigilancia.getTipoDatabaseCalculoPrazoDefesa()) && object.getDataRecebimento() == null) {
            object.setSituacao(AutoInfracao.Status.AGUARDANDO_RECEBIMENTO.value());
        } else if (ConfiguracaoVigilanciaEnum.TipoDatabaseCalculoDefesa.RECEBIMENTO.value().equals(configuracaoVigilancia.getTipoDatabaseCalculoPrazoDefesa()) && object.getDataRecebimento() != null) {
            object.setSituacao(AutoInfracao.Status.PENDENTE.value());
        }

//      object.setSituacao(AutoInfracao.Status.RECEBIDO.value());

        BOFactoryWicket.save(object);
        getSession().getFeedbackMessages().info(this, bundle("msgDataRecebimentoSalvaComSucesso"));
    }

    private boolean isProfissionalAuto(AutoInfracao rowObject) {
        Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if(profissional != null && profissional.getCodigo() != null) {
            return AutosHelper.isFiscalInfracao(rowObject, profissional);
        } else {
            return SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster();
        }
    }

    private boolean existsAutoMulta(AutoInfracao rowObject) {
        return LoadManager.getInstance(AutoMulta.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AutoMulta.PROP_AUTO_INFRACAO, rowObject))
                .exists();
    }

    private void setParameters(PageParameters pageParameters) {
        this.parameters = pageParameters;

        StringValue situacaoParam = parameters.get("situacao");
        if (!situacaoParam.isEmpty()) {
            situacao = situacaoParam.toLong();
        }

        StringValue exibisFiscaisParam = parameters.get("exibirOutrosFiscais");
        if (!exibisFiscaisParam.isEmpty()) {
            exibirOutrosFiscais = exibisFiscaisParam.toLong();
        } else {
            exibirOutrosFiscais = RepositoryComponentDefault.NAO_LONG;
        }

        StringValue returnClassParam = parameters.get("returnClass");
        if (!returnClassParam.isEmpty()) {
            try {
                returnClass = Class.forName(returnClassParam.toString());
                if (returnClass != null) {
                    btnVoltar.setVisible(true);
                }
                if (returnClass != null && returnClass.getName().equals(PendenciasFiscalPage.class.getName())) {
                    cbxSituacao.setEnabled(false);
                }
            } catch (ClassNotFoundException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
    }


    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return AutoInfracao.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(AutoInfracao.class).getProperties(),
                        new HQLProperties(Estabelecimento.class, AutoInfracao.PROP_ESTABELECIMENTO).getProperties(),
                        new HQLProperties(VigilanciaPessoa.class, AutoInfracao.PROP_VIGILANCIA_PESSOA).getProperties(),
                        new HQLProperties(VigilanciaEndereco.class, AutoInfracao.PROP_VIGILANCIA_ENDERECO).getProperties(),
                        new HQLProperties(RequerimentoVigilancia.class, AutoInfracao.PROP_REQUERIMENTO_VIGILANCIA).getProperties(),
                        new HQLProperties(RelatorioInspecao.class, AutoInfracao.PROP_RELATORIO_INSPECAO).getProperties(),
                        new HQLProperties(AutoIntimacao.class, AutoInfracao.PROP_AUTO_INTIMACAO).getProperties(),
                        new HQLProperties(AutoIntimacao.class, AutoInfracao.PROP_AUTO_INTIMACAO_SUBSISTENTE).getProperties()
                );
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(AutoInfracao.PROP_NUMERO), false);
            }

            @Override
            public List getInterceptors() {
                if(RepositoryComponentDefault.NAO_LONG.equals(exibirOutrosFiscais)) {
                    return Arrays.asList(new LoadInterceptor() {
                        @Override
                        public void customHQL(HQLHelper hql, String alias) {
                            if (SessaoAplicacaoImp.getInstance().getUsuario().getProfissional() != null) {
                                StringBuilder where = new StringBuilder();
                                where.append("select 1 ");
                                where.append("from FiscalAutoInfracao fai ");
                                where.append("where fai.autoInfracao.codigo = ").append(alias).append(".codigo");
                                where.append(" and fai.profissional.codigo = ").append(SessaoAplicacaoImp.getInstance().getUsuario().getProfissional().getCodigo());
                                where.insert(0, "exists(").append(")");
                                hql.addToWhereWhithAnd(where.toString());
                            }
                        }
                    });
                } else {
                    return null;
                }
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_NUMERO, this.numeroAutoInfracao));
        parameters.add(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_DENUNCIADO, QueryCustom.QueryCustomParameter.LIKE, this.denunciado));
        parameters.add(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_SITUACAO, this.situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_DATA_INFRACAO, this.periodo));

        if (vigilanciaEndereco != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_VIGILANCIA_ENDERECO, this.vigilanciaEndereco));
        }
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAutoInfracaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaAutoInfracao");
    }
}
