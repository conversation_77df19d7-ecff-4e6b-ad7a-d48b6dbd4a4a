package br.com.celk.view.consorcio.pagamentoguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoPagamentosDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;

import java.util.Arrays;


/**
 *
 * <AUTHOR>
 * Programa - 150
 */
@Private

public class RelatorioDetalhamentoPagamentosPage extends RelatorioPage<RelatorioDetalhamentoPagamentosDTOParam> {

    @Override
    public void init(Form form) {
        form.add(new InputField("codigoPagamento"));
        form.add(new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaEmpresa("consorciado"));
        form.add(new AutoCompleteConsultaTipoConta("tipoConta"));
        form.add(new PnlChoicePeriod("periodo"));
        form.add(new CheckBox("agruparConsorciado"));
        form.add(DropDownUtil.getNaoSimLongDropDown("exibirProcedimentos"));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioDetalhamentoPagamentosDTOParam.Ordenacao.values()));
    }

    @Override
    public Class<RelatorioDetalhamentoPagamentosDTOParam> getDTOParamClass() {
        return RelatorioDetalhamentoPagamentosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioDetalhamentoPagamentosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioDetalhamentoPagamentos(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhamentoPagamentos");
    }

}
