package br.com.celk.view.vigilancia.solicitacaoAgendamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaAtividadeVeterinaria;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaEspecieAnimal;
import br.com.celk.vigilancia.dto.SolicitacaoAgendamentoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 572
 */
@Private
public class CadastroSolicitacaoAgendamentoCVAPage extends CadastroPage<SolicitacaoAgendamentoDTO> {

    private AutoCompleteConsultaAtividadeVeterinaria autoCompleteConsultaAtividadeVeterinaria;
    private Table tblAnimais;
    private AbstractAjaxButton btnAdicionar;
    private AutoCompleteConsultaEspecieAnimal autoCompleteConsultaEspecieAnimal;
    private DropDown dropDownSexo;
    private InputField txtQuantidade;
    private boolean isInformarAnimais;

    private String profissional;
    private String empresa;
    private String dataAgendamento;

    private String motivoCancelamento;
    private String dataCancelamento;
    private String usuarioCancelamento;

    private WebMarkupContainer containerDadosAgendamento;
    private WebMarkupContainer containerDadosCancelamento;
    private WebMarkupContainer containerOcorrencias;

    private Table tblOcorrencias;

    public CadastroSolicitacaoAgendamentoCVAPage(SolicitacaoAgendamentoDTO object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroSolicitacaoAgendamentoCVAPage(SolicitacaoAgendamentoDTO object) {
        this(object, false);
    }

    public CadastroSolicitacaoAgendamentoCVAPage() {
        this(null);
    }

    @Override
    public void init(Form<SolicitacaoAgendamentoDTO> form) {
        SolicitacaoAgendamentoDTO proxy = on(SolicitacaoAgendamentoDTO.class);

        form.add(autoCompleteConsultaAtividadeVeterinaria = new AutoCompleteConsultaAtividadeVeterinaria(path(proxy.getSolicitacaoAgendamentoCVA().getAtividadeVeterinaria()), true));
        autoCompleteConsultaAtividadeVeterinaria.setLabel(new Model(bundle("tipoAtividade")));
        form.add(new RequiredDateChooser(path(proxy.getSolicitacaoAgendamentoCVA().getDataSolicitacao())).setLabel(new Model(bundle("dataSolicitacao"))));
        form.add(new RequiredInputField(path(proxy.getSolicitacaoAgendamentoCVA().getResponsavel())).setLabel(new Model(bundle("responsavel"))));
        form.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getSolicitacaoAgendamentoCVA().getFlagUrgente()), true, true).setLabel(new Model(bundle("urgencia"))));

        form.add(new TelefoneField(path(proxy.getSolicitacaoAgendamentoCVA().getTelefone1())));
        form.add(new TelefoneField(path(proxy.getSolicitacaoAgendamentoCVA().getTelefone2())));
        form.add(new TelefoneField(path(proxy.getSolicitacaoAgendamentoCVA().getTelefone3())));

        form.add(autoCompleteConsultaEspecieAnimal = new AutoCompleteConsultaEspecieAnimal(path(proxy.getSolicitacaoAgendamentoCVAAnimal().getEspecieAnimal())));
        form.add(dropDownSexo = DropDownUtil.getIEnumDropDown(path(proxy.getSolicitacaoAgendamentoCVAAnimal().getSexo()), RegistroAtividadeAnimal.Sexo.values()));
        form.add(txtQuantidade = new InputField(path(proxy.getSolicitacaoAgendamentoCVAAnimal().getQuantidade())));
        form.add(tblAnimais = new Table("tblAnimais", getColumns(), getCollectionProvider()));
        tblAnimais.populate();
        form.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        autoCompleteConsultaAtividadeVeterinaria.add(new ConsultaListener<AtividadeVeterinaria>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, AtividadeVeterinaria object) {
                isInformarAnimais = RepositoryComponentDefault.SIM_LONG.equals(object.getInformarAnimais());
                validaInformaAnimais(target);
            }

        });
        autoCompleteConsultaEspecieAnimal.setOutputMarkupId(true);
        dropDownSexo.setOutputMarkupId(true);
        txtQuantidade.setOutputMarkupId(true);
        tblAnimais.setOutputMarkupId(true);
        btnAdicionar.setOutputMarkupId(true);

        form.add(new InputArea(path(proxy.getSolicitacaoAgendamentoCVA().getObservacao())));

        carregarOcorrencias();

        form.add(containerOcorrencias = new WebMarkupContainer("containerOcorrencias"));
        containerOcorrencias.add(tblOcorrencias = new Table("tblOcorrencias", getColumnsOcorrencias(), getCollectionProviderOcorrencias()));
        tblOcorrencias.populate();

        if (form.getModel().getObject().getSolicitacaoAgendamentoCVA() == null) {
            form.getModel().getObject().setSolicitacaoAgendamentoCVA(new SolicitacaoAgendamentoCVA());
            form.getModel().getObject().getSolicitacaoAgendamentoCVA().setDataSolicitacao(DataUtil.getDataAtual());
        }
        if (getForm().getModel().getObject().getOcorrenciaList().isEmpty() && !isViewOnly()) {
            containerOcorrencias.setVisible(false);
        } else {
            containerOcorrencias.setVisible(true);
        }
        getForm().add(containerOcorrencias);
        if (getForm().getModelObject() != null && getForm().getModelObject().getSolicitacaoAgendamentoCVA() != null && getForm().getModelObject().getSolicitacaoAgendamentoCVA().getAtividadeVeterinaria() != null) {
            isInformarAnimais = RepositoryComponentDefault.SIM_LONG.equals(getForm().getModelObject().getSolicitacaoAgendamentoCVA().getAtividadeVeterinaria().getInformarAnimais());
            validaInformaAnimais();
        }

        form.add(containerDadosAgendamento = new WebMarkupContainer("containerDadosAgendamento"));

        containerDadosAgendamento.add(new DisabledInputField("profissional", new PropertyModel(this, "profissional")));
        containerDadosAgendamento.add(new DisabledInputField("empresa", new PropertyModel(this, "empresa")));
        containerDadosAgendamento.add(new DisabledInputField("dataAgendamento", new PropertyModel(this, "dataAgendamento")));

        if (isViewOnly() && !SolicitacaoAgendamentoCVA.Status.CANCELADO.value().equals(getForm().getModelObject().getSolicitacaoAgendamentoCVA().getStatus())) {
            containerDadosAgendamento.setVisible(true);
            if (((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getProfissionalAgendamento() != null) {
                profissional = ((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getProfissionalAgendamento().getNome();
            }
            if (((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getEmpresaAgendamento() != null) {
                empresa = ((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getEmpresaAgendamento().getDescricao();
            }
            if (((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getDataAgendamento() != null) {
                dataAgendamento = Data.formatarDataHora(((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getDataAgendamento());
            }
        } else {
            containerDadosAgendamento.setVisible(false);
        }

        form.add(containerDadosCancelamento = new WebMarkupContainer("containerDadosCancelamento"));

        containerDadosCancelamento.add(new DisabledInputField("motivoCancelamento", new PropertyModel(this, "motivoCancelamento")));
        containerDadosCancelamento.add(new DisabledInputField("dataCancelamento", new PropertyModel(this, "dataCancelamento")).setEnabled(false));
        containerDadosCancelamento.add(new DisabledInputField("usuarioCancelamento", new PropertyModel(this, "usuarioCancelamento")));

        if (isViewOnly() && SolicitacaoAgendamentoCVA.Status.CANCELADO.value().equals(getForm().getModelObject().getSolicitacaoAgendamentoCVA().getStatus())) {
            containerDadosCancelamento.setVisible(true);
            if (((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getMotivo() != null) {
                motivoCancelamento = ((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getMotivo();
            }
            if (((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getUsuarioCancelamento().getNome() != null) {
                usuarioCancelamento = ((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getUsuarioCancelamento().getNome();
            }
            if (((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getDataCancelamento() != null) {
                dataCancelamento = Data.formatarDataHora(((SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject()).getSolicitacaoAgendamentoCVA().getDataCancelamento());
            }
        } else {
            containerDadosCancelamento.setVisible(false);
        }
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        SolicitacaoAgendamentoCVAAnimal proxy = on(SolicitacaoAgendamentoCVAAnimal.class);
        columns.add(getCustomActionColumn());
        columns.add(createSortableColumn(bundle("especieAnimal"), proxy.getEspecieAnimal().getDescricao(), proxy.getEspecieAnimal().getDescricao()));
        columns.add(createSortableColumn(bundle("sexo"), proxy.getDescricaoSexo(), proxy.getDescricaoSexo()));
        columns.add(createSortableColumn(bundle("quantidade"), proxy.getQuantidade(), proxy.getQuantidade()));
        return columns;
    }

    private void validaInformaAnimais() {
        validaInformaAnimais(null);
    }

    private List<IColumn> getColumnsOcorrencias() {
        List<IColumn> columns = new ArrayList<IColumn>();
        SolicitacaoAgendamentoCVAOcorrencia proxy = on(SolicitacaoAgendamentoCVAOcorrencia.class);

        columns.add(new DateColumn(bundle("data"), path(proxy.getDataOcorrencia())).setPattern("dd/MM/yyyy - HH:mm"));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuarioCadastro().getNome()));
        columns.add(createColumn(bundle("ocorrencia"), proxy.getDescricaoOcorrencia()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderOcorrencias() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModel().getObject().getOcorrenciaList();
            }
        };
    }

    private void validaInformaAnimais(AjaxRequestTarget target) {
        autoCompleteConsultaEspecieAnimal.setEnabled(isInformarAnimais);
        dropDownSexo.setEnabled(isInformarAnimais);
        txtQuantidade.setEnabled(isInformarAnimais);
        tblAnimais.setEnabled(isInformarAnimais);
        btnAdicionar.setEnabled(isInformarAnimais);

        if (target != null) {
            autoCompleteConsultaEspecieAnimal.limpar(target);
            dropDownSexo.limpar(target);
            txtQuantidade.limpar(target);
            tblAnimais.limpar(target);
            target.add(btnAdicionar);

        }
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                SolicitacaoAgendamentoDTO dTO = (SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject();
                if (dTO == null || dTO.getLstSolicitacaoAgendamentoCVAAnimal() == null) {
                    return Collections.emptyList();
                }
                return dTO.getLstSolicitacaoAgendamentoCVAAnimal();
            }
        };
    }

    public void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        SolicitacaoAgendamentoDTO dTO = (SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject();
        SolicitacaoAgendamentoCVAAnimal sacvaa = new SolicitacaoAgendamentoCVAAnimal();
        sacvaa.setEspecieAnimal((EspecieAnimal) autoCompleteConsultaEspecieAnimal.getComponentValue());
        sacvaa.setSexo((Long) dropDownSexo.getComponentValue());
        sacvaa.setQuantidade((Long) txtQuantidade.getComponentValue());
        validarAdicionar(sacvaa);
        if (dTO.getLstSolicitacaoAgendamentoCVAAnimal().isEmpty()) {
            dTO.setLstSolicitacaoAgendamentoCVAAnimal(new ArrayList<SolicitacaoAgendamentoCVAAnimal>());
        }
        dTO.getLstSolicitacaoAgendamentoCVAAnimal().add(sacvaa);
        tblAnimais.populate(target);
        tblAnimais.update(target);
        target.add(getForm());
        autoCompleteConsultaEspecieAnimal.limpar(target);
        dropDownSexo.limpar(target);
        txtQuantidade.limpar(target);
    }

    public void validarAdicionar(SolicitacaoAgendamentoCVAAnimal sacvaa) throws ValidacaoException {
        if (sacvaa.getEspecieAnimal() == null || sacvaa.getSexo() == null || sacvaa.getQuantidade() == null) {
            throw new ValidacaoException(bundle("msgCampoEspecieSexoQuantidadeObrigatorio"));
        }
        if (sacvaa.getQuantidade() < 1) {
            throw new ValidacaoException(bundle("msgQuantidadeMenorUm"));
        }
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<SolicitacaoAgendamentoCVAAnimal>() {
            @Override
            public void customizeColumn(final SolicitacaoAgendamentoCVAAnimal rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        SolicitacaoAgendamentoDTO dTO = (SolicitacaoAgendamentoDTO) getForm().getDefaultModelObject();
                        for (int i = 0; i < dTO.getLstSolicitacaoAgendamentoCVAAnimal().size(); i++) {
                            SolicitacaoAgendamentoCVAAnimal item = dTO.getLstSolicitacaoAgendamentoCVAAnimal().get(i);
                            if (item == rowObject) {
                                dTO.getLstSolicitacaoAgendamentoCVAAnimal().remove(i);
                            }
                        }

                        tblAnimais.populate();
                        tblAnimais.update(target);
                    }
                });
            }
        };
    }

    private void carregarOcorrencias() {
        SolicitacaoAgendamentoCVA solicitacaoAgendamentoCVA = getForm().getModel().getObject().getSolicitacaoAgendamentoCVA();

        if (solicitacaoAgendamentoCVA != null) {
            SolicitacaoAgendamentoCVAOcorrencia proxy = on(SolicitacaoAgendamentoCVAOcorrencia.class);

            List<SolicitacaoAgendamentoCVAOcorrencia> list = LoadManager.getInstance(SolicitacaoAgendamentoCVAOcorrencia.class)
                    .addProperty(path(proxy.getDataOcorrencia()))
                    .addProperty(path(proxy.getUsuarioCadastro().getNome()))
                    .addProperty(path(proxy.getDescricaoOcorrencia()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamentoCVA()), solicitacaoAgendamentoCVA))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataOcorrencia()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(list)) {
                getForm().getModel().getObject().setOcorrenciaList(list);
            }
        }
    }

    @Override
    public Object salvar(SolicitacaoAgendamentoDTO object) throws DAOException, ValidacaoException {
        if (object.getSolicitacaoAgendamentoCVA().getDataSolicitacao().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(bundle("msg_data_solicitacao_maior_que_a_data"));
        }
        if (isInformarAnimais && object.getLstSolicitacaoAgendamentoCVAAnimal().isEmpty()) {
            throw new ValidacaoException(bundle("msg_informe_um_animal"));
        }
        if (object.getSolicitacaoAgendamentoCVA().getProfissionalAgendamento() != null && object.getSolicitacaoAgendamentoCVA().getEmpresa() != null) {
            LoadManager loadAgendamentos = LoadManager.getInstance(SolicitacaoAgendamentoCVA.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_PROFISSIONAL_AGENDAMENTO, object.getSolicitacaoAgendamentoCVA().getProfissionalAgendamento().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_STATUS, SolicitacaoAgendamentoCVA.Status.PENDENTE.value()));
            List<SolicitacaoAgendamentoCVA> list = loadAgendamentos.start().getList();
            for (SolicitacaoAgendamentoCVA cva : list) {
                if (cva.getProfissionalAgendamento() != null) {
                    if (object.getSolicitacaoAgendamentoCVA().getProfissionalAgendamento().getCodigo().equals(cva.getProfissionalAgendamento().getCodigo())) {
                        throw new ValidacaoException(bundle("msg_profissional_possui_uma_atividade_pendente"));
                    }
                }

            }
        }

        return BOFactoryWicket.getBO(VigilanciaFacade.class).salvarSalvarSolicitacaoAgendamento(object);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaAtividadeVeterinaria;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaSolicitacaoAgendamentoCVAPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroSolicitacaoAgendamentoCVA");
    }

    @Override
    public Class<SolicitacaoAgendamentoDTO> getReferenceClass() {
        return SolicitacaoAgendamentoDTO.class;
    }
}
