package br.com.celk.view.materiais.assistentesocial;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.financeiro.assistenteSocial.customize.CustomizeConsultaAssistenteSocial;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.AssistenteSocial;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 64
 */
@Private
public class ConsultaAssistenteSocialPage extends ConsultaPage<AssistenteSocial, List<BuilderQueryCustom.QueryParameter>> {

    private String nome;
    private Long codigo;

    public ConsultaAssistenteSocialPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<Long>("codigo"));
        form.add(new InputField<String>("nome"));
        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(AssistenteSocial.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(AssistenteSocial.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome"), VOUtils.montarPath(AssistenteSocial.PROP_NOME)));

        return columns;
    }

    private CustomColumn<AssistenteSocial> getCustomColumn() {
        return new CustomColumn<AssistenteSocial>() {

            @Override
            public Component getComponent(String componentId, final AssistenteSocial rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAssistenteSocialPage(rowObject,false,true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAssistenteSocialPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAssistenteSocial()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(AssistenteSocial.PROP_NOME, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AssistenteSocial.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, codigo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AssistenteSocial.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nome));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAssistenteSocialPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaAssistenteSocial");
    }
}
