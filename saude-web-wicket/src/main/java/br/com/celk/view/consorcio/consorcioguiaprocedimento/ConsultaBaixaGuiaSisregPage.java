package br.com.celk.view.consorcio.consorcioguiaprocedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsultaBaixaGuiaSisregDTOparam;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * Created by Jonas Américo on 31/10/2018.
 * Programa - 901
 */
@Private
public class ConsultaBaixaGuiaSisregPage extends BasePage {


    private Long situacaoSisreg;
    private DropDown dropDownSituacao;
    private Form<ConsultaBaixaGuiaSisregDTOparam> form;
    private MultiSelectionTable<ConsorcioGuiaProcedimentoItem> pageableTable;
    private List<ConsorcioGuiaProcedimentoItem> itensList;
    private Long countResults;
    private DlgConfirmacaoSimNao confirmarAção;
    private ConsultaBaixaGuiaSisregDTOparam consultaBaixaGuiaSisregDTOparam;

    public ConsultaBaixaGuiaSisregPage() {
        init();
    }

    public void init() {
        form = new Form<ConsultaBaixaGuiaSisregDTOparam>("form");
        form.setDefaultModel(new CompoundPropertyModel(consultaBaixaGuiaSisregDTOparam = new ConsultaBaixaGuiaSisregDTOparam()));

        form.add(new AutoCompleteConsultaEmpresa("empresaConsorcio").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaEmpresa("empresaPrestador"));
        form.add(new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus"));
        form.add(dropDownSituacao = DropDownUtil.getIEnumDropDown("situacaoSisreg", ConsorcioGuiaProcedimentoItem.SituacaoSisreg.values()));
        form.add(new InputField("numeroGuia"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(new DateChooser("dataSisreg"));
        form.add(new InputField("numeroChave"));
        form.add(new InputField("codigoSisreg"));
        situacaoSisreg = ConsorcioGuiaProcedimentoItem.SituacaoSisreg.PENDENTE.value();
        form.add(pageableTable = new MultiSelectionTable("tabelaItens", getColumns(), getPagerProviderInstance()));
        form.add(new AbstractAjaxButton("btnProcurar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (consultaBaixaGuiaSisregDTOparam.getEmpresaConsorcio() == null && consultaBaixaGuiaSisregDTOparam.getEmpresaPrestador() == null && consultaBaixaGuiaSisregDTOparam.getUsuarioCadsus() == null && consultaBaixaGuiaSisregDTOparam.getNumeroGuia() == null) {
                    throw new ValidacaoException(bundle("msgEscolherConsorciadoPrestadorPaciente"));
                }
                if (consultaBaixaGuiaSisregDTOparam.getNumeroGuia() == null && consultaBaixaGuiaSisregDTOparam.getPeriodo() == null || consultaBaixaGuiaSisregDTOparam.getNumeroGuia() == null && consultaBaixaGuiaSisregDTOparam.getPeriodo().getDataFinal() == null && consultaBaixaGuiaSisregDTOparam.getPeriodo().getDataInicial() == null) {
                    throw new ValidacaoException(bundle("obrigatorioPeloMenosUmFiltrodePeriodo2"));
                }
                consultaItens(target);

            }
        });

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                if (CollectionUtils.isNotNullEmpty(pageableTable.getSelectedObjects())) {
                    WindowUtil.addModal(target, this, confirmarAção = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this),
                            bundle("confirmarGuia2")) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            salvarItens(target, ConsorcioGuiaProcedimentoItem.SituacaoSisreg.CONFIRMADA.value());

                        }

                        @Override
                        public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        }
                    });
                    confirmarAção.show(target);
                } else {
                    throw new ValidacaoException("É necessário selecionar os itens para prosseguir.");
                }
            }
        }.setDefaultFormProcessing(false));
        form.add(new AbstractAjaxButton("btnNaoConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                if (CollectionUtils.isNotNullEmpty(pageableTable.getSelectedObjects())) {
                    WindowUtil.addModal(target, this, confirmarAção = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this),
                            bundle("naoConfirmarGuia2")) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            salvarItens(target, ConsorcioGuiaProcedimentoItem.SituacaoSisreg.NAO_CONFIRMADA.value());
                        }

                        @Override
                        public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        }
                    });
                    confirmarAção.show(target);
                } else {
                    throw new ValidacaoException("É necessário selecionar os itens para prosseguir.");
                }
            }
        }.setDefaultFormProcessing(false));
        add(form);

    }

    private void salvarItens(AjaxRequestTarget target, Long status) throws ValidacaoException, DAOException {
        dropDownSituacao.setComponentValue(status);
        List<ConsorcioGuiaProcedimentoItem> lista = pageableTable.getSelectedObjects();
        for (ConsorcioGuiaProcedimentoItem item : lista) {
            item.setSituacaoSisreg(status);
            BOFactoryWicket.save(item);
        }
        pageableTable.limpar(target);
    }

    private void consultaItens(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        form.getModelObject().setCount(true);
        countResults = 0L;
        countResults = BOFactoryWicket.getBO(ConsorcioFacade.class).countConsultaBaixaGuiasSisreg(form.getModelObject());

        if (countResults > 800) {
            itensList = new ArrayList<>();
            throw new ValidacaoException(bundle("msgNumeroMaximoRegistrosAtingidoFavorRedefinaFiltro", "800"));
        } else {
            form.getModelObject().setCount(false);
            itensList = BOFactoryWicket.getBO(ConsorcioFacade.class).ConsultaBaixaGuiasSisreg(form.getModelObject());
        }
        pageableTable.populate();
        target.add(pageableTable);
        pageableTable.clearSelection(target);
    }

    public List<IColumn> getColumns() {
        ConsorcioGuiaProcedimentoItem proxy = on(ConsorcioGuiaProcedimentoItem.class);
        List<IColumn> columns = new ArrayList<>();
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("numeroDaGuia"), proxy.getConsorcioGuiaProcedimento().getCodigo()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getConsorcioGuiaProcedimento().getNomePaciente()));
        columns.add(createSortableColumn(bundle("codigoSisreg"), proxy.getCodigoSisreg()));
        columns.add(createSortableColumn(bundle("chaveSisreg"), proxy.getConsorcioGuiaProcedimento().getNumeroChave()));
        columns.add(createSortableColumn(bundle("datasolicitacaoSisreg"), proxy.getDataSisreg()));
        columns.add(createSortableColumn(bundle("consorciado"), proxy.getConsorcioGuiaProcedimento().getSubConta().getConta().getConsorciado().getDescricao()));
        columns.add(createSortableColumn(bundle("prestador"), proxy.getConsorcioGuiaProcedimento().getConsorcioPrestador().getEmpresaPrestador().getDescricao()));
        columns.add(createSortableColumn(bundle("procedimento"), proxy.getConsorcioProcedimentoDescricaoVO()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacaoSisregDescricao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ConsorcioGuiaProcedimentoItem>() {
            @Override
            public void customizeColumn(ConsorcioGuiaProcedimentoItem rowObject) {
                addAction(ActionType.CURTIR, rowObject, new IModelAction<ConsorcioGuiaProcedimentoItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem) throws ValidacaoException, DAOException {
                        dropDownSituacao.setComponentValue(consorcioGuiaProcedimentoItem.getSituacaoSisreg());
                        consorcioGuiaProcedimentoItem.setSituacaoSisreg(ConsorcioGuiaProcedimentoItem.SituacaoSisreg.CONFIRMADA.value());
                        BOFactoryWicket.save(consorcioGuiaProcedimentoItem);
                        target.add(dropDownSituacao);
                        consultaItens(target);
                        pageableTable.populate(target);
                    }
                }).setTitleBundleKey("confirmarAGuia").setEnabled(ConsorcioGuiaProcedimentoItem.SituacaoSisreg.PENDENTE.value().equals(rowObject.getSituacaoSisreg()) || ConsorcioGuiaProcedimentoItem.SituacaoSisreg.NAO_CONFIRMADA.value().equals(rowObject.getSituacaoSisreg()));

                addAction(ActionType.DESCURTIR, rowObject, new IModelAction<ConsorcioGuiaProcedimentoItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem) throws ValidacaoException, DAOException {
                        dropDownSituacao.setComponentValue(consorcioGuiaProcedimentoItem.getSituacaoSisreg());
                        consorcioGuiaProcedimentoItem.setSituacaoSisreg(ConsorcioGuiaProcedimentoItem.SituacaoSisreg.NAO_CONFIRMADA.value());
                        BOFactoryWicket.save(consorcioGuiaProcedimentoItem);
                        target.add(dropDownSituacao);
                        consultaItens(target);
                        pageableTable.populate(target);
                    }
                }).setTitleBundleKey("naoConfirmarGuia").setEnabled(ConsorcioGuiaProcedimentoItem.SituacaoSisreg.PENDENTE.value().equals(rowObject.getSituacaoSisreg()) || ConsorcioGuiaProcedimentoItem.SituacaoSisreg.CONFIRMADA.value().equals(rowObject.getSituacaoSisreg()));

            }
        };
    }

    public ICollectionProvider getPagerProviderInstance() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itensList;
            }

        };


    }

    public String getTituloPrograma() {
        return BundleManager.getString("baixaGuiaSisreg");
    }

}