package br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetoArquitetonicoSanitario;

import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.base.RequerimentoHidrossanitarioBasePage;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoProjetoArquitetonicoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoArquitetonicoSanitario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * Programa - 1135
 */
public class RequerimentoProjetoArquitetonicoSanitarioPage extends RequerimentoHidrossanitarioBasePage {

    private RequerimentoProjetoArquitetonicoSanitario requerimento;
    private RequerimentoProjetoArquitetonicoDTO requerimentoDTO;


    public RequerimentoProjetoArquitetonicoSanitarioPage(
            TipoSolicitacao tipoSolicitacao,
            Class pageReturn
    ) {
        super(
                true,
                tipoSolicitacao,
                pageReturn
        );
    }

    public RequerimentoProjetoArquitetonicoSanitarioPage(
            RequerimentoVigilancia requerimentoVigilancia,
            boolean modoEdicao,
            Class pageReturn
    ) {
        super(
                requerimentoVigilancia,
                modoEdicao,
                pageReturn
        );
    }

    @Override
    public void setTituloAba(String titulo) {
        super.setTituloAba(bundle("projetoArquitetonicoSanitario"));
    }

    @Override
    public String getTituloPrograma() {
        return bundle("projetoArquitetonicoSanitario");
    }

    @Override
    public void criarEntidadeRequerimento() {
        requerimento = RequerimentoProjetoArquitetonicoSanitario.carregarRequerimentoProjetoArquitetonico(getRequerimentoVigilancia());
        requerimento.setRequerimentoVigilancia(getRequerimentoVigilancia());
    }

    @Override
    public void carregarLists() {
        if (isEdicao() || isConsulta()) {
            carregarAnexos();
            carregarPranchasProjetoArquitetonico();
            carregarMemorial();
            carregarListTipoProjeto(getRequerimentoVigilancia(), false);
        }
    }

    @Override
    public void criarForm() {
        setForm(new Form("form", new CompoundPropertyModel(requerimento)));
    }

    @Override
    public void criarComponentesRequerimento() {
        criarNumeroProtocolo(hasEntity());
        criarDadosProprietario(hasEntity(), true);
        criarTipoProjeto(hasEntity(), TipoProjetoVigilancia.Tipo.PROJETO_ARQUITETONICO_SANITARIO);
        criarDadosProjeto();
        criarAutorProjeto();
        criarPanelDadosSolicitante();
        criarPanelFiscais();
        criarPanelDadosComuns(bundle("analistasFiscais"));
        criarPanelAnexos();
        criarPanelAnexoPranchas(GerenciadorArquivo.OrigemArquivo.PRANCHA_PROJETO_ARQUITETONICO, bundle("anexarPrancha"));
        criarPanelAnexoMemorial(GerenciadorArquivo.OrigemArquivo.MEMORIAL_PROJETO_ARQUITETONICO);

        criarPanelOcorrencias();
    }

    @Override
    public void carregarRegrasComponentes() {
        enableCamposDadosProprietario(null, false);
        configurarEstabelecimento();
        tipoProjetoRequired(null);
        dadosProjetoRequired(null);
    }

    @Override
    public void validarRequerimento(Object requerimentoDTO, AjaxRequestTarget target) throws ValidacaoException, DAOException {
        RequerimentoProjetoArquitetonicoDTO dto = (RequerimentoProjetoArquitetonicoDTO) requerimentoDTO;

        validarRequerimentoVigilancia(dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia());

        if (CollectionUtils.isEmpty(dto.getListAnexosPrancha())) {
            throw new ValidacaoException(bundle("msgAnexarMinimoUmaPranchas"));
        }

        if (CollectionUtils.isEmpty(dto.getListAnexosMemorial())) {
            throw new ValidacaoException(bundle("msgAnexarMinimoUmMemorial"));
        }

        if (CollectionUtils.isEmpty(dto.getListTipoProjetos())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_adicione_pelo_menos_um_tipo_projeto"));
        }

    }

    @Override
    public RequerimentoVigilancia salvarRequerimento(Object requerimentoDTO, AjaxRequestTarget target) throws ValidacaoException, DAOException {
        RequerimentoProjetoArquitetonicoDTO dto = (RequerimentoProjetoArquitetonicoDTO) requerimentoDTO;

        Estabelecimento estabelecimento = salvarEstabelecimento(
                dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().getTipoRequerente(),
                dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().getEstabelecimento()
        );
        if (estabelecimento != null) {
            dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().setEstabelecimento(estabelecimento);
        }

        VigilanciaPessoa vigilanciaPessoa = salvarPessoaVigilancia(
                dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().getTipoRequerente(),
                dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().getVigilanciaPessoa()
        );
        if (vigilanciaPessoa != null) {
            dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().setVigilanciaPessoa(vigilanciaPessoa);
        }

        RequerimentoVigilancia rvSaved = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoProjetoArquitetonico(dto);

        return rvSaved;
    }

    @Override
    public Object getRequerimentoDTO() {
        if (requerimentoDTO == null) {
            requerimentoDTO = new RequerimentoProjetoArquitetonicoDTO();
            requerimentoDTO.setRequerimentoProjetoArquitetonicoSanitario(requerimento);
            requerimentoDTO.getRequerimentoProjetoArquitetonicoSanitario().setRequerimentoVigilancia(getBaseDTO().getRequerimentoVigilancia());
            requerimentoDTO.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().setTipoSolicitacao(getTipoSolicitacao());

            requerimentoDTO.setListTipoProjetos(getBaseDTO().getListTipoProjetos());
            requerimentoDTO.setListTiposProjetosExcluidos(getBaseDTO().getListTiposProjetosExcluidos());

            requerimentoDTO.setListAnexos(getBaseDTO().getListAnexos());
            requerimentoDTO.setListAnexosExcluidos(getBaseDTO().getListAnexosExcluidos());

            requerimentoDTO.setListAnexosPrancha(getBaseDTO().getListAnexosPrancha());
            requerimentoDTO.setListAnexosPranchaExcluidos(getBaseDTO().getListAnexosPranchaExcluidos());

            requerimentoDTO.setListAnexosMemorial(getBaseDTO().getListAnexosMemorial());
            requerimentoDTO.setListAnexosMemorialExcluidos(getBaseDTO().getListAnexosMemorialExcluidos());

            if (getPnlDadosComum() != null && getPnlDadosComum().getParam() != null) {
                requerimentoDTO.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().setObservacaoRequerimento(
                        (String) getPnlDadosComum().getObservacaoRequerimento().getComponentValue()
                );

                requerimentoDTO.setListSetorVigilancia(getPnlDadosComum().getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
                requerimentoDTO.setListSetorVigilanciaExcluidos(getPnlDadosComum().getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
                requerimentoDTO.setListFiscais(getPnlDadosComum().getParam().getRequerimentoVigilanciaFiscalList());
                requerimentoDTO.setListFiscaisExcluidos(getPnlDadosComum().getParam().getRequerimentoVigilanciaFiscalListExcluir());
            }
        }

        return requerimentoDTO;
    }

    public void setRequerimentoDTO(RequerimentoProjetoArquitetonicoDTO requerimentoDTO) {
        this.requerimentoDTO = requerimentoDTO;
    }

    private boolean hasEntity() {
        return (requerimento != null &&
                requerimento.getCodigo() != null &&
                requerimento.getCodigo() > 0L);
    }

    public RequerimentoProjetoArquitetonicoSanitario getRequerimento() {
        return requerimento;
    }

    public void setRequerimento(RequerimentoProjetoArquitetonicoSanitario requerimento) {
        this.requerimento = requerimento;
    }
}
