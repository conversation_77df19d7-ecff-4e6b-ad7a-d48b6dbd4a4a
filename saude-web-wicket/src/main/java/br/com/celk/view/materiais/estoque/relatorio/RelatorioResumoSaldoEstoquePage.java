package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.RequiredMesAnoField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.estoque.relatorio.dialog.DlgConfirmacaoResumoSaldo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioResumoSaldoEstoqueDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioResumoSaldoEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.ResumoSaldoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import static ch.lambdaj.Lambda.on;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 366
 */
@Private
public class RelatorioResumoSaldoEstoquePage extends RelatorioPage<RelatorioResumoSaldoEstoqueDTOParam> {

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DlgConfirmacaoResumoSaldo dlgConfirmacaoResumoSaldo;
    private ResumoSaldoEstoque resumo;

    @Override
    public void init(Form form) {
        RelatorioResumoSaldoEstoqueDTOParam proxy = on(RelatorioResumoSaldoEstoqueDTOParam.class);

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        form.add(getDropDownGrupo(path(proxy.getGrupoProdutoSubGrupo())));
        form.add(getDropDownSubGrupo(path(proxy.getSubGrupo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioResumoSaldoEstoqueDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoResumo()), RelatorioResumoSaldoEstoqueDTOParam.TipoResumo.values()));
        form.add(getDropDownTipoArquivo(path(proxy.getTipoArquivo())));
        form.add(new RequiredMesAnoField(path(proxy.getPeriodo())));
    }

    public DropDown getDropDownTipoArquivo(String id) {
        return DropDownUtil.getTipoRelatorioDropDown(id);
    }

    private DropDown<GrupoProduto> getDropDownGrupo(String id) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>(id);

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();

                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo(String id) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>(id);

            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioResumoSaldoEstoqueDTOParam.class;
    }

    private boolean isAsync() {
        AsyncProcess async = LoadManager.getInstance(AsyncProcess.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AsyncProcess.STATUS_AGUARDANDO_PROCESSAMENTO, AsyncProcess.STATUS_PROCESSANDO)))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_TIPO, AsyncProcess.TIPO_RELATORIO))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_NOME_PROCESSO, "Resumo do Saldo do Estoque"))
                .start().getVO();
        return async == null;
    }

    @Override
    public void gerarRelatorio(AjaxRequestTarget target) throws IOException, ReportException, ValidacaoException {
        if (isAsync()) {
            super.gerarRelatorio(target);
        } else {
            warn(target, BundleManager.getString("msgRelatorioResumoSaldoEstoqueEmProcesso"));
        }
    }

    @Override
    public DataReport getDataReport(RelatorioResumoSaldoEstoqueDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioResumoSaldoEstoque(param);
    }

    private List<RelatorioResumoSaldoEstoqueDTO> getDivergencias() {
        List<RelatorioResumoSaldoEstoqueDTO> lstDivergente = null;
        try {
            lstDivergente = BOFactoryWicket.getBO(EstoqueEmpresaFacade.class).verificarDivergenciaResumoSaldoEstoque(param.getPeriodo());
        } catch (DAOException ex) {
            Logger.getLogger(RelatorioResumoSaldoEstoquePage.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(RelatorioResumoSaldoEstoquePage.class.getName()).log(Level.SEVERE, null, ex);
        }
        return lstDivergente;
    }

    private boolean isRelatorioGerado(Date dataGeracao) {
        resumo = LoadManager.getInstance(ResumoSaldoEstoque.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ResumoSaldoEstoque.PROP_DATA_MOVIMENTACAO), dataGeracao))
                .setMaxResults(1)
                .start().getVO();
        return resumo != null;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoSaldoEstoque");
    }
}
