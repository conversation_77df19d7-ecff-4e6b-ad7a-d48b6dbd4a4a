package br.com.celk.view.vigilancia.requerimentovigilancia;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.ConsultaAutoIntimacaoPage;
import br.com.celk.view.vigilancia.rotinas.autoinfracao.ConsultaAutoInfracaoPage;
import br.com.celk.view.vigilancia.rotinas.automulta.ConsultaAutoMultaPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PendenciasAutoInfracaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PendenciasAutoIntimacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PendenciasAutoMultaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PendenciasVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.resource.CssResourceReference;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 23/05/17.
 * Programa - 762
 */
@Private
public class PendenciasFiscalPage extends BasePage {

    private static final String CSS_FILE = "PendenciasFiscalPage.css";

    private List<PendenciasVigilanciaDTO> pendenciasVigilanciaList = new ArrayList();
    private SelectionTable<PendenciasVigilanciaDTO> tblRequerimentos;

    private WebMarkupContainer containerIntimacao;
    private List<PendenciasAutoIntimacaoDTO> pendenciasAutosIntimacaoList = new ArrayList();
    private SelectionTable<PendenciasAutoIntimacaoDTO> tblAutosIntimacao;

    private WebMarkupContainer containerInfracao;
    private List<PendenciasAutoInfracaoDTO> pendenciasAutosInfracaoList = new ArrayList();
    private SelectionTable<PendenciasAutoInfracaoDTO> tblAutosInfracao;

    private WebMarkupContainer containerMulta;
    private List<PendenciasAutoMultaDTO> pendenciasAutosMultaList = new ArrayList();
    private SelectionTable<PendenciasAutoMultaDTO> tblAutosMulta;

    private WebMarkupContainer messageContainer;
    private AbstractAjaxLink messageLink;

    public PendenciasFiscalPage() {
        init();
    }

    private String coalesce(Long val, IEnum e) {
        if (val != null) {
            return val + " - " + e.descricao();
        } else {
            return "Null";
        }
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(tblRequerimentos = new SelectionTable("tblRequerimentos", getColumnsRequerimentos(), getCollectionProviderRequerimentos()));
        tblRequerimentos.addSelectionAction(new ISelectionAction<PendenciasVigilanciaDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, PendenciasVigilanciaDTO object) {
                RequerimentoVigilancia rv = object.getRequerimentoVigilancia();
                setResponsePage(new ConsultaRequerimentoVigilanciaFiscalPage(rv.getSituacao(), rv.getSituacaoOcorrencia(), rv.getSituacaoAprovacao(), rv.getSituacaoAnaliseProjetos(), PendenciasFiscalPage.class));
            }
        });
        tblRequerimentos.populate();


        containerIntimacao = new WebMarkupContainer("containerIntimacao") {
            @Override
            public boolean isVisible() {
                return CollectionUtils.isNotNullEmpty(pendenciasAutosIntimacaoList) &&
                        new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaAutoIntimacaoPage.class.getName());
            }
        };
        containerIntimacao.setOutputMarkupPlaceholderTag(true);

        { // Mensagem Autos Pendentes
            containerIntimacao.add(messageContainer = new WebMarkupContainer("messageContainer"));
            messageContainer.setVisible(false);
            messageContainer.setOutputMarkupId(true);
            messageContainer.add(messageLink = new AbstractAjaxLink("messageLink") {
                @Override
                public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    PageParameters parameters = new PageParameters();
                    parameters.add("autosVencendo", true);
                    parameters.add("returnClass", PendenciasFiscalPage.class.getName());
                    ConsultaAutoIntimacaoPage consultaAutoIntimacaoPage = new ConsultaAutoIntimacaoPage(parameters);
                    setResponsePage(consultaAutoIntimacaoPage);
                }
            });
            messageLink.add(new Label("messageLabel", bundle("msgAtencaoAlgunsAutosVencendoCliqueAquiVisualizar")));
        }


        containerIntimacao.add(tblAutosIntimacao = new SelectionTable("tblAutosIntimacao", getColumnsIntimacao(), getCollectionProviderIntimacao()));
        tblAutosIntimacao.addSelectionAction(new ISelectionAction<PendenciasAutoIntimacaoDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, PendenciasAutoIntimacaoDTO object) {
                PageParameters parameters = new PageParameters();
                parameters.add("situacao", object.getAutoIntimacao().getSituacao());
                parameters.add("returnClass", PendenciasFiscalPage.class.getName());
                ConsultaAutoIntimacaoPage consultaAutoIntimacaoPage = new ConsultaAutoIntimacaoPage(parameters);
                setResponsePage(consultaAutoIntimacaoPage);
            }
        });
        tblAutosIntimacao.populate();

        containerInfracao = new WebMarkupContainer("containerInfracao") {
            @Override
            public boolean isVisible() {
                return CollectionUtils.isNotNullEmpty(pendenciasAutosInfracaoList)
                        && new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaAutoInfracaoPage.class.getName());

            }
        };
        containerInfracao.setOutputMarkupPlaceholderTag(true);
        containerInfracao.add(tblAutosInfracao = new SelectionTable("tblAutosInfracao", getColumnsInfracao(), getCollectionProviderInfracao()));
        tblAutosInfracao.addSelectionAction(new ISelectionAction<PendenciasAutoInfracaoDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, PendenciasAutoInfracaoDTO object) {
                PageParameters parameters = new PageParameters();
                parameters.add("situacao", object.getAutoInfracao().getSituacao());
                parameters.add("returnClass", PendenciasFiscalPage.class.getName());
                ConsultaAutoInfracaoPage consultaAutoInfracaoPage = new ConsultaAutoInfracaoPage(parameters);
                setResponsePage(consultaAutoInfracaoPage);
            }
        });
        tblAutosInfracao.populate();

        containerMulta = new WebMarkupContainer("containerMulta") {
            @Override
            public boolean isVisible() {
                return CollectionUtils.isNotNullEmpty(pendenciasAutosMultaList)
                        && new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaAutoMultaPage.class.getName());

            }
        };
        containerMulta.setOutputMarkupPlaceholderTag(true);
        containerMulta.add(tblAutosMulta = new SelectionTable("tblAutosMulta", getColumnsMulta(), getCollectionProviderMulta()));
        tblAutosMulta.addSelectionAction(new ISelectionAction<PendenciasAutoMultaDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, PendenciasAutoMultaDTO object) {
                PageParameters parameters = new PageParameters();
                parameters.add("situacao", object.getAutoMulta().getSituacao());
                parameters.add("returnClass", PendenciasFiscalPage.class.getName());
                ConsultaAutoMultaPage consultaAutoMultaPage = new ConsultaAutoMultaPage(parameters);
                setResponsePage(consultaAutoMultaPage);
            }
        });
        tblAutosMulta.populate();

        form.add(new AbstractAjaxButton("btnAtualizar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                carregarPendencias(target);
                carregarIntimacoes(target);
                carregarInfracoes(target);
                carregarMultas(target);
            }
        });

        form.add(new AbstractAjaxButton("btnConsultaRequerimentos") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaRequerimentoVigilanciaFiscalPage(PendenciasFiscalPage.class));
            }
        });

        form.add(containerIntimacao);
        form.add(containerInfracao);
        form.add(containerMulta);
        add(form);

        try {
            carregarPendencias(null);
            carregarInfracoes(null);
            carregarIntimacoes(null);
            carregarMultas(null);
        } catch (DAOException | ValidacaoException e) {
            Loggable.log.error(e);
        }
    }

    private List<IColumn> getColumnsRequerimentos() {
        List<IColumn> columns = new ArrayList<IColumn>();
        PendenciasVigilanciaDTO proxy = on(PendenciasVigilanciaDTO.class);

        columns.add(createColumn(BundleManager.getString("pendencia"), proxy.getRequerimentoVigilancia().getDescricaoSituacao()));
        columns.add(createColumn(BundleManager.getString("quantidade"), proxy.getQuantidade()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderRequerimentos() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return pendenciasVigilanciaList;
            }
        };
    }

    private List<IColumn> getColumnsIntimacao() {
        List<IColumn> columns = new ArrayList<IColumn>();
        PendenciasAutoIntimacaoDTO proxy = on(PendenciasAutoIntimacaoDTO.class);

        columns.add(createColumn(BundleManager.getString("situacao"), proxy.getAutoIntimacao().getStatusDescricao()));
        columns.add(createColumn(BundleManager.getString("quantidade"), proxy.getQuantidade()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderIntimacao() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return pendenciasAutosIntimacaoList;
            }
        };
    }

    private List<IColumn> getColumnsInfracao() {
        List<IColumn> columns = new ArrayList<IColumn>();
        PendenciasAutoInfracaoDTO proxy = on(PendenciasAutoInfracaoDTO.class);

        columns.add(createColumn(BundleManager.getString("situacao"), proxy.getAutoInfracao().getStatusDescricao()));
        columns.add(createColumn(BundleManager.getString("quantidade"), proxy.getQuantidade()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderInfracao() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return pendenciasAutosInfracaoList;
            }
        };
    }

    private List<IColumn> getColumnsMulta() {
        List<IColumn> columns = new ArrayList<IColumn>();
        PendenciasAutoMultaDTO proxy = on(PendenciasAutoMultaDTO.class);

        columns.add(createColumn(BundleManager.getString("situacao"), proxy.getAutoMulta().getSituacaoFormatado()));
        columns.add(createColumn(BundleManager.getString("quantidade"), proxy.getQuantidade()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderMulta() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return pendenciasAutosMultaList;
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("pendenciasFiscais");
    }

    private void carregarPendencias(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        pendenciasVigilanciaList = BOFactoryWicket.getBO(VigilanciaFacade.class).consultarPendenciasFiscaisVigilancia();

        if (target != null) {
            tblRequerimentos.update(target);
        }
    }

    private void carregarIntimacoes(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        pendenciasAutosIntimacaoList = new ArrayList<>();
        List<AutoIntimacao> autos = new ArrayList<>();
        autos.addAll(BOFactoryWicket.getBO(VigilanciaFacade.class).consultarPendenciasAutosIntimacaoPendente());
        autos.addAll(BOFactoryWicket.getBO(VigilanciaFacade.class).consultarPendenciasAutosIntimacaoAguardandoRecebimento());
        if (CollectionUtils.isNotNullEmpty(autos)) {
            disparaAvisoVencimentoAutos(target, autos);
            Group<AutoIntimacao> situacaoGroup = Lambda.group(autos, by(on(AutoIntimacao.class).getSituacao()));
            for (Group<AutoIntimacao> group : situacaoGroup.subgroups()) {
                PendenciasAutoIntimacaoDTO dto = new PendenciasAutoIntimacaoDTO();
                dto.setAutoIntimacao(group.first());
                dto.setQuantidade(new Long(group.findAll().size()));
                pendenciasAutosIntimacaoList.add(dto);
            }

            if (CollectionUtils.isNotNullEmpty(pendenciasAutosIntimacaoList)) {
                pendenciasAutosIntimacaoList = Lambda.sort(pendenciasAutosIntimacaoList, Lambda.on(PendenciasAutoIntimacaoDTO.class).getAutoIntimacao().getStatusDescricao());
            }
        }
        if (target != null) {
            tblAutosIntimacao.update(target);
            target.add(containerIntimacao);
        }
    }

    private void carregarInfracoes(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        pendenciasAutosInfracaoList = new ArrayList<>();
        List<AutoInfracao> autos = new ArrayList<>();
        autos.addAll(BOFactoryWicket.getBO(VigilanciaFacade.class).consultarPendenciasAutosInfracao());

        if (CollectionUtils.isNotNullEmpty(autos)) {
            Group<AutoInfracao> situacaoGroup = Lambda.group(autos, by(on(AutoInfracao.class).getSituacao()));
            for (Group<AutoInfracao> group : situacaoGroup.subgroups()) {
                PendenciasAutoInfracaoDTO dto = new PendenciasAutoInfracaoDTO();
                dto.setAutoInfracao(group.first());
                dto.setQuantidade(new Long(group.findAll().size()));
                pendenciasAutosInfracaoList.add(dto);
            }

            if (CollectionUtils.isNotNullEmpty(pendenciasAutosInfracaoList)) {
                pendenciasAutosInfracaoList = Lambda.sort(pendenciasAutosInfracaoList, Lambda.on(PendenciasAutoInfracaoDTO.class).getAutoInfracao().getStatusDescricao());
            }
        }
        if (target != null) {
            tblAutosInfracao.update(target);
            target.add(containerInfracao);
        }
    }

    private void carregarMultas(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        pendenciasAutosMultaList = new ArrayList<>();
        List<AutoMulta> autos = new ArrayList<>();
        autos.addAll(BOFactoryWicket.getBO(VigilanciaFacade.class).consultarPendenciasAutosMulta());

        if (CollectionUtils.isNotNullEmpty(autos)) {
            Group<AutoMulta> situacaoGroup = Lambda.group(autos, by(on(AutoMulta.class).getSituacao()));
            for (Group<AutoMulta> group : situacaoGroup.subgroups()) {
                PendenciasAutoMultaDTO dto = new PendenciasAutoMultaDTO();
                dto.setAutoMulta(group.first());
                dto.setQuantidade(new Long(group.findAll().size()));
                pendenciasAutosMultaList.add(dto);
            }

            if (CollectionUtils.isNotNullEmpty(pendenciasAutosMultaList)) {
                pendenciasAutosMultaList = Lambda.sort(pendenciasAutosMultaList, Lambda.on(PendenciasAutoMultaDTO.class).getAutoMulta().getSituacaoFormatado());
            }
        }
        if (target != null) {
            tblAutosMulta.update(target);
            target.add(containerMulta);
        }
    }

    private void disparaAvisoVencimentoAutos(AjaxRequestTarget target, List<AutoIntimacao> autosPendentes) {
        Date hoje = DataUtil.getDataAtual();
        Date amanha = Data.addDias(hoje, 1);
        DatePeriod period = Data.adjustRangeHour(hoje, amanha);

        boolean existsAutosVencendo = LoadManager.getInstance(AutoIntimacaoExigencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacaoExigencia.PROP_DATA_CUMPRIMENTO_PRAZO, QueryCustom.QueryCustomParameter.MAIOR_IGUAL, period.getDataInicial()))
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacaoExigencia.PROP_DATA_CUMPRIMENTO_PRAZO, QueryCustom.QueryCustomParameter.MENOR_IGUAL, period.getDataFinal()))
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacaoExigencia.PROP_AUTO_INTIMACAO, QueryCustom.QueryCustomParameter.IN, autosPendentes))
                .exists();

        if (existsAutosVencendo) {
            messageContainer.setVisible(true);
            if (target != null) {
                target.add(messageContainer);
            }
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(PendenciasFiscalPage.class, CSS_FILE)));
    }
}