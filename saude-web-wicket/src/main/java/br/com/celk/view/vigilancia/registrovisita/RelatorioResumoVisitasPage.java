package br.com.celk.view.vigilancia.registrovisita;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.motivovisita.AutoCompleteConsultaMotivoVista;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryRelatorioResumoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 484
 */
@Private
public class RelatorioResumoVisitasPage extends RelatorioPage<QueryRelatorioResumoDTOParam> {

    @Override
    public void init(Form<QueryRelatorioResumoDTOParam> form) {
        QueryRelatorioResumoDTOParam proxy = on(QueryRelatorioResumoDTOParam.class);

        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new AutoCompleteConsultaMotivoVista(path(proxy.getMotivoVisita())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), QueryRelatorioResumoDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoResumo()), QueryRelatorioResumoDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getOrdenacao()), QueryRelatorioResumoDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoOrdenacao()), QueryRelatorioResumoDTOParam.TipoOrdenacao.values()));
        form.add(new PnlChoicePeriod(path(proxy.getPeriodo())));
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioResumoMotivo");
    }

    @Override
    public Class<QueryRelatorioResumoDTOParam> getDTOParamClass() {
        return QueryRelatorioResumoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(QueryRelatorioResumoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioResumoVisita(param);
    }

}
