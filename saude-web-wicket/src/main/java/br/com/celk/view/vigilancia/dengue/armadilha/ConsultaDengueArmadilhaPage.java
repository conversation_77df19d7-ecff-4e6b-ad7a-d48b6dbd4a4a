package br.com.celk.view.vigilancia.dengue.armadilha;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.dengue.armadilha.dialog.DlgSelectColetaArmadilha;
import br.com.celk.view.vigilancia.dengue.autocomplete.AutoCompleteConsultaDengueLocalidade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilha;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilhaVisita;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 * Programa - 648
 */
public class ConsultaDengueArmadilhaPage extends ConsultaPage<DengueArmadilha, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private DengueArmadilha dengueArmadilha;
    private DengueLocalidade dengueLocalidade;
    private DatePeriod periodo;
    private Long situacao = (Long) DengueArmadilha.Situacao.ATIVO.value();
    private DropDown<Long> dropDownSituacao;
    private DlgSelectColetaArmadilha dlgSelectColetaArmadilha;

//    private InputField txtDescricao;
//    private Tooltip  tooltip;

    public ConsultaDengueArmadilhaPage() {
    }

    public ConsultaDengueArmadilhaPage(DengueArmadilha dengueArmadilha) {
        this.dengueArmadilha = dengueArmadilha;
        situacao = null;
        setProcurarAoAbrir(true);
        getPageableTable().populate();
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

//        form.add(txtDescricao = new InputField("descricao"));
//        form.add(tooltip = new Tooltip("tlpDescricao"));
//        tooltip.setText("atendimento");
//        tooltip.setHtmlContent("<html>Teste<br/><b><a href=\"#\" wicket:id=\"jexlLink\" target=\"_blank\">Link</a><br/>teste</b></html>");
//        tooltip.setTrigger(Tooltip.Trigger.FOCUS);

        form.add(new InputField("descricao"));
        form.add(new AutoCompleteConsultaDengueLocalidade("dengueLocalidade"));
        form.add(getDropDownSituacao("situacao"));
        form.add(new PnlDatePeriod("periodo"));

        add(form);
    }

    public DropDown<Long> getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>(id);
            dropDownSituacao.addChoice(null, BundleManager.getString("ambos"));
            dropDownSituacao.addChoice((Long) DengueArmadilha.Situacao.ATIVO.value(), bundle("ativo"));
            dropDownSituacao.addChoice((Long) DengueArmadilha.Situacao.INATIVO.value(), bundle("inativo"));
        }
        return dropDownSituacao;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DengueArmadilha proxy = on(DengueArmadilha.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("dataInstalacao"), proxy.getDataInstalacao()));
        columns.add(createSortableColumn(BundleManager.getString("armadilha"), proxy.getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("endereco"), proxy.getComplementoLogradouro(), proxy.getEnderecoFormatadoComCidade()));
        columns.add(createSortableColumn(BundleManager.getString("localidade"), proxy.getDengueLocalidade().getLocalidade()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getSituacao(), proxy.getSituacaoFormatado()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<DengueArmadilha>() {
            @Override
            public void customizeColumn(final DengueArmadilha rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<DengueArmadilha>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueArmadilha modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroDengueArmadilhaPage(rowObject, false, true));
                    }
                });

                addAction(ActionType.ADICIONAR, rowObject, new IModelAction<DengueArmadilha>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueArmadilha modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new MonitoracaoDengueArmadilhaPage(modelObject));
                    }
                }).setTitleBundleKey("visita").setIcon(Icon.DOC_PLUS).setEnabled(DengueArmadilha.Situacao.ATIVO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DengueArmadilha>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueArmadilha modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesArmadilhaPage(modelObject));
                    }
                }).setTitleBundleKey("detalhes");

                addAction(ActionType.ADICIONAR, rowObject, new IModelAction<DengueArmadilha>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueArmadilha modelObject) throws ValidacaoException, DAOException {
                        if (dlgSelectColetaArmadilha == null) {
                            addModal(target, dlgSelectColetaArmadilha = new DlgSelectColetaArmadilha(newModalId()));
                        }
                        dlgSelectColetaArmadilha.show(target, modelObject.getCodigo());
                    }
                }).setTitleBundleKey("resultado")
                        .setIcon(Icon.LABORATORY)
                        .setVisible(DengueArmadilha.Situacao.ATIVO.value().equals(rowObject.getSituacao()) && existsColeta(rowObject.getCodigo()));

            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return DengueArmadilha.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(DengueArmadilha.class).getProperties(),
                        new HQLProperties(DengueLocalidade.class, DengueArmadilha.PROP_DENGUE_LOCALIDADE).getProperties(),
                        new HQLProperties(VigilanciaEndereco.class, DengueArmadilha.PROP_VIGILANCIA_ENDERECO).getProperties());
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(DengueArmadilha.PROP_DATA_CADASTRO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueArmadilha.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueArmadilha.PROP_SITUACAO), situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueArmadilha.PROP_DENGUE_LOCALIDADE), dengueLocalidade));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueArmadilha.PROP_DATA_INSTALACAO), periodo));

        if (dengueArmadilha != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueArmadilha.PROP_CODIGO), dengueArmadilha.getCodigo()));
        }
        dengueArmadilha = null;
        situacao = (Long) DengueArmadilha.Situacao.ATIVO.value();
        return parameters;
    }

    private boolean existsColeta(Long codigoArmadilha) {
        List<DengueArmadilhaVisita> list = LoadManager.getInstance(DengueArmadilhaVisita.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueArmadilhaVisita.PROP_DENGUE_ARMADILHA, DengueArmadilha.PROP_CODIGO), codigoArmadilha))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueArmadilhaVisita.PROP_FLAG_COLETA), RepositoryComponentDefault.SIM_LONG))
                .addProperties(new HQLProperties(DengueArmadilhaVisita.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(DengueArmadilhaVisita.PROP_DATA_VISITA, QueryCustom.QueryCustomSorter.DECRESCENTE))
                .start().getList();
        return !CollectionUtils.isAllEmpty(list);
    }

    @Override
    public Class getCadastroPage() {
        return CadastroDengueArmadilhaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDengueArmadilha");
    }
}
