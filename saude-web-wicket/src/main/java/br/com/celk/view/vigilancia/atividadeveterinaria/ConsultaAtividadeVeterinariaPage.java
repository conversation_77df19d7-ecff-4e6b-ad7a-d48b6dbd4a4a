package br.com.celk.view.vigilancia.atividadeveterinaria;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.AtividadeVeterinaria;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 * Programa - 562
 */
@Private
public class ConsultaAtividadeVeterinariaPage extends ConsultaPage<AtividadeVeterinaria, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long informarAnimais;
    private Long informarMicrochip;
    private Long atividadeTermoResponsabilidade;

    public ConsultaAtividadeVeterinariaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        form.add(DropDownUtil.getSimNaoLongDropDown("informarAnimais", true, false));
        form.add(DropDownUtil.getSimNaoLongDropDown("informarMicrochip", true, false));
        form.add(DropDownUtil.getSimNaoLongDropDown("atividadeTermoResponsabilidade", true, false));
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        AtividadeVeterinaria proxy = on(AtividadeVeterinaria.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(BundleManager.getString("descricao"), proxy.getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("informarAnimais"), proxy.getInformarAnimais(), proxy.getDescricaoInformarAnimais()));
        columns.add(createSortableColumn(BundleManager.getString("informarMicrochip"), proxy.getInformarMicrochip(), proxy.getDescricaoInformarMicrochip()));
        columns.add(createSortableColumn(BundleManager.getString("atividadesTermoResponsabilidade"), proxy.getAtividadeTermoResponsabilidade(), proxy.getDescricaoAtividadeTermoResponsabilidade()));

        return columns;
    }

    private CustomColumn<AtividadeVeterinaria> getCustomColumn() {
        return new CustomColumn<AtividadeVeterinaria>() {

            @Override
            public Component getComponent(String componentId, final AtividadeVeterinaria rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAtividadeVeterinariaPage(rowObject, false, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public boolean isConsultarVisible() {
                        return false;
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return AtividadeVeterinaria.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(AtividadeVeterinaria.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();
        parameters.add(new QueryCustom.QueryCustomParameter(AtividadeVeterinaria.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(AtividadeVeterinaria.PROP_INFORMAR_ANIMAIS, BuilderQueryCustom.QueryParameter.IGUAL, informarAnimais));
        parameters.add(new QueryCustom.QueryCustomParameter(AtividadeVeterinaria.PROP_INFORMAR_MICROCHIP, BuilderQueryCustom.QueryParameter.IGUAL, informarMicrochip));
        parameters.add(new QueryCustom.QueryCustomParameter(AtividadeVeterinaria.PROP_ATIVIDADE_TERMO_RESPONSABILIDADE, BuilderQueryCustom.QueryParameter.IGUAL, atividadeTermoResponsabilidade));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAtividadeVeterinariaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaAtividadeVeterinaria");
    }
}
