package br.com.celk.view.materiais.estoque.inventario.lancamentoinventario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.parametros.ParametrosMateriaisUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.recebimento.notafiscalentrada.CadastroImportacaoXmlNotaFiscalPageStep2;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 388
 */
@Private
public class ConsultaLancamentoInventarioPage extends ConsultaPage<ControleInventario, List<BuilderQueryCustom.QueryParameter>> {

    private Produto produto;
    private Deposito deposito;
    private Localizacao localizacao;
    private String utilizaLocalizacaoEstoque;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private Inventario inventario;
    private DropDown<Inventario> dropDownInventario;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaDeposito("deposito"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(false);
        form.add(new AutoCompleteConsultaLocalizacao("localizacao"));
        form.add(getDropDownInventario());

        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ControleInventario proxy = on(ControleInventario.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("sequencia"), proxy.getRoSequencia()));
        columns.add(createSortableColumn(bundle("produto"), proxy.getProduto().getDescricao(), proxy.getProduto().getDescricaoFormatado()));
        columns.add(createSortableColumn(bundle("estoque"), proxy.getDeposito().getDescricao()));
        columns.add(createSortableColumn(bundle("grupoEstoque"), proxy.getGrupoEstoque()));
        columns.add(createSortableColumn(bundle("inventario"), proxy.getInventario().getDescricaoInventario()));
        if(RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())){
            columns.add(createSortableColumn(bundle("localizacaoEstrutura"), proxy.getLocalizacaoEstrutura().getMascara()));
        }
        columns.add(createSortableColumn(bundle("quantidade"), proxy.getQuantidade()));
        columns.add(new DoubleColumn(bundle("precoMedio"), path(proxy.getPrecoMedio())).setCasasDecimais(4));
        columns.add(createColumn(bundle("total"), proxy.getPrecoTotal()));
//        columns.add(createSortableColumn(bundle("localizacao"), proxy.getLocalizacao().getDescricao()));

        return columns;
    }

    private DropDown<Inventario> getDropDownInventario() {
        if (this.dropDownInventario == null) {
            this.dropDownInventario = new DropDown<Inventario>("inventario");
            populateDropDownInventario();
        }
        return this.dropDownInventario;
    }

    private void populateDropDownInventario() {
        List<Inventario> inventarios = LoadManager.getInstance(Inventario.class)
                .addProperties(new HQLProperties(Inventario.class).getProperties())
                .addProperties(new HQLProperties (Localizacao.class, VOUtils.montarPath(Inventario.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_LOCALIZACAO)).getProperties())
                .addProperties(new HQLProperties (LocalizacaoEstrutura.class, VOUtils.montarPath(Inventario.PROP_LOCALIZACAO_ESTRUTURA)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Inventario.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.IGUAL, Inventario.Situacao.ABERTO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(Inventario.PROP_EMPRESA, ((Empresa)SessaoAplicacaoImp.getInstance().getEmpresa())))
                .addSorter(new QueryCustom.QueryCustomSorter(Inventario.PROP_DESCRICAO_INVENTARIO, QueryCustom.QueryCustomSorter.CRESCENTE))
                .start().getList();

        dropDownInventario.removeAllChoices();

        dropDownInventario.addChoice(null, "");

        if (CollectionUtils.isNotNullEmpty(inventarios)) {
            for (Inventario inventario : inventarios) {
                dropDownInventario.addChoice(inventario, inventario.getDescricaoInventario());
            }
        }
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ControleInventario>() {
            @Override
            public void customizeColumn(ControleInventario rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ControleInventario>() {
                    @Override
                    public void action(AjaxRequestTarget target, ControleInventario modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesLancamentoInventarioPage(modelObject, RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())));
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ControleInventario>() {
                    @Override
                    public void action(AjaxRequestTarget target, ControleInventario modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesLancamentoInventarioPage(modelObject, true, RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ControleInventario>() {

                    @Override
                    public void action(AjaxRequestTarget target, ControleInventario modelObject) throws ValidacaoException, DAOException {
                        BOFactory.delete(modelObject);
                        getPageableTable().update(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return ControleInventario.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(ControleInventario.class).getProperties(),
                        new HQLProperties(LocalizacaoEstrutura.class, ControleInventario.PROP_LOCALIZACAO_ESTRUTURA).getProperties(),
                        new HQLProperties(Inventario.class, ControleInventario.PROP_INVENTARIO).getProperties(),
                        new String[]{
                                VOUtils.montarPath(ControleInventario.PROP_INVENTARIO, Inventario.PROP_CODIGO),
                                VOUtils.montarPath(ControleInventario.PROP_INVENTARIO, Inventario.PROP_DESCRICAO_INVENTARIO),
                        });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ControleInventario.PROP_RO_SEQUENCIA, false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_PRODUTO, produto));
        parameters.add(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_DEPOSITO, deposito));
        parameters.add(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_LOCALIZACAO, localizacao));
        parameters.add(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_INVENTARIO, inventario));
        parameters.add(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_USUARIO, SessaoAplicacaoImp.getInstance().getUsuario()));
        parameters.add(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_RO_EMPRESA, SessaoAplicacaoImp.getInstance().getEmpresa()));
        parameters.add(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_STATUS, ControleInventario.STATUS_ABERTO));

        return parameters;
    }

    public String getUtilizaLocalizacaoEstoque() {
        if (utilizaLocalizacaoEstoque == null) {
            try {
                utilizaLocalizacaoEstoque = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
            } catch (DAOException ex) {
                Logger.getLogger(ConsultaLancamentoInventarioPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return utilizaLocalizacaoEstoque;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroLancamentoInventarioPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaLancamentoInventario");
    }


}
