package br.com.celk.view.materiais.pedidotransferencia;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.pageable.SelectionPageableTable;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.pedidotransferencia.dialog.DlgConsultarPedidoBranet;
import br.com.celk.view.materiais.pedidotransferencia.dialog.DlgReceberPedidosBranet;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.IntegracaoBranetFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 * Programa - 7
 */
@Private
public class ConsultaRecebimentoPedidoPage extends BasePage {

    private SelectionPageableTable<PedidoTransferencia> pageableTable;

    private Long pedido;
    private AbstractAjaxButton btnReceberBranet;
    private AbstractAjaxButton btnConsultarPedidoBranet;
    private DlgReceberPedidosBranet dlgReceberPedidosBranet;
    private DlgConsultarPedidoBranet dlgConsultarPedidoBranet;
    private boolean habilitarIntegracaoBranetSolicitante = false;


    public ConsultaRecebimentoPedidoPage(PageParameters parameters) {
        super(parameters);
        init();
    }

    public ConsultaRecebimentoPedidoPage(IModel<?> model) {
        super(model);
        init();
    }

    public ConsultaRecebimentoPedidoPage() {
        init();
    }

    private void init(){
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(new InputField("pedido"));

        form.add(getPageableTable());

        form.add(new ProcurarButton<List<QueryParameter>>("btnProcurar", getPageableTable()) {

            @Override
            public List<QueryParameter> getParam() {
                return getParameters();
            }
        });

        form.add(new AbstractAjaxButton("btnReceber") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new RecebimentoPedidoPage(getPedidoSelecionado()));
            }
        });

        form.add(btnReceberBranet = new AbstractAjaxButton("btnReceberBranet") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                initDlgReceberPedidosBranet(target);
            }
        });

        form.add(btnConsultarPedidoBranet = new AbstractAjaxButton("btnConsultarPedidoBranet") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                initDlgConsultarPedidosBranet(target);
            }
        });

        try {
            boolean habilitarIntegracaoBranet = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("habilitarIntegracaoBranet"));

            btnReceberBranet.setVisible(habilitarIntegracaoBranet && isActionPermitted(Permissions.RECEBER));

            habilitarIntegracaoBranetSolicitante = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("habilitarIntegracaoBranetSolicitante"));
            btnConsultarPedidoBranet.setVisible(habilitarIntegracaoBranetSolicitante && isActionPermitted(Permissions.RECEBER));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        add(form);
    }

    private void initDlgReceberPedidosBranet(AjaxRequestTarget target){
        if (dlgReceberPedidosBranet == null) {
            addModal(target, dlgReceberPedidosBranet = new DlgReceberPedidosBranet(newModalId()) {});
        }
        dlgReceberPedidosBranet.showObject(target);
    }

    private void initDlgConsultarPedidosBranet(AjaxRequestTarget target){
        if (dlgConsultarPedidoBranet == null) {
            addModal(target, dlgConsultarPedidoBranet = new DlgConsultarPedidoBranet(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Long idSolicitacao, Serializable object) throws ValidacaoException, DAOException {
                    Long idCCSolicitante = ApplicationSession.get().getSession().getEmpresa().getIdIntegracaoTerceiro();
                    Long idUsuarioLogado = ApplicationSession.get().getSession().getUsuario().getCodigo();
                    BOFactory.getBO(IntegracaoBranetFacade.class).enviarRequisicaoRecebimento(idSolicitacao, idCCSolicitante, idUsuarioLogado, habilitarIntegracaoBranetSolicitante);
                    ConsultaRecebimentoPedidoPage.this.info(target, Bundle.getStringApplication("msg_pedido_concluido_com_sucesso"));
                }
            });
        }
        dlgConsultarPedidoBranet.show(target);
    }

    private SelectionPageableTable<PedidoTransferencia> getPageableTable() {
        if (this.pageableTable == null) {
            this.pageableTable = new SelectionPageableTable<PedidoTransferencia>("table", getColumns(), getPagerProvider(), 10);
        }

        return this.pageableTable;
    }

    private List<ISortableColumn<PedidoTransferencia>> getColumns(){
        List<ISortableColumn<PedidoTransferencia>> columns = new ArrayList<ISortableColumn<PedidoTransferencia>>();

        ColumnFactory columnFactory = new ColumnFactory(PedidoTransferencia.class);

        columns.add(columnFactory.createColumn(BundleManager.getString("pedido"), PedidoTransferencia.PROP_CODIGO));
        columns.add(columnFactory.createColumn(BundleManager.getString("data_pedido"), PedidoTransferencia.PROP_DATA_PEDIDO));
        columns.add(columnFactory.createColumn(BundleManager.getString("data_embarque"), PedidoTransferencia.PROP_DATA_EMBARQUE));
        columns.add(columnFactory.createColumn(BundleManager.getString("unidade_origem"), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("responsavel"), PedidoTransferencia.PROP_RESPONSAVEL_ENTREGA));
        columns.add(columnFactory.createColumn(BundleManager.getString("tipo"), PedidoTransferencia.PROP_DESCRICAO_TIPO));
        columns.add(columnFactory.createColumn(BundleManager.getString("ordem_entrega"), PedidoTransferencia.PROP_CODIGO_ORDEM_ENTREGA));

        return columns;
    }

    private IPagerProvider getPagerProvider(){
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter(){

            @Override
            public void consultaCustomizeSorters(List<BuilderQueryCustom.QuerySorter> sorters) {
                sorters.add(new QueryCustom.QueryCustomSorter(PedidoTransferencia.PROP_CODIGO));
            }

            @Override
            public Class getClassConsulta() {
                return PedidoTransferencia.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(PedidoTransferencia.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_DESTINO)
                                });
            }

        });
    }

    private List<QueryParameter> getParameters(){

        List<QueryParameter> parameters = new ArrayList<QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferencia.PROP_EMPRESA_DESTINO + "." + Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IGUAL, ApplicationSession.get().getSessaoAplicacao().getCodigoEmpresa()));
        parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferencia.PROP_STATUS, PedidoTransferencia.STATUS_PROCESSADO));
        parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferencia.PROP_CODIGO, pedido));

        return parameters;
    }

    private PedidoTransferencia getPedidoSelecionado() throws ValidacaoException{
        if (pageableTable.getSelectedObject()==null) {
            throw new ValidacaoException(BundleManager.getString("selecione_pedido"));
        }
        return pageableTable.getSelectedObject();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("receber_pedido_almoxarifado");
    }

}
