package br.com.celk.view.consorcio.pagamentoguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioRelacaoPagamentosDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.Arrays;
import java.util.List;


/**
 *
 * <AUTHOR>
 * Programa - 149
 */
@Private

public class RelatorioRelacaoPagamentosPage extends RelatorioPage<RelatorioRelacaoPagamentosDTOParam> {

    private DropDown<String> dropDownTipoPessoa;
    private DropDown dropDownComImpostos;
    private DropDown<TipoConta> cbxTipoConta;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(new PnlChoicePeriod("periodo"));
        form.add(getDropDownTipoPessoa());
        form.add(DropDownUtil.getNaoSimLongDropDown("apenasINSS"));
        form.add(getDropDownComImpostos());
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioRelacaoPagamentosDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoResumo", RelatorioRelacaoPagamentosDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioRelacaoPagamentosDTOParam.Ordenacao.values()));
        form.add(getDropDownTipoConta());
    }

    @Override
    public Class<RelatorioRelacaoPagamentosDTOParam> getDTOParamClass() {
        return RelatorioRelacaoPagamentosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoPagamentosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioRelacaoPagamentos(param);
    }

    public DropDown getDropDownTipoPessoa() {
        if (this.dropDownTipoPessoa == null) {
            this.dropDownTipoPessoa = new DropDown<String>("tipoPessoa");
            this.dropDownTipoPessoa.addChoice(null, BundleManager.getString("ambas"));
            this.dropDownTipoPessoa.addChoice(Pessoa.PESSOA_JURIDICA, BundleManager.getString("juridica"));
            this.dropDownTipoPessoa.addChoice(Pessoa.PESSOA_FISICA, BundleManager.getString("fisica"));
        }
        return dropDownTipoPessoa;
    }

    public DropDown getDropDownComImpostos() {
        if(dropDownComImpostos == null){
            dropDownComImpostos = new DropDown("comImposto");
            dropDownComImpostos.addChoice(null, BundleManager.getString("ambos"));
            dropDownComImpostos.addChoice(RepositoryComponentDefault.NAO_LONG, BundleManager.getString("nao"));
            dropDownComImpostos.addChoice(RepositoryComponentDefault.SIM_LONG, BundleManager.getString("sim"));
        }

        dropDownComImpostos.addAjaxUpdateValue();
        dropDownComImpostos.setOutputMarkupId(true);
        return dropDownComImpostos;
    }

    private DropDown getDropDownTipoConta() {
        cbxTipoConta = new DropDown("tipoConta");
        cbxTipoConta.addChoice(null, BundleManager.getString("todos"));

        List<TipoConta> tiposConta;
        tiposConta = LoadManager.getInstance(TipoConta.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoConta.PROP_VISIVEL_CONSORCIADO, RepositoryComponentDefault.SIM))
                .start().getList();
        for (TipoConta tipoConta1 : tiposConta) {
            cbxTipoConta.addChoice(tipoConta1, tipoConta1.getDescricao());
        }

        return cbxTipoConta;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoPagamentos");
    }

}
