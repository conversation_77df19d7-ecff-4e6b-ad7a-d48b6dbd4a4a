package br.com.celk.view.vigilancia.perfilusuarioexterno;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.externo.PerfilUsuarioExternoVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 691
 */
@Private
public class ConsultaPerfilUsuarioExternoPage extends ConsultaPage<PerfilUsuarioExternoVigilancia, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private DlgConfirmacao dlgConfirmacao;

    public ConsultaPerfilUsuarioExternoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));

        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        PerfilUsuarioExternoVigilancia proxy = on(PerfilUsuarioExternoVigilancia.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("descricao"), proxy.getDescricao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<PerfilUsuarioExternoVigilancia>() {
            @Override
            public void customizeColumn(final PerfilUsuarioExternoVigilancia rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<PerfilUsuarioExternoVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PerfilUsuarioExternoVigilancia modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroPerfilUsuarioExternoPage(rowObject, false, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<PerfilUsuarioExternoVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PerfilUsuarioExternoVigilancia modelObject) throws ValidacaoException, DAOException {
                        initDlgConfirmacao(rowObject, target, bundle("msg_remover_perfil_usuario_externo"));
                        dlgConfirmacao.show(target);
                    }
                });
            }

        };
    }

    private void initDlgConfirmacao(PerfilUsuarioExternoVigilancia perfil, AjaxRequestTarget target, String mensagemErro) {
        dlgConfirmacao = new DlgConfirmacao(newModalId(), BundleManager.getString("msg_remover_perfil_usuario_externo")){
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(VigilanciaFacade.class).removerPerfilUsuarioExternoVigilancia(perfil);
                getPageableTable().populate(target);
            }
        };
        addModal(target, dlgConfirmacao);
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return PerfilUsuarioExternoVigilancia.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(PerfilUsuarioExternoVigilancia.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PerfilUsuarioExternoVigilancia.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroPerfilUsuarioExternoPage.class;
    }


    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaPerfiUsuarioExternoVigilancia");
    }
}
