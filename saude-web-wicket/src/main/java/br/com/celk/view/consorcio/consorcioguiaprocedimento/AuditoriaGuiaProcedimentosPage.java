package br.com.celk.view.consorcio.consorcioguiaprocedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.consorcio.consorcioguiaprocedimento.columnpanel.AuditoriaGuiaProcedimentoColumnPanel;
import br.com.celk.view.consorcio.consorcioguiaprocedimento.customize.CustomizeConsultaConsorcioGuiaProcedimentoAudit;
import br.com.ksisolucoes.Procedimento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import br.com.ksisolucoes.vo.consorcio.Conta;
import br.com.ksisolucoes.vo.consorcio.SubConta;
import br.com.ksisolucoes.vo.controle.web.UsuarioGrupo;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 1013
 */
@Private
public class AuditoriaGuiaProcedimentosPage extends BasePage {

    private static final String PARAMETRO_AUDITOR_GUIAS = "Auditoria de Guia de Procedimento";
    private static final String PARAMETRO_AGENDAMENTO_SEM_AUDITORIA = "Permite agendamento sem auditoria";

    private PageableTable tblGuias;

    private final String AUDITOR_DE_GUIAS = "Auditor de Guias";
    private String nomePaciente;
    private UsuarioCadsus paciente;
    private Procedimento procedimento;
    private Empresa prestador;
    private Empresa consorciado;
    private Long nGuia;
    private InputField txtNumGuias;
    private DatePeriod periodo;
    private final Long situacao = 4L;
    private boolean isUsuarioAuditor = false;
    private boolean isAuditoriaGuia = false;
    private boolean isAgendamentoSemAuditoria = false;

    public AuditoriaGuiaProcedimentosPage() throws DAOException, ValidacaoException{
        permiteAcoesUsuario();
        init();
    }

    private void permiteAcoesUsuario() {
        List<UsuarioGrupo> usuarioGrupos = LoadManager.getInstance(UsuarioGrupo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(on(UsuarioGrupo.class).getUsuario().getCodigo()), ApplicationSession.get().getSessaoAplicacao().getUsuario().getCodigo()))
                .start().getList();
        for(UsuarioGrupo usuarioGrupo: usuarioGrupos) {
            if(usuarioGrupo.getGrupo().getNome().equals(AUDITOR_DE_GUIAS))
                isUsuarioAuditor = true;
        }

        try {
            isAuditoriaGuia = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.AUDITORIA).getParametro(PARAMETRO_AUDITOR_GUIAS));
            isAgendamentoSemAuditoria = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.AUDITORIA).getParametro(PARAMETRO_AGENDAMENTO_SEM_AUDITORIA));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaUsuarioCadsus("paciente"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(txtNumGuias = new InputField("nGuia"));
        form.add(getDropDownSituacao());

        form.add(tblGuias = new PageableTable("tblGuias", getColumns(), getPagerProvider()));
        tblGuias.getDataProvider().setParameters(getParameters());
        tblGuias.populate();

        form.add(new ProcurarButton("btnProcurar", tblGuias) {
            @Override
            public Object getParam() {
                return getParameters();
            }
        });

        add(form);
    }

    //    private boolean isConsorcioPadrao(){
//        return isConsorcioPadrao;
//    }
//
    private DropDown getDropDownSituacao() {
        DropDown cbxSituacao = new DropDown("situacao");
        DropDownUtil.setIEnumChoices(cbxSituacao, ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.values(), false);
        cbxSituacao.addChoice(null, BundleManager.getString("todas"));
        return cbxSituacao;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(ConsorcioGuiaProcedimento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nGuia"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_NOME_PACIENTE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("idade"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DESCRICAO_IDADE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("cadastro"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("prestador"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("consorciado"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DESCRICAO_STATUS_AUDIT)));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<ConsorcioGuiaProcedimento>() {

            @Override
            public Component getComponent(String componentId, ConsorcioGuiaProcedimento rowObject) {
                return new AuditoriaGuiaProcedimentoColumnPanel(componentId, rowObject, isUsuarioAuditor, isAgendamentoSemAuditoria) {
                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        tblGuias.update(target);
                    }
                };
            }
        };
    }

    private IPagerProvider getPagerProvider() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaConsorcioGuiaProcedimentoAudit()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO), false);
            }
        };
    }

    private List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (paciente != null)
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_NOME_PACIENTE), BuilderQueryCustom.QueryParameter.ILIKE, paciente.getNome()));
        if (prestador != null)
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR), prestador));
        if (consorciado != null)
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO), consorciado));
        if (nGuia != null)
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CODIGO), nGuia));
        if (periodo != null)
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO), periodo));

        if(situacao != null)
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), situacao));
        else {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.allValues()));
        }

        return parameters;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("auditoriaGuiaProcedimento");
    }

}
