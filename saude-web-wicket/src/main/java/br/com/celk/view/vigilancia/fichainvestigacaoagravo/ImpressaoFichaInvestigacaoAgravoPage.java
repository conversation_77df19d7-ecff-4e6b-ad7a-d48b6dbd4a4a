package br.com.celk.view.vigilancia.fichainvestigacaoagravo;

import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cid.autocomplete.AutoCompleteConsultaCidMulti;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.basico.classificacaocid.autocomplete.AutoCompleteConsultaClassificacaoCid;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioAgravosDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 967
 */
public class ImpressaoFichaInvestigacaoAgravoPage extends RelatorioPage<FichaInvestigacaoAgravoDTOParam> {

    private AutoCompleteConsultaClassificacaoCid classificacaoCid;
    private WebMarkupContainer containerTipoRelatorio;
    private WebMarkupContainer containerTodasNotificacoesAgravo;

    @Override
    public void init(Form<FichaInvestigacaoAgravoDTOParam> form) {
        FichaInvestigacaoAgravoDTOParam proxy = on(FichaInvestigacaoAgravoDTOParam.class);

        Long diasMaximo = Long.valueOf(0);
        try {
            diasMaximo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("diasMaximoGeracaoFichaInvestigacaoAgravo");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidade())));
        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidadeReferencia())));
        form.add(new AutoCompleteConsultaCidMulti(path(proxy.getListCid())).add(new Tooltip().setHtmlText(BundleManager.getString("toolTipCidAgravoRelatorio"))));
        form.add(classificacaoCid = (AutoCompleteConsultaClassificacaoCid) new AutoCompleteConsultaClassificacaoCid(path(proxy.getClassificacaoCids())).carregarFichaInvestigacaoAgravo(true).setRequired(true));
        form.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getStatus()), RelatorioAgravosDTOParam.Status.values()));
        form.add(new RequiredPnlDatePeriod("periodo").add(new Tooltip().setHtmlText(BundleManager.getString("toolTipPeriodoMaximoRelAgravo"))));
        containerTipoRelatorio = new WebMarkupContainer("containerTipoRelatorio");
        containerTipoRelatorio.add(getTipoRelatorioDropDown("tipoArquivo"));
        containerTipoRelatorio.setVisible(true);
        containerTipoRelatorio.setOutputMarkupPlaceholderTag(true);

        containerTodasNotificacoesAgravo = new WebMarkupContainer("containerTodasNotificacoesAgravo");
        containerTodasNotificacoesAgravo.add(getTodasNotificacoesCheckBox("todasNotificacoes"));
        containerTodasNotificacoesAgravo.setVisible(false);
        containerTodasNotificacoesAgravo.setOutputMarkupPlaceholderTag(true);
        containerTipoRelatorio.add(containerTodasNotificacoesAgravo);

        form.add(containerTipoRelatorio);

        consultaListenerClassificacaoCid();
        removeListnerClassificacaoCid();
    }

    public DropDown<TipoRelatorio> getTipoRelatorioDropDown(String id) {
        DropDown<TipoRelatorio> dropDown = new DropDown(id);
        dropDown.addChoice(TipoRelatorio.PDF_TEMPLATE, BundleManager.getString("pdf"));
        dropDown.addChoice(TipoRelatorio.XLS2, BundleManager.getString("xls"));
        dropDown.addChoice(TipoRelatorio.CSV, BundleManager.getString("csv"));
        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                renderTodasNotificacoesCheckBox(target, dropDown);
            }
        });
        return dropDown;
    }

    private void renderTodasNotificacoesCheckBox(AjaxRequestTarget target, DropDown<TipoRelatorio> dropDown) {
        if ((TipoRelatorio.XLS2.equals(dropDown.getComponentValue()) || TipoRelatorio.CSV.equals(dropDown.getComponentValue())) && classificacaoCid.getComponentValue() != null &&
                getModel().getObject().getClassificacaoCids() != null && getModel().getObject().getClassificacaoCids().getFichaInvestigacaoAgravo() != null &&
                RegistroAgravo.TipoFichaInvestigacaoAgravo.COVID_19.value().equals(getModel().getObject().getClassificacaoCids().getFichaInvestigacaoAgravo().getCodigo())) {
            containerTodasNotificacoesAgravo.setVisible(true);
        } else {
            containerTodasNotificacoesAgravo.setVisible(false);
        }
        target.add(containerTodasNotificacoesAgravo);
    }

    public static CheckBoxLongValue getTodasNotificacoesCheckBox(String id) {
        CheckBoxLongValue checkBoxLongValue = new CheckBoxLongValue(id);
        checkBoxLongValue.addAjaxUpdateValue();
        return checkBoxLongValue;
    }

    private void consultaListenerClassificacaoCid() {
        classificacaoCid.add(new ConsultaListener<ClassificacaoCids>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ClassificacaoCids object) {
                containerTodasNotificacoesAgravo.setVisible(false);
                if (object != null && object.getFichaInvestigacaoAgravo() != null &&
                        RegistroAgravo.TipoFichaInvestigacaoAgravo.COVID_19.value().equals(object.getFichaInvestigacaoAgravo().getCodigo()) &&
                        (TipoRelatorio.XLS2.equals(getModel().getObject().getTipoArquivo()) || TipoRelatorio.CSV.equals(getModel().getObject().getTipoArquivo()))) {
                    containerTodasNotificacoesAgravo.setVisible(true);
                }
                target.add(containerTodasNotificacoesAgravo);
            }
        });
    }


    private void removeListnerClassificacaoCid() {
        classificacaoCid.add(new RemoveListener<ClassificacaoCids>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ClassificacaoCids object) {
                containerTodasNotificacoesAgravo.setVisible(false);
                target.add(containerTodasNotificacoesAgravo);
            }
        });
    }


    @Override
    public Class<FichaInvestigacaoAgravoDTOParam> getDTOParamClass() {
        return FichaInvestigacaoAgravoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(FichaInvestigacaoAgravoDTOParam param) throws ReportException, ValidacaoException {
        validarPeriodo(param);
        if (param != null && param.getClassificacaoCids() != null && param.getClassificacaoCids().getFichaInvestigacaoAgravo() != null) {
            switch (RegistroAgravo.TipoFichaInvestigacaoAgravo.valueOf(param.getClassificacaoCids().getFichaInvestigacaoAgravo().getCodigo())) {
                case TRATAMENTO_ANTIRRABICO:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoTratamentoAntirrabico(param);
                case HIV_GESTANTE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoHivGestante(param);
                case SIFILIS_CONGENITA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoSifilisCongenita(param);
                case SIFILIS_GESTANTE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoSifilisGestante(param);
                case AIDS_ADULTO:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoAcidenteHivAdulto(param);
                case ACIDENTE_GRAVE_TRABALHO:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoAcidenteTrabalhoGrave(param);
                case COVID_19:
                    if (TipoRelatorio.XLS2.equals(param.getTipoArquivo()) || TipoRelatorio.CSV.equals(param.getTipoArquivo())) {
                        return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoCovid19XLS(param);
                    }
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoCovid19(param);
                case DENGUE_ZIKA_CHIKUNGUNYA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoDengueZikaChikungunya(param);
                case SARS:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoSars(param);
                case VIOLENCIA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoViolencia(param);
                case SIFILIS_ADQUIRIDA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoSifilisAdquirida(param);
                case INTOXICACAO_EXOGENA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoIntoxicacaoExogena(param);
                case TUBERCULOSE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoTuberculose(param);
                case ACIDENTE_TRABALHO_DERMATOSE_OCUPACIONAL:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoDermatosesOcupacional(param);
                case HANSENIASE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoHanseniase(param);
                case DOENCAS_EXANTEMATICAS_RUBEOLA_SARAMPO:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoRubeolaSarampo(param);
                case HANTAVIROSE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoHantavirose(param);
                case DOENCA_RELACIOANADA_TRABALHO:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoDoencaTrabalhoLerDort(param);
                case DOENCA_RELACIONADA_TRABALHO_PAIR:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoDoencaTrabalhoPair(param);
                case ACIDENTE_TRABALHO_TRANSTORNO_MENTAL:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoAcidenteDeTrabalhoTranstornoMental(param);
                case DOENCA_RELACIONADA_TRABALHO_PNEUMONOCONIOSES:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoDoencaPneumoconioses(param);
                case SURTO:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoSurto(param);
                case DOENCA_RELACIOANADA_TRABALHO_CANCER:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoDoencaTrabalhoCancer(param);
                case BOTULISMO:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoBotulismo(param);
                case DOENCA_CREUTZFELDT_JACOB:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoDoencaCreutzfeldtJacob(param);
                case AIDS_CRIANCA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoAidsCrianca(param);
                case CISTICERCOSE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoCisticercose(param);
                case DOENCA_LYME:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoDoencaLyme(param);
                case EPIZOOTIA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoEpizootia(param);
                case DOENCA_CHAGAS:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoDoencaChagas(param);
                case BRUCELOSE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoBrucelose(param);
                case ESQUISTOSSOMOSE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoEsquistossomose(param);
                case FEBRE_NILO_OCIDENTAL:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoFebreNiloOcidental(param);
                case FEBRE_TIFOIDE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoFebreTifoide(param);
                case CASOS_INUSITADOS:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoCasosInusitados(param);
                case PESTE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoPeste(param);
                case MICROCEFALIA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoMicrocefalia(param);
                case PARALISIA_FLACIDA_AGUDA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoParalisiaFlacidaAguda(param);
                case TRACOMA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoTracoma(param);
                case ROTAVIRUS:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoRotavirus(param);
                case RUBEOLA_CONGENITA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoRubeolaCongenita(param);
                case TETANO_NEONATAL:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoTetanoNeonatal(param);
                case TETANO_ACIDENTAL:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoTetanoAcidental(param);
                case NOTIFICACAO_NEGATIVA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoNotificacaoNegativa(param);
                case VIOLENCIA_INTERPESSOAL_AUTOPROVOCADA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoViolenciaAutoProvocada(param);
                case MENINGOCOCICA_MENINGITES:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoMenigococicaMeningite(param);
                case ACIDENTE_POR_ANIMAL_PECONHENTO:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoAcidenteAnimalPeconhento(param);
                case ACIDENTE_TRABALHO_MATERIAL_BIOLOGICO:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoAcidenteTrabalhoMaterialBiologico(param);
                case MALARIA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoMalaria(param);
                case FEBRE_MACULOSA_OUTRAS_RICKETTSIOSES:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoFebreMaculosa(param);
                case LEISHMANIOSE_TEGUMENTAR_AMERICANA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoLeishmanioseTegumentarAmericana(param);
                case FEBRE_AMARELA:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoFebreAmarela(param);
                case HEPATITE_VIRAL:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoHepatiteViral(param);
                case LEPTOSPIROSE:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravoLeptospirose(param);
                default:
                    return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravo(param);
            }
        }
        return BOFactory.getBO(VigilanciaFacade.class).impressaoFichaInvestigacaoAgravo(param);
    }

    private void validarPeriodo(FichaInvestigacaoAgravoDTOParam param) throws ValidacaoException, ReportException {
        Long diasMaximo;
        try {
            diasMaximo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("diasMaximoGeracaoFichaInvestigacaoAgravo");
        } catch (DAOException ex) {
            throw new ReportException(ex.getMessage(), ex);
        }

        int diferenca = DataUtil.getDiasDiferenca(param.getPeriodo().getDataInicial(), param.getPeriodo().getDataFinal());
        if (diferenca > diasMaximo)
            throw new ValidacaoException("É permitido um período de no máximo " + diasMaximo + " dias.");
    }

    @Override
    public String getTituloPrograma() {
        return bundle("fichaInvestigacaoAgravo");
    }

}
