package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioResumoConsumoProdutoDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.util.MapBean;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaSetor;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 61
 */
@Private
public class RelatorioResumoConsumoProdutosPage extends RelatorioPage<RelatorioResumoConsumoProdutoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaLocalizacao autoCompleteConsultaLocalizacao;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;

    private Long quantidadeMesesMedia;
    private String mesAno;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresas").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(new AutoCompleteConsultaEmpresa("empresaDestino"));
        form.add(new AutoCompleteConsultaPessoa("fornecedor"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(autoCompleteConsultaLocalizacao = new AutoCompleteConsultaLocalizacao("localizacao"));
        form.add(getDropDownCurva());
        form.add(new RequiredInputField("mesAno", new PropertyModel(this, "mesAno")));
        form.add(new RequiredInputField("quantidadeMesesMedia", new PropertyModel(this, "quantidadeMesesMedia")));
        form.add(getDropDownOrdenacao());
        form.add(DropDownUtil.getNaoSimDropDown("agruparEmpresa"));

        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaLocalizacao.setMultiplaSelecao(true);
        autoCompleteConsultaLocalizacao.setOperadorValor(true);

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioResumoConsumoProdutoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoConsumoProdutoDTOParam param) throws ReportException {
        Date data = Data.parserMounthYear(mesAno);
        if (quantidadeMesesMedia != null && quantidadeMesesMedia == 0) {
            try {
                throw new ValidacaoException(BundleManager.getString("msgMesMediaObrigatorio"));
            } catch (ValidacaoException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        param.setQuantidadeMesesMedia(quantidadeMesesMedia);
        param.setMesAno(Data.adjustRangeDay(data).getDataInicial());
        OperadorValor<List<GrupoProduto>> grupoProdutoVazio = new OperadorValor<List<GrupoProduto>>();
        param.setGrupoProduto(grupoProdutoVazio);

        Empresa empresa = ApplicationSession.get().getSession().getEmpresa();
        OperadorValor<List<Empresa>> empresaOperadorValor = (OperadorValor<List<Empresa>>) autoCompleteConsultaEmpresa.getComponentValue();
        if (empresaOperadorValor != null
                && CollectionUtils.isNotNullEmpty(empresaOperadorValor.getValue())
                && empresaOperadorValor.getValue().size() == 1) {
            empresa = empresaOperadorValor.getValue().get(0);
        }

        List<EmpresaSetor> empresaSetorList = LoadManager.getInstance(EmpresaSetor.class)
                .addProperty(VOUtils.montarPath(EmpresaSetor.PROP_SETOR, Empresa.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_EMPRESA, empresa))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_SITUACAO, EmpresaSetor.Situacao.ATIVO.value()))
                .start().getList();

        if(CollectionUtils.isNotNullEmpty(empresaSetorList)){
            List<Empresa> setores = Lambda.extract(empresaSetorList, Lambda.on(EmpresaSetor.class).getSetor());
            setores.add(empresa);

            OperadorValor<List<Empresa>> empresas = new OperadorValor<>();
            empresas.setValue(setores);
            empresas.setOperador(OperadorValor.Operadores.IN);

            param.setEmpresas(empresas);
        }

        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioResumoConsumoProduto(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoConsumoProdutos");
    }

    public DropDown getDropDownCurva() {
        DropDown dropDown = new DropDown("curva");
        dropDown.addChoice(new MapBean(BundleManager.getString("todos"), RepositoryComponentDefault.TODOS), BundleManager.getString("todos"));
        dropDown.addChoice(new MapBean(BundleManager.getString("curvaA"), Produto.CURVA_A), BundleManager.getString("curvaA"));
        dropDown.addChoice(new MapBean(BundleManager.getString("curvaB"), Produto.CURVA_B), BundleManager.getString("curvaB"));
        dropDown.addChoice(new MapBean(BundleManager.getString("curvaC"), Produto.CURVA_C), BundleManager.getString("curvaC"));

        return dropDown;
    }

    public DropDown getDropDownOrdenacao() {
        DropDown dropDown = new DropDown("ordenacao");
        dropDown.addChoice(Produto.PROP_DESCRICAO, BundleManager.getString("descricao"));
        dropDown.addChoice(Produto.PROP_CODIGO, BundleManager.getString("codigo"));

        return dropDown;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }
}
