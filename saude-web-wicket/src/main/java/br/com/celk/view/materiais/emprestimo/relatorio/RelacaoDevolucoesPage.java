package br.com.celk.view.materiais.emprestimo.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.emprestimo.interfaces.dto.RelacaoDevolucaoDTOParam;
import br.com.celk.bo.emprestimo.interfaces.facade.EmprestimoReportFacade;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.emprestimo.relatorio.autocomplete.AutoCompleteConsultaTipoDevolucao;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;

/**
 *
 * <AUTHOR>
 * Programa - 472
 */
@Private
public class RelacaoDevolucoesPage extends RelatorioPage<RelacaoDevolucaoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    @Override
    public void init(Form form) {
        RelacaoDevolucaoDTOParam proxy = on(RelacaoDevolucaoDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaTipoDevolucao(path(proxy.getTipoDevolucao())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoOperacao()), RelacaoDevolucaoDTOParam.TipoOperacao.values()));
        form.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getPaciente())));
        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimentoEmprestimo())));
        form.add(new AutoCompleteConsultaProduto(path(proxy.getProduto())));
        form.add(new AutoCompleteConsultaProduto(path(proxy.getProdutoEmprestimo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelacaoDevolucaoDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getOrdenacao()), RelacaoDevolucaoDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoOrdenacao()), RelacaoDevolucaoDTOParam.TipoOrdenacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getVisualizarTotais()), RelacaoDevolucaoDTOParam.VisualizarTotais.values()));
        form.add(new RequiredPnlChoicePeriod(path(proxy.getPeriodo())));

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);

    }

    @Override
    public Class<RelacaoDevolucaoDTOParam> getDTOParamClass() {
        return RelacaoDevolucaoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelacaoDevolucaoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EmprestimoReportFacade.class).relatorioRelacaoDevolucoes(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoDevolucoes");
    }
}
