package br.com.celk.view.vigilancia.solicitacaoAgendamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaAtividadeVeterinaria;
import br.com.celk.view.vigilancia.solicitacaoAgendamento.dialog.DlgDadosParaAgendamento;
import br.com.celk.vigilancia.dto.SolicitacaoAgendamentoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.AtividadeVeterinaria;
import br.com.ksisolucoes.vo.vigilancia.SolicitacaoAgendamentoCVA;
import br.com.ksisolucoes.vo.vigilancia.SolicitacaoAgendamentoCVAAnimal;
import br.com.ksisolucoes.vo.vigilancia.SolicitacaoAgendamentoCVAOcorrencia;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 571
 */
@Private
public class ConsultaSolicitacaoAgendamentoCVAPage extends ConsultaPage<SolicitacaoAgendamentoCVA, List<BuilderQueryCustom.QueryParameter>> {

    private DatePeriod periodo;
    private Empresa estabelecimento;
    private Profissional profissional;
    private AtividadeVeterinaria atividadeVeterinaria;
    private Long situacao;
    private Long urgencia;
    private String responsavelAnimal;

    private DlgMotivoObject<SolicitacaoAgendamentoCVA> dlgMotivo;
    private DlgDadosParaAgendamento dlgDadosParaAgendamento;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new PnlDatePeriod("periodo"));
        form.add(new AutoCompleteConsultaEmpresa("estabelecimento"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(new AutoCompleteConsultaAtividadeVeterinaria("atividadeVeterinaria"));
        form.add(new InputField("responsavelAnimal"));
        form.add(DropDownUtil.getIEnumDropDown("situacao", SolicitacaoAgendamentoCVA.Status.values(), true, bundle("todos")));
        form.add(DropDownUtil.getIEnumDropDown("urgencia", RepositoryComponentDefault.SimNaoLong.values(), true, bundle("ambos")));

        getLinkNovo().add(new AttributeModifier("value", BundleManager.getString("novaSolicitacao")));

        situacao = SolicitacaoAgendamentoCVA.Status.PENDENTE.value();

        setExibeExpandir(true);
    }

    @Override
    public List getColumns(List columns) {
        columns = new ArrayList();

        SolicitacaoAgendamentoCVA proxy = on(SolicitacaoAgendamentoCVA.class);

        columns.add(getCustomActionColumn());
        columns.add(createSortableColumn(bundle("dataSolicitacaoAbv"), proxy.getDataSolicitacao()));
        columns.add(createSortableColumn(bundle("responsavel"), proxy.getResponsavel()));
        columns.add(createSortableColumn(bundle("atividade"), proxy.getAtividadeVeterinaria().getDescricao()));
        columns.add(createSortableColumn(bundle("urgencia"), proxy.getFlagUrgente(), proxy.getDescricaoUrgencia()));
        columns.add(new DateColumn(bundle("dataAgendamentoAbv"), path(proxy.getDataAgendamento())).setPattern("dd/MM/yyyy HH:mm"));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));

        return columns;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<SolicitacaoAgendamentoCVA>() {
            @Override
            public void customizeColumn(final SolicitacaoAgendamentoCVA rowObject) {

                addAction(ActionType.AGENDAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        viewDialogAgendar(target, rowObject);
                    }
                }).setEnabled(SolicitacaoAgendamentoCVA.Status.PENDENTE.value().equals(rowObject.getStatus()));

                addAction(ActionType.EDITAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroSolicitacaoAgendamentoCVAPage(getSolicitacaoConsultada(rowObject)));
                    }
                }).setEnabled(SolicitacaoAgendamentoCVA.Status.PENDENTE.value().equals(rowObject.getStatus()));;

                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroSolicitacaoAgendamentoCVAPage(getSolicitacaoConsultada(rowObject), true));
                    }
                });

                addAction(ActionType.CANCELAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        viewDialogMotivo(target, rowObject);
                    }
                }).setQuestionDialogBundleKey(null)
                        .setEnabled(
                                (rowObject.getDataAgendamento() == null || rowObject.getDataAgendamento().compareTo(DataUtil.getDataAtual()) >= 0)
                                && (SolicitacaoAgendamentoCVA.Status.PENDENTE.value().equals(rowObject.getStatus()) || SolicitacaoAgendamentoCVA.Status.AGENDADO.value().equals(rowObject.getStatus())));

                addAction(ActionType.REVERTER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        reverterCancelamentoSolicitacao(target, rowObject);
                    }
                }).setTitleBundleKey("reverterSolicitacaoAgendamentoCVA").setQuestionDialogBundleKey("desejaReativarSolicitacao")
                        .setEnabled((rowObject.getDataAgendamento() == null && SolicitacaoAgendamentoCVA.Status.CANCELADO.value().equals(rowObject.getStatus())));
            }
        };
    }

    private SolicitacaoAgendamentoDTO getSolicitacaoConsultada(SolicitacaoAgendamentoCVA object) {
        SolicitacaoAgendamentoDTO dto = new SolicitacaoAgendamentoDTO();
        if (object == null) {
            dto.setSolicitacaoAgendamentoCVA(new SolicitacaoAgendamentoCVA());
        } else {
            dto.setSolicitacaoAgendamentoCVA(object);
            List<SolicitacaoAgendamentoCVAAnimal> listSolicitacaoAgendamentoCVAAnimal = LoadManager.getInstance(SolicitacaoAgendamentoCVAAnimal.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVAAnimal.PROP_SOLICITACAO_AGENDAMENTO_C_V_A, object))
                    .start().getList();
            dto.setLstSolicitacaoAgendamentoCVAAnimal(listSolicitacaoAgendamentoCVAAnimal);

            AtividadeVeterinaria at = LoadManager.getInstance(AtividadeVeterinaria.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(AtividadeVeterinaria.PROP_CODIGO, dto.getSolicitacaoAgendamentoCVA().getAtividadeVeterinaria().getCodigo()))
                    .start().getVO();
            dto.getSolicitacaoAgendamentoCVA().setAtividadeVeterinaria(at);
        }

        return dto;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return SolicitacaoAgendamentoCVA.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(SolicitacaoAgendamentoCVA.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_EMPRESA_AGENDAMENTO, Empresa.PROP_CODIGO),
                            VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_EMPRESA_AGENDAMENTO, Empresa.PROP_DESCRICAO),
                            VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_PROFISSIONAL_AGENDAMENTO, Profissional.PROP_CODIGO),
                            VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_PROFISSIONAL_AGENDAMENTO, Profissional.PROP_NOME),
                            VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_ATIVIDADE_VETERINARIA, AtividadeVeterinaria.PROP_CODIGO),
                            VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_ATIVIDADE_VETERINARIA, AtividadeVeterinaria.PROP_DESCRICAO),});
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_DATA_SOLICITACAO), false);
            }
        };
    }

    public void viewDialogMotivo(AjaxRequestTarget target, SolicitacaoAgendamentoCVA raa) {
//        if (dlgMotivo == null) {
            StringBuilder sb = new StringBuilder();
            sb.append(bundle("motivo_cancelamento"));
            if (SolicitacaoAgendamentoCVA.Status.AGENDADO.value().equals(raa.getStatus())) {
                sb.append(" - ").append(bundle("agendamento"));
            } else if (SolicitacaoAgendamentoCVA.Status.PENDENTE.value().equals(raa.getStatus())) {
                sb.append(" - ").append(bundle("solicitacao"));
            }
            addModal(target, dlgMotivo = new DlgMotivoObject<SolicitacaoAgendamentoCVA>(newModalId(), sb.toString()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, SolicitacaoAgendamentoCVA object) throws ValidacaoException, DAOException {
                    if (object.getDataAgendamento() != null && object.getDataAgendamento().before(DataUtil.getDataAtual())) {
                        throw new ValidacaoException(bundle("msg_data_agendamendo_menor_que_a_data"));
                    }

                    if (SolicitacaoAgendamentoCVA.Status.AGENDADO.value().equals(object.getStatus())) {
                        object.setStatus(SolicitacaoAgendamentoCVA.Status.PENDENTE.value());
                        lancarOcorrencia(object, bundle("cancelamentoAgendamentoCva", object.getDataAgendamento(), motivo));
                    } else {
                        object.setStatus(SolicitacaoAgendamentoCVA.Status.CANCELADO.value());
                        lancarOcorrencia(object, bundle("cancelamentoSolicitacaoAgendamentoCva", motivo));
                    }

                    object.setMotivo(motivo);
                    BOFactoryWicket.save(object);
                    getPageableTable().populate();
                    getPageableTable().update(target);
                }

                @Override
                public Long getMaxLengthMotivo() {
                    return 100L;
                }
            });
//        }
        dlgMotivo.setObject(raa);
        dlgMotivo.show(target);
    }

    private void reverterCancelamentoSolicitacao(AjaxRequestTarget target, SolicitacaoAgendamentoCVA rowObject) throws ValidacaoException, DAOException {
        rowObject.setStatus(SolicitacaoAgendamentoCVA.Status.PENDENTE.value());
        BOFactoryWicket.save(rowObject);
        lancarOcorrencia(rowObject, bundle("reversaoSolicitacaoAgendamentoCva"));
        getPageableTable().populate();
        getPageableTable().update(target);

    }

    private void lancarOcorrencia(SolicitacaoAgendamentoCVA agendamentoCVA, String descricaoOcorrencia) throws DAOException, ValidacaoException{
        SolicitacaoAgendamentoCVAOcorrencia ocorrencia = new SolicitacaoAgendamentoCVAOcorrencia();
        ocorrencia.setSolicitacaoAgendamentoCVA(agendamentoCVA);
        ocorrencia.setDescricaoOcorrencia(descricaoOcorrencia);

        BOFactory.save(ocorrencia);
    }

    public void viewDialogAgendar(AjaxRequestTarget target, SolicitacaoAgendamentoCVA sac) {
        if (dlgDadosParaAgendamento == null) {
            addModal(target, dlgDadosParaAgendamento = new DlgDadosParaAgendamento(newModalId()) {
                @Override
                public void onAgendar(AjaxRequestTarget target, SolicitacaoAgendamentoCVA solicitacaoAgendamentoCVA) throws ValidacaoException, DAOException {
                    solicitacaoAgendamentoCVA.setStatus(SolicitacaoAgendamentoCVA.Status.AGENDADO.value());
                    BOFactoryWicket.save(solicitacaoAgendamentoCVA);
                    getPageableTable().populate();
                    getPageableTable().update(target);
                }
            });
        }
        dlgDadosParaAgendamento.show(target, sac);
    }

    @Override
    public Class getCadastroPage() {
        return CadastroSolicitacaoAgendamentoCVAPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaSolicitacaoAgendamentoCVA");
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_STATUS, situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_DATA_SOLICITACAO, periodo));
        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_EMPRESA, estabelecimento));
        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_PROFISSIONAL_AGENDAMENTO, profissional));
        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_ATIVIDADE_VETERINARIA, atividadeVeterinaria));
        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_RESPONSAVEL, QueryCustom.QueryCustomParameter.CONSULTA_LIKED, responsavelAnimal));
        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_FLAG_URGENTE, urgencia));

        return parameters;
    }
}
