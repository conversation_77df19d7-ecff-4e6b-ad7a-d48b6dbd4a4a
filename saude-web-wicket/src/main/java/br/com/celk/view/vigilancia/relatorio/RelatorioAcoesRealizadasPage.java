package br.com.celk.view.vigilancia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.report.vigilancia.acoesrealizadas.dto.RelatorioAcoesRealizadasDTOParam;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.BasicoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import ch.lambdaj.Lambda;
import org.apache.wicket.markup.html.form.Form;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 * <AUTHOR>
 * Programa - 968
 */
@Private
public class RelatorioAcoesRealizadasPage extends RelatorioPage<RelatorioAcoesRealizadasDTOParam> {

    @Override
    public void init(Form form) {
        RelatorioAcoesRealizadasDTOParam proxy = Lambda.on(RelatorioAcoesRealizadasDTOParam.class);

        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new PnlAtividadeEstabelecimento(path(proxy.getAtividadeEstabelecimento())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoDocumento()), TipoSolicitacao.TipoDocumento.values(), true, false, true));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoResumo()), RelatorioAcoesRealizadasDTOParam.TipoResumo.values()));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioAcoesRealizadasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioAcoesRealizadasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(BasicoReportFacade.class).relatorioAcoesRealizadas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioAcoesRealizadas");
    }
}
