package br.com.celk.view.vigilancia.processoadministrativo.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.report.vigilancia.processoadministrativo.RelatorioProcessoAdministrativoDTOParam;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.grupoestabelecimento.autocomplete.AutoCompleteConsultaGrupoEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 957
 */
@Private
public class RelatorioProcessoAdministrativoPage extends RelatorioPage<RelatorioProcessoAdministrativoDTOParam> {

    private DropDown dropDownTipoAutuado;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private AutoCompleteConsultaGrupoEstabelecimento autoCompleteConsultaGrupoEstabelecimento;
    private DropDown dropDownFormaApresentacao;
    private DropDown dropDownSituacao;
    private DropDown dropDownSituacaoFinanceira;
    private DropDown dropDownTipo;

    @Override
    public void init(final Form<RelatorioProcessoAdministrativoDTOParam> form) {
        RelatorioProcessoAdministrativoDTOParam proxy = on(RelatorioProcessoAdministrativoDTOParam.class);

        form.add(dropDownTipoAutuado = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAutuado()), ProcessoAdministrativo.TipoDenunciado.values(), true, bundle("ambos")));
        form.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getVigilanciaPessoa())));
        form.add(autoCompleteConsultaGrupoEstabelecimento = new AutoCompleteConsultaGrupoEstabelecimento(path(proxy.getGrupoEstabelecimento())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(dropDownTipo = DropDownUtil.getIEnumDropDown(path(proxy.getTipo()), ProcessoAdministrativo.Tipo.values(), true, bundle("todos")));
        form.add(getDropDownSituacao(path(proxy.getSituacao())));
        form.add(dropDownSituacaoFinanceira = DropDownUtil.getIEnumDropDown(path(proxy.getSituacaoFinanceira()), ProcessoAdministrativo.SituacaoFinanceira.values(), true, bundle("todas")));
        form.add(dropDownFormaApresentacao = DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioProcessoAdministrativoDTOParam.FormaApresentacao.values()));
        enableCampos(null, null);
        dropDownTipoAutuado.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCampos(target, (Long) dropDownTipoAutuado.getComponentValue());
            }
        });
    }

    private DropDown<Long> getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<>(id);
            dropDownSituacao.addAjaxUpdateValue();
            dropDownSituacao.setOutputMarkupPlaceholderTag(true);
            dropDownSituacao.addChoice(null, BundleManager.getString("todas"));

            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.PENDENTE.value(),ProcessoAdministrativo.Situacao.PENDENTE.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.EM_ANALISE.value(), ProcessoAdministrativo.Situacao.EM_ANALISE.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_RECURSO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.EM_ANALISE_1_RECURSO.value(), ProcessoAdministrativo.Situacao.EM_ANALISE_1_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO_1_RECURSO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO_1_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.EM_ANALISE_2_RECURSO.value(), ProcessoAdministrativo.Situacao.EM_ANALISE_2_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO_2_RECURSO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO_2_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_ARQUIVAMENTO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_ARQUIVAMENTO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.CONCLUIDO.value(), ProcessoAdministrativo.Situacao.CONCLUIDO.descricao());
        }
        return dropDownSituacao;
    }

    public void enableCampos(AjaxRequestTarget target, Long tipoDenunciado) {
        if (tipoDenunciado != null) {

            Boolean controle;
            if (ProcessoAdministrativo.TipoDenunciado.PESSOA.value().equals(tipoDenunciado)) {
                controle = false;
            } else {
                controle = true;
            }

            autoCompleteConsultaGrupoEstabelecimento.setEnabled(controle);
            autoCompleteConsultaEstabelecimento.setEnabled(controle);
            autoCompleteConsultaVigilanciaPessoa.setEnabled(!controle);
        } else {
            autoCompleteConsultaGrupoEstabelecimento.setEnabled(true);
            autoCompleteConsultaEstabelecimento.setEnabled(true);
            autoCompleteConsultaVigilanciaPessoa.setEnabled(true);
        }

        if (target != null) {
            autoCompleteConsultaEstabelecimento.limpar(target);
            autoCompleteConsultaGrupoEstabelecimento.limpar(target);
            autoCompleteConsultaVigilanciaPessoa.limpar(target);
            target.add(autoCompleteConsultaVigilanciaPessoa);
            target.add(autoCompleteConsultaEstabelecimento);
            target.add(autoCompleteConsultaGrupoEstabelecimento);
        }
    }


    @Override
    public String getTituloPrograma() {
        return bundle("relatorioProcessoAdministrativo");
    }

    @Override
    public Class<RelatorioProcessoAdministrativoDTOParam> getDTOParamClass() {
        return RelatorioProcessoAdministrativoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioProcessoAdministrativoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioProcessoAdministrativo(param);
    }

}
