package br.com.celk.view.consorcio.pagamentoguiaprocedimento;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.pagamentoguiaprocedimento.columnpanel.PagamentoColumnPanel;
import br.com.celk.view.consorcio.pagamentoguiaprocedimento.customize.CustomizeConsultaPagamentoGuiaProcedimento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import br.com.ksisolucoes.vo.consorcio.PagamentoGuiaProcedimento;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 148
 */
@Private
public class ConsultaPagamentoPage extends ConsultaPage<PagamentoGuiaProcedimento, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa prestador;
    private Long chave;
    private String documento;
    private DatePeriod periodo;
    private Long situacao;
    private Long status;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        
        form.add(new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(new InputField("chave"));
        form.add(new InputField("documento"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(DropDownUtil.getIEnumDropDown("status", PagamentoGuiaProcedimento.StatusPagamentoGuiaProcedimento.values(), true));
        form.add(DropDownUtil.getIEnumDropDown("situacao", PagamentoGuiaProcedimento.SituacaoImpostoPagamentoGuiaProcedimento.values(), true));
        
        getLinkNovo().setVisible(false);

        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        
        ColumnFactory columnFactory = new ColumnFactory(PagamentoGuiaProcedimento.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_CHAVE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("data"), VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_DATA_PAGAMENTO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("prestador"), VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("documento"), VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_DOCUMENTO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("valorPagamento"), VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_VALOR_TOTAL_PAGAMENTO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("impostos"), VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_VALOR_TOTAL_IMPOSTOS)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacaoImposto_abv"), PagamentoGuiaProcedimento.PROP_SITUACAO_PAGAMENTO_FORMATADO));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("status"), PagamentoGuiaProcedimento.PROP_DESCRICAO_STATUS));
        
        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<PagamentoGuiaProcedimento>() {

            @Override
            public Component getComponent(String componentId, PagamentoGuiaProcedimento rowObject) {
                return new PagamentoColumnPanel(componentId, rowObject) {
                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        ConsultaPagamentoPage.this.getPageableTable().update(target);
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaPagamentoGuiaProcedimento()){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_DATA_PAGAMENTO), false);
            }

        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> param = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_DOCUMENTO), documento));
        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR), prestador));
        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_DATA_PAGAMENTO), periodo));
        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_SITUACAO_PAGAMENTO), situacao));
        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_STATUS), status));
        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PagamentoGuiaProcedimento.PROP_CHAVE), chave));
        
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaPagamentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaPagamentos");
    }

}
