package br.com.celk.view.materiais.liberacaoreceita;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.dispensacao.ConsultaDispensacaoPage;
import br.com.celk.view.materiais.liberacaoreceita.customize.CustomizeConsultaLiberacaoReceita;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.LiberacaoReceita;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 * Programa - 76
 */
@Private
public class ConsultaLiberacaoReceitaPage extends ConsultaPage<LiberacaoReceita, List<BuilderQueryCustom.QueryParameter>> {

    private Date dataUsuario;
    private Produto produto;
    private Long status;
    private UsuarioCadsus usuarioCadsus;
    private DropDown<Long> dropDownStatus;
    private List<Empresa> estabelecimento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEstabelecimentoExecutante;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    public ConsultaLiberacaoReceitaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new DateChooser(LiberacaoReceita.PROP_DATA_USUARIO));
        form.add(autoCompleteConsultaEstabelecimentoExecutante = new AutoCompleteConsultaEmpresa("estabelecimento"));
        autoCompleteConsultaEstabelecimentoExecutante.setValidaUsuarioEmpresa(true);
        autoCompleteConsultaEstabelecimentoExecutante.setMultiplaSelecao(true);
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(LiberacaoReceita.PROP_PRODUTO));
        autoCompleteConsultaProduto.setIncluirInativos(false);
        form.add(new AutoCompleteConsultaUsuarioCadsus(LiberacaoReceita.PROP_USUARIO_CADSUS));
        form.add(getDropDownStatus());

        setExibeExpandir(true);
        
        Usuario usuario = SessaoAplicacaoImp.getInstance().<Usuario>getUsuario();
        if (!usuario.isNivelAdminOrMaster() && estabelecimento == null){
            try {
                if (usuario.getEmpresasUsuario() == null) {
                    usuario.setEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                }
                List<Long> list = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getEmpresasUsuario();
                
                if(CollectionUtils.isNotNullEmpty(list)){
                    estabelecimento = LoadManager.getInstance(Empresa.class)
                            .addProperties(new HQLProperties(Empresa.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, list))
                            .start().getList();
                }
            } catch (SGKException ex) {
                Logger.getLogger(ConsultaDispensacaoPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private DropDown<Long> getDropDownStatus(){
        if (this.dropDownStatus == null) {
            this.dropDownStatus = new DropDown<Long>(LiberacaoReceita.PROP_STATUS);
            dropDownStatus.addChoice(null, BundleManager.getString("ambos"));
            dropDownStatus.addChoice(LiberacaoReceita.STATUS_ABERTO, BundleManager.getString("aberto"));
            dropDownStatus.addChoice(LiberacaoReceita.STATUS_LIBERADO, BundleManager.getString("liberado"));
        }
        
        return this.dropDownStatus;
    }
    
    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(LiberacaoReceita.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataUsuario"), VOUtils.montarPath(LiberacaoReceita.PROP_DATA_USUARIO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estabelecimento"), VOUtils.montarPath(LiberacaoReceita.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("produto"), VOUtils.montarPath(LiberacaoReceita.PROP_PRODUTO, Produto.PROP_DESCRICAO),VOUtils.montarPath(LiberacaoReceita.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(LiberacaoReceita.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),VOUtils.montarPath(LiberacaoReceita.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DESCRICAO_SOCIAL_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(LiberacaoReceita.PROP_STATUS),VOUtils.montarPath(LiberacaoReceita.PROP_DESCRICAO_STATUS)));

        return columns;
    }

    private CustomColumn<LiberacaoReceita> getCustomColumn() {
        return new CustomColumn<LiberacaoReceita>() {

            @Override
            public Component getComponent(String componentId, final LiberacaoReceita rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLiberacaoReceitaPage(rowObject,false,true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLiberacaoReceitaPage(rowObject, true));
                    }

                    @Override
                    public boolean isEditarVisible() {
                        return LiberacaoReceita.STATUS_ABERTO.equals(rowObject.getStatus());
                    }

                    @Override
                    public boolean isExcluirVisible() {
                        return LiberacaoReceita.STATUS_ABERTO.equals(rowObject.getStatus());
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaLiberacaoReceita()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(LiberacaoReceita.PROP_DATA_USUARIO, false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_DATA_USUARIO, Data.adjustRangeHour(new DatePeriod(dataUsuario, dataUsuario))));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LiberacaoReceita.PROP_USUARIO_CADSUS), BuilderQueryCustom.QueryParameter.IGUAL, usuarioCadsus));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LiberacaoReceita.PROP_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, produto));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LiberacaoReceita.PROP_STATUS), BuilderQueryCustom.QueryParameter.IGUAL, status));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LiberacaoReceita.PROP_EMPRESA), BuilderQueryCustom.QueryParameter.IN, estabelecimento));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroLiberacaoReceitaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaLiberacaoReceita");
    }
}
