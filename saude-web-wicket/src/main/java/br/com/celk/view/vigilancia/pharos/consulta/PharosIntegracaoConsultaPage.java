package br.com.celk.view.vigilancia.pharos.consulta;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.pharos.detalhes.PharosIntegracaoDetalhesPage;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PharosConsultaIntegracaoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PharosIntegracaoEnvioDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.AwsUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.pharos.PharosIntegracaoRegistro;
import org.apache.commons.lang.time.DateUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 1079
 */
public class PharosIntegracaoConsultaPage extends ConsultaPage<PharosIntegracaoRegistro, PharosConsultaIntegracaoDTOParam> {

    private PharosConsultaIntegracaoDTOParam param;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel<>(param = new PharosConsultaIntegracaoDTOParam()));

        PharosConsultaIntegracaoDTOParam proxy = on(PharosConsultaIntegracaoDTOParam.class);

        WebMarkupContainer containerFiltros = new WebMarkupContainer("containerFiltros");
        containerFiltros.setOutputMarkupId(true);
        containerFiltros.setVisible(true);
        containerFiltros.setEnabled(true);

        DropDown ddTipoIntegracao = DropDownUtil.getIEnumDropDown("tipoIntegracao", PharosIntegracaoRegistro.TipoIntegracao.values(), false, true);
        ddTipoIntegracao.setEnabled(true);
        ddTipoIntegracao.addRequiredClass();
        ddTipoIntegracao.setRequired(true);

        DropDown ddSituacao = DropDownUtil.getIEnumDropDown("situacao", PharosIntegracaoRegistro.SituacaoIntegracao.getDefaultValues(), false, true);
        ddSituacao.setEnabled(true);
        ddSituacao.addRequiredClass();
        ddSituacao.setRequired(true);

        PnlDatePeriod periodo = new PnlDatePeriod(path(proxy.getPeriodo()));
        periodo.getDataInicial().setMinDate(new DateOption(DateUtils.addDays(DataUtil.getDataAtual(), -60)));
        periodo.getDataInicial().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        periodo.getDataFinal().setMinDate(new DateOption(DateUtils.addDays(DataUtil.getDataAtual(), -60)));
        periodo.getDataFinal().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        containerFiltros.add(ddTipoIntegracao, ddSituacao, periodo);

        form.add(containerFiltros);

        getLinkNovo().setVisible(false);
        setProcurarAoAbrir(false);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        PharosIntegracaoRegistro proxy = on(PharosIntegracaoRegistro.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("rotulo_codigo_saude"), proxy.getCodigoRegistro()));
        columns.add(createSortableColumn(bundle("rotulo_data_envio_pharos"), proxy.getDataEnvioPharos()));
        columns.add(createSortableColumn(bundle("rotulo_tipo_integracao"), proxy.getTipoIntegracao(), proxy.getDescricaoTipoIntegracao()));
        columns.add(createSortableColumn(bundle("rotulo_situacao"), proxy.getSituacao(), proxy.getDescricaoSituacaoIntegracao()));
        columns.add(createSortableColumn(bundle("rotulo_numero_chave_consulta_pharos"), proxy.getNumeroChaveConsultaPharos()));

        return columns;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<PharosIntegracaoEnvioDTO, PharosConsultaIntegracaoDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarIntegracaoPharos(dataPaging);
            }
        };
    }

    @Override
    public PharosConsultaIntegracaoDTOParam getParameters() {
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return PharosIntegracaoDetalhesPage.class;
    }


    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<PharosIntegracaoRegistro>() {
            @Override
            public void customizeColumn(final PharosIntegracaoRegistro rowObject) {
                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new PharosIntegracaoDetalhesPage(rowObject));
                    }
                });

                addAction(ActionType.ENVIAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactory.getBO(VigilanciaFacade.class).reintegrarRegistroPharos(rowObject);
                        getPageableTable().populate(target);
                    }
                }).setQuestionDialogBundleKey("reintegrarRegistro")
                .setIcon(Icon.REFRESH)
                .setTitleBundleKey(bundle("rotulo_reintegrar"))
                .setEnabled(isIntegrarPharos(rowObject));

            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return bundle("integracaoPharosConsulta");
    }

    public boolean isIntegrarPharos(PharosIntegracaoRegistro rowObject) {
        return (PharosIntegracaoRegistro.SituacaoIntegracao.isErro(rowObject.getSituacao())) && AwsUtils.isIntegracaoPharosHabilitada();
    }
}
