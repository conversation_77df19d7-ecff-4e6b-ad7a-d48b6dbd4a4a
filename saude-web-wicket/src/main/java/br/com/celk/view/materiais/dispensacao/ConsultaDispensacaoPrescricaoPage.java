package br.com.celk.view.materiais.dispensacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.ListaDispensacaoMedicamentoParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.QueryImpressaoDispensacaoPrescricaoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioImpressaoDispensacaoMedicamentoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 * Programa - 502
 */
@Private
public class ConsultaDispensacaoPrescricaoPage extends ConsultaPage<DispensacaoMedicamento, ListaDispensacaoMedicamentoParam> {

    private Empresa empresaOrigem;
    private List<Empresa> estabelecimento;
    private String nomePaciente;
    private Produto produto;
    private DatePeriod periodo;
    private Long numeroDispensacao;
    private Long receituario;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEstabelecimentoExecutante;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaEstabelecimentoExecutante = new AutoCompleteConsultaEmpresa("estabelecimento"));
        autoCompleteConsultaEstabelecimentoExecutante.setValidaUsuarioEmpresa(true);
        autoCompleteConsultaEstabelecimentoExecutante.setMultiplaSelecao(true);
        
        form.add(new AutoCompleteConsultaEmpresa("empresaOrigem"));
        form.add(new InputField("nomePaciente"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new PnlDatePeriod("periodo"));
        form.add(new InputField("numeroDispensacao"));
        form.add(new InputField("receituario"));
        getLinkNovo().setVisible(false);
        setExibeExpandir(true);
        
        Usuario usuario = SessaoAplicacaoImp.getInstance().<Usuario>getUsuario();
        if (!usuario.isNivelAdminOrMaster() && estabelecimento == null){
            try {
                if (usuario.getEmpresasUsuario() == null) {
                    usuario.setEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                }
                List<Long> list = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getEmpresasUsuario();
                
                if(CollectionUtils.isNotNullEmpty(list)){
                    estabelecimento = LoadManager.getInstance(Empresa.class)
                            .addProperties(new HQLProperties(Empresa.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, list))
                            .start().getList();
                }
            } catch (SGKException ex) {
                Logger.getLogger(ConsultaDispensacaoPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(DispensacaoMedicamento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estabelecimento"), VOUtils.montarPath(DispensacaoMedicamento.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dispensacao"), VOUtils.montarPath(DispensacaoMedicamento.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("prescricao"), VOUtils.montarPath(DispensacaoMedicamento.PROP_RECEITUARIO, Receituario.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_NOME), VOUtils.montarPath(DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoReceita"), VOUtils.montarPath(DispensacaoMedicamento.PROP_TIPO_RECEITA, TipoReceita.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataDispensacao"), VOUtils.montarPath(DispensacaoMedicamento.PROP_DATA_DISPENSACAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidadeOrigem"), VOUtils.montarPath(DispensacaoMedicamento.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO)));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<DispensacaoMedicamento>() {

            @Override
            public void customizeColumn(final DispensacaoMedicamento rowObject) {
                addAction(ActionType.CONSULTAR, new IAction() {

                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesDispensacaoPrescricaoPage(rowObject));
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<DispensacaoMedicamento>() {
                    @Override
                    public DataReport action(DispensacaoMedicamento modelObject) throws ReportException {
                        if (modelObject.getTipoReceita() != null && TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO.equals(modelObject.getTipoReceita().getTipoReceita())) {
                            QueryImpressaoDispensacaoPrescricaoDTOParam param = new QueryImpressaoDispensacaoPrescricaoDTOParam();
                            param.setCodigoDispensacao(modelObject.getCodigo());
                            return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).impressaoDispensacaoPrescricao(param);
                        } else {
                            RelatorioImpressaoDispensacaoMedicamentoDTOParam param = new RelatorioImpressaoDispensacaoMedicamentoDTOParam();
                            param.setCodigosDispensasaoMedicamento(Arrays.asList(modelObject.getCodigo()));
                            return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioImpressaoDispensacaoMedicamento(param);
                        }
                    }

                }).setEnabled(existsItemPermissaoComprovante(rowObject));

                Long codigoReceituario = null;

                if (rowObject.getReceituario() != null) {
                    codigoReceituario = rowObject.getReceituario().getCodigo();
                }

                if (codigoReceituario != null) {
                    final List<ReceituarioItem> itens = buscarQuantidadesItensReceituario(codigoReceituario);

                    for (ReceituarioItem item : itens) {
                        if (item != null && item.getProduto() != null) {
                            List<DispensacaoMedicamentoItem> dispensacaoMedicamentoItems = buscarDispensacoesDoItemNoReceituario(
                                    rowObject.getUsuarioCadsusDestino(),
                                    item.getProduto(),
                                    rowObject.getReceituario());

                            Double totalDispensado = calcularTotalDispensado(dispensacaoMedicamentoItems);

                            atribuirZeroQuantidadeIgualNulo(item);

                            long resultado = subtrairQuantidadeDispensadaDaPrescrita(item, totalDispensado.longValue());

                            if (resultado > 0) {
                                addAction(ActionType.WARN, new IAction() {
                                    @Override
                                    public void action(AjaxRequestTarget target) throws ValidacaoException {
                                        throw new ValidacaoException(BundleManager.getString(("msgExistemMedicamentosPrescritosNaoRetirados")));
                                    }
                                });
                                break;
                            }
                        }

                    }
                }
            }
        };
    }

    private List<DispensacaoMedicamentoItem> buscarDispensacoesDoItemNoReceituario(UsuarioCadsus usuarioCadsus,
                                                                                   Produto produto,
                                                                                   Receituario receituario) {
        return LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                .addProperties(new HQLProperties(DispensacaoMedicamentoItem.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, usuarioCadsus.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_PRODUTO, produto.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(
                        VOUtils.montarPath(
                                DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO,
                                DispensacaoMedicamento.PROP_RECEITUARIO),
                        receituario.getCodigo())
                )
                .start().getList();
    }

    private Double calcularTotalDispensado(List<DispensacaoMedicamentoItem> itens) {
        Double totalDispensado = 0D;

        if (itens != null && itens.size() > 0) {
            for (DispensacaoMedicamentoItem item : itens) {
                totalDispensado += item.getQuantidadeDispensada();
            }
        }
        return totalDispensado;
    }

    private List<ReceituarioItem> buscarQuantidadesItensReceituario(Long codigoReceituario) {
        return LoadManager.getInstance(ReceituarioItem.class)
                .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_RECEITUARIO, QueryCustom.QueryCustomParameter.IGUAL, codigoReceituario))
                .start()
                .getList();
    }

    private void atribuirZeroQuantidadeIgualNulo(ReceituarioItem item) {
        if (item.getQuantidadePrescrita() == null) {
            item.setQuantidadePrescrita(0L);
        }
        if (item.getQuantidadeDispensada() == null) {
            item.setQuantidadeDispensada(0L);
        }
    }

    private long subtrairQuantidadeDispensadaDaPrescrita(ReceituarioItem item, Long totalDispensado) {
        return item.getQuantidadePrescrita() - totalDispensado;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<DispensacaoMedicamento, ListaDispensacaoMedicamentoParam>() {

            @Override
            public DataPagingResult<DispensacaoMedicamento> executeQueryPager(DataPaging<ListaDispensacaoMedicamentoParam> dataPaging) throws DAOException, ValidacaoException {
                try {
                    dataPaging.getParam().setPropSort(((SingleSortState<String>) getPagerProvider().getSortState()).getSort().getProperty());
                    dataPaging.getParam().setAscending(((SingleSortState) getPagerProvider().getSortState()).getSort().isAscending());
                    return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).getListaDispensacaoMedicamento(dataPaging);
                } catch (SGKException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return null;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(DispensacaoMedicamento.PROP_CODIGO, false);
            }
        };
    }

    @Override
    public ListaDispensacaoMedicamentoParam getParameters() {
        ListaDispensacaoMedicamentoParam param = new ListaDispensacaoMedicamentoParam();
        
        param.setTruncDate(false);
        param.setEmpresa(empresaOrigem);
        param.setEstabelecimentoList(estabelecimento);
        param.setNomeUsuario(nomePaciente);
        param.setNumeroDispensacao(numeroDispensacao);
        param.setProduto(produto);
        param.setDatePeriod(periodo);
        param.setReceituario(receituario);

        return param;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaDispensacaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDispensacaoPrescricao");
    }

    private boolean existsItemPermissaoComprovante(DispensacaoMedicamento dispensacaoMedicamento) {
        return LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, dispensacaoMedicamento))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_COMPROVANTE), RepositoryComponentDefault.SIM_LONG))
                .exists();
    }

}
