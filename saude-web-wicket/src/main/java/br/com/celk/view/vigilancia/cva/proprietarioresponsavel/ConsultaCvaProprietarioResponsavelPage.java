package br.com.celk.view.vigilancia.cva.proprietarioresponsavel;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.cva.proprietarioresponsavel.CvaProprietarioResponsavel;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 601
 */
@Private
public class ConsultaCvaProprietarioResponsavelPage extends ConsultaPage<CvaProprietarioResponsavel, List<BuilderQueryCustom.QueryParameter>> {

    private String proprietarioResponsavel;
    private String cpf;
    private String rg;
    private Long tipoProprietarioResponsavel;
    private DropDown<Long> dropDownTipoPropResp;

    public ConsultaCvaProprietarioResponsavelPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("proprietarioResponsavel"));
        form.add(new InputField("rg"));
        form.add(new InputField("cpf"));
        form.add(getDropDownTipoPropResp());
        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        CvaProprietarioResponsavel proxy = on(CvaProprietarioResponsavel.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("proprietarioResponsavel"), proxy.getProprietarioResponsavel()));
        columns.add(createColumn(BundleManager.getString("cpfCnpj"), proxy.getCnpjCpfFormatado()));
        columns.add(createSortableColumn(BundleManager.getString("rg"), proxy.getRg()));
        columns.add(createSortableColumn(BundleManager.getString("endereco"), proxy.getVigilanciaEndereco().getLogradouro(), proxy.getEnderecoFormatadoEstrangeiro()));

        return columns;
    }

    public DropDown<Long> getDropDownTipoPropResp() {
        if (dropDownTipoPropResp == null) {
            dropDownTipoPropResp = new DropDown<Long>("tipoProprietarioResponsavel");
            dropDownTipoPropResp.addChoice(null, BundleManager.getString("todos"));
            dropDownTipoPropResp.addChoice(CvaProprietarioResponsavel.TipoProprietarioResponsavel.COMUM.value(), bundle("comum"));
            dropDownTipoPropResp.addChoice(CvaProprietarioResponsavel.TipoProprietarioResponsavel.CUIDADOR.value(), bundle("cuidador"));
            dropDownTipoPropResp.addChoice(CvaProprietarioResponsavel.TipoProprietarioResponsavel.ACUMULADOR.value(), bundle("acumulador"));
        }
        return dropDownTipoPropResp;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<CvaProprietarioResponsavel>() {
            @Override
            public void customizeColumn(final CvaProprietarioResponsavel rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<CvaProprietarioResponsavel>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaProprietarioResponsavel modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCvaProprietarioResponsavelPage(rowObject, false, true));
                    }
                });
                
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<CvaProprietarioResponsavel>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaProprietarioResponsavel modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesCvaProprietarioResponsavelPage(rowObject));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<CvaProprietarioResponsavel>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaProprietarioResponsavel modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return CvaProprietarioResponsavel.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(CvaProprietarioResponsavel.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_LOGRADOURO),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_BAIRRO),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CEP),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_CODIGO),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_DESCRICAO),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_NUMERO_LOGRADOURO),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_COMPLEMENTO_LOGRADOURO),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_PROPRIETARIO_RESPONSAVEL),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_CPF),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_RG),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_TIPO_PROPRIETARIO_RESPONSAVEL),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_FOTO, GerenciadorArquivo.PROP_CODIGO),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_FOTO, GerenciadorArquivo.PROP_CAMINHO),
                            VOUtils.montarPath(CvaProprietarioResponsavel.PROP_PAIS_NASCIMENTO, Pais.PROP_DESCRICAO),
                        });
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(CvaProprietarioResponsavel.PROP_PROPRIETARIO_RESPONSAVEL, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        String cpfFilter = StringUtil.getDigits(this.cpf);
        String rgFilter = StringUtil.getDigits(this.rg);
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CvaProprietarioResponsavel.PROP_PROPRIETARIO_RESPONSAVEL), BuilderQueryCustom.QueryParameter.ILIKE, proprietarioResponsavel));
        parameters.add(new QueryCustom.QueryCustomParameter(CvaProprietarioResponsavel.PROP_TIPO_PROPRIETARIO_RESPONSAVEL , tipoProprietarioResponsavel));
        if (!"".equals(cpfFilter)) {
            parameters.add(new QueryCustom.QueryCustomParameter(CvaProprietarioResponsavel.PROP_CPF, cpfFilter));
        }
        if (!"".equals(rgFilter)) {
            parameters.add(new QueryCustom.QueryCustomParameter(CvaProprietarioResponsavel.PROP_RG, rgFilter));
        }
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroCvaProprietarioResponsavelPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaProprietarioResponsavel");
    }
}
