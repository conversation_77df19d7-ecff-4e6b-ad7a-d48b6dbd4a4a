package br.com.celk.view.vigilancia.financeiro;

import br.com.celk.bo.vigilancia.interfaces.dto.ConsultaVigilanciaFinanceiroDTOParam;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.externo.template.base.RequerimentosVigilanciaPage;
import br.com.celk.view.vigilancia.financeiro.dialog.DlgAnexoComprovantePagamentoBoleto;
import br.com.celk.view.vigilancia.financeiro.dialog.DlgAnexoComprovantePagamentoRequerimento;
import br.com.celk.view.vigilancia.financeiro.dialog.DlgCancelamentoFinanceiroVigilancia;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 * Created by Leonardo Daros.
 * Programa - 947
 */
public class ConsultaVigilanciaFinanceiroPage extends ConsultaPage<VigilanciaFinanceiro, ConsultaVigilanciaFinanceiroDTOParam> {

    private Long status;
    private Long visualizarIsentos;
    private Long codigo;
    private String descricaoPagador;
    private Long processo;
    private DatePeriod periodoEmissao;
    private DatePeriod periodoVencimento;
    private DatePeriod periodoPagamento;
    private ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private DlgAnexoComprovantePagamentoRequerimento dlgAnexoComprovantePagamentoRequerimento;
    private DlgAnexoComprovantePagamentoBoleto dlgAnexoComprovantePagamentoBoleto;
    private DropDown dropDownStatus;


    public ConsultaVigilanciaFinanceiroPage() {
        super();
    }

    @Override
    public void initForm(Form form) {
        if (configuracaoVigilanciaFinanceiro == null) {
            try {
                configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
            } catch (ValidacaoException e) {
                Logger.getLogger(ConsultaVigilanciaFinanceiroPage.class.getName()).log(Level.SEVERE, null, e);
            }
        }
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField("codigo"));
        form.add(new InputField("processo"));
        form.add(dropDownStatus = DropDownUtil.getIEnumDropDown("status", VigilanciaFinanceiro.Status.values(), true, "Todos"));
        DropDown<Long> dropDownVisualizarIsentos = DropDownUtil.getNaoSimLongDropDown("visualizarIsentos");
        form.add(dropDownVisualizarIsentos);
        dropDownVisualizarIsentos.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (RepositoryComponentDefault.SIM_LONG.equals(visualizarIsentos)) {
                    status = null;
                    dropDownStatus.setComponentValue(null);
                    target.add(dropDownStatus);
                }
            }
        });
        form.add(new PnlDatePeriod("periodoEmissao"));
        form.add(new PnlDatePeriod("periodoVencimento"));
        form.add(new PnlDatePeriod("periodoPagamento"));
        form.add(new InputField("descricaoPagador"));

        getLinkNovo().setVisible(false);
        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());
        if (configuracaoVigilanciaFinanceiro != null) {
            if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
                status = VigilanciaFinanceiro.Status.AGUARDANDO_PAGAMENTO.value();
            } else {
                status = VigilanciaFinanceiro.Status.EMITIDO.value();
            }
        }
    }

    @Override
    public ConsultaVigilanciaFinanceiroDTOParam getParameters() {
        ConsultaVigilanciaFinanceiroDTOParam parameters = new ConsultaVigilanciaFinanceiroDTOParam();

        parameters.setCodigo(codigo);
        parameters.setProcesso(processo);
        parameters.setDescricaoPagador(descricaoPagador);
        parameters.setPeriodoEmissao(periodoEmissao);
        parameters.setPeriodoVencimento(periodoVencimento);
        parameters.setPeriodoPagamento(periodoPagamento);
        parameters.setProcesso(processo);
        parameters.setStatus(status);
        parameters.setVisualizarIsentos(visualizarIsentos);

        return parameters;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<VigilanciaFinanceiro, ConsultaVigilanciaFinanceiroDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaVigilanciaFinanceiroDTOParam> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setAscending(((SingleSortState) getPagerProvider().getSortState()).getSort().isAscending());
                dataPaging.getParam().setPropSort(((SingleSortState<String>) getPagerProvider().getSortState()).getSort().getProperty());
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarVigilanciaFinanceiroPage(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                VigilanciaFinanceiro proxy = on(VigilanciaFinanceiro.class);
                return new SortParam(path(proxy.getCodigo()), false);
            }
        };
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {

        VigilanciaFinanceiro proxy = on(VigilanciaFinanceiro.class);
        ColumnFactory columnFactory = new ColumnFactory(VigilanciaFinanceiro.class);
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("numeroDocumentoAbv"), path(proxy.getCodigo())));
        columns.add(columnFactory.createColumn(BundleManager.getString("processo"), path(proxy.getDescricaoProcessoVinculado())));
        columns.add(columnFactory.createColumn(BundleManager.getString("pagador"), path(proxy.getBoleto().getDescricaoPagador())));
        columns.add(columnFactory.createColumn(BundleManager.getString("situacao"), path(proxy.getDescricaoSituacao())));
        columns.add(columnFactory.createColumn(BundleManager.getString("emissao"), path(proxy.getDataEmissao())));
        columns.add(columnFactory.createColumn(BundleManager.getString("vencimento"), path(proxy.getDataVencimento())));
        columns.add(columnFactory.createColumn(BundleManager.getString("valorSimboloReais"), path(proxy.getValor())));
        columns.add(columnFactory.createColumn(BundleManager.getString("pagamento"), path(proxy.getDataPagamento())));
        return columns;
    }

    private MultipleActionCustomColumn<VigilanciaFinanceiro> getCustomColumn() {
        return new MultipleActionCustomColumn<VigilanciaFinanceiro>() {
            @Override
            public void customizeColumn(final VigilanciaFinanceiro rowObject) {

                addAction(ActionType.CANCELAR_SEM_CONFIRMACAO, rowObject, new IModelAction<VigilanciaFinanceiro>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws ValidacaoException, DAOException {
                        showConfirmacaoCancelamento(target, vigilanciaFinanceiro);
                    }
                }).setTitleBundleKey("cancelar")
                        .setEnabled(rowObject.getTokenBoleto() != null && (VigilanciaFinanceiro.Status.EMITIDO.value().equals(rowObject.getStatus()) || VigilanciaFinanceiro.Status.AGUARDANDO_PAGAMENTO.value().equals(rowObject.getStatus())))
                        .setVisible(isActionPermitted(Permissions.CANCELAR));


                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<VigilanciaFinanceiro>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesVigilanciaFinanceiroPage(vigilanciaFinanceiro.getCodigo(), ConsultaVigilanciaFinanceiroPage.class));
                    }
                }).setTitleBundleKey("detalhes")
                        .setVisible(isActionPermitted(Permissions.CONSULTAR));


                addAction(ActionType.IMPRIMIR, rowObject, new IModelAction<VigilanciaFinanceiro>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws ValidacaoException, DAOException {
                        if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
                            imprimirBoleto(target, vigilanciaFinanceiro);
                        } else if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.MEMORANDO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
                            imprimirMemorando(target, vigilanciaFinanceiro);
                        }
                    }
                }).setTitleBundleKey("imprimir")
                        .setIcon(Icon.CODE_BAR)
                        .setEnabled(enableSeBoleto(rowObject) || enableSeMemorando())
                        .setVisible(isActionPermitted(Permissions.IMPRIMIR));

                ModelActionLinkPanel acaoAlterarVencimento = addAction(ActionType.SUBSTITUIR, rowObject, new IModelAction<VigilanciaFinanceiro>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaFinanceiro modelObject) throws ValidacaoException, DAOException {
                        if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
//                            if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
//                                addModal(target, dlgMotivoDataBoleto = new DlgMotivo2(newModalId(), bundle("solicitarNovaDataValidadeBoleto")) {
//                                    @Override
//                                    public void onConfirmar(AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException {
//                                        BOFactoryWicket.getBO(VigilanciaFacade.class).solicitarNovaDataVencimentoBoleto(rowObject.getRequerimentoVigilancia(), motivo);
//                                        getPageableTable().populate(target);
//                                    }
//
//                                    @Override
//                                    public Long getMaxLengthMotivo() {
//                                        return 500L;
//                                    }
//                                });
//                                dlgMotivoDataBoleto.show(target);
//                            }
                        }
                    }
                });
                acaoAlterarVencimento.setIcon(Icon.CALENDAR_EDIT).setTitleBundleKey("alterarVencimentoBoleto");
                acaoAlterarVencimento.setEnabled(false/*(rowObject.getRequerimentoVigilancia() != null ? !RequerimentoVigilancia.SituacaoAprovacao.COMPROVANTE_PAGAMENTO.value().equals(rowObject.getRequerimentoVigilancia().getSituacaoAprovacao()) : true) && rowObject.getTokenBoleto() != null*/);
                acaoAlterarVencimento.setVisible(ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca()) && isActionPermitted(Permissions.SUBSTITUIR));

                ModelActionLinkPanel acaoConsultaBanco = addAction(ActionType.RETORNO, rowObject, new IModelAction<VigilanciaFinanceiro>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarSituacaoBoleto(vigilanciaFinanceiro.getCodigo(), "Boleto atualizado manualmente através da Gestão Financeira");
                    }
                });
                acaoConsultaBanco.setEnabled(rowObject.getTokenBoleto() != null);
                acaoConsultaBanco.setVisible(isVisibleAcaoConsultaWS(Permissions.RETORNO));
                acaoConsultaBanco.setIcon(Icon.REFRESH);
                acaoConsultaBanco.setTitleBundleKey("consultarBanco");

                ModelActionLinkPanel acaoComprovantePagamento = addAction(ActionType.ANEXAR, rowObject, new IModelAction<VigilanciaFinanceiro>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaFinanceiro modelObject) throws ValidacaoException, DAOException {
                        if (modelObject.getRequerimentoVigilancia() != null) {
                            initDlgComprovantePagamentoRequerimento(target, modelObject.getRequerimentoVigilancia(), modelObject);
                        } else {
                            initDlgComprovantePagamentoBoleto(target, modelObject);
                        }
                    }
                });
                acaoComprovantePagamento.setEnabled(enableComprovantePagamento(rowObject));
                acaoComprovantePagamento.setVisible(isActionPermitted(Permissions.ANEXAR) && ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca()));
                acaoComprovantePagamento.setIcon(Icon.MONEY);
                acaoComprovantePagamento.setTitleBundleKey("comprovantePagamento");

            }

        };
    }

    private boolean isVisibleAcaoConsultaWS(Permissions retorno) {
        String tipoIntegracao = null;
        try {
            tipoIntegracao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("TipoIntegracao_BoletoAPI");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }
        return ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())
                && isActionPermitted(retorno)
                && "WEBSERVICE".equals(tipoIntegracao);
    }

    private void initDlgComprovantePagamentoRequerimento(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia, VigilanciaFinanceiro vigilanciaFinanceiro) {
        if (dlgAnexoComprovantePagamentoRequerimento == null) {
            addModal(target, dlgAnexoComprovantePagamentoRequerimento = new DlgAnexoComprovantePagamentoRequerimento(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    getPageableTable().update(target);
                }
            });
        }
        dlgAnexoComprovantePagamentoRequerimento.show(target, requerimentoVigilancia, vigilanciaFinanceiro);
    }

    private void initDlgComprovantePagamentoBoleto(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) {
        if (dlgAnexoComprovantePagamentoBoleto == null) {
            addModal(target, dlgAnexoComprovantePagamentoBoleto = new DlgAnexoComprovantePagamentoBoleto(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    getPageableTable().update(target);
                }
            });
        }
        dlgAnexoComprovantePagamentoBoleto.show(target, vigilanciaFinanceiro);
    }

    private boolean enableSeBoleto(VigilanciaFinanceiro rowObject) {
        return ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca()) &&
                (rowObject.getTokenBoleto() != null || rowObject.getAnexoBoleto() != null) && !VigilanciaFinanceiro.Status.CANCELADO.value().equals(rowObject.getStatus());
    }

    private boolean enableSeMemorando() {
        return ConfiguracaoVigilanciaFinanceiro.FormaCobranca.MEMORANDO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca());
    }

    private boolean enableComprovantePagamento(VigilanciaFinanceiro vigilanciaFinanceiro) {
        if (VigilanciaFinanceiro.Status.AGUARDANDO_PAGAMENTO.value().equals(vigilanciaFinanceiro.getStatus())) {
            if (RepositoryComponentDefault.NAO_LONG.equals(vigilanciaFinanceiro.getFlagComplementar())) {
                if (vigilanciaFinanceiro.getRequerimentoVigilancia() != null) {
                    return RequerimentoVigilancia.Origem.INTERNO.value().equals(vigilanciaFinanceiro.getRequerimentoVigilancia().getOrigem()) && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(vigilanciaFinanceiro.getRequerimentoVigilancia().getSituacao())
                            && (RequerimentoVigilancia.SituacaoAprovacao.EMISAO_BOLETO.value().equals(vigilanciaFinanceiro.getRequerimentoVigilancia().getSituacaoAprovacao()))
                            || (RequerimentoVigilancia.SituacaoAprovacao.DEVOLVIDO.value().equals(vigilanciaFinanceiro.getRequerimentoVigilancia().getSituacaoAprovacao())));
                } else {
                    return true;
                }
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    private void showConfirmacaoCancelamento(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) {
        DlgCancelamentoFinanceiroVigilancia dlgConfirmacao = new DlgCancelamentoFinanceiroVigilancia(newModalId(), vigilanciaFinanceiro) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, boolean cancelarRequermento) throws ValidacaoException, DAOException {
                vigilanciaFinanceiro.setDescricaoMotivoCancelamento(motivo);
                BOFactoryWicket.getBO(VigilanciaFacade.class).cancelarVigilanciaFinanceiro(vigilanciaFinanceiro, false, cancelarRequermento);
                getPageableTable().update(target);
            }
        };
        addModal(target, dlgConfirmacao);
        dlgConfirmacao.show(target);
    }

    private void imprimirMemorando(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws ValidacaoException {
        try {
            DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoMemorando(vigilanciaFinanceiro.getRequerimentoVigilancia());
            File file = File.createTempFile("memorando", ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());

            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(file));
            ajaxPreviewBlank.initiate(target, "Memorando.pdf", resourceStream);
        } catch (ReportException | IOException | JRException ex) {
            Loggable.log.error(ex.getMessage());
            throw new ValidacaoException(ex.getMessage());
        }
    }

    private void imprimirBoleto(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws ValidacaoException {
        String boletoBase64 = FinanceiroVigilanciaHelper.getBoletoBase64(Arrays.asList(vigilanciaFinanceiro));
        ajaxPreviewBlank.initiatePdfBase64(target, boletoBase64);
    }

    @Override
    public Class getCadastroPage() {
        return RequerimentosVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("gestaoFinanceira");
    }

}
