package br.com.celk.view.vigilancia.talonario;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.profissional.autocomplete.AutoCompleteConsultaProfissionalVigilancia;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.TalonarioReceitaTalidomida;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 911
 */
public class ConsultaTalonarioReceitaTalidomidaPage extends ConsultaPage<TalonarioReceitaTalidomida, List<BuilderQueryCustom.QueryParameter>> {

    private Long status = (Long) TalonarioReceitaTalidomida.Status.DISPONIVEL.value();
    private Long numeracaoInicial;
    private Long numeracaoFinal;
    private VigilanciaProfissional vigilanciaProfissional;
    private Estabelecimento estabelecimento;
    private DropDown<Long> dropDownStatus;

    public ConsultaTalonarioReceitaTalidomidaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField<Long>("numeracaoInicial"));
        form.add(new InputField<Long>("numeracaoFinal"));
        form.add(getDropDownSituacao("status"));
        form.add(new AutoCompleteConsultaProfissionalVigilancia("vigilanciaProfissional"));
        form.add(new AutoCompleteConsultaEstabelecimento("estabelecimento"));
        setExibeExpandir(true);
    }

    public DropDown<Long> getDropDownSituacao(String id) {
        if (dropDownStatus == null) {
            dropDownStatus = new DropDown<Long>(id);
            dropDownStatus.addChoice(null, BundleManager.getString("ambos"));
            dropDownStatus.addChoice((Long) TalonarioReceitaTalidomida.Status.DISPONIVEL.value(), bundle("disponivel"));
            dropDownStatus.addChoice((Long) TalonarioReceitaTalidomida.Status.ENTREGUE.value(), bundle("entregue"));
        }
        return dropDownStatus;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        TalonarioReceitaTalidomida proxy = on(TalonarioReceitaTalidomida.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("numeracaoInicial"), proxy.getNumeracaoInicial()));
        columns.add(createColumn(bundle("numeracaoFinal"), proxy.getNumeracaoFinal()));
        columns.add(createColumn(bundle("status"), proxy.getDescricaoStatus()));
        columns.add(createColumn(bundle("estabelecimentoProfissional"), proxy.getDescricaoEstabelecimentoProfissional()));

        return columns;
    }
    
    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TalonarioReceitaTalidomida>() {
            @Override
            public void customizeColumn(TalonarioReceitaTalidomida rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<TalonarioReceitaTalidomida>() {
                    @Override
                    public void action(AjaxRequestTarget target, TalonarioReceitaTalidomida modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().update(target);
                    }
                }).setEnabled(TalonarioReceitaTalidomida.Status.DISPONIVEL.value().equals(rowObject.getStatus()));
            }
        };
    }


    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return TalonarioReceitaTalidomida.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(TalonarioReceitaTalidomida.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(TalonarioReceitaTalidomida.PROP_VIGILANCIA_PROFISSIONAL, VigilanciaProfissional.PROP_NOME_PROFISSIONAL),
                                VOUtils.montarPath(TalonarioReceitaTalidomida.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RAZAO_SOCIAL),
                        });
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TalonarioReceitaTalidomida.PROP_NUMERACAO_INICIAL, true);
            }
        };
    }


    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TalonarioReceitaTalidomida.PROP_VIGILANCIA_PROFISSIONAL), vigilanciaProfissional));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TalonarioReceitaTalidomida.PROP_ESTABELECIMENTO), estabelecimento));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TalonarioReceitaTalidomida.PROP_STATUS), status));

        if (numeracaoInicial != null && numeracaoFinal == null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TalonarioReceitaTalidomida.PROP_NUMERACAO_INICIAL), BuilderQueryCustom.QueryParameter.IGUAL, numeracaoInicial));

        } else if (numeracaoInicial == null && numeracaoFinal != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TalonarioReceitaTalidomida.PROP_NUMERACAO_FINAL), BuilderQueryCustom.QueryParameter.IGUAL, numeracaoFinal));
        }
        if (numeracaoInicial != null && numeracaoFinal != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TalonarioReceitaTalidomida.PROP_NUMERACAO_INICIAL), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, numeracaoInicial));
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TalonarioReceitaTalidomida.PROP_NUMERACAO_FINAL), BuilderQueryCustom.QueryParameter.MENOR_IGUAL, numeracaoFinal));
        }

        status = (Long) TalonarioReceitaTalidomida.Status.DISPONIVEL.value();
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTalonarioReceitaTalidomidaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTalonarioReceitaTalidomida");
    }
}
