package br.com.celk.view.materiais.emprestimo.lancamentos.registrodevolucaoemprestimo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.emprestimo.interfaces.dto.ConsultaDevolucaoEmprestimoDTOParam;
import br.com.celk.bo.emprestimo.interfaces.facade.EmprestimoFacade;
import br.com.celk.bo.emprestimo.interfaces.facade.EmprestimoReportFacade;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.materiais.emprestimo.lancamentos.registrodevolucaoemprestimo.dialog.DlgCancelarDevolucaoEmprestimo;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo;
import br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimo;
import br.com.ksisolucoes.vo.emprestimo.TipoDevolucao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 471
 */
@Private
public class ConsultaDevolucaoEmprestimoPage extends ConsultaPage<DevolucaoEmprestimo, ConsultaDevolucaoEmprestimoDTOParam> {

    private ConsultaDevolucaoEmprestimoDTOParam param;
    private DlgCancelarDevolucaoEmprestimo dlgCancelarDevolucaoEmprestimo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel<ConsultaDevolucaoEmprestimoDTOParam>(param = new ConsultaDevolucaoEmprestimoDTOParam()));

        form.add(new InputField<String>("pacienteEstabelecimento"));
        form.add(new AutoCompleteConsultaProduto("produto", false));
        form.add(new PnlDatePeriod("periodo"));
        form.add(new InputField<Long>("codigo"));
        form.add(DropDownUtil.getIEnumDropDown("situacao", DevolucaoEmprestimo.Status.values()));
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DevolucaoEmprestimo proxy = on(DevolucaoEmprestimo.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("codigo"), proxy.getCodigo()));
        columns.add(new DateTimeColumn<DevolucaoEmprestimo>(bundle("data"), path(proxy.getDataDevolucao()), path(proxy.getDataDevolucao())).setPattern("dd/MM/yyyy"));
        columns.add(createSortableColumn(bundle("pacienteEstabelecimento"), proxy.getNomePacienteEstabelecimento()));
        columns.add(createSortableColumn(bundle("tipoDevolucao"), proxy.getTipoDevolucao().getDescricao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<DevolucaoEmprestimo>() {
            @Override
            public void customizeColumn(DevolucaoEmprestimo rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<DevolucaoEmprestimo>() {

                    @Override
                    public void action(AjaxRequestTarget target, DevolucaoEmprestimo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new RegistroDevolucaoEmprestimoStep1Page(modelObject));
                    }

                }).setEnabled(DevolucaoEmprestimo.Status.NORMAL.value().equals(rowObject.getStatus())
                        && Data.adjustRangeHour(DataUtil.getDataAtual()).equals(Data.adjustRangeHour(rowObject.getDataCadastro())));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<DevolucaoEmprestimo>() {

                    @Override
                    public void action(AjaxRequestTarget target, DevolucaoEmprestimo modelObject) throws ValidacaoException, DAOException {
                        initDlgConfirmacaoCancelar(target, modelObject);
                    }

                }).setQuestionDialogBundleKey(null).setEnabled(DevolucaoEmprestimo.Status.NORMAL.value().equals(rowObject.getStatus())
                        && Data.adjustRangeHour(DataUtil.getDataAtual()).equals(Data.adjustRangeHour(rowObject.getDataCadastro())));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DevolucaoEmprestimo>() {

                    @Override
                    public void action(AjaxRequestTarget target, DevolucaoEmprestimo devolucaoEmprestimo) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesDevolucaoEmprestimoPage(devolucaoEmprestimo));
                    }

                }).setTitleBundleKey("visualizarEmprestimos");

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<DevolucaoEmprestimo>() {
                    @Override
                    public DataReport action(DevolucaoEmprestimo devolucaoEmprestimo) throws ReportException {
                        return BOFactoryWicket.getBO(EmprestimoReportFacade.class).comprovanteDevolucao(devolucaoEmprestimo);
                    }
                }).setTitleBundleKey("comprovante")
                        .setEnabled(!DevolucaoEmprestimo.Status.CANCELADA.value().equals(rowObject.getStatus())
                                && TipoDevolucao.DevolucaoTipo.SAIDA.value().equals(rowObject.getTipoDevolucao().getTipoDevolucao()));
            }
        };
    }

    private void initDlgConfirmacaoCancelar(AjaxRequestTarget target, DevolucaoEmprestimo modelObject) {
        if (dlgCancelarDevolucaoEmprestimo == null) {
            addModal(target, dlgCancelarDevolucaoEmprestimo = new DlgCancelarDevolucaoEmprestimo(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, DevolucaoEmprestimo devolucaoEmprestimo, String motivoCancelamento) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(EmprestimoFacade.class).cancelarDevolucaoEmprestimo(devolucaoEmprestimo, motivoCancelamento);

                    getPageableTable().update(target);
                }
            });
        }
        dlgCancelarDevolucaoEmprestimo.show(target, modelObject);
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<DevolucaoEmprestimo, ConsultaDevolucaoEmprestimoDTOParam>() {

            @Override
            public DataPagingResult<DevolucaoEmprestimo> executeQueryPager(DataPaging<ConsultaDevolucaoEmprestimoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(EmprestimoFacade.class).getConsultaDevolucaoEmprestimoQueryPager(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(LancamentoEmprestimo.PROP_CODIGO, false);
            }

            @Override
            public void customizeParam(ConsultaDevolucaoEmprestimoDTOParam param) {
                SingleSortState<String> sortState = (SingleSortState) getPagerProvider().getSortState();

                param.setCampoOrdenacao(sortState.getSort().getProperty());
                param.setTipoOrdenacao(sortState.getSort().isAscending() ? "asc" : "desc");

                if (param.getSituacao() == null) {
                    param.setSituacao(DevolucaoEmprestimo.Status.NORMAL.value());
                }
            }
        };
    }

    @Override
    public ConsultaDevolucaoEmprestimoDTOParam getParameters() {
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return RegistroDevolucaoEmprestimoStep1Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDevolucaoEmprestimo");
    }
}
