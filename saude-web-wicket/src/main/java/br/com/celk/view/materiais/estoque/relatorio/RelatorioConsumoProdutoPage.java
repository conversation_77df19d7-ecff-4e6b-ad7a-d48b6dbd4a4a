package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.checkbox.CheckBox;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioConsumoProdutoDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.text.ParseException;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 53
 */
@Private
public class RelatorioConsumoProdutoPage extends RelatorioPage<RelatorioConsumoProdutoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaDispensadora;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaOrigem;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaLocalizacao autoCompleteConsultaLocalizacao;
    private AutoCompleteConsultaCentroCusto autoCompleteConsultaCentroCusto;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresaDispensadora = new AutoCompleteConsultaEmpresa("empresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(autoCompleteConsultaEmpresaOrigem = new AutoCompleteConsultaEmpresa("empresaDestino"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(autoCompleteConsultaLocalizacao = new AutoCompleteConsultaLocalizacao("localizacao"));
        form.add(autoCompleteConsultaCentroCusto = new AutoCompleteConsultaCentroCusto("centroCusto"));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getDropDownTipoRelatorio());
        form.add(getDropDownTipoOrdenacao());
        form.add(getDropDownFormaApresentacao());
        form.add(new CheckBox("quebrarPagina"));
        form.add(getDropDownAgruparEmpresa());
        form.add(getDropDownTipoArquivo());

        autoCompleteConsultaEmpresaDispensadora.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresaDispensadora.setOperadorValor(true);
        autoCompleteConsultaEmpresaOrigem.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresaOrigem.setOperadorValor(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setOperadorValor(true);
        autoCompleteConsultaLocalizacao.setMultiplaSelecao(true);
        autoCompleteConsultaLocalizacao.setOperadorValor(true);
        autoCompleteConsultaCentroCusto.setMultiplaSelecao(true);
        autoCompleteConsultaCentroCusto.setOperadorValor(true);

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioConsumoProdutoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioConsumoProdutoDTOParam param) throws ReportException {
        try {
            if (Data.getMesDiferenca(param.getDataInicial(), param.getDataFinal()) > 11 && param.getTipoRelatorio().equals(ReportProperties.MAPA)) {
                throw new ReportException(BundleManager.getString("periodoDeveConterApenas12Meses"));
            }
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        OperadorValor<List<GrupoProduto>> grupoProdutoVazio = new OperadorValor<List<GrupoProduto>>();
        param.setGrupoProduto(grupoProdutoVazio);
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioConsumoProduto(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consumoProdutos");
    }

    public DropDown getDropDownTipoRelatorio() {
        DropDown dropDown = new DropDown("tipoRelatorio");
        dropDown.addChoice(ReportProperties.NORMAL, BundleManager.getString("detalhado"));
        dropDown.addChoice(ReportProperties.MAPA, BundleManager.getString("mapa"));

        return dropDown;
    }

    public DropDown getDropDownTipoOrdenacao() {
        DropDown dropDown = new DropDown("ordenacao");
        dropDown.addChoice(BundleManager.getString("produto"), BundleManager.getString("produto"));
        dropDown.addChoice(BundleManager.getString("quantidade"), BundleManager.getString("quantidade"));
        dropDown.addChoice(BundleManager.getString("valorUnitario"), BundleManager.getString("valorUnitario"));
        dropDown.addChoice(BundleManager.getString("valor"), BundleManager.getString("valor"));

        return dropDown;
    }

    public DropDown getDropDownFormaApresentacao() {
        DropDown dropDown = new DropDown("formaApresentacao");
        dropDown.addChoice(ReportProperties.GERAL, BundleManager.getString("geral"));
        dropDown.addChoice(ReportProperties.AGRUPAR_OPERACAO, BundleManager.getString("unidadeOrigemGrupo"));
        dropDown.addChoice(ReportProperties.AGRUPAR_GRUPO, BundleManager.getString("grupoProduto"));
        dropDown.addChoice(ReportProperties.AGRUPAR_CENTRO_CUSTO, BundleManager.getString("centroCusto"));

        return dropDown;
    }

    public DropDown getDropDownTipoArquivo() {
        return DropDownUtil.getTipoRelatorioDropDownXls2("tipoArquivo");
    }

    public DropDown getDropDownAgruparEmpresa() {
        DropDown dropDown = new DropDown("agruparUnidade");
        dropDown.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("nao"));
        dropDown.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("sim"));

        return dropDown;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }
}
