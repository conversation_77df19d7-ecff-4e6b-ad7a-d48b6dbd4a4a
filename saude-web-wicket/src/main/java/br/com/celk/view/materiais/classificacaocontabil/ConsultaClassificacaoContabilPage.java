package br.com.celk.view.materiais.classificacaocontabil;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.ClassificacaoContabil;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 422
 */
@Private
public class ConsultaClassificacaoContabilPage extends ConsultaPage<ClassificacaoContabil, List<BuilderQueryCustom.QueryParameter>> {

    private Long codigo;
    private String descricao;

    public ConsultaClassificacaoContabilPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<Long>("codigo"));
        form.add(new InputField<String>("descricao"));
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ClassificacaoContabil proxy = on(ClassificacaoContabil.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("codigo"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));

        return columns;
    }

    private CustomColumn<ClassificacaoContabil> getCustomColumn() {
        return new CustomColumn<ClassificacaoContabil>() {
            @Override
            public Component getComponent(String componentId, final ClassificacaoContabil rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroClassificacaoContabilPage(rowObject, false, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroClassificacaoContabilPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return ClassificacaoContabil.class;
            }

            @Override
            public String[] getProperties() {
                return new HQLProperties(ClassificacaoContabil.class).getProperties();
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ClassificacaoContabil.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(ClassificacaoContabil.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IGUAL, codigo));
        parameters.add(new QueryCustom.QueryCustomParameter(ClassificacaoContabil.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroClassificacaoContabilPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("classificacaoProdutoConsulta");
    }
}
