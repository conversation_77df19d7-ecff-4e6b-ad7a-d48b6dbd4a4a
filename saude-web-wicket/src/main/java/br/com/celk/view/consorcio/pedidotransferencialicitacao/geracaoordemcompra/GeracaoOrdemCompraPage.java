package br.com.celk.view.consorcio.pedidotransferencialicitacao.geracaoordemcompra;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 360
 */
@Private
public class GeracaoOrdemCompraPage extends BasePage {

    private Long pedido;
    private Empresa consorciado;
    private DatePeriod periodo;
    private MultiSelectionTableOld tblPedidosTransferenciaLicitacao;
    private List<PedidoTransferenciaLicitacao> pedidoTransferenciaLicitacaoList;

    @Override
    protected void postConstruct() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(new InputField("pedido"));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new PnlDatePeriod("periodo"));

        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                carregarListaPedidosTransferenciaLicitacao(target);
            }
        });

        form.add(tblPedidosTransferenciaLicitacao = new MultiSelectionTableOld("tblPedidosTransferenciaLicitacao", getColumns(), getCollectionProvider()));
        tblPedidosTransferenciaLicitacao.populate();

        form.add(new AbstractAjaxButton("btnGerarOrdemCompra") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                gerarOrdemCompraItensSelecionados(target);
            }
        });

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        PedidoTransferenciaLicitacao proxy = on(PedidoTransferenciaLicitacao.class);

        columns.add(createColumn(bundle("codigo"), proxy.getCodigo()));
        columns.add(createColumn(bundle("consorciado"), proxy.getEmpresaConsorciado().getDescricao()));
        columns.add(createColumn(bundle("cadastro"), proxy.getDataCadastro()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return pedidoTransferenciaLicitacaoList;
            }
        };
    }

    private void carregarListaPedidosTransferenciaLicitacao(AjaxRequestTarget target) {
        pedidoTransferenciaLicitacaoList = LoadManager.getInstance(PedidoTransferenciaLicitacao.class)
                .addProperties(new HQLProperties(PedidoTransferenciaLicitacao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacao.PROP_CODIGO, pedido))
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO, consorciado))
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacao.PROP_DATA_CADASTRO, periodo))
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacao.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.ABERTO.value(), PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.ENCAMINHADO.value(), PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.SEPARADO.value())))
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacao.PROP_FLAG_ORDEM_COMPRA, BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.NAO_LONG, HQLHelper.RESOLVE_CHAR_TYPE, RepositoryComponentDefault.NAO_LONG))
                .addSorter(new QueryCustom.QueryCustomSorter(PedidoTransferenciaLicitacao.PROP_CODIGO, QueryCustom.QueryCustomSorter.CRESCENTE))
                .start().getList();

        if (target != null) {
            tblPedidosTransferenciaLicitacao.update(target);
        }
    }

    private void gerarOrdemCompraItensSelecionados(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        List<PedidoTransferenciaLicitacao> pedidoTransferenciaLicitacaoList = tblPedidosTransferenciaLicitacao.getSelectedObjects();
        if (!CollectionUtils.isNotNullEmpty(pedidoTransferenciaLicitacaoList)) {
            throw new ValidacaoException(BundleManager.getString("msgSelecionePeloMenosUmPedidoTransferencia"));
        }

        BOFactoryWicket.getBO(EstoqueEmpresaFacade.class).gerarOrdemCompra(pedidoTransferenciaLicitacaoList);

        info(target, BundleManager.getString("processoConcluidoComSucesso"));
        tblPedidosTransferenciaLicitacao.clearSelection(target);
        carregarListaPedidosTransferenciaLicitacao(target);
    }

    //<editor-fold defaultstate="collapsed" desc="GETTER'S AND SETTER'S">
    public Long getPedido() {
        return pedido;
    }
    
    public void setPedido(Long pedido) {
        this.pedido = pedido;
    }
    
    public Empresa getConsorciado() {
        return consorciado;
    }
    
    public void setConsorciado(Empresa consorciado) {
        this.consorciado = consorciado;
    }
    
    public DatePeriod getPeriodo() {
        return periodo;
    }
    
    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }
//</editor-fold>

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("geracaoOrdemCompra");
    }

}
