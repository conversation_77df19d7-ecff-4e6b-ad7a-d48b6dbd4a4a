package br.com.celk.view.materiais.compras.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.ResumoRelacaoOrdemCompraDTOParam;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 361
 */
public class ResumoRelacaoOrdemCompraPage extends RelatorioPage<ResumoRelacaoOrdemCompraDTOParam> {

    private RequiredPnlChoicePeriod requiredPnlChoicePeriod;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaPessoa("fornecedor"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(requiredPnlChoicePeriod = new RequiredPnlChoicePeriod("periodo"));
        requiredPnlChoicePeriod.setDefaultOutro();
        form.add(new InputField("nrPregao"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", ResumoRelacaoOrdemCompraDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("baseCalculo", ResumoRelacaoOrdemCompraDTOParam.BaseCalculo.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoResumo", ResumoRelacaoOrdemCompraDTOParam.TipoResumo.values()));
        
        form.add(new CheckBox("situacaoPendente"));
        form.add(new CheckBox("situacaoRecebida"));
        form.add(new CheckBox("situacaoCancelada"));
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoOrdemCompra");
    }

    @Override
    public Class<ResumoRelacaoOrdemCompraDTOParam> getDTOParamClass() {
        return ResumoRelacaoOrdemCompraDTOParam.class;
    }

    @Override
    public DataReport getDataReport(ResumoRelacaoOrdemCompraDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(MaterialBasicoFacade.class).relatorioResumoOrdemCompra(param);
    }
}