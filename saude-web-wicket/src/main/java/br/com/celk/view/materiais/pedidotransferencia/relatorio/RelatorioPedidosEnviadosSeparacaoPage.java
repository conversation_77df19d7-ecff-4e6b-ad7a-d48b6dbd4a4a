package br.com.celk.view.materiais.pedidotransferencia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaTipoVacina;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioPedidosEnviadosSeparacaoDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 115
 */
@Private
public class RelatorioPedidosEnviadosSeparacaoPage extends RelatorioPage<RelatorioPedidosEnviadosSeparacaoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaDestino;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaCentroCusto autoCompleteConsultaCentroCusto;
    private AutoCompleteConsultaTipoVacina autoCompleteConsultaTipoVacina;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresaDestino = new AutoCompleteConsultaEmpresa("empresa"));
        form.add(autoCompleteConsultaTipoVacina = new AutoCompleteConsultaTipoVacina("vacina"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                autoCompleteConsultaTipoVacina.setEnabled(false);
                target.add(autoCompleteConsultaTipoVacina);
            }
        });
        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                autoCompleteConsultaTipoVacina.setEnabled(true);
                target.add(autoCompleteConsultaTipoVacina);
            }
        });
        try {
            autoCompleteConsultaTipoVacina.add(new ConsultaListener<TipoVacina>() {
                @Override
                public void valueObjectLoaded(AjaxRequestTarget target, TipoVacina object) {
                    autoCompleteConsultaProduto.setEnabled(false);
                    target.add(autoCompleteConsultaProduto);
                }
            });
            autoCompleteConsultaTipoVacina.add(new RemoveListener<TipoVacina>() {
                @Override
                public void valueObjectUnLoaded(AjaxRequestTarget target, TipoVacina object) {
                    autoCompleteConsultaProduto.setEnabled(true);
                    target.add(autoCompleteConsultaProduto);
                }
            });
        } catch (Exception e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(autoCompleteConsultaCentroCusto = new AutoCompleteConsultaCentroCusto("centroCusto"));
        form.add(getDropDownFormaApresentacao());
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getDropDownTipoRelatorio());

        autoCompleteConsultaEmpresaDestino.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setIncluirInativos(true);
        autoCompleteConsultaCentroCusto.setMultiplaSelecao(true);
        autoCompleteConsultaCentroCusto.setOperadorValor(true);

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresaDestino.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioPedidosEnviadosSeparacaoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioPedidosEnviadosSeparacaoDTOParam param) throws ReportException {
        param.setStatusItem(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.ABERTO.value());

        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioPedidosEnviadosSeparacao(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("pedidosEnviadosSeparacao");
    }

    public DropDown getDropDownFormaApresentacao() {
        DropDown dropDown = new DropDown("formaApresentacao");
        DropDownUtil.setEnumChoices(dropDown, RelatorioPedidosEnviadosSeparacaoDTOParam.FormaApresentacao.values());
        return dropDown;
    }

    public DropDown<String> getDropDownTipoRelatorio() {
        DropDown dropDown = new DropDown("tipoRelatorio");
        DropDownUtil.setEnumChoices(dropDown, RelatorioPedidosEnviadosSeparacaoDTOParam.TipoRelatorio.values());

        return dropDown;
    }
}
