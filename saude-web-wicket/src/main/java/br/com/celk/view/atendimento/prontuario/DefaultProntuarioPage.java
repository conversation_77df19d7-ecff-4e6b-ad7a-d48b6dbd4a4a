package br.com.celk.view.atendimento.prontuario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.bo.service.rest.assinaturadigital.AbstractAssinaturaDigitalHelper;
import br.com.celk.bo.service.rest.assinaturadigital.AbstractAssinaturaDigitalService;
import br.com.celk.bo.service.rest.assinaturadigital.bry.service.provedor.PscService;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.dialog.*;
import br.com.celk.component.dirtyforms.button.SubmitLink;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.jgrowl.JGrowl;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.notification.NotificationPanel;
import br.com.celk.component.popover.Popover;
import br.com.celk.component.window.Window;
import br.com.celk.io.FtpImageUtil;
import br.com.celk.resources.Resources;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.celk.template.TemplatePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.Util;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.atendimento.atencaobasica.ConsultaAtendimentoAtencaoBasicaPage;
import br.com.celk.view.atendimento.prontuario.button.NodeButton;
import br.com.celk.view.atendimento.prontuario.interfaces.IProntuarioController;
import br.com.celk.view.atendimento.prontuario.interfaces.NodeButtonEventListener;
import br.com.celk.view.atendimento.prontuario.model.LoadableAtendimentoModel;
import br.com.celk.view.atendimento.prontuario.nodes.FichaAcolhimentoNode;
import br.com.celk.view.atendimento.prontuario.nodes.IProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.FichaAcolhimentoPanel;
import br.com.celk.view.atendimento.prontuario.panel.dialog.*;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtil;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtilDTO;
import br.com.celk.view.atendimento.prontuario.registroorientacao.RegistroOrientacaoPage;
import br.com.celk.view.cadsus.usuariocadsus.dialog.DlgCadastroPaciente;
import br.com.celk.view.novidades.JGrowlNovidade;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.dto.AnexosPacienteDTO;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.export.PdfUtil;
import br.com.ksisolucoes.report.geral.interfaces.dto.LoadInterceptorNotExistsExameExameBpaApac;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioImpressaoAtendimentoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioImpressaoNotaAltaDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.factory.exception.FactoryException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.AtendimentoNotificacao;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.integracao.DocumentoAssinado;
import br.com.ksisolucoes.vo.prontuario.FichaAcolhimento;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.AtendimentoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EncaminhamentoTipo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import ch.lambdaj.Lambda;
import com.lowagie.text.DocumentException;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.Session;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;
import org.apache.wicket.feedback.FeedbackMessage;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.TransparentWebMarkupContainer;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.io.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 * <AUTHOR>
 */
@Private
public class DefaultProntuarioPage extends TemplatePage implements INotificationPanel {

    private static final String PANEL_ID = "nodePanel";
    private final List<NodeButtonEventListener> nodeButtonListeners = new ArrayList<NodeButtonEventListener>();
    private final List<NodesAtendimentoRef> nodeList = new ArrayList<NodesAtendimentoRef>();
    boolean vacinaEmDia;
    JGrowlNovidade jGrowlNovidade;
    JGrowl jGrowl;
    private NotificationPanel notificationPanel;
    private Form form;
    private WebMarkupContainer panelContainer;
    private IProntuarioNode activeNode;
    private Map<NodesAtendimentoRef, ValidacaoProcesso> validacoes = new EnumMap<NodesAtendimentoRef, ValidacaoProcesso>(NodesAtendimentoRef.class);
    private IProntuarioController prontuarioController;
    private DlgImpressaoObjectSignDigital<Atendimento> dlgConfirmacaoImpressaoProntuario;
    private DlgImpressaoObject<Atendimento> dlgConfirmacaoImpressaoNotaAlta;
    private DlgConfirmacaoDesfazerAtendimento dlgConfirmacaoDesfazerAtendimento;
    private DlgConfirmacaoOk dlgConfirmacaoOk;
    private DlgConfirmacaoOk dlgAtendSalvoAnteriormente;
    private DlgConfirmacaoOkObject<Atendimento> dlgAvisoNotificacaoCompulsoria;
    private DlgCadastroPaciente dlgCadastroPaciente;
    private DlgLembretesAtendimento dlgLembretesAtendimento;
    private DlgInformarSenhaFinalizarAtendimento dlgInformarSenhaFinalizarAtendimento;
    private DlgConfirmacaoOkObject dlgConfirmacaoParametroEstabelecimentoPaciente;
    private Long vacinaEmDiaLong;
    private boolean atendimentoAtencaoBasica;
    private Atendimento atendimento;
    private SubmitLink btnFinalizarAtendimento;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private Boolean gerarNovoAgravo = true;
    private boolean isValidarUsuarioTemporario;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNaoFichaAcolhimento;

    private AbstractAjaxLink linkNotificacaoAvulsa;

    private DlgNotificacaoAvulsaAtendimento dlgNotificacaoAvulsaAtendimento;

    private AtendimentoNotificacaoAvulsa atendimentoNotificacaoAvulsa;
    private DlgImpressaoHtml<DocumentoAtendimento> dlgConfirmacaoImpressao;

    private AjaxPreviewBlank ajaxPreviewBlank;

    @Deprecated
    public DefaultProntuarioPage() {
    }

    public DefaultProntuarioPage(Atendimento atendimento, PageParameters parameters) {
        super(parameters);
        this.atendimento = atendimento;
        iniciarProntuario(atendimento);
    }

    public DefaultProntuarioPage(Atendimento atendimento, PageParameters parameters, boolean atendimentoAtencaoBasica) {
        super(parameters);
        this.atendimentoAtencaoBasica = atendimentoAtencaoBasica;
        this.atendimento = atendimento;

        iniciarProntuario(atendimento);
    }

    private void iniciarProntuario(Atendimento atendimento) {
        form = new Form("form");

        setDefaultModel(new LoadableAtendimentoModel(atendimento.getCodigo()));

        TransparentWebMarkupContainer section = new TransparentWebMarkupContainer("section");

        form.add(new Label("nomePrograma", new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return getTituloPrograma();
            }
        }));

        ListView<IProntuarioNode> listView;
        form.add(listView = new ListView<IProntuarioNode>("nodes", new LoadableDetachableModel<List<? extends IProntuarioNode>>() {

            @Override
            protected List<? extends IProntuarioNode> load() {
                return getNodes();
            }
        }) {
            @Override
            protected void populateItem(ListItem<IProntuarioNode> item) {
                NodeButton nodeButton;
                item.add(nodeButton = new NodeButton("btnNode", item.getModelObject()) {

                    @Override
                    public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        getProntuarioController().changeNode(target, getProntuarioNode());
                    }

                });
                NodeButtonEventListener createListener = nodeButton.createListener();
                nodeButtonListeners.add(createListener);
                createListener.validar(null, validacoes);
                createListener.ativar(null, activeNode);
            }
        });
        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());
        form.add(new Label("nomePaciente", atendimento.getUsuarioCadsus().getNomeSocial() + " | " + atendimento.getUsuarioCadsus().getIdadePorExtensoOmiteIncompleto() + " | DN: " + atendimento.getUsuarioCadsus().getDataNascimentoFormatado(true)));
        String vacinaEmDiaDescricao = vacinaEmDiaDescricao(getAtendimento());
        form.add(getCondicoesSaude(atendimento.getUsuarioCadsus()));
        form.add(getParticipaDoProgramaBolsaFamilia(atendimento.getUsuarioCadsus()));
        form.add(new Label("dadosAvaliacao", dadosAvaliacao()));
        form.add(new Label("vacinaEmDia", "Vacina em dia: " + vacinaEmDiaDescricao).setVisible(!vacinaEmDiaDescricao.isEmpty()));
        form.add(new Label("exameCitopatologico", setLabelMessage(getAtendimento().getUsuarioCadsus())).setVisible(true));


        try {
            final String urlTelessaude = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("urlTelessaude");

            AbstractAjaxLink btnUrlTelessaude;
            form.add(btnUrlTelessaude = new AbstractAjaxLink("btnUrlTelessaude") {
                @Override
                public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    target.appendJavaScript("setTimeout(\"window.open('" + urlTelessaude + "','_blank')\", 100);");
                }

                @Override
                public boolean isVisible() {
                    return urlTelessaude != null;
                }
            });
        } catch (DAOException e) {
            Loggable.log.error(e);
        }

        form.add(new Label("idoso", new Model<String>(getDescricaoIdoso())).setVisible(isIdoso()));

        AbstractAjaxLink linkAlergia;
        form.add(linkAlergia = new AbstractAjaxLink("linkAlergia") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgConfirmacaoAlergia(target);
            }
        });

        linkAlergia.add(new Label("labelAlergial", bundle("alergia")));
        String descricaoAlergia = getDescricaoAlergia();
        if (descricaoAlergia == null) {
            linkAlergia.setVisible(false);
        } else {
            linkAlergia.add(new AttributeModifier("title", descricaoAlergia));
        }

        AbstractAjaxLink linkAnotacao;
        form.add(linkAnotacao = new AbstractAjaxLink("linkAnotacao") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgConfirmacaoAnotacao(target);
            }
        });

        linkAnotacao.add(new Label("labelAnotacao", bundle("anotacoes")));
        List<LembreteAtendimento> lembretesAtendimento = new ConsultaLembretesAtendimento(getAtendimento()).findLembretesAtendimento();
        String anotacao = ConsultaAnotacaoAtendimento.findAnotacoesFromPaciente(atendimento);
        linkAnotacao.setVisible(anotacao != null || !lembretesAtendimento.isEmpty());

        form.add(containerDadosProfissional());

        form.add(ImagemAvatarHelper.carregarAvatar(atendimento.getUsuarioCadsus()));

        form.add(notificationPanel = new NotificationPanel("notificationPanel"));

        form.add(panelContainer = new WebMarkupContainer("panelContainer"));
        panelContainer.setOutputMarkupId(true);

        form.add(new AbstractAjaxLink("btnVoltar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                voltarAction(target);
            }
        });
        form.setMultiPart(true);
        form.add(btnFinalizarAtendimento = new SubmitLink("btnFinalizarAtendimento", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                finalizarAtendimento(target);
            }
        }));

        form.add(section);

        if (CollectionUtils.isNotNullEmpty(listView.getModelObject())) {
            changeNode(listView.getModelObject().get(0));
        } else {
            panelContainer.add(new Fragment(getProntuarioController().panelId(), "atendimentoNaoConfigurado-fragment", panelContainer));
        }
        jGrowlNovidade = new JGrowlNovidade();
        form.add(jGrowlNovidade.createBehavior(getCodigoPrograma()));

        getSession().setAttribute("pageIdVersion", getPageReference().getPageId());

        Usuario usuarioLogado = ApplicationSession.get().getSession().getUsuario();
        isValidarUsuarioTemporario = AtendimentoHelper.isValidarUsuarioTemporario(usuarioLogado.getFlagUsuarioTemporario(), usuarioLogado.getFlagIdentificavel());
        add(form);
    }

    private boolean isIdoso() {
        return getAtendimento().getUsuarioCadsus().getIdade() >= 60;
    }

    private String getDescricaoIdoso() {
        return UsuarioCadsus.SEXO_MASCULINO.equals(getAtendimento().getUsuarioCadsus().getSexo()) ? "IDOSO" : "IDOSA";
    }

    private WebMarkupContainer containerDadosProfissional() {
        WebMarkupContainer container = new WebMarkupContainer("containerProfissional");
        container.setOutputMarkupId(true);

        StringBuilder profissionalStr = new StringBuilder();
        profissionalStr.append(bundle("profissional"))
                .append(": ")
                .append(getAtendimento().getProfissional().getNome())
                .append(" | ");

        if (getAtendimento().getProfissional().getNumeroRegistro() != null) {
            if (getAtendimento().getProfissional().getConselhoClasse() != null) {
                profissionalStr.append(getAtendimento().getProfissional().getConselhoClasse().getSigla());
            } else {
                profissionalStr.append(bundle("orgaoEmissorNaoInformado"));
            }
            profissionalStr.append(": ").append(getAtendimento().getProfissional().getNumeroRegistro());
        } else {
            profissionalStr.append(bundle("registroConselhoNaoInformado"));
        }

        container.add(new Label("profissional", profissionalStr.toString()));

        StringBuilder atendimentoStr = new StringBuilder();
        atendimentoStr.append(bundle("atendimento")).append(": ").append(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao());
        try {
            String selecionarConvenio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("SelecionarConvenio");
            if (RepositoryComponentDefault.SIM.equals(selecionarConvenio)) {
                StringBuilder convenio = new StringBuilder();
                convenio.append(bundle("convenio")).append(": ").append(getAtendimento().getConvenio().getDescricao());
                if (getAtendimento().getConvenio().getSubconvenio() != null) {
                    Convenio subConvenio = LoadManager.getInstance(Convenio.class)
                            .setId(getAtendimento().getConvenio().getSubconvenio())
                            .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_SUBCONVENIO, RepositoryComponentDefault.SIM_LONG))
                            .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_CONVENIO_PAI, getAtendimento().getConvenio()))
                            .start().getVO();
                    if (Util.isNotNull(subConvenio) && subConvenio.getDescricao() != null) {
                        convenio.append("/").append(bundle("subconvenio")).append(": ").append(subConvenio.getDescricao());
                    }
                }
                atendimentoStr.append(" | ").append(convenio);
            }
            container.add(new Label("atendimento", atendimentoStr.toString()));

            container.add(linkNotificacaoAvulsa = new AbstractAjaxLink("linkNotificacaoAvulsa") {
                @Override
                public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    viewDlgNotificacaoAvulsa(target, getAtendimento());
                }
            });

            linkNotificacaoAvulsa.add(new Label("labelNotificacaoAvulsa", bundle("registrarNotificacao")));

            Long flagNotificacaoAvulsa = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getFlagNotificacaoAvulsaAtendimento();
            Long nivelEnsino = (getAtendimento().getProfissional().getCboProfissional() != null && getAtendimento().getProfissional().getCboProfissional().getNivelEnsino() != null) ?
                    getAtendimento().getProfissional().getCboProfissional().getNivelEnsino() :
                    getAtendimento().getTabelaCbo().getNivelEnsino();
            Boolean isProfissionalNivelSuperior = nivelEnsino != null && nivelEnsino.equals(TabelaCbo.NivelEnsino.SUPERIOR.value());

            linkNotificacaoAvulsa.setVisible(
                    (flagNotificacaoAvulsa != null && flagNotificacaoAvulsa.equals(RepositoryComponentDefault.SIM_LONG))
                            && isProfissionalNivelSuperior);

//            final String urlTelessaude = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("urlTelessaude");
//            container.add(new AbstractAjaxLink("btnUrlTelessaude") {
//                @Override
//                public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                    target.appendJavaScript("setTimeout(\"window.open('" + urlTelessaude + "','_blank')\", 100);");
//                }
//
//                @Override
//                public boolean isVisible() {
//                    return urlTelessaude != null;
//                }
//            });
        } catch (DAOException e) {
            Loggable.log.error(e);
        }

        return container;
    }

    private Label getParticipaDoProgramaBolsaFamilia(UsuarioCadsus usuarioCadsus) {
        Label programaBolsaFamiliaLabel = new Label("programaBolsaFamilia");

        UsuarioCadsus usuarioCadsusProgramaBolsaFamilia = usuarioCadsus;

        if (UsuarioCadsusHelper.participaDoProgramaBolsaFamilia(usuarioCadsusProgramaBolsaFamilia)) {
            programaBolsaFamiliaLabel.setDefaultModel(new Model(bundle("pbf")));
            Popover popover = new Popover(true);
            popover.setPlacement(Popover.Placement.RIGHT);
            popover.setTrigger(Popover.Trigger.HOVER);

            String programaBolsaFamiliaFormatada = "<b>" +
                    bundle("pbf") +
                    "</b>: " +
                    bundle("programa_bolsa_familia");

            popover.setHtmlContent(programaBolsaFamiliaFormatada);
            programaBolsaFamiliaLabel.add(popover);
        } else {
            programaBolsaFamiliaLabel.setVisible(false);
        }

        return programaBolsaFamiliaLabel;
    }

    private Label getCondicoesSaude(UsuarioCadsus usuarioCadsus) {
        Label condicoesSaude = new Label("condicoesSaude");

        List<UsuarioCadsusDoenca> usuarioCadsusDoencas = UsuarioCadsusHelper.carregarUsuarioCadsusDoencaList(usuarioCadsus);
        if (CollectionUtils.isNotNullEmpty(usuarioCadsusDoencas)) {
            List<String> siglas = Lambda.extract(usuarioCadsusDoencas, Lambda.on(UsuarioCadsusDoenca.class).getDoenca().getSigla());
            String siglasFormatado = Lambda.join(siglas, ", ") + " | ";
            condicoesSaude.setDefaultModel(new Model(siglasFormatado));

            Popover popover = new Popover(true);
            popover.setPlacement(Popover.Placement.RIGHT);
            popover.setTrigger(Popover.Trigger.HOVER);

            StringBuilder siglasComDescricaoFormatado = new StringBuilder();
            for (UsuarioCadsusDoenca usuarioCadsusDoenca : usuarioCadsusDoencas) {
                siglasComDescricaoFormatado.append("<b>")
                        .append(usuarioCadsusDoenca.getDoenca().getSigla())
                        .append("</b>: ").append(usuarioCadsusDoenca.getDoenca().getDescricao()).append("<br>");
            }
            popover.setHtmlContent(siglasComDescricaoFormatado.toString());
            condicoesSaude.add(popover);
        } else {
            condicoesSaude.setVisible(false);
        }

        return condicoesSaude;
    }

    private String getDadosVacina() {
        if (vacinaEmDia) {
            return "Vacina em dia";
        }
        return "Vacina nao em dia";
    }

    private String dadosAvaliacao() {
        StringBuilder descricao = new StringBuilder();
        UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, getAtendimento().getUsuarioCadsus()))
                .start().getVO();

        if (usuarioCadsusDado != null) {
            if (usuarioCadsusDado.getPeso() != null) {
                descricao.append(bundle("peso"))
                        .append(": ")
                        .append(usuarioCadsusDado.getPeso())
                        .append(" | ");
            }
            if (usuarioCadsusDado.getAltura() != null) {
                descricao.append(bundle("altura"))
                        .append(": ")
                        .append(usuarioCadsusDado.getAltura())
                        .append(" | ");
            }
            if (usuarioCadsusDado.getFrequenciaCardiaca() != null) {
                descricao.append(bundle("frequenciaCardiacaAbrev"))
                        .append(": ")
                        .append(usuarioCadsusDado.getFrequenciaCardiaca())
                        .append(" | ");
            }
            if (usuarioCadsusDado.getSaturacaoOxigenio() != null) {
                descricao.append(bundle("saturacaoOxigenioAbrev"))
                        .append(": ")
                        .append(usuarioCadsusDado.getSaturacaoOxigenio())
                        .append(" | ");
            }
        }

        Date ultimas24Horas = Data.removeDias(DataUtil.getDataAtual(), 1);
        AtendimentoPrimario ap = LoadManager.getInstance(AtendimentoPrimario.class)
                .addProperties(new HQLProperties(AtendimentoPrimario.class).getProperties())
                .addProperty(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO))
                .addProperty(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_ATENDIMENTO_PRINCIPAL, Atendimento.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoPrimario.PROP_DATA_AVALIACAO, QueryCustom.QueryCustomParameter.MAIOR_IGUAL, ultimas24Horas))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .setMaxResults(1)
                .start().getVO();

        if (ap != null) {
            if (ap.getPressaoArterial() != null && !"".equals(ap.getPressaoArterial())) {
                descricao.append(bundle("pa"))
                        .append(": ")
                        .append(ap.getPressaoArterial())
                        .append(" | ");
            }
            if (ap.getTemperatura() != null) {
                descricao.append(bundle("temperatura"))
                        .append(": ")
                        .append(ap.getTemperatura())
                        .append(" | ");
            }
            if (ap.getGlicemia() != null) {
                descricao.append(bundle("glicemia"))
                        .append(": ")
                        .append(ap.getGlicemia())
                        .append(" | ");
            }
            if (ap.getImc() != null) {
                descricao.append(bundle("imc"))
                        .append(": ")
                        .append(ap.getImc())
                        .append(" | ");
            }
//                if (ap.getDescricaoAlergia() != null) {
//                    descricao.append(bundle("alergias"))
//                            .append(": ")
//                            .append(ap.getDescricaoAlergia())
//                            .append(" | ");
//                }
        }

//        if (!"".equals(descricao.toString())) {
//            return descricao.substring(0, descricao.length() - 2);
//        }

        return descricao.toString();
    }

    private boolean isVacinaEmDia() throws FactoryException {
        try {
            return BOFactoryWicket.getBO(UsuarioCadsusFacade.class).isVacinaEmDia(getAtendimento().getUsuarioCadsus());
        } catch (DAOException ex) {
            Logger.getLogger(DefaultProntuarioPage.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(DefaultProntuarioPage.class.getName()).log(Level.SEVERE, null, ex);
        }
        return false;
    }

    private void finalizarAtendimento(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (isValidarUsuarioTemporario) {
            showDlgInformarSenha(target);
        } else {
            try {
                validacoes.clear();
                Usuario usuarioLogado = ApplicationSession.get().getSession().getUsuario();
                isValidarUsuarioTemporario = AtendimentoHelper.isValidarUsuarioTemporario(usuarioLogado.getFlagUsuarioTemporario(), usuarioLogado.getFlagIdentificavel());
                if (validarFichaAcolhimento() && !existsFichaAcolhimento()) {
                    showDlgFichaAcolhimento(target);
                } else {
                    finalizarAtendimento(target, getAtendimento());
                }
            } catch (ValidacaoException ex) {
                validacoes = ex.getValues();
                throw ex;
            } finally {
                callNodeButtonsValidar(target);
                if (activeNode == null) {
                    throw new ValidacaoException(bundle("atendimentoNaoConfigurado"));
                }
                getProntuarioController().exibirValidacao(target, activeNode.getIdentificador());
            }
        }
    }

    private boolean validarFichaAcolhimento() {
        NaturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
                .addProperties(new HQLProperties(NaturezaProcuraTipoAtendimento.class).getProperties())
                .addProperty(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_FLAG_VALIDA_FICHA_ACOLHIMENTO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_CODIGO), getAtendimento().getNaturezaProcuraTipoAtendimento().getCodigo()))
                .start().getVO();

        return naturezaProcuraTipoAtendimento.getTipoAtendimento().getFlagValidaFichaAcolhimento() != null
                && RepositoryComponentDefault.SIM_LONG.equals(naturezaProcuraTipoAtendimento.getTipoAtendimento().getFlagValidaFichaAcolhimento());
    }

    private void showDlgFichaAcolhimento(AjaxRequestTarget target) {
        if (dlgConfirmacaoSimNaoFichaAcolhimento == null) {
            getProntuarioController().addWindow(target, dlgConfirmacaoSimNaoFichaAcolhimento = new DlgConfirmacaoSimNao(getProntuarioController().newWindowId(), bundle("msgDesejaAbrirFichaAcolhimento")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException {
                    if (!existsNodeTipoAtendimento()) {
                        throw new ValidacaoException(Bundle.getStringApplication("msgAdicionarNoFichaAcolhimento", getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()));
                    } else {
                        getProntuarioController().changePanel(target, new FichaAcolhimentoPanel(getProntuarioController().panelId()));
                    }
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    finalizarAtendimento(target, getAtendimento());
                }
            });
        }
        dlgConfirmacaoSimNaoFichaAcolhimento.show(target);
    }

    private boolean existsFichaAcolhimento() {
        return LoadManager.getInstance(FichaAcolhimento.class)
                .addProperties(new HQLProperties(FichaAcolhimento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(FichaAcolhimento.PROP_STATUS, BuilderQueryCustom.QueryParameter.IGUAL, FichaAcolhimento.Status.PENDENTE.value()))
                .start().exists();
    }

    private boolean existsNodeTipoAtendimento() {
        return LoadManager.getInstance(NodoAtendimentoWeb.class)
                .addParameter(new QueryCustom.QueryCustomParameter(NodoAtendimentoWeb.PROP_TIPO_ATENDIMENTO, getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(NodoAtendimentoWeb.PROP_CLASSE_NODO, FichaAcolhimentoNode.class.getName()))
                .start().exists();
    }

    public void finalizarAtendimento(AjaxRequestTarget target, Atendimento atendimento) throws ValidacaoException, DAOException {
        if (isTentativaSalvarAtendimentoVoltarDoNavegador(target)) {
            return;
        }
        addEquipeProfissional(atendimento);
        BOFactoryWicket.getBO(AtendimentoFacade.class).validarEncerrarAtendimento(atendimento.getCodigo(), nodeList);

        Atendimento atendimentoAux = LoadManager.getInstance(Atendimento.class)
                .addProperty(VOUtils.montarPath(Atendimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_CID_PRINCIPAL, Cid.PROP_CODIGO))
                .setId(atendimento.getCodigo()).start().getVO();
        Cid cidPrincipal = null;
        if (atendimentoAux != null) {
            cidPrincipal = atendimentoAux.getCidPrincipal();
        }

        ClassificacaoCids classificacaoCid = AtendimentoHelper.getClassificacaoCid(cidPrincipal);
        if (classificacaoCid != null) {

            List<String> cids = new ArrayList<>();
            cids.add(cidPrincipal.getCodigo());
            if (classificacaoCid.getCidAgrupador() != null) {
                cids.add(classificacaoCid.getCidAgrupador().getCodigo());
            }

            if (isHabilitaCriarNotificacaoAgravo(atendimento, classificacaoCid, cids)) {
                atendimento.setCidPrincipal(cidPrincipal);
                validarNotificacaoCid(target, cidPrincipal, classificacaoCid, atendimento);
                return;
            }
        } else {
            if (getAtendimentoNotificacaoAvulsa() != null) {
                ClassificacaoCids classificacaoCidNotificavel = AtendimentoHelper.getClassificacaoCid(getAtendimentoNotificacaoAvulsa().getCid());

                if (classificacaoCidNotificavel != null) {

                    List<String> cids = new ArrayList<>();
                    cids.add(getAtendimentoNotificacaoAvulsa().getCid().getCodigo());
                    if (classificacaoCidNotificavel.getCidAgrupador() != null) {
                        cids.add(classificacaoCidNotificavel.getCidAgrupador().getCodigo());
                    }

                    if (isHabilitaCriarNotificacaoAgravo(atendimento, classificacaoCidNotificavel, cids)) {
                        validarNotificacaoCid(target, getAtendimentoNotificacaoAvulsa().getCid(), classificacaoCidNotificavel, atendimento);
                        return;
                    }
                }
            }
        }

        atendimentoEncaminhamento(target, atendimento);
    }

    private boolean isTentativaSalvarAtendimentoVoltarDoNavegador(AjaxRequestTarget target) {
        Integer pageId = (Integer) getSession().getAttribute("pageIdVersion");
        if (Atendimento.STATUS_FINALIZADO.equals(getAtendimento().getStatus()) && getAtendimento().getDataFechamento() != null && pageId == getPageReference().getPageId()) {
            if (dlgAtendSalvoAnteriormente == null) {
                addModal(target, dlgAtendSalvoAnteriormente = new DlgConfirmacaoOk(newModalId(), BundleManager.getString("msg_atendimento_salvo_anteriormente")) {

                    @Override
                    public String getDialogTitle() {
                        return bundle("aviso");
                    }

                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        target.appendJavaScript("window.history.back();");
                    }
                });
            }
            dlgAtendSalvoAnteriormente.show(target);
            return true;
        }
        return false;
    }

    private void showDlgInformarSenha(AjaxRequestTarget target) throws ValidacaoException {
        Usuario usuarioProfissionalAtendimento = buscarUsuarioDoProfissional();
        if (dlgInformarSenhaFinalizarAtendimento == null) {
            getProntuarioController().addWindow(target, dlgInformarSenhaFinalizarAtendimento = new DlgInformarSenhaFinalizarAtendimento(getProntuarioController().newWindowId(), usuarioProfissionalAtendimento) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, boolean isValidar) throws ValidacaoException, DAOException {
                    isValidarUsuarioTemporario = isValidar;
                    finalizarAtendimento(target);
                }
            });
        }
        dlgInformarSenhaFinalizarAtendimento.show(target, usuarioProfissionalAtendimento);
    }

    private Usuario buscarUsuarioDoProfissional() throws ValidacaoException {
        List<Usuario> usuarioList = LoadManager.getInstance(Usuario.class)
                .addProperty(Usuario.PROP_CODIGO)
                .addProperty(Usuario.PROP_LOGIN)
                .addProperty(Usuario.PROP_SENHA)
                .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_PROFISSIONAL, getAtendimento().getProfissional()))
                .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_FLAG_USUARIO_TEMPORARIO, RepositoryComponentDefault.NAO_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_STATUS, Usuario.STATUS_ATIVO))
                .start().getList();

        if (usuarioList.isEmpty()) {
            throw new ValidacaoException(BundleManager.getString("msgNaoFoiEncontradoUsuarioProfissionalInformado"));
        } else if (usuarioList.size() > 1) {
            throw new ValidacaoException(BundleManager.getString("msgExisteMaisUmUsuarioRelacionadoEsteProfissional"));
        }

        return usuarioList.get(0);
    }

    private void showDlgAvisoNotificacaoCompulsoria(AjaxRequestTarget target, Atendimento atendimento) {
        if (dlgAvisoNotificacaoCompulsoria == null || (Boolean.TRUE.equals(gerarNovoAgravo))) {
            getProntuarioController().addWindow(target, dlgAvisoNotificacaoCompulsoria = new DlgConfirmacaoOkObject<Atendimento>(getProntuarioController().newWindowId(), bundle("msgFoiInformadoUmCidNecessarioAtualizarEndereco")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Atendimento atendimento) throws ValidacaoException, DAOException {
                    showDlgCadastroPaciente(target, atendimento);
                }

                @Override
                public String getConfirmarLabel() {
                    return bundle("avancar");
                }
            });

            dlgAvisoNotificacaoCompulsoria.setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {
                @Override
                public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                    return false;
                }
            });
        }

        dlgAvisoNotificacaoCompulsoria.show(target, atendimento);
    }

    private void validarNotificacaoCid(AjaxRequestTarget target, Cid cid, ClassificacaoCids classificacaoCid, Atendimento atendimento) throws DAOException {
        if (classificacaoCid != null && RepositoryComponentDefault.SIM_LONG.equals(classificacaoCid.getPermiteNotificacaoConcomitante())) {

            List<String> cids = new ArrayList<>();
            cids.add(cid.getCodigo());

            if (classificacaoCid.getCidAgrupador() != null) {
                cids.add(classificacaoCid.getCidAgrupador().getCodigo());
            }

            RegistroAgravo registroAgravo = getRegistroAgravoPendente(atendimento, classificacaoCid, cids);

            if (registroAgravo != null) {
                if (dlgConfirmacaoSimNao == null) {
                    getProntuarioController().addWindow(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(getProntuarioController().newWindowId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            gerarNovoAgravo = true;
                            if (RepositoryComponentDefault.NAO.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("DadosAdicionais_CID"))) {
                                showDlgAvisoNotificacaoCompulsoria(target, getAtendimento());
                            } else {
                                showDlgCadastroPaciente(target, atendimento);
                            }
                        }

                        @Override
                        public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            super.onFechar(target);
                            gerarNovoAgravo = false;
                            atendimentoEncaminhamento(target, getAtendimento());
                        }
                    });
                }

                dlgConfirmacaoSimNao.setMessage(target, BundleManager.getString("pacienteJaPossuiNotificacaoCID", registroAgravo.getDataRegistro()));
                dlgConfirmacaoSimNao.show(target);
            } else {
                if (RepositoryComponentDefault.NAO.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("DadosAdicionais_CID"))) {
                    showDlgAvisoNotificacaoCompulsoria(target, getAtendimento());
                } else {
                    showDlgCadastroPaciente(target, atendimento);
                }
            }
        } else {
            gerarNovoAgravo = false;
            if (RepositoryComponentDefault.NAO.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("DadosAdicionais_CID"))) {
                showDlgAvisoNotificacaoCompulsoria(target, getAtendimento());
            } else {
                showDlgCadastroPaciente(target, atendimento);
            }
        }
    }

    private void showDlgCadastroPaciente(AjaxRequestTarget target, Atendimento atendimento) {
        getProntuarioController().addWindow(target, dlgCadastroPaciente = new DlgCadastroPaciente(getProntuarioController().newWindowId(), false, getAtendimento().getUsuarioCadsus().getCodigo(), atendimento, gerarNovoAgravo) {
            @Override
            public void onFechar(AjaxRequestTarget target) {
                atendimentoEncaminhamento(target, getAtendimento());
            }
        });

        dlgCadastroPaciente.setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                return false;
            }
        });

        dlgCadastroPaciente.show(target);
    }

    public void atendimentoEncaminhamento(AjaxRequestTarget target, Atendimento atendimento) {
        List<AtendimentoEncaminhamento> atendimentoEncaminhamentoList = LoadManager.getInstance(AtendimentoEncaminhamento.class)
                .addProperty(VOUtils.montarPath(AtendimentoEncaminhamento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AtendimentoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO, EncaminhamentoTipo.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AtendimentoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO, EncaminhamentoTipo.PROP_IMPRIME_PRONTUARIO))
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoEncaminhamento.PROP_ATENDIMENTO, atendimento))
                .start().getList();

        if ((CollectionUtils.isEmpty(atendimentoEncaminhamentoList) && RepositoryComponentDefault.SIM.equals(atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getImprimeProntuario()))
                || (CollectionUtils.isNotNullEmpty(atendimentoEncaminhamentoList) && RepositoryComponentDefault.SIM.equals(atendimentoEncaminhamentoList.get(0).getEncaminhamentoTipo().getImprimeProntuario()))
                || (RepositoryComponentDefault.SIM_LONG.equals(atendimento.getCorrecao()) && RepositoryComponentDefault.SIM.equals(atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getImprimeProntuario()))) {
            initDialogImpressaoProntuario(target);
            dlgConfirmacaoImpressaoProntuario.show(target, getAtendimento());
        } else if (RepositoryComponentDefault.SIM.equals(atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getImprimeNotaAlta()) && hasAlta(atendimento)) {
            initDialogImpressaoNotaAlta(target);
            dlgConfirmacaoImpressaoNotaAlta.show(target, getAtendimento());
        } else if (!getPageParameters().get("ORIENTACAO").isEmpty() && RepositoryComponentDefault.SIM_LONG.equals(getPageParameters().get("ORIENTACAO").toLong())) {
            Page page = new RegistroOrientacaoPage();
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString("atendimentoFinalizadoSucesso"));
        } else if (atendimentoAtencaoBasica) {
            Page page = new ConsultaAtendimentoAtencaoBasicaPage(getPageParameters());
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString("atendimentoFinalizadoSucesso"));
        } else {
            Page page = new DefaultConsultaAtendimentoPage(getPageParameters());
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString("atendimentoFinalizadoSucesso"));
        }
    }

    private void initDialogImpressaoProntuario(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressaoProntuario == null) {
            dlgConfirmacaoImpressaoProntuario = new DlgImpressaoObjectSignDigital<Atendimento>(getProntuarioController().newWindowId(), bundle("atendimentoConcluidoImpressaoProntuario"), true) {
                @Override
                public void onFechar(AjaxRequestTarget target, Atendimento atendimento) throws ValidacaoException, DAOException {
                    if (!RepositoryComponentDefault.SIM_LONG.equals(atendimento.getCorrecao())) {
                        atendimento = LoadManager.getInstance(Atendimento.class)
                                .addProperty(Atendimento.PROP_CODIGO)
                                .addProperty(Atendimento.PROP_DATA_IMPRESSAO)
                                .addProperty(VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO,
                                        NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO,
                                        TipoAtendimento.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO,
                                        NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO,
                                        TipoAtendimento.PROP_IMPRIME_NOTA_ALTA))
                                .addParameter(new QueryCustom.QueryCustomParameter(Atendimento.PROP_CODIGO, atendimento.getCodigo()))
                                .start().getVO();

                        if (atendimento.getDataImpressao() == null) {
                            throw new ValidacaoException(BundleManager.getString("msgImprimirProntuario"));
                        }
                    }
                }

                @Override
                public void afterClose(AjaxRequestTarget target, Atendimento atendimento) throws ValidacaoException, DAOException {
//                    if (RepositoryComponentDefault.SIM.equals(atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getImprimeNotaAlta()) && hasAlta(atendimento)) {
//                        initDialogImpressaoNotaAlta(target);
//                        dlgConfirmacaoImpressaoNotaAlta.show(target, atendimento);
//                    } else {
                    retornarConsulta();
//                    }
                }

                @Override
                public File getDataReport(Atendimento object) throws ReportException {
                    GerenciadorArquivo arquivoAssinado = loadGerenciadorArquivoAssinado(object);
                    if (arquivoAssinado != null) {
                        try {
                            File f = File.createTempFile(UUID.randomUUID().toString(), ".pdf");
                            FileUtils.buscarArquivoFtp(arquivoAssinado.getCaminho(), f.getAbsolutePath());
                            confirmarImpressao(object);
                            this.isDefaultAssinatura(false);
                            return f;
                        } catch (IOException | ValidacaoException e) {
                            Loggable.log.error(e);
                        }
                    }
                    confirmarImpressao(object);
                    File file = null;
                    try {
                        file = File.createTempFile("prontuario " + getAtendimento().getUsuarioCadsus().getNome(), ".pdf");
                    } catch (IOException e) {
                        Loggable.log.error(e);
                    }

                    DataReport dataReport = loadReport(atendimento);

                    try {
                        JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
                        List<InputStream> relatorio = gerarPDF(file);
                        file = PdfUtil.mergePdf(relatorio, "prontuario");
                    } catch (JRException | IOException | DocumentException e) {
                        Loggable.log.error(e);
                    }

                    return file;
                }

                @Override
                public void signedDocument(File signedDocument) throws ValidacaoException, DAOException {
                    anexarProntuarioAssinado(signedDocument);
                }

                @Override
                public void onAssinar(AjaxRequestTarget target, Atendimento object, String senha) throws ValidacaoException, DAOException, ReportException, JRException, IOException {
                    AbstractAssinaturaDigitalHelper helper = new AbstractAssinaturaDigitalService().carregarProvedorServico();
                    AssinaturaDigitalUtilDTO dto = new AssinaturaDigitalUtilDTO();
                    dto.setAtendimento(getAtendimento());
                    dto.setHelper(helper);
                    dto.setAjaxPreviewBlank(ajaxPreviewBlank);
                    dto.setSenhaCertificado(senha);
                    dto.setOrigemTipoDocumento(DocumentoAssinado.OrigemTipoDocumento.PRONTUARIO);
                    dto.setTipoDocumento(DocumentoAssinado.TipoDocumento.PRONTUARIO.descricao());
                    try {
                        Usuario usuario = AssinaturaDigitalUtil.loadUsuario();
                        if (Usuario.TipoCertificado.PFX.value().equals(usuario.getTipoCertificado())) {
                            assinarSalvarDocumento(dto, object, target);
                            confirmarImpressao(object);
                        } else {
                            DocumentoAssinado documentoAssinado = assinarSalvarDocumento(dto, object, null);

                            PscService pscService = AssinaturaDigitalUtil.preparePsc(documentoAssinado, usuario);
                            target.appendJavaScript("setTimeout(\"window.open('" + pscService.getAuthUrl() + "','_blank')\", 100);");
                        }
                    } catch (Exception e) {
                        throw new ValidacaoException(e);
                    }
                }

                @Override
                public String getDialogTitle() {
                    return bundle("prontuario");
                }
            };

            dlgConfirmacaoImpressaoProntuario.setProfissional(getAtendimento().getProfissional());

            getProntuarioController().addWindow(target, dlgConfirmacaoImpressaoProntuario);
        }
    }

    private void confirmarImpressao(Atendimento object) {
        try {
            BOFactoryWicket.getBO(ProntuarioReportFacade.class).confirmarImpressaoAtendimento(object);
        } catch (DAOException | ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private DataReport loadReport(Atendimento object, boolean assinarDocumento) throws ReportException {
        RelatorioImpressaoAtendimentoDTOParam param = new RelatorioImpressaoAtendimentoDTOParam();
        if (RepositoryComponentDefault.SIM_LONG.equals(object.getCorrecao())) {
            param.setAtendimento(object);
            param.setCorrecao(RepositoryComponentDefault.SIM_LONG);
        } else {
            param.setAtendimento(object.getAtendimentoPrincipal());
        }
        param.setUsuarioCadsus(object.getUsuarioCadsus());
        param.setAssinadoDigitalmente(assinarDocumento);

        String impressaoDoProntuario;
        boolean exibeApenasUltimoAtendimentoNaImpressao = false;
        try {
            impressaoDoProntuario = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ImpressaoDoProntuario");
            if (RepositoryComponentDefault.SIM.equals(impressaoDoProntuario)) {
                exibeApenasUltimoAtendimentoNaImpressao = true;
            }
        } catch (DAOException ignored) {
        }

        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoAtendimentoInternacao(param, exibeApenasUltimoAtendimentoNaImpressao);
    }

    private DataReport loadReport(Atendimento object) throws ReportException {
        return loadReport(object, false);
    }

    private GerenciadorArquivo loadGerenciadorArquivoAssinado(Atendimento atendimento) {
        DocumentoAssinado documentoAssinado = LoadManager.getInstance(DocumentoAssinado.class)
                .addProperties(new HQLProperties(DocumentoAssinado.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, DocumentoAssinado.PROP_GERENCIADOR_ARQUIVO_ASSINADO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(DocumentoAssinado.PROP_ATENDIMENTO, atendimento.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(DocumentoAssinado.PROP_TIPO_DOCUMENTO_ORIGEM, DocumentoAssinado.OrigemTipoDocumento.PRONTUARIO.value()))
                .start().getVO();
        if (documentoAssinado != null)
            return documentoAssinado.getGerenciadorArquivoAssinado();
        return null;
    }

    private DocumentoAssinado assinarSalvarDocumento(AssinaturaDigitalUtilDTO dto, Atendimento atendimento, AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
        DataReport dr = loadReport(atendimento, true);
        AssinaturaDigitalUtil util = new AssinaturaDigitalUtil(dto, dr);
        DocumentoAssinado da = util.assinarDocumentoDataReport();

        if (target != null)
            util.exibirDocumento(target, ajaxPreviewBlank);
        return da;
    }

    private List<InputStream> gerarPDF(File files) {
        LoadManager loadManager = LoadManager.getInstance(AnexoPacienteElo.class)
                .addProperties(new HQLProperties(GerenciadorArquivo.class, AnexoPacienteElo.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPacienteElo.PROP_ANEXO_PACIENTE, AnexoPaciente.PROP_TIPO_ANEXO, TipoAnexo.PROP_FLAG_IMPRESSAO_PRONTUARIO), RepositoryComponentDefault.SIM_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPacienteElo.PROP_ANEXO_PACIENTE, AnexoPaciente.PROP_DATA_CADASTRO), BuilderQueryCustom.QueryParameter.MENOR_IGUAL, DataUtil.getDataAtual(), HQLHelper.NOT_RESOLVE_TYPE, DataUtil.getDataAtual()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPacienteElo.PROP_ANEXO_PACIENTE, AnexoPaciente.PROP_DATA_CADASTRO), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, atendimento.getDataChegada(), HQLHelper.NOT_RESOLVE_TYPE, atendimento.getDataChegada()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPacienteElo.PROP_ANEXO_PACIENTE, AnexoPaciente.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPacienteElo.PROP_ANEXO_PACIENTE, AnexoPaciente.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE, AnexoPaciente.Status.CANCELADO.value()));
        List<AnexoPacienteElo> anexoPacienteEloList = loadManager.addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AnexoPacienteElo.PROP_ANEXO_PACIENTE, AnexoPaciente.PROP_DATA_CADASTRO), BuilderQueryCustom.QuerySorter.CRESCENTE_NULLS_LAST)).start().getList();
        List<InputStream> anexos = new ArrayList<>();
        try {
            anexos.add(new FileInputStream(files));
        } catch (FileNotFoundException e) {
            Loggable.log.error(e);
        }
        if (br.com.ksisolucoes.util.CollectionUtils.isNotNullEmpty(anexoPacienteEloList)) {
            for (AnexoPacienteElo anexoPacienteElo : anexoPacienteEloList) {
                File file = new FtpImageUtil().downloadFile(anexoPacienteElo.getGerenciadorArquivo().getCaminho());
                if (FileUtils.isValidFile(file, FileUtils.MAGIC_NUMBER_PDF)) {
                    try {
                        anexos.add(new FileInputStream(file));
                    } catch (FileNotFoundException e) {
                        Loggable.log.error(e);
                    }
                }
            }
        }
        return anexos;
    }

    private void initDialogImpressaoNotaAlta(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressaoNotaAlta == null) {
            dlgConfirmacaoImpressaoNotaAlta = new DlgImpressaoObject<Atendimento>(getProntuarioController().newWindowId(), bundle("desejaImprimirNotaAlta")) {

                @Override
                public void onFechar(AjaxRequestTarget target, Atendimento object) throws ValidacaoException, DAOException {
                    retornarConsulta();
                }

                @Override
                public DataReport getDataReport(Atendimento object) throws ReportException {
                    RelatorioImpressaoNotaAltaDTOParam param = new RelatorioImpressaoNotaAltaDTOParam();
                    param.setAtendimento(object);
                    try {
                        BOFactoryWicket.getBO(ProntuarioReportFacade.class).confirmarImpressaoNotaAlta(object);
                    } catch (DAOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    } catch (ValidacaoException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoNotaAlta(param);
                }

                @Override
                public String getDialogTitle() {
                    return bundle("notaAlta");
                }
            };

            dlgConfirmacaoImpressaoNotaAlta.setLabelImprimir(target, bundle("sim"));
            dlgConfirmacaoImpressaoNotaAlta.setLabelFechar(target, bundle("nao"));

            getProntuarioController().addWindow(target, dlgConfirmacaoImpressaoNotaAlta);
        }
    }

    private void anexarProntuarioAssinado(File prontuario) throws ValidacaoException, DAOException {
        AnexoPaciente anexoPaciente = createAnexoPaciente();

        MensagemAnexoDTO anexo = new MensagemAnexoDTO();
        anexo.setNomeArquivoOriginal(prontuario.getName());
        anexo.setNomeArquivoUpload(prontuario.getAbsolutePath());

        AnexosPacienteDTO dto = new AnexosPacienteDTO();
        dto.setAnexoPaciente(anexoPaciente);
        dto.addAnexo(anexo);

        BOFactoryWicket.getBO(AtendimentoFacade.class).salvarAnexosPaciente(dto);
    }

    private void salvarAnexo(DocumentoAssinado documentoAssinado) throws DAOException, ValidacaoException {
        AnexoPaciente anexoPaciente = createAnexoPaciente();
        AnexoPaciente anexo = BOFactory.save(anexoPaciente);

        AnexoPacienteElo elo = new AnexoPacienteElo();
        elo.setAnexoPaciente(anexo);
        elo.setGerenciadorArquivo(documentoAssinado.getGerenciadorArquivoAssinado());

        BOFactory.save(elo);
    }

    private AnexoPaciente createAnexoPaciente() {
        AnexoPaciente anexoPaciente = new AnexoPaciente();
        anexoPaciente.setDescricao(bundle("assinadoDigitalmentePorX", getAtendimento().getProfissional().getNome()));
        anexoPaciente.setDataDocumento(DataUtil.getDataAtual());
        anexoPaciente.setTipoAnexo(TipoAnexo.PRONTUARIO_DIGITALMENTE_ASSINADO);
        anexoPaciente.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
        return anexoPaciente;
    }

    private void retornarConsulta() {
        if (!getPageParameters().get("ORIENTACAO").isEmpty() && RepositoryComponentDefault.SIM_LONG.equals(getPageParameters().get("ORIENTACAO").toLong())) {
            Page page = new RegistroOrientacaoPage();
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString("atendimentoFinalizadoSucesso"));
        } else if (atendimentoAtencaoBasica) {
            Page page = new ConsultaAtendimentoAtencaoBasicaPage(getPageParameters());
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString("atendimentoFinalizadoSucesso"));
        } else {
            Page page = new DefaultConsultaAtendimentoPage(getPageParameters());
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString("atendimentoFinalizadoSucesso"));
        }
    }

    private boolean hasAlta(Atendimento atendimento) {
        boolean hasAlta = LoadManager.getInstance(AtendimentoAlta.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoAlta.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO), atendimento.getCodigo()))
                .exists();
        return hasAlta;
    }

    private void callNodeButtonsValidar(AjaxRequestTarget target) {
        for (NodeButtonEventListener nodeButtonValidacaoListener : nodeButtonListeners) {
            nodeButtonValidacaoListener.validar(target, validacoes);
        }
    }

    private void callNodeButtonsAtivar(AjaxRequestTarget target) {
        for (NodeButtonEventListener nodeButtonValidacaoListener : nodeButtonListeners) {
            nodeButtonValidacaoListener.ativar(target, activeNode);
        }
    }

    protected List<IProntuarioNode> getNodes() {
        try {
            nodeList.clear();

            List<String> classList = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarNodosProntuario(getAtendimento().getCodigo(), getAtendimento().getDataReclassificacao() != null);

            List<IProntuarioNode> nodes = new ArrayList<IProntuarioNode>();
            for (String className : classList) {
                try {
                    IProntuarioNode node = (IProntuarioNode) Class.forName(className).newInstance();
                    if (node.validar(getAtendimento())) {
                        nodeList.add(node.getIdentificador());
                        nodes.add(node);
                    }
                } catch (ClassNotFoundException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
            return nodes;
        } catch (InstantiationException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return new ArrayList<IProntuarioNode>();
    }

    private Atendimento getAtendimento() {
        return ((LoadableObjectModel<Atendimento>) getDefaultModel()).getObject();
    }

    private void changeNode(IProntuarioNode node) {
        activeNode = node;
        node.setAtendimento(getAtendimento());
        if (getAtendimento().getEquipe() == null)
            getAtendimento().setEquipe(this.atendimento.getEquipe());
        DefaultProntuarioPanel newInstance = node.getPanel(getProntuarioController().panelId());
        changePanel(null, newInstance);
    }

    private void changePanel(AjaxRequestTarget target, DefaultProntuarioPanel panel) {
        Component activePanel = panelContainer.get(getProntuarioController().panelId());
        if (activePanel != null) {
            activePanel.replaceWith(panel);
        } else {
            panelContainer.add(panel);
        }
        panel.setProntuarioController(getProntuarioController());
        panel.setIdentificador(activeNode.getIdentificador());
        if (target != null) {
            panel.changePanelAction(target);
        }
    }

    public IProntuarioController getProntuarioController() {
        if (this.prontuarioController == null) {
            this.prontuarioController = new IProntuarioController() {

                @Override
                public Atendimento getAtendimento() {
                    return DefaultProntuarioPage.this.getAtendimento();
                }

                @Override
                public void changeNode(AjaxRequestTarget target, IProntuarioNode node) {
                    DefaultProntuarioPage.this.changeNode(node);
                    DefaultProntuarioPage.this.callNodeButtonsAtivar(target);
                    target.add(panelContainer);
                    target.appendJavaScript(JScript.bubbleHistoricoProntuario());
                    getProntuarioController().exibirValidacao(target, node.getIdentificador());
                }

                @Override
                public void changePanel(AjaxRequestTarget target, DefaultProntuarioPanel panel) {
                    DefaultProntuarioPage.this.changePanel(target, panel);
                    target.add(panelContainer);
                    target.appendJavaScript(JScript.bubbleHistoricoProntuario());
                    target.appendJavaScript(JScript.scrollToTop());
                    target.appendJavaScript(JScript.initMasks());
                }

                @Override
                public void exibirValidacao(AjaxRequestTarget target, NodesAtendimentoRef nodeIdentifier) {
                    if (validacoes.containsKey(nodeIdentifier)) {
                        warn(target, validacoes.get(nodeIdentifier).toString());
                    } else {
                        target.prependJavaScript(JScript.scrollToTop());
                    }
                }

                @Override
                public String newWindowId() {
                    return DefaultProntuarioPage.this.newModalId();
                }

                @Override
                public void addWindow(AjaxRequestTarget target, Window window) {
                    DefaultProntuarioPage.this.addModal(target, window);
                }

                @Override
                public String panelId() {
                    return PANEL_ID;
                }

                @Override
                public boolean isNodePermitted(NodesAtendimentoRef nodeIdentifier) {
                    return nodeList.contains(nodeIdentifier);
                }
            };
        }

        return this.prontuarioController;
    }

    private void voltarAction(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (isTentativaSalvarAtendimentoVoltarDoNavegador(target)) {
            return;
        }

        if (dlgConfirmacaoDesfazerAtendimento == null) {
            dlgConfirmacaoDesfazerAtendimento = new DlgConfirmacaoDesfazerAtendimento(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(AtendimentoFacade.class).voltarAtendimento(getAtendimento().getCodigo(), false);
                    if (!getPageParameters().get("ORIENTACAO").isEmpty() && RepositoryComponentDefault.SIM_LONG.equals(getPageParameters().get("ORIENTACAO").toLong())) {
                        setResponsePage(RegistroOrientacaoPage.class, getPageParameters());
                    } else if (atendimentoAtencaoBasica) {
                        setResponsePage(ConsultaAtendimentoAtencaoBasicaPage.class, getPageParameters());
                    } else {
                        setResponsePage(DefaultConsultaAtendimentoPage.class, getPageParameters());
                    }
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoDesfazerAtendimento);
        }
        dlgConfirmacaoDesfazerAtendimento.show(target);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("atendimento");
    }

    @Override
    public void info(AjaxRequestTarget target, String message) {
        message(target, message, FeedbackMessage.INFO);
    }

    @Override
    public void warn(AjaxRequestTarget target, String message) {
        message(target, message, FeedbackMessage.WARNING);
    }

    @Override
    public void error(AjaxRequestTarget target, String message) {
        message(target, message, FeedbackMessage.ERROR);
    }

    @Override
    public void clearNotifications(AjaxRequestTarget target) {
        getSession().getFeedbackMessages().clear();
        updateNotificationPanel(target);
    }

    @Override
    public void message(AjaxRequestTarget target, String message, int lvl) {
        getSession().getFeedbackMessages().add(new FeedbackMessage(notificationPanel, message, lvl));
        updateNotificationPanel(target);
    }

    @Override
    public void updateNotificationPanel(AjaxRequestTarget target) {
        updateNotificationPanel(target, true);
    }

    @Override
    public void updateNotificationPanel(AjaxRequestTarget target, boolean scrollToTop) {
        target.add(notificationPanel);
        if (scrollToTop) {
            target.appendJavaScript(JScript.scrollToTop());
        }
    }

    public String getDescricaoAlergia() {
        UsuarioCadsusDado dado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, getAtendimento().getUsuarioCadsus().getCodigo()))
                .addProperty(UsuarioCadsusDado.PROP_DESCRICAO_ALERGICO).start().getVO();

        if (dado == null || (dado.getDescricaoAlergico() == null || dado.getDescricaoAlergico().isEmpty())) {
            return null;
        } else {
            return dado.getDescricaoAlergico();
        }
    }

    public void viewDlgConfirmacaoAlergia(AjaxRequestTarget target) {
        if (dlgConfirmacaoOk == null) {
            addModal(target, dlgConfirmacaoOk = new DlgConfirmacaoOk(newModalId(), getDescricaoAlergia()) {

                @Override
                public String getDialogTitle() {
                    return bundle("alergias");
                }

                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    close(target);
                }
            });
        }
        dlgConfirmacaoOk.show(target);
    }

    private void viewDlgConfirmacaoAnotacao(AjaxRequestTarget target) {
        if (dlgLembretesAtendimento == null) {
            addModal(target, dlgLembretesAtendimento = new DlgLembretesAtendimento(newModalId(), getAtendimento(), true) {
            });
        }

        dlgLembretesAtendimento.show(target, getAtendimento());
    }

    private void viewDlgNotificacaoAvulsa(AjaxRequestTarget target, Atendimento atendimento) {
        if (dlgNotificacaoAvulsaAtendimento == null) {
            addModal(target, dlgNotificacaoAvulsaAtendimento = new DlgNotificacaoAvulsaAtendimento(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Cid cid) throws ValidacaoException, DAOException {
                    if (getAtendimentoNotificacaoAvulsa() != null) {
                        BOFactoryWicket.delete(atendimentoNotificacaoAvulsa);
                        atendimentoNotificacaoAvulsa = null;
                    }

                    if (cid != null) {
                        AtendimentoNotificacaoAvulsa atendimentoNotificacaoAvulsaAux = new AtendimentoNotificacaoAvulsa();
                        atendimentoNotificacaoAvulsaAux.setAtendimento(atendimento);
                        atendimentoNotificacaoAvulsaAux.setCid(cid);
                        BOFactoryWicket.save(atendimentoNotificacaoAvulsaAux);
                    }

                    close(target);
                }
            });
        }

        dlgNotificacaoAvulsaAtendimento.show(target);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        response.render(CssHeaderItem.forReference(Resources.CSS_GEM_SAUDE));
        response.render(CssHeaderItem.forReference(Resources.CSS_WICKET_BOOTSTRAP));
        response.render(CssHeaderItem.forReference(Resources.CSS_JQUERY_UI_CUSTOM));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_MIGRATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DATA_TABLE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_VALIDATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MASKEDINPUT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUTS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TREEVIEW));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JGROWL));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_PRINT_ELEMENT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DIRTY_FORMS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FORM));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUT));
        if (nodeList != null && nodeList.contains(NodesAtendimentoRef.SOLICITACAO_EXAMES_UNIDADE) && hasExamesPendentes()) {
            response.render(OnDomReadyHeaderItem.forScript(jGrowlNovidade.growlNovaMensagem(bundle("existemExamesPendentesPaciente"), false)));
        }

        response.render(OnDomReadyHeaderItem.forScript(getShortchut()));
    }

    private String getShortchut() {

        String sb = "\n" +
                "function checkEventObj(_event_) {\n" +
                "    // --- IE explorer \n" +
                "    if (window.event)\n" +
                "        return window.event;\n" +
                "    // --- Netscape and other explorers \n" +
                "    else\n" +
                "        return _event_;\n" +
                "}\n" +
                "\n" +
                "function execute(_event_) {\n" +
                "\n" +
                "    // --- Retrieve event object from current web explorer s\n" +
                "\n" +
                "    var winObj = checkEventObj(_event_);\n" +
                "\n" +
                "    var intKeyCode = winObj.keyCode;\n" +
                "    var intAltKey = winObj.altKey;\n" +
                "    var intCtrlKey = winObj.ctrlKey;\n" +
                "\n" +
                "    if (intAltKey) {\n" +
                "        if (intKeyCode == 81) { // 81 = Q\n" +
                "            clickFinalizarAtendimento();" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "document.onkeydown = execute;" +
                "var clickFinalizarAtendimento = function() {" +
                "$('#" + btnFinalizarAtendimento.getMarkupId() + "').click();" +
                "};";

        return sb;
    }

    private boolean hasExamesPendentes() {
        ExameRequisicao proxy = Lambda.on(ExameRequisicao.class);
        Long count = LoadManager.getInstance(ExameRequisicao.class)
                .addGroup(new QueryCustom.QueryCustomGroup(path(proxy.getCodigo()), QueryCustom.QueryCustomGroup.COUNT))
                .addParameter(new QueryCustom.QueryCustomParameter(
                        new BuilderQueryCustom.QueryGroupAnd(
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IGUAL, ExameRequisicao.Status.ABERTO.value()))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IS_NULL))))))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getAtendimento().getCodigo()), QueryCustom.QueryCustomParameter.DIFERENTE, getAtendimento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getUsuarioCadsus().getCodigo()), getAtendimento().getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getTipoExame().getTipo()), QueryCustom.QueryCustomParameter.DIFERENTE, RepositoryComponentDefault.Tipo.LACEN.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getDataConfirmacao()), QueryCustom.QueryCustomParameter.IS_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSituacaoLaboratorio()), QueryCustom.QueryCustomParameter.IGUAL, ExameRequisicao.SituacaoLaboratorio.CONCLUIDO.value()))
                .addInterceptor(new LoadInterceptorNotExistsExameExameBpaApac(LoadInterceptorNotExistsExameExameBpaApac.Tipo.EXAME_REQUISICAO))
                .setMaxResults(1)
                .start().getVO();

        return count == 1L;
    }

    private String vacinaEmDiaDescricao(Atendimento atendimento) {
        Atendimento ultimoAtendimento = LoadManager.getInstance(Atendimento.class)
                .addProperty(Atendimento.PROP_VACINA_EM_DIA)
                .addParameter(new QueryCustom.QueryCustomParameter(Atendimento.PROP_USUARIO_CADSUS, getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(Atendimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.DIFERENTE, getAtendimento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(Atendimento.PROP_DATA_FECHAMENTO, QueryCustom.QueryCustomParameter.IS_NOT_NULL))
                .addSorter(new QueryCustom.QueryCustomSorter(Atendimento.PROP_DATA_FECHAMENTO, QueryCustom.QueryCustomSorter.DECRESCENTE))
                .setMaxResults(1)
                .start().getVO();

        if (ultimoAtendimento != null) {

            if (RepositoryComponentDefault.NAO_LONG.equals(ultimoAtendimento.getVacinaEmDia())) {
                return Bundle.getStringApplication("rotulo_nao");
            } else if (RepositoryComponentDefault.SIM_LONG.equals(ultimoAtendimento.getVacinaEmDia())) {
                return Bundle.getStringApplication("rotulo_sim");
            } else if (RepositoryComponentDefault.NAO_SABE_LONG.equals(ultimoAtendimento.getVacinaEmDia())) {
                return Bundle.getStringApplication("rotulo_nao_informado");
            }

        }

        return Bundle.getStringApplication("rotulo_nao_informado");
    }

    private boolean isIdadeMamografia(UsuarioCadsus usuarioCadsus) {
        return UsuarioCadsus.SEXO_FEMININO.equals(usuarioCadsus.getSexo()) && usuarioCadsus.getIdade() != null && usuarioCadsus.getIdade() >= 50 && usuarioCadsus.getIdade() <= 69;
    }

    private boolean isIdadeCitopatologico(UsuarioCadsus usuarioCadsus) {
        return UsuarioCadsus.SEXO_FEMININO.equals(usuarioCadsus.getSexo()) && usuarioCadsus.getIdade() != null && usuarioCadsus.getIdade() >= 25 && usuarioCadsus.getIdade() <= 64;
    }

    private String setLabelMessage(UsuarioCadsus usuarioCadsus) {
        boolean idadeMamografia = isIdadeMamografia(usuarioCadsus);
        boolean idadeCitopatologica = isIdadeCitopatologico(usuarioCadsus);

        if (idadeMamografia && idadeCitopatologica) {
            return "| " + Bundle.getStringApplication("msg_exame_citopatologico_mamografia");
        } else if (idadeCitopatologica) {
            return "| " + Bundle.getStringApplication("msg_exame_citopatologico");
        } else if (idadeMamografia) {
            return "| " + Bundle.getStringApplication("msg_exame_mamografia");
        } else return "";
    }

    private RegistroAgravo getRegistroAgravoPendente(Atendimento atendimento, ClassificacaoCids classificacaoCid, List<String> cids) {
        LoadManager loadManager = LoadManager.getInstance(RegistroAgravo.class)
                .addProperty(RegistroAgravo.PROP_CODIGO)
                .addProperty(RegistroAgravo.PROP_DATA_REGISTRO)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroAgravo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), atendimento.getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroAgravo.PROP_CID, Cid.PROP_CODIGO), QueryCustom.QueryCustomParameter.IN, cids))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroAgravo.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE, RegistroAgravo.Status.CANCELADO.value()));

        if (classificacaoCid != null && classificacaoCid.getFichaInvestigacaoAgravo() != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(RegistroAgravo.PROP_STATUS, BuilderQueryCustom.QueryParameter.NOT_IN,
                    Collections.singletonList(RegistroAgravo.Status.INVESTIGACAO_CONCLUIDA.value())));
        } else {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(RegistroAgravo.PROP_STATUS, BuilderQueryCustom.QueryParameter.NOT_IN,
                    Collections.singletonList(RegistroAgravo.Status.CONCLUIDO.value())));//monitoramento concluido
        }

        return loadManager.setMaxResults(1).start().getVO();
    }

    private boolean isHabilitaCriarNotificacaoAgravo(Atendimento atendimento, ClassificacaoCids classificacaoCid, List<String> cids) {
        boolean hasAtentimentoNotificacao = LoadManager.getInstance(AtendimentoNotificacao.class)
                .addProperty(AtendimentoNotificacao.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoNotificacao.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO), atendimento.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoNotificacao.PROP_FLAG_REGISTRO_AGRAVO_CRIADO), RepositoryComponentDefault.NAO_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoNotificacao.PROP_CID, Cid.PROP_CODIGO), QueryCustom.QueryCustomParameter.IN, cids))
                .start().exists();

        return isPermiteNotificacao(atendimento, classificacaoCid, cids) && (hasAtentimentoNotificacao || Boolean.TRUE.equals(gerarNovoAgravo));
    }

    private boolean isPermiteNotificacao(Atendimento atendimento, ClassificacaoCids classificacaoCid, List<String> cids) {
        boolean hasNotificacaoAgravoAberta = getRegistroAgravoPendente(atendimento, classificacaoCid, cids) != null;
        return Boolean.FALSE.equals(hasNotificacaoAgravoAberta) || RepositoryComponentDefault.SIM_LONG.equals(classificacaoCid.getPermiteNotificacaoConcomitante());
    }

    private AtendimentoNotificacaoAvulsa getAtendimentoNotificacaoAvulsa() {
        if (atendimentoNotificacaoAvulsa == null) {
            atendimentoNotificacaoAvulsa = LoadManager.getInstance(AtendimentoNotificacaoAvulsa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoNotificacaoAvulsa.PROP_ATENDIMENTO, getAtendimento()))
                    .start().getVO();
        }
        return atendimentoNotificacaoAvulsa;
    }

    private void addEquipeProfissional(Atendimento atendimento) throws DAOException, ValidacaoException {
        EquipeProfissional equipeProfissional = (EquipeProfissional) Session.get().getAttribute("equipeProfissional");
        Equipe equipe = null;
        if (equipeProfissional != null && equipeProfissional.getEquipe() != null) {
            equipe = equipeProfissional.getEquipe();
        } else {
            List<EquipeProfissional> equipeProfissionalList =
                    LoadManager.getInstance(EquipeProfissional.class)
                            .addProperty(EquipeProfissional.PROP_CODIGO)
                            .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_REFERENCIA))
                            .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                            .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO_CNS))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, atendimento.getProfissional()))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), atendimento.getEmpresa()))
                            .start().getList();

            if (br.com.celk.util.CollectionUtils.isNotNullEmpty(equipeProfissionalList) && equipeProfissionalList.size() > 1) {
                equipe = equipeProfissionalList.get(0).getEquipe();
            }
        }
        atendimento.setEquipe(equipe);
    }
}
