package br.com.celk.view.vigilancia.dengue.tipoinseticida;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoInseticida;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 646
 */
@Private
public class ConsultaTipoInseticidaPage extends ConsultaPage<DengueTipoInseticida, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DengueTipoInseticida proxy = on(DengueTipoInseticida.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createSortableColumn(bundle("sigla"), proxy.getSigla()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<DengueTipoInseticida>() {
            @Override
            public void customizeColumn(final DengueTipoInseticida rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<DengueTipoInseticida>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueTipoInseticida modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoInseticidaPage(rowObject));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<DengueTipoInseticida>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueTipoInseticida modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return DengueTipoInseticida.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(DengueTipoInseticida.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(DengueTipoInseticida.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(DengueTipoInseticida.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoInseticidaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTipoInseticida");
    }
}
