package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.grupoproduto.autocomplete.AutoCompleteConsultaGrupoProduto;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioValorizacaoEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 * Programa - 144
 */
@Private

public class RelatorioValorizacaoEstoquePage extends RelatorioPage<RelatorioValorizacaoEstoqueDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaGrupoProduto autoCompleteConsultaGrupoProduto;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        form.add(autoCompleteConsultaGrupoProduto = new AutoCompleteConsultaGrupoProduto("grupoProduto"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(getCbxFormaApresentacao());
        form.add(DropDownUtil.getNaoSimDropDown("agruparUnidade"));
        
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaGrupoProduto.setMultiplaSelecao(true);
        autoCompleteConsultaGrupoProduto.setOperadorValor(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setOperadorValor(true);
    }
    
    private DropDown getCbxFormaApresentacao(){
        DropDown cbxFormaApresentacao = new DropDown("formaApresentacao");
        
        cbxFormaApresentacao.addChoice(ReportProperties.AGRUPAR_GRUPO, BundleManager.getString("grupoSubGrupo"));
        cbxFormaApresentacao.addChoice(ReportProperties.AGRUPAR_PRODUTO, BundleManager.getString("produto"));
        
        return cbxFormaApresentacao;
    }

    @Override
    public Class<RelatorioValorizacaoEstoqueDTOParam> getDTOParamClass() {
        return RelatorioValorizacaoEstoqueDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioValorizacaoEstoqueDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioValorizacaoEstoque(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("valorizacaoEstoque");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

}
