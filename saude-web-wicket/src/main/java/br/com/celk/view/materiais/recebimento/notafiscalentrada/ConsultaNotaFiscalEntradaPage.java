package br.com.celk.view.materiais.recebimento.notafiscalentrada;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.consorcio.importacaopedido.ImportacaoPedidoConsorcioPage;
import br.com.celk.view.consorcio.importacaopedido.dlg.DlgInformarGuia;
import br.com.celk.view.materiais.recebimento.notafiscalentrada.customcolumn.ConsultaNotaFiscalColumnPanel;
import br.com.celk.view.materiais.recebimento.notafiscalentrada.customize.CustomizeConsultaNotaFiscalEntrada;
import br.com.celk.view.materiais.recebimento.notafiscalentrada.dialog.DlgRecebimentoBrm;
import br.com.celk.view.materiais.tipodocumento.autocomplete.AutoCompleteConsultaTipoDocumento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsultaGuiaSaidaDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.bo.entradas.recebimento.interfaces.facade.RegistroNotaFiscalFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoEntrega;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;
import br.com.ksisolucoes.vo.financeiro.Serie;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 * Programa - 97
 */
@Private
public class ConsultaNotaFiscalEntradaPage extends ConsultaPage<RegistroNotaFiscal, List<BuilderQueryCustom.QueryParameter>> {

    private InputField<Long> txtNota;

    private Long documento;
    private Pessoa fornecedor;
    private String tipoData;
    private DatePeriod periodo;
    private Long situacao;
    private TipoDocumento tipoDocumento;

    private DlgInformarGuia dlgInformarGuia;

    private AbstractAjaxButton btnImportarNFe;
    private Long entradaNotaFiscalParcial;
    private AbstractAjaxButton btnImportarBrm;
    private DlgRecebimentoBrm dlgRecebimentoBrm;
    private String integrarSmar;

    @Override
    public void initForm(Form form) {
        try {
            entradaNotaFiscalParcial = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("entradaNotaFiscalParcial");
            integrarSmar = (BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("integrarSmar"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }

        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(txtNota = new InputField<Long>("documento"));
        form.add(new AutoCompleteConsultaPessoa("fornecedor"));
        form.add(new AutoCompleteConsultaTipoDocumento("tipoDocumento")
                .setApenasInterno(false)
                .setApenasNotaFiscalEntrada(true));

        form.add(new PnlDatePeriod("periodo"));
        form.add(getDropDownTipoData());
        form.add(getDropDownSituacao());

        form.add(btnImportarNFe = new AbstractAjaxButton("btnImportarNFe") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new CadastroImportacaoXmlNotaFiscalPage());
            }

        });
        getControls().add(btnImportarNFe);

        btnImportarNFe.add(new AttributeModifier("class", "btn-green"));
        btnImportarNFe.add(new AttributeModifier("value", BundleManager.getString("importarXml")));
        btnImportarNFe.add(new AttributeModifier("style", "margin-left: 5px;"));

        setExibeExpandir(true);

        AbstractAjaxButton btnReceberPedidoConsorcio;
        getControls().add(btnReceberPedidoConsorcio = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (dlgInformarGuia == null) {
                    addModal(target, dlgInformarGuia = new DlgInformarGuia(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, Long guiaSaida, Serializable object) throws ValidacaoException, DAOException {
                            validarImportacaoProduto();
                            ConsultaGuiaSaidaDTO dto = BOFactoryWicket.getBO(ConsorcioFacade.class).consultaGuiaSaidaExterno(guiaSaida);
                            if (dto != null) {
                                validarMunicipio(dto, guiaSaida);
                                validarStatus(dto, guiaSaida);
                                validarDuplicidade(dto, guiaSaida);
                                setResponsePage(new ImportacaoPedidoConsorcioPage(dto));
                            } else {
                                throw new ValidacaoException(bundle("msgGuiaSaidaNaoEncontrada"));
                            }
                        }
                    });
                }
                dlgInformarGuia.show(target);
            }
        });

        btnReceberPedidoConsorcio.add(new AttributeModifier("class", "save"));
        btnReceberPedidoConsorcio.add(new AttributeModifier("value", bundle("receberPedidoConsorcio")));
        btnReceberPedidoConsorcio.add(new AttributeModifier("style", "margin-left: 5px;"));

        getControls().add(btnImportarBrm = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (dlgRecebimentoBrm == null) {
                    addModal(target, dlgRecebimentoBrm = new DlgRecebimentoBrm(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, Long idBrm, Serie serie, TipoDocumento tipoDocumento, Date dataEntrada) throws ValidacaoException, DAOException {
                            RegistroNotaFiscal registroNotaFiscal = BOFactoryWicket.getBO(RegistroNotaFiscalFacade.class).receberIntegracaoBrm(idBrm, serie, tipoDocumento, dataEntrada);
                            setResponsePage(new DetalhesNotaFiscalEntradaPage(registroNotaFiscal, true));
                        }
                    });
                }
                dlgRecebimentoBrm.show(target);
            }
        });

        btnImportarBrm.add(new AttributeModifier("class", "btn-green"));
        btnImportarBrm.add(new AttributeModifier("value", BundleManager.getString("receber_brm")));
        btnImportarBrm.add(new AttributeModifier("style", "margin-left: 5px;"));
        btnImportarBrm.setVisible(integrarSmar != null && integrarSmar.equals(RepositoryComponentDefault.SIM));

        situacao = RegistroNotaFiscal.STATUS_ABERTO;
    }

    public void validarMunicipio(ConsultaGuiaSaidaDTO dto, Long guiaSaida) throws ValidacaoException {
        if (!SessaoAplicacaoImp.getInstance().getEmpresa().getCidade().equals(dto.getPedidoTransferenciaLicitacaoEntrega().getPedidoTransferenciaLicitacao().getEmpresaConsorciado().getCidade())) {
            throw new ValidacaoException(bundle("msgGuiaXPertenceOutroMunicipio", guiaSaida));
        }
    }

    public void validarStatus(ConsultaGuiaSaidaDTO dto, Long guiaSaida) throws ValidacaoException {
        if (PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.CANCELADA.value().equals(dto.getPedidoTransferenciaLicitacaoEntrega().getStatus())) {
            throw new ValidacaoException(bundle("msgGuiaXEstaCancelada", guiaSaida));
        }
    }

    public void validarImportacaoProduto() throws DAOException, ValidacaoException {
        TipoDocumento tipoDocumentoParam = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("tipoDocumentoRecebimento");
        Serie serieParam = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("serieRecebimento");
        if (serieParam == null) {
            throw new ValidacaoException(bundle("msgSerieNaoConfigurado"));
        }
        if (tipoDocumentoParam != null) {
            if (TipoDocumento.IS_SAIDA.equals(tipoDocumentoParam.getFlagTipoMovimento())) {
                throw new ValidacaoException(bundle("msgTipoDocumentoNaoEntrada"));
            }
        } else {
            throw new ValidacaoException(bundle("msgTipoDocumentoNaoConfigurado"));
        }
    }

    public void validarDuplicidade(ConsultaGuiaSaidaDTO dto, Long numero) throws ValidacaoException, DAOException {
        Pessoa f = LoadManager.getInstance(Pessoa.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Pessoa.PROP_CNPJ_CPF, dto.getPedidoTransferenciaLicitacaoEntrega().getPedidoTransferenciaLicitacao().getEmpresaAlmoxarifado().getCnpj()))
                .start().getVO();
        if (f != null) {
            Serie serieParam = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("serieRecebimento");
            int count = LoadManager.getInstance(RegistroNotaFiscal.class)
                    .addProperties(RegistroItemNotaFiscal.PROP_STATUS)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroNotaFiscal.PROP_EMPRESA), SessaoAplicacaoImp.getInstance().getEmpresa()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroNotaFiscal.PROP_FORNECEDOR), f))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroNotaFiscal.PROP_NUMERO_NOTA_FISCAL), numero))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroNotaFiscal.PROP_SERIE), serieParam))
                    .start().getList().size();
            if (count > 0) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nota_fiscal_jah_existente"));
            }
        } else {
            throw new ValidacaoException(BundleManager.getString("msgFornecedorNaoEncontradoCnpj", dto.getPedidoTransferenciaLicitacaoEntrega().getPedidoTransferenciaLicitacao().getEmpresaAlmoxarifado().getCnpjFormatado()));
        }
    }

    private DropDown getDropDownSituacao() {
        DropDown dropDown = new DropDown("situacao");

        dropDown.addChoice(RegistroNotaFiscal.STATUS_ABERTO, BundleManager.getString("pendente"));
        dropDown.addChoice(RegistroNotaFiscal.STATUS_ENCERRADO, BundleManager.getString("confirmada"));
        dropDown.addChoice(null, BundleManager.getString("todos"));

        return dropDown;
    }

    private DropDown getDropDownTipoData() {
        DropDown dropDown = new DropDown("tipoData");

        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_EMISSAO, BundleManager.getString("dataEmissao"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_LANCAMENTO, BundleManager.getString("dataCadastro"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_PORTARIA, BundleManager.getString("dataPortaria"));

        return dropDown;
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(RegistroNotaFiscal.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("fornecedor"), VOUtils.montarPath(RegistroNotaFiscal.PROP_FORNECEDOR, Pessoa.PROP_DESCRICAO), VOUtils.montarPath(RegistroNotaFiscal.PROP_FORNECEDOR, Pessoa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("documento"), VOUtils.montarPath(RegistroNotaFiscal.PROP_NUMERO_NOTA_FISCAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataEmissaoAbv"), VOUtils.montarPath(RegistroNotaFiscal.PROP_DATA_EMISSAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataPortariaAbv"), VOUtils.montarPath(RegistroNotaFiscal.PROP_DATA_PORTARIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(RegistroNotaFiscal.PROP_STATUS), VOUtils.montarPath(RegistroNotaFiscal.PROP_STATUS_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("valorTotal"), VOUtils.montarPath(RegistroNotaFiscal.PROP_VALOR_TOTAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoDocumento"), VOUtils.montarPath(RegistroNotaFiscal.PROP_TIPO_DOCUMENTO, TipoDocumento.PROP_DESCRICAO)));

        return columns;
    }

    private CustomColumn<RegistroNotaFiscal> getCustomColumn() {
        return new CustomColumn<RegistroNotaFiscal>() {

            @Override
            public Component getComponent(String componentId, final RegistroNotaFiscal rowObject) {
                return new ConsultaNotaFiscalColumnPanel(componentId, rowObject,
                        isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.ESTORNAR), entradaNotaFiscalParcial) {

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        ConsultaNotaFiscalEntradaPage.this.getPageableTable().update(target);
                    }

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroNotaFiscalStep1Page(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(RegistroNotaFiscalFacade.class).deletarRegistroNotaFiscal(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesNotaFiscalEntradaPage(rowObject));
                    }

                    @Override
                    public void onEstornar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(RegistroNotaFiscalFacade.class).estornarNotaFiscal(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesNotaFiscalEntradaPage(rowObject, true));
                    }

                    @Override
                    public void onImprimirEtiqueta(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesImpressaoEtiquetaNotaFiscalEntradaPage(rowObject));
                    }

                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaNotaFiscalEntrada()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(RegistroNotaFiscal.PROP_DATA_EMISSAO, false);
            }

        };
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroNotaFiscal.PROP_NUMERO_NOTA_FISCAL), QueryParameter.IGUAL, documento));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroNotaFiscal.PROP_FORNECEDOR), QueryParameter.IGUAL, fornecedor));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroNotaFiscal.PROP_EMPRESA), QueryParameter.IGUAL, br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa()));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroNotaFiscal.PROP_TIPO_DOCUMENTO), QueryParameter.IGUAL, tipoDocumento));
        if (periodo != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(tipoData, Data.adjustRangeHour(periodo)));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroNotaFiscal.PROP_STATUS), QueryParameter.IGUAL, situacao));

        return parameters;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtNota;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroNotaFiscalStep1Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaEntradaMateriaisMedicamentos");
    }

}
