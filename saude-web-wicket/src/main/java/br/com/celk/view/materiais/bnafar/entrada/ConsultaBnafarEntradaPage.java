package br.com.celk.view.materiais.bnafar.entrada;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.table.selection.DTOSelection;
import br.com.celk.component.table.selection.MultiSelectionPageableTable;
import br.com.celk.materiais.bnafar.consultaIntegracao.entrada.ConsultaIntegracaoBnafarEntradaDTO;
import br.com.celk.materiais.bnafar.consultaIntegracao.entrada.ConsultaIntegracaoBnafarEntradaDTOParam;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.dao.paginacao.DataPagingResultImpl;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Marques
 * Programa - 1084
 */
@Private
public class ConsultaBnafarEntradaPage extends ConsultaPage<ConsultaIntegracaoBnafarEntradaDTO, ConsultaIntegracaoBnafarEntradaDTOParam> {

    private ConsultaIntegracaoBnafarEntradaDTOParam param = new ConsultaIntegracaoBnafarEntradaDTOParam();

    private Empresa empresa;
    private Produto produto;
    private DatePeriod dataEntrada;
    private Long statusRegistro;
    private String numeroDocumento;

    private AbstractAjaxButton reenviar;
    private DlgConfirmacaoSimNao dlgMensagemConfirmacao;

    public ConsultaBnafarEntradaPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {

        // Colocado um warn para avisar que o processamento está desabilitado pelo momento.
        ConsultaBnafarEntradaPage.this.warn(BundleManager.getString("msgProcessoDesabilitado"));

        form.add(new AutoCompleteConsultaEmpresa("empresa", new PropertyModel(this, "empresa")));
        form.add(new AutoCompleteConsultaProduto("produto", new PropertyModel(this, "produto")));
        form.add(new InputField("numeroDocumento", new PropertyModel(this, "numeroDocumento")));
        form.add(getEnumStatusRegistros());
        form.add(new PnlDatePeriod("dataEntrada", new PropertyModel(this, "dataEntrada")));

        getControls().add(reenviar = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                reprocessarItensSelecionados(getIntegracoesSelecionadas());
                getPageableTable().update(target);
            }
        });
        reenviar.add(new AttributeModifier("class", "btn-orange"));
        reenviar.add(new AttributeModifier("value", bundle("reenviar")));

        /*Setado para o Botão de reenviar ficar invisivel, pois o BNAFAR no momento não está enviando esse tipo de integração
        será reabilitado posteriormente quando vir a integracao da entrada pelo RNDS*/
        reenviar.setVisible(false);

        AbstractAjaxButton reenviarTodosComErro = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmarReenvio(target);
                getPageableTable().update(target);
            }
        };
        reenviarTodosComErro.add(new AttributeModifier("class", "btn-blue"));
        reenviarTodosComErro.add(new AttributeModifier("value", bundle("reenviarTodosComErro")));
        reenviarTodosComErro.add(new AttributeModifier("style", "margin-left: 5px;"));

        /*Setado para o Botão de reenviar ficar invisivel, pois o BNAFAR no momento não está enviando esse tipo de integração
        será reabilitado posteriormente quando vir a integracao da entrada pelo RNDS*/
        reenviarTodosComErro.setVisible(false);

        getControls().add(reenviarTodosComErro);
        getLinkNovo().setVisible(false);
        add(form);
    }

    private void confirmarReenvio(AjaxRequestTarget target) throws ValidacaoException {
        if (isProcessando()) {
            throw new ValidacaoException("Já existe um processo de reenvio de erros em execução. Aguarde a conclusão para novo reenvio.");
        }
        if (dlgMensagemConfirmacao == null) {
            addModal(target, dlgMensagemConfirmacao = new DlgConfirmacaoSimNao(newModalId(), bundle("msgConfirmacaoReenvioErrosBnafarX", BnafarHelper.TipoSincronizacao.ENTRADA.descricao())) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    reprocessarTodosItensComErro(target);
                }
            });
        }
        dlgMensagemConfirmacao.show(target);
    }

    private boolean isProcessando() {
        return LoadManager.getInstance(AsyncProcess.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AsyncProcess.STATUS_AGUARDANDO_PROCESSAMENTO, AsyncProcess.STATUS_PROCESSANDO)))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_TIPO, AsyncProcess.TIPO_PROCESSO))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_DATA_REGISTRO, BuilderQueryCustom.QueryParameter.MAIOR, Data.removeDias(DataUtil.getDataAtual(), 1)))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_NOME_PROCESSO, BnafarHelper.getDescricaoProcessoAssincrono(BnafarHelper.TipoSincronizacao.ENTRADA)))
                .start().exists();
    }

    private void reprocessarTodosItensComErro(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        info(target, "Processamento iniciado! Em alguns instantes você receberá uma mensagem com mais informações.");
        BOFactory.getBO(MaterialBasicoFacade.class).processarReenvioErrosBnafar(BnafarHelper.TipoSincronizacao.ENTRADA);
    }

    private DropDown getEnumStatusRegistros() {

        DropDown dropDown = new DropDown("statusRegistro", new PropertyModel(this, "statusRegistro"));

        dropDown.addChoice(null, BundleManager.getString("todos"));
        dropDown.addChoice(BnafarHelper.StatusRegistro.ENVIADO.value(), BnafarHelper.StatusRegistro.ENVIADO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.PROCESSADO.value(), BnafarHelper.StatusRegistro.PROCESSADO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.ERRO.value(), BnafarHelper.StatusRegistro.ERRO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.ERRO_SEM_REENVIO.value(), BnafarHelper.StatusRegistro.ERRO_SEM_REENVIO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.CANCELADO.value(), BnafarHelper.StatusRegistro.CANCELADO.descricao());

        return dropDown;
    }

    @Override
    public List getColumns(List<IColumn> columns) {

        ConsultaIntegracaoBnafarEntradaDTO proxy = on(ConsultaIntegracaoBnafarEntradaDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(BundleManager.getString("dataEntrada"), proxy.getDataEntrada()));
        columns.add(createColumn(BundleManager.getString("numeroDocumento"), proxy.getNumeroDocumento()));
        columns.add(createColumn(BundleManager.getString("estabelecimento"), proxy.getEmpresa().getDescricao()));
        columns.add(createColumn(BundleManager.getString("produto"), proxy.getProduto().getDescricao()));
        columns.add(createColumn(BundleManager.getString("lote"), proxy.getBnafarEntradaElo().getBnafarEntradaIntegracao().getCodigo()));
        columns.add(createColumn(BundleManager.getString("ultimoEnvio"), proxy.getBnafarEntrada().getDataUltimoEnvio()));
        columns.add(createColumn(BundleManager.getString("situacao"), proxy.getDescricaoSituacao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ConsultaIntegracaoBnafarEntradaDTO>() {
            @Override
            public void customizeColumn(ConsultaIntegracaoBnafarEntradaDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarEntradaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarEntradaDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesBnafarEntradaPage(modelObject.getBnafarEntrada(), true));
                    }
                });
                /*Setado para o Botão de reenviar ficar invisivel, pois o BNAFAR no momento não está enviando esse tipo de integração
                            será reabilitado posteriormente quando vir a integracao da entrada pelo RNDS*/
                addAction(ActionType.REENVIAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarEntradaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarEntradaDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactory.getBO(MaterialBasicoFacade.class).reenviarBnafarEntrada(modelObject.getBnafarEntradaElo().getBnafarEntradaIntegracao().getCodigo());
                        getPageableTable().update(target);
                    }
                }).setVisible(rowObject.getBnafarEntrada().getStatusRegistro().equals(BnafarHelper.StatusRegistro.ERRO.value()) && false);
                addAction(ActionType.CANCELAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarEntradaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarEntradaDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactory.getBO(MaterialBasicoFacade.class).alterarStatusBnafarEntrada(modelObject.getBnafarEntrada().getCodigo(), modelObject.getBnafarEntradaElo().getCodigo(), BnafarHelper.StatusRegistro.CANCELADO.value());
                        getPageableTable().update(target);
                    }
                }).setVisible(rowObject.getBnafarEntrada().getStatusRegistro().equals(BnafarHelper.StatusRegistro.ERRO.value()));
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<ConsultaIntegracaoBnafarEntradaDTO, ConsultaIntegracaoBnafarEntradaDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaIntegracaoBnafarEntradaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                DataPagingResult<ConsultaIntegracaoBnafarEntradaDTO> result = BOFactoryWicket.getBO(MaterialBasicoFacade.class).queryConsultaIntegracaoBnafarEntrada(dataPaging);
                return getDataPagingSelectedDTO(result);
            }
        };
    }

    static private DataPagingResult getDataPagingSelectedDTO(DataPagingResult<ConsultaIntegracaoBnafarEntradaDTO> result) {
        List<ConsultaIntegracaoBnafarEntradaDTO> resultList = result.getList();
        List<DTOSelection<ConsultaIntegracaoBnafarEntradaDTO>> resultSelectList = new ArrayList<>();
        if (resultList != null) {
            for (ConsultaIntegracaoBnafarEntradaDTO dto : resultList) {
                resultSelectList.add(new DTOSelection<>(false, dto));
            }
        }
        DataPagingResult<DTOSelection<ConsultaIntegracaoBnafarEntradaDTO>> resultSelection = new DataPagingResultImpl<>(resultSelectList, result.getAmountResults());
        return resultSelection;
    }

    private List<ConsultaIntegracaoBnafarEntradaDTO> getIntegracoesSelecionadas() {
        return ((MultiSelectionPageableTable) getPageableTable()).getSelectedObjects();
    }

    @Override
    public ConsultaIntegracaoBnafarEntradaDTOParam getParameters() {

        ConsultaIntegracaoBnafarEntradaDTOParam param = new ConsultaIntegracaoBnafarEntradaDTOParam();

        param.setEmpresa(empresa);
        param.setProduto(produto);
        param.setDataEntrada(dataEntrada);
        param.setNumeroDocumento(numeroDocumento);
        param.setStatusRegistro(statusRegistro);

        return param;
    }

    @Override
    public PageableTable newPageableTable(String tableId, List<IColumn> columns, IPagerProvider pagerProvider, int rowsPerPage) {
        return new MultiSelectionPageableTable(tableId, columns, pagerProvider);
    }

    private void reprocessarItensSelecionados(List<ConsultaIntegracaoBnafarEntradaDTO> bnafarEntradaList) throws DAOException, ValidacaoException {
        if (CollectionUtils.isEmpty(bnafarEntradaList)) return;

        ArrayList<Long> codigosReenvio = new ArrayList<>();
        for (ConsultaIntegracaoBnafarEntradaDTO modelObject : bnafarEntradaList) {
            if (modelObject.getBnafarEntradaElo() != null && modelObject.getBnafarEntradaElo().getBnafarEntradaIntegracao() != null) {
                codigosReenvio.add(modelObject.getBnafarEntradaElo().getBnafarEntradaIntegracao().getCodigo());
            }
        }
        if (CollectionUtils.isEmpty(codigosReenvio)) return;

        BOFactory.getBO(MaterialBasicoFacade.class).reenviarBnafarEntrada(codigosReenvio);
    }

    @Override
    public Class getCadastroPage() {
        return this.getClass();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consulta_bnafar_entrada");
    }
}

