package br.com.celk.view.vigilancia.solicitacaoAgendamento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaAtividadeVeterinaria;
import br.com.celk.vigilancia.dto.RelatorioSolicitacaoAgendamentoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 576
 */
@Private
public class RelatorioSolicitacaoPendentePage extends RelatorioPage<RelatorioSolicitacaoAgendamentoDTOParam> {

    @Override
    public void init(Form<RelatorioSolicitacaoAgendamentoDTOParam> form) {

        RelatorioSolicitacaoAgendamentoDTOParam proxy = on(RelatorioSolicitacaoAgendamentoDTOParam.class);

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidade())));
        form.add(new AutoCompleteConsultaAtividadeVeterinaria(path(proxy.getAtividadeVeterinaria())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getUrgente()), RelatorioSolicitacaoAgendamentoDTOParam.Urgente.values()));
        form.add(getDropDownFA());
        form.add(new PnlDatePeriod(path(proxy.getPeriodo())));

    }
    
    public DropDown getDropDownFA(){
        DropDown dropDownFA = new DropDown("formaApresentacao");
        
        dropDownFA.addChoice(RelatorioSolicitacaoAgendamentoDTOParam.FormaApresentacao.GERAL, RelatorioSolicitacaoAgendamentoDTOParam.FormaApresentacao.GERAL.toString());
        dropDownFA.addChoice(RelatorioSolicitacaoAgendamentoDTOParam.FormaApresentacao.TIPO_ATIVIDADE, RelatorioSolicitacaoAgendamentoDTOParam.FormaApresentacao.TIPO_ATIVIDADE.toString());
        
        return dropDownFA;
    }

    @Override
    public Class<RelatorioSolicitacaoAgendamentoDTOParam> getDTOParamClass() {
        return RelatorioSolicitacaoAgendamentoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioSolicitacaoAgendamentoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioSolicitacaoPendenteCva(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoSolicitacaoPendente");
    }

}
