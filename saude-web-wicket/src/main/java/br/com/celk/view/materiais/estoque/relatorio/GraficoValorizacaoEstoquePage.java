package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.materiais.grupoproduto.autocomplete.AutoCompleteConsultaGrupoProduto;
import br.com.celk.view.materiais.grupoproduto.pnl.PnlConsultaGrupoProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.GraficoValorizacaoEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;


/**
 *
 * <AUTHOR>
 * Programa - 145
 */
@Private

public class GraficoValorizacaoEstoquePage extends RelatorioPage<GraficoValorizacaoEstoqueDTOParam> {

    private AutoCompleteConsultaGrupoProduto autoCompleteConsultaGrupoProduto;
    
    private Long mesInicial;
    private Long anoInicial;
    private Long mesFinal;
    private Long anoFinal;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaGrupoProduto = new AutoCompleteConsultaGrupoProduto("grupoProduto"));
        form.add(DropDownUtil.setMesesChoices(new DropDown("mesInicial", new PropertyModel(this, "mesInicial")), false));
        form.add(DropDownUtil.setAnoChoices(new DropDown("anoInicial", new PropertyModel(this, "anoInicial")), false, false));
        form.add(DropDownUtil.setMesesChoices(new DropDown("mesFinal", new PropertyModel(this, "mesFinal")), false));
        form.add(DropDownUtil.setAnoChoices(new DropDown("anoFinal", new PropertyModel(this, "anoFinal")), false, false));
        form.add(getCbxFormaApresentacao());
        
        autoCompleteConsultaGrupoProduto.setMultiplaSelecao(true);
        autoCompleteConsultaGrupoProduto.setOperadorValor(true);
    }
    
    private DropDown getCbxFormaApresentacao(){
        DropDown cbxFormaApresentacao = new DropDown("formaApresentacao");
        
        cbxFormaApresentacao.addChoice(ReportProperties.GERAL, BundleManager.getString("geral"));
        cbxFormaApresentacao.addChoice(ReportProperties.AGRUPAR_GRUPO, BundleManager.getString("grupoSubGrupo"));
        
        return cbxFormaApresentacao;
    }

    @Override
    public Class<GraficoValorizacaoEstoqueDTOParam> getDTOParamClass() {
        return GraficoValorizacaoEstoqueDTOParam.class;
    }

    @Override
    public DataReport getDataReport(GraficoValorizacaoEstoqueDTOParam param) throws ReportException {
        Calendar cInicial = GregorianCalendar.getInstance();
        cInicial.set(Calendar.MONTH, mesInicial.intValue()-1);
        cInicial.set(Calendar.YEAR, anoInicial.intValue());
        Date dataInicio = cInicial.getTime();
        Calendar cFinal = GregorianCalendar.getInstance();
        cFinal.set(Calendar.MONTH, mesFinal.intValue()-1);
        cFinal.set(Calendar.YEAR, anoFinal.intValue());
        Date dataFim = cFinal.getTime();
        param.setPeriodo(Data.adjustRangeDay(dataInicio, dataFim));
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).graficoValorizacaoEstoque(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("graficoConsumoProdutos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaGrupoProduto.getTxtDescricao().getTextField();
    }

}
