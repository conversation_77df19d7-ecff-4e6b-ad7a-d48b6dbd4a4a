package br.com.celk.view.materiais.dispensacao.tabelapreco;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.EditDeleteActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.hospital.convenio.autocomplete.AutoCompleteConsultaConvenio;
import br.com.celk.view.materiais.dispensacao.tabelapreco.customize.CustomizeConsultaTabelaPreco;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.TabelaPreco;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 269
 */
@Private
public class ConsultaTabelaPrecoPage extends ConsultaPage<TabelaPreco, List<BuilderQueryCustom.QueryParameter>> {

    private Convenio convenio;
    private Produto produto;
    private Date data;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaConvenio("convenio"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(false);
        form.add(new DateChooserAjax("data"));

        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(TabelaPreco.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("inicioVigencia"), VOUtils.montarPath(TabelaPreco.PROP_INICIO_VIGENCIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("convenio"), VOUtils.montarPath(TabelaPreco.PROP_CONVENIO, Convenio.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("produto"), VOUtils.montarPath(TabelaPreco.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("preco"), VOUtils.montarPath(TabelaPreco.PROP_PRECO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("ultimaAlteracao"), VOUtils.montarPath(TabelaPreco.PROP_DATA_USUARIO), DateColumn.class));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("versaoBrasindice"), VOUtils.montarPath(TabelaPreco.PROP_VERSAO_BRASINDICE)));
        return columns;
    }

    private CustomColumn<TabelaPreco> getCustomColumn(){
        return new CustomColumn<TabelaPreco>() {

            @Override
            public Component getComponent(String componentId, final TabelaPreco rowObject) {
                return new EditDeleteActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTabelaPrecoStep2(rowObject, false));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaTabelaPreco()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TabelaPreco.PROP_CODIGO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TabelaPreco.PROP_INICIO_VIGENCIA), data));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TabelaPreco.PROP_CONVENIO), this.convenio));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TabelaPreco.PROP_PRODUTO), this.produto));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTabelaPrecoStep1.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTabelaPreco");
    }
}
