package br.com.celk.view.vigilancia.externo.view.home;

import br.com.celk.component.behavior.AjaxTimerDelayComponentBehavior;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.resources.Resources;
import br.com.celk.system.Application;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.login.NodeButtonEventListener;
import br.com.celk.view.vigilancia.externo.template.base.BasePageVigilancia;
import br.com.celk.view.vigilancia.externo.view.consulta.ConsultaRequerimentoVigilanciaExternoPage;
import br.com.celk.view.vigilancia.externo.view.estabelecimento.ConsultaEstabelecimentoExternoPage;
import br.com.celk.view.vigilancia.externo.view.home.dialog.DlgDocumentosNecessarios;
import br.com.celk.view.vigilancia.externo.view.home.dialog.DlgRoteiroInspecao;
import br.com.celk.view.vigilancia.externo.view.servicos.*;
import br.com.celk.view.vigilancia.externo.view.servicos.dialogs.*;
import br.com.celk.view.vigilancia.requerimentos.NodeButton;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.AutosPendentesDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroInspecao;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.ResourceReference;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

/**
 * Created by maicon on 17/05/16.
 * Programa - 692
 */
public class VigilanciaHomePage extends BasePageVigilancia {

    private Form<RequerimentoVigilancia> form;
    private Form containerListView;
    private List<TipoSolicitacao> tipoSolicitacaoList = new ArrayList<TipoSolicitacao>();
    private List<TipoSolicitacao> tipoSolicitacaoPermitedList = new ArrayList<TipoSolicitacao>();
    private List<RequerimentoVigilancia> protocolosList;
    private TipoSolicitacao tipoSolicitacaoSelecionado;
    private ListView<List<TipoSolicitacao>> listView;

    private Long protocoloId;
    private InputField txtBuscaRequerimento;
    private InputField txtBuscaProtocolo;
    private String buscaRequerimento;
    private String buscaProtocolo;
    private List<NodeButtonEventListener> nodeButtonListeners = new ArrayList<NodeButtonEventListener>();
    private AjaxTimerDelayComponentBehavior ajaxTimerDelayComponentBehavior;
    private AjaxTimerDelayComponentBehavior ajaxTimerDelayComponentBehaviorProtocolo;
    private AbstractAjaxLink ico;
    private AbstractAjaxButton btnAbrirEstabPage;
    private AbstractAjaxButton abrirConsultaPage;

    private DlgEscolherTipoAlvaraExterno dlgEscolherTipoAlvaraExterno;
    private DlgEscolherTipoInspecaoSanitariaExterno dlgEscolherTipoInspecaoSanitaria;
    private DlgEscolherTipoResponsabilidadeTecnicaExterno dlgEscolherTipoResponsabilidadeTecnicaExterno;
    private DlgEscolherTipoContratoSocialExterno dlgEscolherTipoContratoSocial;

    private DlgDocumentosNecessarios dlgDocumentosNecessarios;
    private DlgRoteiroInspecao dlgRoteiroInspecao;

    private AbstractAjaxLink btnDocumentosNecessarios;
    private AbstractAjaxLink btnRoteiroInspecao;
    private DlgEscolherTipoProjetoExterno dlgEscolherTipoProjetoExterno;
    private WebMarkupContainer containerExibirRoteiroInspecao;
    private WebMarkupContainer containerCampoBusca;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    //Pop-up alertas Autos
    private DlgConfirmacaoOk dlgConfirmacaoOk;
    private DlgConfirmacaoOk dlgConfirmacaoOkEmail;
    private boolean exibirPopUp =  false;
    private AutosPendentesDTO autosPendentesDTO;

    public VigilanciaHomePage() {
        init();
    }

    private void init() {
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        buscarTipoSolicitacao();
        form = new Form("form", new CompoundPropertyModel(new RequerimentoVigilancia()));

        containerCampoBusca = new WebMarkupContainer("containerCampoBusca");
        containerCampoBusca.setOutputMarkupId(true);

        containerCampoBusca.add(txtBuscaRequerimento = new InputField("buscaRequerimento", new PropertyModel(this, "buscaRequerimento")));

        containerCampoBusca.setVisible(false);
        form.add(containerCampoBusca);

        form.add(txtBuscaProtocolo = new InputField("buscaProtocolo", new PropertyModel(this, "buscaProtocolo")));
        form.add(ico = new AbstractAjaxLink("ico") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                carregarRequerimentoVigilancia();
            }
        });
        form.add(abrirConsultaPage = new AbstractAjaxButton("abrirConsultaPage") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarDataRecebimentoAcessoAutoPendentes(autosPendentesDTO);
                setResponsePage(new ConsultaRequerimentoVigilanciaExternoPage());
            }
        });
        form.add(btnAbrirEstabPage = new AbstractAjaxButton("btnAbrirEstabPage") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaEstabelecimentoExternoPage());
            }
        });
        ico.setOutputMarkupId(true);
        ico.setOutputMarkupPlaceholderTag(true);
        ico.setEnabled(false);
        txtBuscaRequerimento.addAjaxUpdateValue();
        ajaxTimerDelayComponentBehavior = new AjaxTimerDelayComponentBehavior() {
            @Override
            public void onAction(AjaxRequestTarget target, String value) {
                buscarRequerimento(target, value);
            }
        };
        ajaxTimerDelayComponentBehaviorProtocolo = new AjaxTimerDelayComponentBehavior() {
            @Override
            public void onAction(AjaxRequestTarget target, String value) {
                buscarProtocolo(target, value);
            }
        };

        txtBuscaRequerimento.add(ajaxTimerDelayComponentBehavior);
        txtBuscaProtocolo.add(ajaxTimerDelayComponentBehaviorProtocolo);

        listView = getListView();
        listView.setOutputMarkupId(true);
        containerListView = new Form("containerListView");
        containerListView.setOutputMarkupId(true);
        containerListView.add(listView);
        form.add(containerListView);

        WebMarkupContainer panelMsg = new WebMarkupContainer("panelMsg");
        form.add(panelMsg);
        panelMsg.setVisible(false);
        if (CollectionUtils.isAllEmpty(listView.getList())) {
            Label lblMsg = new Label("msg", BundleManager.getString("msgNenhumTipoSolicitacaoAtivoCadastrado"));
            panelMsg.add(lblMsg);
            panelMsg.setVisible(true);
        } else {
            panelMsg.setVisible(false);
        }

        form.add(btnDocumentosNecessarios = new AbstractAjaxLink("btnDocumentosNecessarios") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgDocumentosNecessarios(target);
            }
        });

        form.add(btnRoteiroInspecao = new AbstractAjaxLink("btnRoteiroInspecao") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgRoteiroInspecao(target);
            }
        });

        containerExibirRoteiroInspecao = new WebMarkupContainer("containerExibirRoteiroInspecao");
        containerExibirRoteiroInspecao.setOutputMarkupId(true);
        form.add(containerExibirRoteiroInspecao);

        final String urlRoteiroInspecao = configuracaoVigilancia != null ? configuracaoVigilancia.getUrlRoteiroInspecao() : null;

        containerExibirRoteiroInspecao.add(new AbstractAjaxLink("btnRoteiroInspecao") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.appendJavaScript("setTimeout(\"window.open('" + urlRoteiroInspecao + "','_blank')\", 100);");
            }
        });

        if (urlRoteiroInspecao == null) {
            containerExibirRoteiroInspecao.setVisible(false);
        }


        if (new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaEstabelecimentoExternoPage.class.getName())) {
            btnAbrirEstabPage.setVisible(true);
        } else {
            btnAbrirEstabPage.setVisible(false);
        }

        if (new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaRequerimentoVigilanciaExternoPage.class.getName())) {
            abrirConsultaPage.setVisible(true);
        } else {
            abrirConsultaPage.setVisible(false);
        }

        btnDocumentosNecessarios.setVisible(isActionPermitted(Permissions.VISUALIZAR_DOCUMENTOS_NECESSARIOS, true));
        btnRoteiroInspecao.setVisible(isActionPermitted(Permissions.VISUALIZAR_ROTEIRO_INSPECAO, true));


        add(form);
    }

    private void dlgDocumentosNecessarios(AjaxRequestTarget target) {
        if (dlgDocumentosNecessarios == null) {
            addModal(target, dlgDocumentosNecessarios = new DlgDocumentosNecessarios(newModalId()) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        dlgDocumentosNecessarios.show(target, tipoSolicitacaoPermitedList);
    }

    private void dlgRoteiroInspecao(AjaxRequestTarget target) {
        if (dlgRoteiroInspecao == null) {
            addModal(target, dlgRoteiroInspecao = new DlgRoteiroInspecao(newModalId()) {
                @Override
                public void onFechar(AjaxRequestTarget target) {
                }

                @Override
                public DataReport onPrint(RoteiroInspecao roteiroInspecao) throws ReportException {
                    return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRoteiroInspecaoExterno(roteiroInspecao);
                }
            });
        }
        dlgRoteiroInspecao.show(target);
    }

    private ListView<List<TipoSolicitacao>> getListView() {
        LoadableDetachableModel<List<List<TipoSolicitacao>>> model = new LoadableDetachableModel<List<List<TipoSolicitacao>>>() {
            @Override
            protected List<List<TipoSolicitacao>> load() {
                List<List<TipoSolicitacao>> matrizTipoSolicitacao = new ArrayList<List<TipoSolicitacao>>();
                List<TipoSolicitacao> colunaTipoSolicitacao;

                for (Iterator<TipoSolicitacao> iterator = tipoSolicitacaoList.iterator(); iterator.hasNext(); ) {
                    colunaTipoSolicitacao = new ArrayList<>();
                    for (int i = 0; i < 3; i++) {
                        if (iterator.hasNext()) {
                            colunaTipoSolicitacao.add(iterator.next());
                        }
                    }
                    matrizTipoSolicitacao.add(colunaTipoSolicitacao);
                }

                return matrizTipoSolicitacao;
            }
        };

        return new ListView<List<TipoSolicitacao>>("nodes", model) {
            @Override
            protected void populateItem(final ListItem<List<TipoSolicitacao>> item) {

                LoadableDetachableModel<List<TipoSolicitacao>> modelLinha = new LoadableDetachableModel<List<TipoSolicitacao>>() {
                    @Override
                    protected List<TipoSolicitacao> load() {
                        return item.getModelObject();
                    }
                };

                ListView<TipoSolicitacao> listViewLinha = new ListView<TipoSolicitacao>("nodesLinha", modelLinha) {
                    @Override
                    protected void populateItem(final ListItem item) {
                        item.setOutputMarkupId(true);
                        NodeButton nodeButton;

                        item.add(nodeButton = new NodeButton("btnNode", (TipoSolicitacao) item.getModelObject()) {
                            @Override
                            public String getDescricao() {
                                return ((TipoSolicitacao) item.getModel().getObject()).getDescricao();
                            }

                            @Override
                            public ResourceReference getImageNode() {
                                return Resources.getResourceReferenceTipoSolicitacao(((TipoSolicitacao) item.getModel().getObject()).getEnumTipoRequerimento());
                            }

                            @Override
                            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                VigilanciaHomePage.this.tipoSolicitacaoSelecionado = (TipoSolicitacao) getNode();

                                if (TipoSolicitacao.TipoRequerimento.EXUMACAO_RESTOS_MORTAIS.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoExumacaoExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                                } else if (TipoSolicitacao.TipoRequerimento.ALVARA.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    if (dlgEscolherTipoAlvaraExterno == null) {
                                        addModal(target, dlgEscolherTipoAlvaraExterno = new DlgEscolherTipoAlvaraExterno(newModalId(), isActionPermitted(Permissions.ALVARA_INICIAL, true), isActionPermitted(Permissions.ALVARA_CADASTRO_EVENTO, true), isActionPermitted(Permissions.ALVARA_PARTICIPANTE_EVENTO, true), isActionPermitted(Permissions.ALVARA_REVALIDACAO, true), isActionPermitted(Permissions.AUTORIZACAO_SANITARIA, true), isActionPermitted(Permissions.LICENCA_SANITARIA, true), isActionPermitted(Permissions.REVALIDACAO_LICENCA_SANITARIA, true)));
                                    }
                                    dlgEscolherTipoAlvaraExterno.show(target, tipoSolicitacaoSelecionado);
                                } else if (TipoSolicitacao.TipoRequerimento.DECLARACAO_CARTORIO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoDeclaracaoVeracidadeExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                                } else if (TipoSolicitacao.TipoRequerimento.INSPECAO_SANITARIA.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    if (dlgEscolherTipoInspecaoSanitaria == null) {
                                        addModal(target, dlgEscolherTipoInspecaoSanitaria = new DlgEscolherTipoInspecaoSanitariaExterno(newModalId(),
                                                isActionPermitted(Permissions.INSPECAO_SANITARIA_ROTINA, true),
                                                isActionPermitted(Permissions.INSPECAO_SANITARIA_AFE_ANVISA, true)));
                                    }
                                    dlgEscolherTipoInspecaoSanitaria.show(target, tipoSolicitacaoSelecionado);
                                } else if (TipoSolicitacao.TipoRequerimento.LICENCA_TRANSPORTE.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoLicencaTransporteExternoPage(tipoSolicitacaoSelecionado, false, VigilanciaHomePage.class));
                                } else if (TipoSolicitacao.TipoRequerimento.REQUISICAO_RECEITUARIO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoReceitaBExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                                } else if (TipoSolicitacao.TipoRequerimento.RESPONSABILIDADE_TECNICA.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    if (dlgEscolherTipoResponsabilidadeTecnicaExterno == null) {
                                        addModal(target, dlgEscolherTipoResponsabilidadeTecnicaExterno = new DlgEscolherTipoResponsabilidadeTecnicaExterno(newModalId(),
                                                isActionPermitted(Permissions.BAIXA_RESPONSABILIDADE_TECNICA, true),
                                                isActionPermitted(Permissions.ENTRADA_RESPONSABILIDADE_TECNICA, true)));
                                    }
                                    dlgEscolherTipoResponsabilidadeTecnicaExterno.show(target, tipoSolicitacaoSelecionado);
                                } else if (TipoSolicitacao.TipoRequerimento.CONTRATO_SOCIAL.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    if (dlgEscolherTipoContratoSocial == null) {
                                        addModal(target, dlgEscolherTipoContratoSocial = new DlgEscolherTipoContratoSocialExterno(newModalId(),
                                                isActionPermitted(Permissions.ALTERACAO_RESPONSABILIDADE_LEGAL, true),
                                                isActionPermitted(Permissions.ALTERACAO_ATIVIDADE_ECONOMICA, true),
                                                isActionPermitted(Permissions.ALTERACAO_ENDERECO, true),
                                                isActionPermitted(Permissions.ALTERACAO_RAZAO_SOCIAL, true)));
                                    }
                                    dlgEscolherTipoContratoSocial.show(target, tipoSolicitacaoSelecionado);
                                } else if (TipoSolicitacao.TipoRequerimento.PEDIDO_DOCUMENTO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoPedidoDocumentoExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                                } else if (TipoSolicitacao.TipoRequerimento.VACINACAO_EXTRAMURO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoVacinacaoExtramuroExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                                } else if (TipoSolicitacao.TipoRequerimento.BAIXA_ESTABELECIMENTO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoBaixaEstabelecimentoExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                                } else if (TipoSolicitacao.TipoRequerimento.BAIXA_VEICULO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoBaixaVeiculoExternoPage(tipoSolicitacaoSelecionado, false, VigilanciaHomePage.class));
                                } else if (TipoSolicitacao.TipoRequerimento.ANALISE_PROJETOS.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    if (dlgEscolherTipoProjetoExterno == null) {
                                        addModal(target, dlgEscolherTipoProjetoExterno = new DlgEscolherTipoProjetoExterno(newModalId(),
                                                isActionPermitted(Permissions.PROJETO_BASICO_ARQUITETURA, true),
                                                isActionPermitted(Permissions.VISTORIA_LAUDO_CONFORMIDADE_PBA, true),
                                                isActionPermitted(Permissions.ANALISE_PROJETO_HIDROSSANITARIO, true),
                                                isActionPermitted(Permissions.VISTORIA_HABITESE_SANITARIO, true),
                                                isActionPermitted(Permissions.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO, true),
                                                isActionPermitted(Permissions.HABITE_SE_DECLARATORIO, true),
                                                isActionPermitted(Permissions.PROJETO_ARQUITETONICO_SANITARIO_EXTERNO, true)
                                        ));
                                    }
                                    dlgEscolherTipoProjetoExterno.show(target, tipoSolicitacaoSelecionado);
                                } else if (TipoSolicitacao.TipoRequerimento.TREINAMENTO_ALIMENTO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoTreinamentoExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                                } else if (TipoSolicitacao.TipoRequerimento.SOLICITACAO_JURIDICA.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
                                    setResponsePage(new RequerimentoRestituicaoTaxaExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                                }
                            }
                        });
                        NodeButtonEventListener createListener = nodeButton.createListener();
                        nodeButtonListeners.add(createListener);
                    }
                };
                item.add(listViewLinha);
            }
        };
    }

    private void buscarRequerimento(AjaxRequestTarget target, String value) {
        tipoSolicitacaoList.clear();
        if (StringUtils.trimToNull(value) == null) {
            tipoSolicitacaoList.addAll(tipoSolicitacaoPermitedList);
        } else {
            for (TipoSolicitacao ts : tipoSolicitacaoPermitedList) {
                if (StringUtil.removeAcentos(ts.getDescricao()).toUpperCase().contains(StringUtil.removeAcentos(value).toUpperCase())) {
                    tipoSolicitacaoList.add(ts);
                }
            }
        }
        target.add(containerListView);
    }

    private void buscarProtocolo(AjaxRequestTarget target, String value) {
        if (!StringUtil.getDigits(value).isEmpty()) {
            Long aLong = Long.valueOf(StringUtil.getDigits(value));
            List<RequerimentoVigilancia> select = Lambda.select(protocolosList, having(on(RequerimentoVigilancia.class).getProtocolo(), equalTo(aLong)));
            if (select.size() > 0) {
                ico.setEnabled(true);
                protocoloId = select.get(0).getProtocolo();
            } else {
                ico.setEnabled(false);
            }
            target.add(ico);
        }

    }

    private void buscarTipoSolicitacao() {

        for (TipoSolicitacao tipoSolicitacao : buscarTipoSolicitacoes()) {
            if (TipoSolicitacao.TipoRequerimento.ALVARA.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.ALVARA_INICIAL, Permissions.ALVARA_PARTICIPANTE_EVENTO, Permissions.ALVARA_REVALIDACAO);
            } else if (TipoSolicitacao.TipoRequerimento.CONTRATO_SOCIAL.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.ALTERACAO_RESPONSABILIDADE_LEGAL, Permissions.ALTERACAO_ATIVIDADE_ECONOMICA, Permissions.ALTERACAO_ENDERECO, Permissions.ALTERACAO_RAZAO_SOCIAL);
            } else if (TipoSolicitacao.TipoRequerimento.RESPONSABILIDADE_TECNICA.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.BAIXA_RESPONSABILIDADE_TECNICA, Permissions.ENTRADA_RESPONSABILIDADE_TECNICA);
            } else if (TipoSolicitacao.TipoRequerimento.LICENCA_TRANSPORTE.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.LICENCA_TRANSPORTE);
            } else if (isActionPermitted(Permissions.EXUMACAO_RESTOS_MORTAIS, true) && TipoSolicitacao.TipoRequerimento.EXUMACAO_RESTOS_MORTAIS.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.EXUMACAO_RESTOS_MORTAIS);
            } else if (TipoSolicitacao.TipoRequerimento.INSPECAO_SANITARIA.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.INSPECAO_SANITARIA_ROTINA, Permissions.INSPECAO_SANITARIA_AFE_ANVISA);
            } else if (TipoSolicitacao.TipoRequerimento.DECLARACAO_CARTORIO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.DECLARACAO_CARTORIO);
            } else if (TipoSolicitacao.TipoRequerimento.REQUISICAO_RECEITUARIO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.REQUISICAO_RECEITUARIO_B);
            } else if (TipoSolicitacao.TipoRequerimento.PEDIDO_DOCUMENTO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.PEDIDO_DOCUMENTO);
            } else if (TipoSolicitacao.TipoRequerimento.VACINACAO_EXTRAMURO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.VACINACAO_EXTRAMURO);
            } else if (TipoSolicitacao.TipoRequerimento.BAIXA_ESTABELECIMENTO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.BAIXA_ESTABELECIMENTO);
            } else if (TipoSolicitacao.TipoRequerimento.BAIXA_VEICULO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.BAIXA_VEICULO);
            } else if (TipoSolicitacao.TipoRequerimento.ANALISE_PROJETOS.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao,
                        Permissions.PROJETO_BASICO_ARQUITETURA,
                        Permissions.VISTORIA_LAUDO_CONFORMIDADE_PBA,
                        Permissions.ANALISE_PROJETO_HIDROSSANITARIO,
                        Permissions.VISTORIA_HABITESE_SANITARIO,
                        Permissions.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO,
                        Permissions.PROJETO_ARQUITETONICO_SANITARIO_EXTERNO
                );
            } else if (TipoSolicitacao.TipoRequerimento.TREINAMENTO_ALIMENTO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.TREINAMENTOS_ALIMENTO);
            } else if (TipoSolicitacao.TipoRequerimento.SOLICITACAO_JURIDICA.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.RESTITUICAO_TAXA);
            }
        }
    }

    private void addTipoSolicitacaoIFPermitted(TipoSolicitacao tipoSolicitacao, Permissions... permissions) {
        for (Permissions permission : permissions) {
            if (isActionPermitted(permission, true)) {
                tipoSolicitacaoList.add(tipoSolicitacao);
                tipoSolicitacaoPermitedList.add(tipoSolicitacao);
                return;
            }
        }
    }

    private List<TipoSolicitacao> buscarTipoSolicitacoes() {
        return LoadManager.getInstance(TipoSolicitacao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoSolicitacao.PROP_ATIVO, RepositoryComponentDefault.SIM_LONG))
                .addSorter(new QueryCustom.QueryCustomSorter(TipoSolicitacao.PROP_DESCRICAO))
                .start().getList();
    }

    private boolean getParametroExibePopUp(){
        try {
            return RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("exiberpopuppdeautospendentes"));
        } catch (DAOException e) {
            throw new RuntimeException(e);
        }
    }

    private boolean existsAutos(){
        Usuario usuario = ApplicationSession.get().getSession().getUsuario();
        if (usuario != null){
            autosPendentesDTO = AutosHelper.getAutosPendentesUsuario(usuario);
            return AutosHelper.hasAutosUsuario(autosPendentesDTO);
        }
        return false;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("requerimentoProtocolo");
    }

    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();

        exibirPopUp = getParametroExibePopUp();
        if (exibirPopUp) {
            addModal(dlgConfirmacaoOkEmail = new DlgConfirmacaoOk(newModalId(), Bundle.getStringApplication("msg_autos_enviado_email")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
            dlgConfirmacaoOkEmail.setContentVisible(true);

            addModal(dlgConfirmacaoOk = new DlgConfirmacaoOk(newModalId(), Bundle.getStringApplication("msg_autos_existentes_usuario")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    Usuario usuario = ApplicationSession.get().getSession().getUsuario();
                    BOFactoryWicket.getBO(VigilanciaFacade.class).enviarEmailAutosPendentes(autosPendentesDTO,usuario);
                    BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarDataRecebimentoAcessoAutoPendentes(autosPendentesDTO);
                    dlgConfirmacaoOkEmail.show(target);
                }
            });
            dlgConfirmacaoOk.setContentVisible(true);
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(JavaScriptHeaderItem.forReference(Application.get().getJavaScriptLibrarySettings().getJQueryReference()));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_MIGRATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DATA_TABLE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_VALIDATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MASKEDINPUT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUTS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TREEVIEW));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JGROWL));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_PRINT_ELEMENT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DIRTY_FORMS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FORM));
        response.render(OnDomReadyHeaderItem.forScript("$('body').find('form').keydown(function(ev){\n"
                + "        if(ev.which == 13 && ev.target.nodeName!='TEXTAREA') ev.preventDefault();\n"
                + "    });"));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_UNIDADE_LOGIN));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_SCROLL_INTO_VIEW));
//        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(txtBuscaRequerimento)));

        response.render(OnDomReadyHeaderItem.forScript(JScript.timerDelayComponent(txtBuscaRequerimento, ajaxTimerDelayComponentBehavior.getCallbackScript().toString())));

        response.render(OnDomReadyHeaderItem.forScript(JScript.timerDelayComponent(txtBuscaProtocolo, ajaxTimerDelayComponentBehaviorProtocolo.getCallbackScript().toString())));

        if (dlgConfirmacaoOk != null &&  existsAutos()) {
            dlgConfirmacaoOk.show(response);
        }
    }
}
