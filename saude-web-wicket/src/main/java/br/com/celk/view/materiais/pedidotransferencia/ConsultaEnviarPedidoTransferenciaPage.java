package br.com.celk.view.materiais.pedidotransferencia;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.estoque.deposito.pnl.PnlConsultaDeposito;
import br.com.celk.view.materiais.pedidotransferencia.customcolumn.ConsultaEnviarPedidoTransferenciaColumnPanel;
import br.com.celk.view.materiais.pedidotransferencia.customize.CustomizeConsultaPedidoTransferencia;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoTransferenciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioEmbarquePedidoTransferenciaDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.Deposito;
import br.com.ksisolucoes.vo.entradas.estoque.EmbarquePedidoTransferencia;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * Programa - 112
 */
@Private
public class ConsultaEnviarPedidoTransferenciaPage extends BasePage {

    private PageableTable<PedidoTransferencia> pageableTable;
    private CustomizeConsultaPagerProvider<PedidoTransferencia> dataProvider;
    private Long pedido;
    private Empresa unidadeDestino;
    private Deposito deposito;
    private DatePeriod periodo;
    private Long situacao = PedidoTransferencia.STATUS_ABERTO;
    private DlgEnviarPedidoTransferencia dlgEnviarPedidoTransferencia;
    private DlgConfirmacaoImpressaoPedidoTransferencia dlgConfirmacaoImpressao;

    public ConsultaEnviarPedidoTransferenciaPage(PageParameters parameters) {
        super(parameters);
        init();
    }

    public ConsultaEnviarPedidoTransferenciaPage(IModel<?> model) {
        super(model);
        init();
    }

    public ConsultaEnviarPedidoTransferenciaPage() {
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(new InputField<Long>("pedido"));
        form.add(new AutoCompleteConsultaEmpresa("unidadeDestino"));
        form.add(new PnlConsultaDeposito("deposito"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(populateDropDownSituacao(new DropDown<Long>("situacao")));

        pageableTable = new PageableTable<PedidoTransferencia>("table", getColumns(), getDataProvider());

        form.add(new ProcurarButton<List<QueryParameter>>("btnProcurar", pageableTable) {
            @Override
            public List<QueryParameter> getParam() {
                return ConsultaEnviarPedidoTransferenciaPage.this.getParam();
            }
        });
        pageableTable.setScrollX("1500px");
        form.add(pageableTable);
        add(form);

        addModal(dlgEnviarPedidoTransferencia = new DlgEnviarPedidoTransferencia(newModalId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgConfirmacaoImpressao.show(target);
            }
        });

        addModal(dlgConfirmacaoImpressao = new DlgConfirmacaoImpressaoPedidoTransferencia(newModalId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                super.onConfirmar(target);
                pageableTable.update(target);
            }

            @Override
            public DataReport getDataReport() throws ReportException {
                return imprimir(dlgEnviarPedidoTransferencia.getPedidoTransferencia());
            }

        });

    }

    private DropDown<Long> populateDropDownSituacao(DropDown<Long> dropDown) {
        dropDown.addChoice(PedidoTransferencia.STATUS_SEPARANDO, BundleManager.getString("separando"));
        dropDown.addChoice(PedidoTransferencia.STATUS_PROCESSADO, BundleManager.getString("processado"));

        return dropDown;
    }

    private CustomizeConsultaPagerProvider<PedidoTransferencia> getDataProvider() {
        if (this.dataProvider == null) {
            this.dataProvider = new CustomizeConsultaPagerProvider<PedidoTransferencia>(new CustomizeConsultaPedidoTransferencia()) {
                @Override
                public SortParam getDefaultSort() {
                    return new SortParam(PedidoTransferencia.PROP_CODIGO, false);
                }
            };
        }

        return this.dataProvider;
    }

    private List<ISortableColumn<PedidoTransferencia>> getColumns() {
        List<ISortableColumn<PedidoTransferencia>> columns = new ArrayList<ISortableColumn<PedidoTransferencia>>();

        ColumnFactory columnFactory = new ColumnFactory(PedidoTransferencia.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("pedido"), VOUtils.montarPath(PedidoTransferencia.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("destino"), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_DESTINO, Empresa.PROP_DESCRICAO), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_DESTINO, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("data_pedido"), VOUtils.montarPath(PedidoTransferencia.PROP_DATA_PEDIDO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataSeparacao"), VOUtils.montarPath(PedidoTransferencia.PROP_DATA_SEPARACAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(PedidoTransferencia.PROP_STATUS), VOUtils.montarPath(PedidoTransferencia.PROP_DESCRICAO_STATUS)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estoque"), VOUtils.montarPath(PedidoTransferencia.PROP_DEPOSITO, Deposito.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("usuarioSeparacao"), VOUtils.montarPath(PedidoTransferencia.PROP_USUARIO_SEPARACAO, Usuario.PROP_NOME)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipo"), VOUtils.montarPath(PedidoTransferencia.PROP_DESCRICAO_TIPO)));

        return columns;
    }

    private List<QueryParameter> getParam() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_ORIGEM), ApplicationSession.get().getSessaoAplicacao().getEmpresa()));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_DESTINO), unidadeDestino));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_DEPOSITO), deposito));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_CODIGO), pedido));
        if (periodo != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_DATA_PEDIDO), Data.adjustRangeHour(periodo)));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_STATUS), situacao));

        return parameters;
    }

    private CustomColumn<PedidoTransferencia> getCustomColumn() {
        return new CustomColumn<PedidoTransferencia>() {
            @Override
            public Component getComponent(String componentId, final PedidoTransferencia rowObject) {
                return new ConsultaEnviarPedidoTransferenciaColumnPanel(componentId, rowObject) {

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        EmbarquePedidoTransferencia embarquePedidoTransferencia = getEmbarquePedido(rowObject);
                        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cancelarEmbarquePedidoTransferencia(embarquePedidoTransferencia);
                        pageableTable.populate(target);
                    }

                    @Override
                    public DataReport onImprimir() throws ReportException {
                        return imprimir(rowObject);
                    }

                    @Override
                    public void refreshTable(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        pageableTable.update(target);
                    }

                    @Override
                    public void onEnviar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        dlgEnviarPedidoTransferencia.setModelObject(rowObject);
                        dlgEnviarPedidoTransferencia.show(target);
                    }

                    @Override
                    public void onReabrir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        EmbarquePedidoTransferencia embarquePedidoTransferencia = getEmbarquePedido(rowObject);
                        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).reabrirEmbarquePedidoTransferencia(embarquePedidoTransferencia);
                        pageableTable.populate(target);
                    }
                };
            }
        };
    }

    private DataReport imprimir(PedidoTransferencia pedidoTransferencia) throws ReportException {
        final RelatorioEmbarquePedidoTransferenciaDTOParam bean = new RelatorioEmbarquePedidoTransferenciaDTOParam();
        bean.setCodigoPedidoTransferenciaList(Arrays.asList(pedidoTransferencia.getCodigo()));
        bean.setExibirQuantidadePedido(RepositoryComponentDefault.NAO);
        bean.setOrdenacao(Produto.PROP_DESCRICAO);
        bean.setReimpressao(true);
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioEmbarquePedidoTransferenciaWeb(bean);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaPedidoTranferencia");
    }

    public EmbarquePedidoTransferencia getEmbarquePedido(PedidoTransferencia pedidoTransferencia) {
        Set<Long> codigos = new HashSet<Long>(1);
        codigos.add(pedidoTransferencia.getCodigo());
        EmbarquePedidoTransferencia embarquePedidoTransferencia = new EmbarquePedidoTransferencia();
        embarquePedidoTransferencia.setCodigoPedidoTransferenciaSet(codigos);
        return embarquePedidoTransferencia;
    }
}
