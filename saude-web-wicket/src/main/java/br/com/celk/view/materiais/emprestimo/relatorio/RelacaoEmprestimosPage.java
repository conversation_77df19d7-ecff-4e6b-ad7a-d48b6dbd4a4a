package br.com.celk.view.materiais.emprestimo.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.emprestimo.interfaces.dto.RelacaoEmprestimoDTOParam;
import br.com.celk.bo.emprestimo.interfaces.facade.EmprestimoReportFacade;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.emprestimo.cadastro.autocomplete.AutoCompleteConsultaTipoEmprestimo;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;

/**
 *
 * <AUTHOR>
 * Programa - 468
 */
@Private
public class RelacaoEmprestimosPage extends RelatorioPage<RelacaoEmprestimoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    @Override
    public void init(Form form) {
        RelacaoEmprestimoDTOParam proxy = on(RelacaoEmprestimoDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaTipoEmprestimo(path(proxy.getTipoEmprestimo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoOperacao()), RelacaoEmprestimoDTOParam.TipoOperacao.values()));
        form.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getPaciente())));
        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimentoEmprestimo())));
        form.add(new AutoCompleteConsultaProduto(path(proxy.getProduto())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelacaoEmprestimoDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getSituacao()), RelacaoEmprestimoDTOParam.Situacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getOrdenacao()), RelacaoEmprestimoDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoOrdenacao()), RelacaoEmprestimoDTOParam.TipoOrdenacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getVisualizarTotais()), RelacaoEmprestimoDTOParam.VisualizarTotais.values()));
        form.add(new RequiredPnlChoicePeriod(path(proxy.getPeriodo())));

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);

    }

    @Override
    public Class<RelacaoEmprestimoDTOParam> getDTOParamClass() {
        return RelacaoEmprestimoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelacaoEmprestimoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EmprestimoReportFacade.class).relatorioRelacaoEmprestimo(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoEmprestimo");
    }
}
