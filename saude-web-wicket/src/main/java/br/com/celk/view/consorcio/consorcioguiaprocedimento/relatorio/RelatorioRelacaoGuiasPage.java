package br.com.celk.view.consorcio.consorcioguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.consorcioprocedimento.pnl.PnlConsultaConsorcioProcedimento;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioRelacaoGuiasDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.util.Arrays;


/**
 *
 * <AUTHOR>
 * Programa - 878
 */
@Private

public class RelatorioRelacaoGuiasPage extends RelatorioPage<RelatorioRelacaoGuiasDTOParam> {
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaConsorciado;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaConsorciado = new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(new PnlConsultaConsorcioProcedimento("consorcioProcedimento"));
        form.add(DropDownUtil.getEnumDropDown("tipoData", RelatorioRelacaoGuiasDTOParam.TipoData.values()));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(new CheckBox("situacaoAberta"));
        form.add(new CheckBox("situacaoCancelada"));
        form.add(new CheckBox("situacaoPaga"));
        form.add(new CheckBox("situacaoAPagar"));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioRelacaoGuiasDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getIEnumDropDown("tipoOrdenacao", RelatorioRelacaoGuiasDTOParam.TipoOrdenacao.values(), false));
        form.add(DropDownUtil.getIEnumDropDown("situacaoSisreg", ConsorcioGuiaProcedimentoItem.SituacaoSisreg.values(), true, BundleManager.getString("todas")));
    }
    
    @Override
    public Class<RelatorioRelacaoGuiasDTOParam> getDTOParamClass() {
        return RelatorioRelacaoGuiasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoGuiasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioRelacaoGuias(param);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoGuiasSisreg");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaConsorciado;
    }

}
