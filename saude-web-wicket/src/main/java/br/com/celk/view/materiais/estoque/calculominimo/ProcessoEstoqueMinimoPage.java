package br.com.celk.view.materiais.estoque.calculominimo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.duracaofield.RequiredMesAnoField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaProdutosCadastradosDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 78
 */
@Private
public class ProcessoEstoqueMinimoPage extends BasePage {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private RequiredMesAnoField mesAnoInicial;
    private RequiredMesAnoField mesAnoFinal;
    private InputField txtFatorConversao;
    private Date dataInicial;
    private Date dataFinal;
    private Empresa empresa;
    private SubGrupo subGrupo;
    private GrupoProduto grupoProduto;
    private AbstractAjaxButton btnProcessar;
    private Long fatorConversao;

    public ProcessoEstoqueMinimoPage() {
        init();
    }

    private void init() {
        Form form = new Form("form");

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa", new PropertyModel(this, "empresa")).setValidaUsuarioEmpresa(true));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(mesAnoInicial = new RequiredMesAnoField("dataInicial", new PropertyModel(this, "dataInicial")));
        form.add(mesAnoFinal = new RequiredMesAnoField("dataFinal", new PropertyModel(this, "dataFinal")));
        this.autoCompleteConsultaEmpresa.setComponentValue(br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa());
        form.add(txtFatorConversao = new InputField("fatorConversao", new PropertyModel(this, "fatorConversao")));
        txtFatorConversao.add(new Tooltip().setText("msgDefinirFatorConversaoEstoqueCalculadoParaPeriodoMultiplicado"));

        form.add(btnProcessar = new AbstractAjaxButton("btnProcessar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                processar(target);
            }
        });

        add(form);
    }

    public void processar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        QueryConsultaProdutosCadastradosDTOParam param = new QueryConsultaProdutosCadastradosDTOParam();

        if (grupoProduto != null) {
            param.setGrupoProduto(grupoProduto);
            if (subGrupo != null) {
                param.setSubGrupo(subGrupo);
            }
        }

        if (empresa != null) {
            param.setEmpresa(empresa);
        }

        DatePeriod datePeriod = Data.adjustRangeHour(dataInicial, Data.adjustMonthToLastDay(dataFinal));
        param.setDataInicial(datePeriod.getDataInicial());
        param.setDataFinal(datePeriod.getDataFinal());
        param.setFatorConversao(fatorConversao);

        BOFactoryWicket.getBO(EstoqueEmpresaFacade.class).processoEstoqueMinimo(param);
        try {
            Page page = (Page) getResponsePage().newInstance();
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        } catch (InstantiationException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public Class getResponsePage() {
        return ProcessoEstoqueMinimoPage.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("calculoEstoqueMinimo");
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo", new PropertyModel<SubGrupo>(this, "subGrupo"));
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }
        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto", new PropertyModel<GrupoProduto>(this, "grupoProduto"));
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                    }
                    target.add(dropDownSubGrupo);
                }
            });
            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }
}
