package br.com.celk.view.materiais.emprestimo.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.emprestimo.interfaces.dto.ResumoEmprestimosDTOParam;
import br.com.celk.bo.emprestimo.interfaces.facade.EmprestimoReportFacade;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.emprestimo.cadastro.autocomplete.AutoCompleteConsultaTipoEmprestimo;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 470
 */
@Private
public class ResumoEmprestimosPage extends RelatorioPage<ResumoEmprestimosDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    @Override
    public void init(Form<ResumoEmprestimosDTOParam> form) {
        ResumoEmprestimosDTOParam proxy = on(ResumoEmprestimosDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaTipoEmprestimo(path(proxy.getTipoEmprestimo())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoOperacao()), ResumoEmprestimosDTOParam.TipoOperacao.values(), true, BundleManager.getString("ambos")));
        form.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getPaciente())));
        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimentoEmprestimo())));
        form.add(new AutoCompleteConsultaProduto(path(proxy.getProduto())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), ResumoEmprestimosDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoResumo()), ResumoEmprestimosDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), ResumoEmprestimosDTOParam.Situacao.values(), true, BundleManager.getString("todos")));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getOrdenacao()), ResumoEmprestimosDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoOrdenacao()), ResumoEmprestimosDTOParam.TipoOrdenacao.values()));
        form.add(DropDownUtil.getNaoSimDropDown(path(proxy.getVisualizarTotais())));
        form.add(new RequiredPnlChoicePeriod(path(proxy.getPeriodo())));

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
    }

    @Override
    public Class<ResumoEmprestimosDTOParam> getDTOParamClass() {
        return ResumoEmprestimosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(ResumoEmprestimosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EmprestimoReportFacade.class).relatorioResumoEmprestimos(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoEmprestimos");
    }
}
