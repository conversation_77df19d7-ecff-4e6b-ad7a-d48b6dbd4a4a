package br.com.celk.view.vigilancia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.report.vigilancia.dto.RelatorioDenunciasDTOParam;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.denuncia.autocomplete.AutoCompleteConsultaTipoDenuncia;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 590
 */
@Private
public class RelatorioDenunciasPage extends RelatorioPage<RelatorioDenunciasDTOParam> {

    @Override
    public void init(Form<RelatorioDenunciasDTOParam> form) {
        RelatorioDenunciasDTOParam proxy = on(RelatorioDenunciasDTOParam.class);

        form.add(new InputField<Long>(path(proxy.getProtocolo())));
        form.add(new AutoCompleteConsultaTipoDenuncia(path(proxy.getTipoDenuncia())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new AutoCompleteConsultaSetorVigilancia(path(proxy.getSetorVigilancia())));
        form.add(new InputField(path(proxy.getDenunciante())));
        form.add(new InputField(path(proxy.getDenunciado())));
        form.add(new InputField(path(proxy.getBairro())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), Denuncia.Status.values(), true));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioDenunciasDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getOrdenacao()), RelatorioDenunciasDTOParam.Ordenacao.values()));
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioDenuncias");
    }

    @Override
    public Class<RelatorioDenunciasDTOParam> getDTOParamClass() {
        return RelatorioDenunciasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioDenunciasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioDenuncias(param);
    }

}
