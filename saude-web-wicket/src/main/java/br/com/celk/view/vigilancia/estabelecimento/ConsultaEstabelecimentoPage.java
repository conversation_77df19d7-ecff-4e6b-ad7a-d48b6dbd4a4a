package br.com.celk.view.vigilancia.estabelecimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.IFileAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.classificacaogrupoestabelecimento.autocomplete.AutoCompleteConsultaClassificacaoGrupoEstabelecimento;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.celk.view.vigilancia.grupoestabelecimento.autocomplete.AutoCompleteConsultaGrupoEstabelecimento;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaEstabelecimentoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaEstabelecimentoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.io.File;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 223
 */
@Private
public class ConsultaEstabelecimentoPage extends ConsultaPage<ConsultaEstabelecimentoDTO, ConsultaEstabelecimentoDTOParam> {

    private CompoundPropertyModel<ConsultaEstabelecimentoDTOParam> model;
    private InputField<String> txtRazaoSocial;
    private ConfiguracaoVigilancia configuracaoVigilancia;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaEndereco;

    @Override
    public void initForm(Form form) {
        try {
            this.configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
        }
        if (configuracaoVigilancia == null) {
            warn("Verifique as configurações da Vigilância.");
            getLinkNovo().setVisible(false);
        }
        form.setDefaultModel(model = new CompoundPropertyModel(new ConsultaEstabelecimentoDTOParam()));
        ConsultaEstabelecimentoDTOParam proxy = on(ConsultaEstabelecimentoDTOParam.class);

        form.add(txtRazaoSocial = new InputField<>(path(proxy.getRazaoSocial())));
        form.add(new InputField(path(proxy.getFantasia())));
        form.add(new PnlAtividadeEstabelecimento(path(proxy.getAtividadeEstabelecimento())));
        form.add(new AutoCompleteConsultaGrupoEstabelecimento(path(proxy.getGrupoEstabelecimento())));
        form.add(new AutoCompleteConsultaClassificacaoGrupoEstabelecimento(path(proxy.getClassificacaoGrupoEstabelecimento())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), Estabelecimento.Situacao.values()));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getOrigemCadastro()), Estabelecimento.OrigemCadastro.values(), true, "Ambos"));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getMei()), RepositoryComponentDefault.SimNaoLong.values(), true, "Ambos"));
        form.add(new InputField(path(proxy.getCpfCnpj())));
        form.add(autoCompleteConsultaEndereco = new AutoCompleteConsultaVigilanciaEndereco(path(proxy.getVigilanciaEndereco())));
        setExibeExpandir(true);

    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ConsultaEstabelecimentoDTO proxy = on(ConsultaEstabelecimentoDTO.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(BundleManager.getString("alvara"), proxy.getEstabelecimento().getAlvara(), proxy.getEstabelecimento().getAlvaraFormatado()));
        columns.add(createSortableColumn(BundleManager.getString("razaoSocial"), proxy.getEstabelecimento().getRazaoSocial()));
        columns.add(createSortableColumn(BundleManager.getString("fantasia"), proxy.getEstabelecimento().getFantasia()));
        columns.add(createColumn(BundleManager.getString("cpfCnpj"), proxy.getCnpjCpf()));
        columns.add(createColumn(VigilanciaHelper.isGestaoAtividadeCnae() ? BundleManager.getString("cnae") : BundleManager.getString("atividade"), proxy.getDescricaoAtividadePrincipal()));
        columns.add(createSortableColumn(BundleManager.getString("endereco"), proxy.getEstabelecimento().getVigilanciaEndereco().getLogradouro(), proxy.getEstabelecimento().getVigilanciaEndereco().getEnderecoFormatadoComCidade()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getEstabelecimento().getSituacao(), proxy.getEstabelecimento().getDescricaoSituacao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ConsultaEstabelecimentoDTO>() {
            @Override
            public void customizeColumn(final ConsultaEstabelecimentoDTO rowObject) {
                addAction(ActionType.EDITAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEstabelecimentoPage(rowObject.getEstabelecimento(), null, null));
                    }
                });


                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject.getEstabelecimento());
                        getPageableTable().populate(target);
                    }
                }).setEnabled(isActionPermitted(Permissions.EDITAR));

                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEstabelecimentoPage(rowObject.getEstabelecimento(), true, null, null, null));
                    }
                });

                addAction(ActionType.REATIVAR, rowObject, new IModelAction<ConsultaEstabelecimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaEstabelecimentoDTO modelObject) throws ValidacaoException, DAOException {
                        modelObject.getEstabelecimento().setSituacao(Estabelecimento.Situacao.ATIVO.value());
                        BOFactoryWicket.getBO(CadastroFacade.class).save(rowObject.getEstabelecimento());
                        getPageableTable().update(target);
                    }
                }).setEnabled(isActionPermitted(Permissions.EDITAR))
                        .setVisible(rowObject.getEstabelecimento().getSituacao().equals(Estabelecimento.Situacao.INATIVO.value()));

                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<ConsultaEstabelecimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaEstabelecimentoDTO modelObject) throws ValidacaoException, DAOException {
                        modelObject.getEstabelecimento().setSituacao(Estabelecimento.Situacao.INATIVO.value());
                        BOFactoryWicket.getBO(CadastroFacade.class).save(rowObject.getEstabelecimento());
                        getPageableTable().update(target);
                    }
                }).setEnabled(isActionPermitted(Permissions.EDITAR))
                        .setVisible(rowObject.getEstabelecimento().getSituacao().equals(Estabelecimento.Situacao.ATIVO.value()));

                addAction(ActionType.HISTORICO, rowObject, new IFileAction<ConsultaEstabelecimentoDTO>() {
                    @Override
                    public File action(ConsultaEstabelecimentoDTO modelObject) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).historico(modelObject.getEstabelecimento());
                    }
                });

            }
        };
    }

    @Override
    public IPagerProvider<ConsultaEstabelecimentoDTO, ConsultaEstabelecimentoDTOParam> getPagerProviderInstance() {
        return new QueryPagerProvider<ConsultaEstabelecimentoDTO, ConsultaEstabelecimentoDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaEstabelecimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarEstabelecimentoVigilancia(dataPaging);
            }

            @Override
            public void customizeParam(ConsultaEstabelecimentoDTOParam param) {
                ConsultaEstabelecimentoPage.this.model.getObject().setSortProp(getSort().getProperty());
                ConsultaEstabelecimentoPage.this.model.getObject().setAscending(getSort().isAscending());
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam("estabelecimento.razaoSocial", true);
            }
        };
    }

    @Override
    public ConsultaEstabelecimentoDTOParam getParameters() {
        return model.getObject();
    }

    @Override
    public Class getCadastroPage() {
        return CadastroEstabelecimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaEstabelecimentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtRazaoSocial;
    }
}
