package br.com.celk.view.exportacao.processo.consulta;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.report.RelatorioPage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.dto.EventoSistemaDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.exportacao.consulta.dto.RelatorioExportacaoConsultaCsvDTOParam;
import br.com.ksisolucoes.report.exportacao.consulta.interfaces.facade.ExportacaoConsultaCsvReportFacade;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EventoSistema;
import br.com.ksisolucoes.vo.exportacao.ExportacaoConsultaCsv;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.as;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 1106
 */
@Private
public class ExportacaoConsultaCsvPage extends RelatorioPage<RelatorioExportacaoConsultaCsvDTOParam> {
    
    private DropDown dropDownConsulta;
    private WebMarkupContainer containerPeriodo;
    private RequiredPnlDatePeriod pnlDatePeriod;
    private DlgConfirmacaoOk dlgConfirmacaoOk;

    @Override
    public void init(Form form) {
        RelatorioExportacaoConsultaCsvDTOParam proxy = on(RelatorioExportacaoConsultaCsvDTOParam.class);
        
        form.add(getDropDownConsulta(path(proxy.getExportacaoConsultaCsv())));
        form.add(getTipoRelatorioDropDownXlsCsv("tipoArquivo"));
        
        containerPeriodo = new WebMarkupContainer("containerPeriodo");
        containerPeriodo.setOutputMarkupId(true);
        containerPeriodo.setOutputMarkupPlaceholderTag(true);
        containerPeriodo.setVisible(false);
        
        containerPeriodo.add(pnlDatePeriod = new RequiredPnlDatePeriod(path(proxy.getPeriodo())));

        form.add(containerPeriodo);
    }

    private void initDlgConfirmacao(AjaxRequestTarget target, String mensagemErro) {
        dlgConfirmacaoOk = new DlgConfirmacaoOk(newModalId(), mensagemErro, "img-warn", 70, 500){
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        };

        addModal(target, dlgConfirmacaoOk);
    }

    private DropDown<TipoRelatorio> getTipoRelatorioDropDownXlsCsv(String id) {
        DropDown<TipoRelatorio> dropDown = new DropDown(id);
        dropDown.addChoice(TipoRelatorio.XLS2, BundleManager.getString("xls"));
        dropDown.addChoice(TipoRelatorio.CSV, BundleManager.getString("csv"));

        return dropDown;
    }
    
    private DropDown getDropDownConsulta(String id){
        if (dropDownConsulta == null) {
            dropDownConsulta = new RequiredDropDown<>(id);
            dropDownConsulta.setLabel(new Model<String>(bundle("consulta")));
            List<ExportacaoConsultaCsv> exportacaoConsultaCsvList = LoadManager.getInstance(ExportacaoConsultaCsv.class)
                    .addProperties(new HQLProperties(ExportacaoConsultaCsv.class).getProperties())
                    .addSorter(new QueryCustom.QueryCustomSorter(ExportacaoConsultaCsv.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            dropDownConsulta.addChoice(null, "");
            
            for (ExportacaoConsultaCsv exportacaoConsultaCsv : exportacaoConsultaCsvList) {
                dropDownConsulta.addChoice(exportacaoConsultaCsv, exportacaoConsultaCsv.getDescricao());
            }
            
            dropDownConsulta.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    pnlDatePeriod.setModelObject(new DatePeriod(null, null));
                    target.add(pnlDatePeriod);
                    
                    ExportacaoConsultaCsv exportacaoConsultaCsv = (ExportacaoConsultaCsv) dropDownConsulta.getComponentValue();
                    
                    if(exportacaoConsultaCsv != null && exportacaoConsultaCsv.getFiltros() != null){
                        List<Long> valores = Valor.resolveSomatorio(exportacaoConsultaCsv.getFiltros());

                        if(valores.contains(ExportacaoConsultaCsv.Filtros.PERIODO.value())){
                            containerPeriodo.setVisible(true);
                        } else {
                            containerPeriodo.setVisible(false);
                        }
                    } else {
                        containerPeriodo.setVisible(false);
                    }

                    target.add(containerPeriodo);

                    target.appendJavaScript(JScript.initMasks());
                }
            });
        }
        
        return dropDownConsulta;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioExportacaoConsultaCsvDTOParam.class;
    }

    @Override
    public void antesGerarRelatorio(AjaxRequestTarget target) throws ValidacaoException {
        if(param.getExportacaoConsultaCsv().getSql() == null){
            throw new ValidacaoException(BundleManager.getString("msgConsultaSelecionadoNaoPossuiSQLConfiguradoFavorConfigurar"));
        }
    }

    @Override
    public DataReport getDataReport(RelatorioExportacaoConsultaCsvDTOParam param) throws ReportException {
        ExportacaoConsultaCsv exportacaoConsultaCsv = LoadManager.getInstance(ExportacaoConsultaCsv.class)
                        .addProperties(new HQLProperties(ExportacaoConsultaCsv.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(ExportacaoConsultaCsv.PROP_CODIGO, param.getExportacaoConsultaCsv().getCodigo()))
                        .start().getVO();
        
        param.setExportacaoConsultaCsv(exportacaoConsultaCsv);

        return BOFactoryWicket.getBO(ExportacaoConsultaCsvReportFacade.class).relatorioConsultas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("exportacaoConsulta");
    }

    @Override
    public void notifyReport(AjaxRequestTarget target, AsyncProcess asyncProcess) {
        super.notifyReport(target, asyncProcess);

        if (AsyncProcess.STATUS_PROCESSANDO.equals(asyncProcess.getStatus())) {
            Loggable.log.info("Exportação de Dados Iniciada - " + asyncProcess.getCodigo() + " - Consulta: " + param.getSqlWithParams());
        }

        if (AsyncProcess.STATUS_CONCLUIDO_EXITO.equals(asyncProcess.getStatus()) ||
                AsyncProcess.STATUS_CONCLUIDO_ERRO.equals(asyncProcess.getStatus()) ||
                AsyncProcess.STATUS_SEM_PAGINAS.equals(asyncProcess.getStatus()) ||
                AsyncProcess.STATUS_MUITAS_PAGINAS.equals(asyncProcess.getStatus())) {
            Loggable.log.info("Exportação de Dados Finalizada - " + asyncProcess.getCodigo());
        }

        if (AsyncProcess.STATUS_CONCLUIDO_EXITO.equals(asyncProcess.getStatus())) {
            salvarEvento();
        }

        if (AsyncProcess.STATUS_CONCLUIDO_ERRO.equals(asyncProcess.getStatus())) {
            String msgErro = asyncProcess.getMensagemErro();

            if (msgErro.contains("DAOException")) {
                String msgSemStackTrace = msgErro.substring(msgErro.indexOf(":") + 1, msgErro.indexOf("at"));
                initDlgConfirmacao(target, BundleManager.getString("msgErroSql", msgSemStackTrace));
                dlgConfirmacaoOk.show(target);
            }
        }
    }

    private void salvarEvento() {
        EventoSistemaDTO eventoSistemaDTO = new EventoSistemaDTO();
        eventoSistemaDTO.setNivelCriticidade(EventoSistema.NivelCriticidade.INFORMACAO.value());
        eventoSistemaDTO.setKeyword(BundleManager.getString("keyExportacao"));
        eventoSistemaDTO.setTipoEvento(EventoSistema.TipoEvento.PROGRAMA.value());
        eventoSistemaDTO.setIdentificacaoEvento(EventoSistema.IdentificacaoEvento.EXPORTACAO.value());
        eventoSistemaDTO.setFonteEvento(BundleManager.getString("exportacaoDados"));
        eventoSistemaDTO.setDescricao(BundleManager.getString("exportacaoDadosConsultaSql", param.getSqlWithParams()));

        try {
            BOFactoryWicket.getBO(CommomFacade.class).gerarEventoSistema(eventoSistemaDTO);
        } catch (DAOException | ValidacaoException e) {
            Loggable.log.error(e.getMessage());
        }
    }
}
