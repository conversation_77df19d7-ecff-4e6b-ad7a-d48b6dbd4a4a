package br.com.celk.view.materiais.estoque.consulta;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.bo.materiais.estoque.interfaces.dto.LoteProdutoDTO;
import br.com.ksisolucoes.bo.materiais.estoque.interfaces.dto.LoteProdutoDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 584
 */
public class ManutencaoLotePage extends ConsultaPage<GrupoEstoque, LoteProdutoDTOParam> {

    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private DlgDetalhesHistoricoValidadeLote dlgDetalhesHistoricoValidadeLote;
    private Produto produto;
    private String lote;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(false);
        form.add(new InputField<String>("lote"));
        getLinkNovo().setVisible(false);
        addModal(dlgDetalhesHistoricoValidadeLote = new DlgDetalhesHistoricoValidadeLote(newModalId()));

        setProcurarAoAbrir(false);
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<LoteProdutoDTO>() {
            @Override
            public void customizeColumn(LoteProdutoDTO rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<LoteProdutoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, LoteProdutoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new EditarLotePage(modelObject));
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<LoteProdutoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, LoteProdutoDTO modelObject) throws ValidacaoException, DAOException {
                        dlgDetalhesHistoricoValidadeLote.setModelObject(modelObject.getLote(), modelObject.getProduto());
                        dlgDetalhesHistoricoValidadeLote.show(target);
                    }
                });
            }

        };
    }

    @Override
    public Class getCadastroPage() {
        return ManutencaoLotePage.class;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(LoteProdutoDTO.class);
        LoteProdutoDTO proxy = on(LoteProdutoDTO.class);
        GrupoEstoque ge = on(GrupoEstoque.class);
        columns.add(getActionColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("produto"), "produto.descricao", path(proxy.getProduto().getDescricaoFormatado())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataValidade"), path(proxy.getDataValidade())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("lote"), path(proxy.getLote())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidadeMedida"), path(proxy.getUnidadeMedida())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estoqueFisico"), path(proxy.getEstoqueFisico())));
        //columns.add(columnFactory.createColumn(BundleManager.getString("fabricante"), path(proxy.getFabricante().getDescricao())));
        columns.add(columnFactory.createColumn(BundleManager.getString("fabricante"), path(proxy.getFabricanteFormatado())));
        return columns;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<LoteProdutoDTO, LoteProdutoDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<LoteProdutoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setSort(((SingleSortState<String>) getPagerProvider().getSortState()).getSort().getProperty());
                dataPaging.getParam().setAscending(((SingleSortState) getPagerProvider().getSortState()).getSort().isAscending());
                return BOFactoryWicket.getBO(ProdutoFacade.class).queryLoteProduto(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                GrupoEstoque p = on(GrupoEstoque.class);
                return new SortParam("produto.descricao", true);
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("manutencaoLote");
    }

    @Override
    public LoteProdutoDTOParam getParameters() {
        LoteProdutoDTOParam param = new LoteProdutoDTOParam();
        param.setLote(lote);
        param.setProduto(produto);
        return param;
    }

}
