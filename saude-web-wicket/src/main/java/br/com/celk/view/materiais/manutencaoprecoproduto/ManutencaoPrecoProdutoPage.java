package br.com.celk.view.materiais.manutencaoprecoproduto;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.doublefield.RequiredDoubleField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.ManutencaoPrecoProdutoDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import static ch.lambdaj.Lambda.on;
import java.util.Arrays;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 405
 */
public class ManutencaoPrecoProdutoPage extends BasePage {

    private Form<ManutencaoPrecoProdutoDTO> form;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private DoubleField txtPrecoCusto;
    private DoubleField txtPrecoMedio;
    private DlgConfirmacaoSimNao dlgConfirmacaoManutencao;

    public ManutencaoPrecoProdutoPage() {
        init();
    }

    private void init() {
        add(form = new Form("form", new CompoundPropertyModel(new ManutencaoPrecoProdutoDTO())));

        ManutencaoPrecoProdutoDTO proxy = on(ManutencaoPrecoProdutoDTO.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento()), true));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxy.getProduto()), true));
        autoCompleteConsultaProduto.setIncluirInativos(false);
        form.add(txtPrecoCusto = new DoubleField(path(proxy.getEstoqueEmpresa().getPrecoCusto())));
        form.add(txtPrecoMedio = new DoubleField(path(proxy.getEstoqueEmpresa().getPrecoMedio())));
        form.add(new RequiredDoubleField(path(proxy.getNovoPreco()))
            .setMDec(4));
        form.add(new RequiredInputField(path(proxy.getMotivo())));

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarSalvar(target)) {
                    salvar(target);
                }
            }
        });

        addModal(dlgConfirmacaoManutencao = new DlgConfirmacaoSimNao(newModalId(), BundleManager.getString("msgProdutoInformadoPossuiEstoqueSeraNecessarioSaidaTodoEstoqueEntradaNovoPrecoConfirma")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });

        initComponents();
        txtPrecoCusto.setMDec(4);
        txtPrecoMedio.setMDec(4);
    }

    private void initComponents() {
        txtPrecoCusto.setEnabled(false);
        txtPrecoMedio.setEnabled(false);

        if (!isActionPermitted(Permissions.EMPRESA, ManutencaoPrecoProdutoPage.class)) {
            autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(true);
        }

        autoCompleteConsultaProduto.setEnabled(false); // Inicia desabilitado até que uma empresa seja informada.
        autoCompleteConsultaProduto.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO);

        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                autoCompleteConsultaProduto.setEnabled(true);
                autoCompleteConsultaProduto.setEmpresas(Arrays.asList(empresa));
                target.add(autoCompleteConsultaProduto);
                autoCompleteConsultaProduto.focus(target);
            }
        });

        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa empresa) {
                autoCompleteConsultaProduto.setEnabled(false);
                autoCompleteConsultaProduto.limpar(target);
                txtPrecoCusto.limpar(target);
                txtPrecoMedio.limpar(target);
            }
        });

        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto produto) {
                atualizarPrecos(target);
            }
        });

        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto produto) {
                txtPrecoCusto.limpar(target);
                txtPrecoMedio.limpar(target);
            }
        });
    }

    private void atualizarPrecos(AjaxRequestTarget target) {
        Empresa estabelecimento = (Empresa) autoCompleteConsultaEmpresa.getComponentValue();
        Produto produto = (Produto) autoCompleteConsultaProduto.getComponentValue();

        EstoqueEmpresa estoqueEmpresa = LoadManager.getInstance(EstoqueEmpresa.class)
                .setId(new EstoqueEmpresaPK(produto, estabelecimento))
                .start().getVO();

        form.getModel().getObject().setEstoqueEmpresa(estoqueEmpresa);

        target.add(txtPrecoCusto);
        target.add(txtPrecoMedio);
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        ManutencaoPrecoProdutoDTO dto = (ManutencaoPrecoProdutoDTO) form.getModel().getObject();

        BOFactoryWicket.getBO(ProdutoFacade.class).manutencaoPrecoProduto(dto);

        info(target, BundleManager.getString("msgManutencaoPrecoProdutoXEstabelecimentoYRealizadaComSucesso", dto.getProduto().getDescricao(), dto.getEstabelecimento().getDescricao()));
        limparForm(target);
    }

    private boolean validarSalvar(AjaxRequestTarget target) throws ValidacaoException {
        ManutencaoPrecoProdutoDTO dto = (ManutencaoPrecoProdutoDTO) form.getModel().getObject();

        if (dto.getEstabelecimento() == null) {
            throw new ValidacaoException(BundleManager.getString("informeEstabelecimento"));            
        }

        if (dto.getProduto() == null) {
            throw new ValidacaoException(BundleManager.getString("informeProduto"));
        }

        if (dto.getNovoPreco() != null && dto.getEstoqueEmpresa().getPrecoMedio() != null && dto.getNovoPreco().equals(dto.getEstoqueEmpresa().getPrecoMedio())) {
            throw new ValidacaoException(BundleManager.getString("msgNovoPrecoInformadoDeveSerDiferentePrecoMedio"));
        }

        if (Coalesce.asDouble(dto.getEstoqueEmpresa().getPrecoMedio()) > 0D) {
            dlgConfirmacaoManutencao.show(target);
            return false;
        }

        return true;
    }

    private void limparForm(AjaxRequestTarget target) {
        autoCompleteConsultaEmpresa.limpar(target);
        autoCompleteConsultaProduto.setEnabled(false);
        autoCompleteConsultaProduto.limpar(target);
        form.getModel().setObject(new ManutencaoPrecoProdutoDTO());
        target.add(form);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("manutencaoPrecoProduto");
    }
}
