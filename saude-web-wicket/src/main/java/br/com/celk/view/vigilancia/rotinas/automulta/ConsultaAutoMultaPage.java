package br.com.celk.view.vigilancia.rotinas.automulta;

import br.com.celk.boleto.dto.boletocloud.BoletoDTO;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgData;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.requerimentos.CadastroRelatorioInspecaoPage;
import br.com.celk.view.vigilancia.requerimentos.ConsultaRelatorioInspecaoPage;
import br.com.celk.view.vigilancia.requerimentovigilancia.PendenciasFiscalPage;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.dialog.DlgAutoPenalidadeReceber;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.EmissaoBoletoVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.VigilanciaFinanceiroBoletoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.bo.vigilancia.interfaces.VigilanciaAnexosHelper;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.util.string.StringValue;

import java.io.File;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 933
 */
public class ConsultaAutoMultaPage extends ConsultaPage<AutoMulta, List<BuilderQueryCustom.QueryParameter>> {

    private PageParameters parameters;
    private Class returnClass;

    private Long numero;
    private String autuado;
    private Long situacao;
    private DatePeriod periodo;
    private Long exibirOutrosFiscais;
    private DlgConfirmacaoSimNao<AutoMulta> dlgConfirmacaoSimNao;
    private DropDown cbxSituacao;
    private DlgAutoPenalidadeReceber dlgAutoPenalidadeReceber;
    private Component btnVoltar;
    private AbstractAjaxButton btnNovo;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private DlgData<AutoMulta> dlgDataVencimento;
    private DropDown<Long> dropDownExibirOutrosFiscais;
    private ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro;

    public ConsultaAutoMultaPage() {
        super();
    }

    public ConsultaAutoMultaPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        try {
            configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
        } catch (ValidacaoException e) {
            Loggable.vigilancia.error(e.getMessage(), e);
        }

        form.add(new InputField("numero"));
        form.add(new InputField("autuado"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(cbxSituacao = DropDownUtil.getIEnumDropDown("situacao", AutoMulta.Situacao.values(), true));
        cbxSituacao.addAjaxUpdateValue();
        cbxSituacao.setOutputMarkupId(true);

        form.add(dropDownExibirOutrosFiscais = DropDownUtil.getNaoSimLongDropDown("exibirOutrosFiscais"));
        dropDownExibirOutrosFiscais.addAjaxUpdateValue();
        dropDownExibirOutrosFiscais.setOutputMarkupPlaceholderTag(true);

        setExibeExpandir(true);

        getLinkNovo().setVisible(false);

        getControls().add(btnVoltar = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(returnClass);
            }

            @Override
            public boolean isVisible() {
                return returnClass != null;
            }
        }.setDefaultFormProcessing(false));

        btnVoltar.add(new AttributeModifier("type", "button"));
        btnVoltar.add(new AttributeModifier("class", "arrow-left"));
        btnVoltar.add(new AttributeModifier("value", bundle("voltar")));
        btnVoltar.add(new AttributeModifier("style", "margin-left: 5px;"));


        getControls().add(btnNovo = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                CadastroAutoMultaPage cadastroAutoMultaPage = new CadastroAutoMultaPage(getPageParameters());
                setResponsePage(cadastroAutoMultaPage);
            }
        });

        btnNovo.add(new AttributeModifier("class", "doc-new"));
        btnNovo.add(new AttributeModifier("value", bundle("novo")));
        btnNovo.add(new AttributeModifier("style", "margin-left: 5px;"));

        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());

        setParameters(this.getPageParameters());

    }

    private void setParameters(PageParameters pageParameters) {
        this.parameters = pageParameters;

        StringValue situacaoParam = parameters.get("situacao");
        if (!situacaoParam.isEmpty()) {
            situacao = situacaoParam.toLong();
        }

        StringValue exibisFiscaisParam = parameters.get("exibirOutrosFiscais");
        if (!exibisFiscaisParam.isEmpty()) {
            exibirOutrosFiscais = exibisFiscaisParam.toLong();
        } else {
            exibirOutrosFiscais = RepositoryComponentDefault.NAO_LONG;
        }

        StringValue returnClassParam = parameters.get("returnClass");
        if (!returnClassParam.isEmpty()) {
            try {
                returnClass = Class.forName(returnClassParam.toString());
                if (returnClass != null) {
                    btnVoltar.setVisible(true);
                }
                if (returnClass != null && returnClass.getName().equals(PendenciasFiscalPage.class.getName())) {
                    cbxSituacao.setEnabled(false);
                }
            } catch (ClassNotFoundException e) {
                Loggable.log.error(e);
            }
        }
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        AutoMulta proxy = on(AutoMulta.class);
        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("numeroAutoMultaAbv"), proxy.getNumeroFormatado()));
        columns.add(createSortableColumn(bundle("dataMulta"), proxy.getDataMulta()));
        columns.add(createSortableColumn(bundle("autuado"), proxy.getDescricaoAutuado()));
        columns.add(createSortableColumn(bundle("prazoRecurso"), proxy.getDataPrazoRecurso()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacaoFormatado()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AutoMulta>() {

            @Override
            public void customizeColumn(final AutoMulta rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<AutoMulta>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoMulta modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAutoMultaPage(modelObject, getPageParameters()));
                    }
                }).setEnabled(enableAlterarMulta(rowObject));
                ;

                addAction(ActionType.REMOVER, rowObject, new IModelAction<AutoMulta>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoMulta modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).removerAutoMulta(modelObject);
                        getPageableTable().populate();
                        getPageableTable().update(target);
                    }
                }).setEnabled(enableAlterarMulta(rowObject));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AutoMulta>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoMulta modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAutoMultaPage(modelObject, true, getPageParameters()));
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<AutoMulta>() {
                    @Override
                    public DataReport action(AutoMulta modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoMulta(modelObject.getCodigo(), modelObject.getNumeroFormatado());
                    }
                });


                ModelActionLinkPanel acaoConcluir = addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<AutoMulta>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoMulta modelObject) throws ValidacaoException, DAOException {
                        if (dlgConfirmacaoSimNao == null) {
                            addModal(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao<AutoMulta>(newModalId(), BundleManager.getString("desejaFinalizar").concat(" ").concat(BundleManager.getString("estaAcaoIraGerarNovoProcessoAdministrativo"))) {
                                @Override
                                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                    BOFactoryWicket.getBO(VigilanciaFacade.class).finalizarAutoMulta(getObject());
                                    getPageableTable().populate();
                                    getPageableTable().update(target);
                                }

                                @Override
                                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                    getPageableTable().populate();
                                    getPageableTable().update(target);
                                }
                            });
                        }

                        dlgConfirmacaoSimNao.setObject(modelObject);
                        dlgConfirmacaoSimNao.show(target);
                    }
                });
                acaoConcluir.setEnabled(AutoMulta.Situacao.PENDENTE.value().equals(rowObject.getSituacao()) && isProfissionalAuto(rowObject));
                acaoConcluir.setTitleBundleKey("finalizarArquivar");

                ModelActionLinkPanel acaoRelatorioInspecao = addAction(ActionType.LAUDAR, rowObject, new IModelAction<AutoMulta>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoMulta modelObject) throws ValidacaoException, DAOException {
                        CadastroRelatorioInspecaoPage relatorioClass = new CadastroRelatorioInspecaoPage();
                        relatorioClass.setClassReturn(ConsultaRelatorioInspecaoPage.class);
                        relatorioClass.setAutoMulta(modelObject);
                        relatorioClass.buscarAutoMulta(target, modelObject.getCodigo());
                        setResponsePage(relatorioClass);
                    }
                });
                acaoRelatorioInspecao.setTitleBundleKey("tituloNumRelatorioInspecao");
                acaoRelatorioInspecao.setEnabled(isProfissionalAuto(rowObject) /*&& AutoMulta.Situacao.CONCLUIDO.value().equals(rowObject.getSituacao())*/);
                acaoRelatorioInspecao.setVisible(new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaRelatorioInspecaoPage.class.getName()));


                ModelActionLinkPanel acaoBoleto = addAction(ActionType.MONEY, rowObject, new IModelAction<AutoMulta>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoMulta modelObject) throws ValidacaoException, DAOException {
                        VigilanciaFinanceiro vigilanciaFinanceiro = VigilanciaHelper.getVigilanciaFinanceiro(modelObject);
                        if (vigilanciaFinanceiro != null) {
                            try {
                                imprimirBoleto(target, vigilanciaFinanceiro);
                            } catch (Exception e) {
                                br.com.ksisolucoes.util.log.Loggable.log.error(e);
                            }
                        } else {
                            showDlgDataVencimentoBoleto(target, modelObject);
                        }
                    }
                });
                acaoBoleto.setTitleBundleKey("boleto");
                acaoBoleto.setVisible(isActionPermitted(Permissions.BOLETO) && ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca()));
                acaoBoleto.setIcon(Icon.CODE_BAR);
            }
        };
    }

    private boolean enableAlterarMulta(AutoMulta rowObject) {
        boolean pendente = !AutoMulta.Situacao.CONCLUIDO.value().equals(rowObject.getSituacao());
        if (pendente) {
            Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
            if (profissional != null && profissional.getCodigo() != null) {
                return AutosHelper.isFiscalMulta(rowObject, profissional);
            }
        }
        return pendente;
    }

    private boolean isProfissionalAuto(AutoMulta rowObject) {
        Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if (profissional != null && profissional.getCodigo() != null) {
            return AutosHelper.isFiscalMulta(rowObject, profissional);
        } else {
            return SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster();
        }
    }

    private void showDlgDataVencimentoBoleto(AjaxRequestTarget target, AutoMulta autoMulta) {
        if (dlgDataVencimento == null) {
            addModal(target, dlgDataVencimento = new DlgData<AutoMulta>(newModalId(), bundle("msgConfirmeDataVencimento"), "dataVencimento") {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Date date) throws ValidacaoException, DAOException {
                    VigilanciaFinanceiroBoletoDTO dto = new VigilanciaFinanceiroBoletoDTO();
                    dto.setAutoMulta(getObject());
                    dto.setValorBoleto(getObject().getValorTotalMulta());
                    dto.setDataVencimento(date);
                    List<VigilanciaFinanceiro> vigilanciaFinanceiroList = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarEmissaoBoletoAuto(dto);

                    if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
                        try {
                            imprimirBoleto(target, vigilanciaFinanceiroList.get(0));
                        } catch (Exception e) {
                            br.com.ksisolucoes.util.log.Loggable.log.error(e);
                            throw new ValidacaoException(e);
                        }
                    }
                }
            });
        }
        dlgDataVencimento.setObject(autoMulta);
        dlgDataVencimento.show(target, DataUtil.getDataAtual());
    }

    private void imprimirBoleto(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws Exception {
        File boleto = null;
        if (vigilanciaFinanceiro.getAnexoBoleto() != null) {
            boleto = VigilanciaAnexosHelper.getFileAnexoBoleto(vigilanciaFinanceiro);
            FileUtils.buscarArquivoFtp(vigilanciaFinanceiro.getAnexoBoleto().getCaminho(), boleto.getAbsolutePath());
        } else if (vigilanciaFinanceiro.getTokenBoleto() != null) {
            BoletoDTO boletoDTO = BOFactoryWicket.getBO(BasicoFacade.class).consultarBoleto(vigilanciaFinanceiro.getTokenBoleto());
            boleto = boletoDTO.getBoleto();
            if (boleto != null) {
                FinanceiroVigilanciaHelper.anexarBoleto(vigilanciaFinanceiro, boleto);
                BOFactoryWicket.save(vigilanciaFinanceiro);
            }
        } else if (Coalesce.asDouble(vigilanciaFinanceiro.getValor()) > 0D) {
            EmissaoBoletoVigilanciaDTO dto = new EmissaoBoletoVigilanciaDTO();
            dto.setVigilanciaFinanceiro(vigilanciaFinanceiro);

            boleto = BOFactoryWicket.getBO(VigilanciaReportFacade.class).gerarBoletoVigilancia(dto);
        }
        if (boleto != null) {
            String fileToBase64 = FileUtils.getFileToBase64(boleto);
            Files.deleteIfExists(boleto.toPath());
            ajaxPreviewBlank.initiatePdfBase64(target, fileToBase64);
        }
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return AutoMulta.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(AutoMulta.class).getProperties(),
                        new HQLProperties(Estabelecimento.class, AutoMulta.PROP_ESTABELECIMENTO).getProperties(),
                        new HQLProperties(VigilanciaPessoa.class, AutoMulta.PROP_VIGILANCIA_PESSOA).getProperties(),
                        new HQLProperties(VigilanciaEndereco.class, AutoMulta.PROP_VIGILANCIA_ENDERECO).getProperties(),
                        new HQLProperties(AutoIntimacao.class, AutoMulta.PROP_AUTO_INTIMACAO).getProperties(),
                        new HQLProperties(AutoInfracao.class, AutoMulta.PROP_AUTO_INFRACAO).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(AutoMulta.PROP_NUMERO), false);
            }

            @Override
            public List getInterceptors() {
                if (RepositoryComponentDefault.NAO_LONG.equals(exibirOutrosFiscais)) {
                    return Arrays.asList(new LoadInterceptor() {
                        @Override
                        public void customHQL(HQLHelper hql, String alias) {
                            if (SessaoAplicacaoImp.getInstance().getUsuario().getProfissional() != null) {
                                StringBuilder where = new StringBuilder();
                                where.append("select 1 ");
                                where.append("from AutoMultaFiscal fai ");
                                where.append("where fai.autoMulta.codigo = ").append(alias).append(".codigo");
                                where.append(" and fai.profissional.codigo = ").append(SessaoAplicacaoImp.getInstance().getUsuario().getProfissional().getCodigo());
                                where.insert(0, "exists(").append(")");
                                hql.addToWhereWhithAnd(where.toString());
                            }
                        }
                    });
                } else {
                    return null;
                }
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> lstParametros = new ArrayList<BuilderQueryCustom.QueryParameter>();
        lstParametros.add(new QueryCustom.QueryCustomParameter(AutoMulta.PROP_NUMERO, numero));
        lstParametros.add(new QueryCustom.QueryCustomParameter(AutoMulta.PROP_DESCRICAO_AUTUADO, QueryCustom.QueryCustomParameter.ILIKE, autuado));
        lstParametros.add(new QueryCustom.QueryCustomParameter(AutoMulta.PROP_DATA_MULTA, periodo));
        lstParametros.add(new QueryCustom.QueryCustomParameter(AutoMulta.PROP_SITUACAO, situacao));
        return lstParametros;
    }

    public void setClassVoltar(Class classVoltar) {
        this.returnClass = classVoltar;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAutoMultaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaAutoMulta");
    }
}
