package br.com.celk.view.materiais.estoque.inventario.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.inventario.autocomplete.AutoCompleteConsultaInventario;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioControleInventarioDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 395
 */
@Private
public class RelatorioRelacaoLancamentoInventarioPage extends RelatorioPage<RelatorioControleInventarioDTOParam> {

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<Long> dropDownSituacao;
    private DropDown<String> dropDownTipo;
    private DropDown dropDownFormaApresentacao;
    private DropDown dropDownOrdenacao;
    private PnlChoicePeriod pnlChoicePeriod;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaUsuario autoCompleteConsultaUsuario;
    private AutoCompleteConsultaInventario autoCompleteConsultaInventario;
    private DropDown dropDownDivergencia;

    @Override
    public void init(Form form) {
        RelatorioControleInventarioDTOParam proxy = on(RelatorioControleInventarioDTOParam.class);

        configuraComponentes(proxy);

        form.add(autoCompleteConsultaEmpresa);

        form.add(new AutoCompleteConsultaDeposito(path(proxy.getDepositos())).setOperadorValor(true));
        form.add(autoCompleteConsultaUsuario);
        form.add(autoCompleteConsultaInventario);
        form.add(getDropDownGrupo(path(proxy.getGrupoProduto())));
        form.add(getDropDownSubGrupo(path(proxy.getSubGrupo())));
        form.add(new AutoCompleteConsultaLocalizacao(path(proxy.getLocalizacao())).setOperadorValor(true));
        form.add(dropDownSituacao);
        form.add(dropDownTipo);
        form.add(dropDownFormaApresentacao);
        form.add(dropDownOrdenacao);
        form.add(pnlChoicePeriod);
        form.add(dropDownDivergencia);
    }

    private void configuraComponentes(RelatorioControleInventarioDTOParam proxy) {
        pnlChoicePeriod = new PnlChoicePeriod(path(proxy.getPeriodo()));
        pnlChoicePeriod.setDefaultOutro();

        autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresas()));
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);

        autoCompleteConsultaInventario = new AutoCompleteConsultaInventario(path(proxy.getInventario()));
        if (!isPermissaoEmpresa) {
            try {
                List<Long> empresasUsuario = BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
                List<Empresa> empresas = LoadManager.getInstance(Empresa.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, empresasUsuario))
                        .start().getList();

                autoCompleteConsultaInventario.setEmpresaList(empresas);
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            } catch (ValidacaoException e) {
                Loggable.log.error(e.getMessage());
            }
        }

        autoCompleteConsultaUsuario = new AutoCompleteConsultaUsuario(path(proxy.getUsuario()));
        autoCompleteConsultaUsuario.setOperadorValor(true);

        dropDownSituacao = new DropDown<Long>(path(proxy.getSituacao()));
        dropDownSituacao.addChoice(ControleInventario.STATUS_ABERTO, Bundle.getStringApplication("rotulo_nao_processado"));
        dropDownSituacao.addChoice(ControleInventario.STATUS_PROCESSADO, Bundle.getStringApplication("rotulo_processado"));

        dropDownTipo = new DropDown<String>(path(proxy.getTipo()));
        dropDownTipo.addChoice(ReportProperties.INVENTARIO, Bundle.getStringApplication("rotulo_inventario"));
        dropDownTipo.addChoice(ReportProperties.PRODUTO_ZERADOS, Bundle.getStringApplication("rotulo_produto_zerados_abv"));

        dropDownFormaApresentacao = new DropDown(path(proxy.getFormaApresentacao()));
        dropdownFAaddchoices(true);

        dropDownOrdenacao = new DropDown(path(proxy.getOrdenacao()));
        dropDownOrdenacao.addChoice(Produto.PROP_CODIGO, Bundle.getStringApplication("rotulo_codigo"));
        dropDownOrdenacao.addChoice(Produto.PROP_DESCRICAO, Bundle.getStringApplication("rotulo_descricao"));


        dropDownDivergencia = new DropDown(path(proxy.getDivergencia()));
        dropDownDivergencia.addChoice(null, BundleManager.getString("ambos"));
        dropDownDivergencia.addChoice(ReportProperties.FLAG_SIM, BundleManager.getString("sim"));
        dropDownDivergencia.addChoice(ReportProperties.FLAG_NAO, BundleManager.getString("nao"));
        dropDownDivergencia.add(new Tooltip().setText("msgDefineSeraExibidoItensComDivergenciaEstoqueFisicoVirtual"));

        dropDownTipo.setEnabled(true);
        pnlChoicePeriod.setEnabled(false);

        dropDownSituacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownSituacao.getComponentValue().equals(ControleInventario.STATUS_ABERTO)) {
                    dropDownTipo.setEnabled(true);
                    pnlChoicePeriod.setEnabled(false);
                } else {
                    dropDownTipo.setEnabled(false);
                    pnlChoicePeriod.setEnabled(true);
                    dropDownTipo.setComponentValue(ReportProperties.INVENTARIO);
                    dropdownFAaddchoices(true);
                    autoCompleteConsultaEmpresa.setEnabled(true);
                    autoCompleteConsultaUsuario.setEnabled(true);
                    target.add(autoCompleteConsultaEmpresa);
                    target.add(autoCompleteConsultaUsuario);
                    target.add(dropDownFormaApresentacao);
                }
                target.add(dropDownTipo);
                target.add(pnlChoicePeriod);
            }
        });

        dropDownTipo.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownTipo.getComponentValue().equals(ReportProperties.INVENTARIO)) {
                    autoCompleteConsultaEmpresa.setEnabled(true);
                    autoCompleteConsultaUsuario.setEnabled(true);
                    autoCompleteConsultaInventario.setEnabled(true);
                    dropdownFAaddchoices(true);
                } else {
                    autoCompleteConsultaEmpresa.setComponentValue(new OperadorValor<Empresa>(SessaoAplicacaoImp.getInstance().getEmpresa(), OperadorValor.Operadores.IN));
                    autoCompleteConsultaUsuario.setComponentValue(new OperadorValor<Usuario>(SessaoAplicacaoImp.getInstance().getUsuario(), OperadorValor.Operadores.IN));
                    autoCompleteConsultaEmpresa.setEnabled(false);
                    autoCompleteConsultaUsuario.setEnabled(false);
                    autoCompleteConsultaInventario.setEnabled(false);
                    autoCompleteConsultaEmpresa.limpar(target);
                    autoCompleteConsultaUsuario.limpar(target);
                    autoCompleteConsultaInventario.limpar(target);
                    dropdownFAaddchoices(false);
                }
                target.add(autoCompleteConsultaInventario);
                target.add(autoCompleteConsultaEmpresa);
                target.add(autoCompleteConsultaUsuario);
                target.add(dropDownFormaApresentacao);
            }
        });

    }

    private void dropdownFAaddchoices(Boolean addUsuario) {
        dropDownFormaApresentacao.removeAllChoices();
        if (addUsuario) {
            dropDownFormaApresentacao.addChoice(ReportProperties.FORMA_APRESENTACAO_GERAL, Bundle.getStringApplication("rotulo_geral"));
            dropDownFormaApresentacao.addChoice(VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), Bundle.getStringApplication("rotulo_grupo"));
            dropDownFormaApresentacao.addChoice(Produto.PROP_USUARIO, Bundle.getStringApplication("rotulo_usuario"));
        }else{
            dropDownFormaApresentacao.addChoice(ReportProperties.FORMA_APRESENTACAO_GERAL, Bundle.getStringApplication("rotulo_geral"));
            dropDownFormaApresentacao.addChoice(VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), Bundle.getStringApplication("rotulo_grupo"));
            
        }
    }

    private DropDown<GrupoProduto> getDropDownGrupo(String id) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>(id);

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();

                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo(String id) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>(id);

            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioControleInventarioDTOParam.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoLancamentoInventario");
    }

    @Override
    public DataReport getDataReport(RelatorioControleInventarioDTOParam param) throws ReportException {
        if (dropDownGrupoProduto.getComponentValue() != null) {
            param.setGruposProdutoInformado();
        } else {
            param.setGruposProduto(null);
        }
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioControleInventario(param);
    }
}
