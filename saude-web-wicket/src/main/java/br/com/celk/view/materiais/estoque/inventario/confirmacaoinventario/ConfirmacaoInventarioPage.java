package br.com.celk.view.materiais.estoque.inventario.confirmacaoinventario;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.asyncprocess.interfaces.IAsyncProcessNotification;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.inventario.autocomplete.AutoCompleteConsultaInventario;
import br.com.celk.view.materiais.estoque.inventario.confirmacaoinventario.customcolumn.StatusConfirmacaoInventarioProcessoColumnPanel;
import br.com.celk.view.materiais.estoque.inventario.confirmacaoinventario.customize.CustomizeConsultaConfirmacaoInventarioProcesso;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ControleInventarioFacade;
import br.com.ksisolucoes.bo.materiais.inventario.InventarioHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 * Programa - 391
 */
public class ConfirmacaoInventarioPage extends BasePage implements IAsyncProcessNotification {
    
    private Inventario inventario;
    private DropDown dropDownZerarProdutos;
    private DropDown dropDownZerarLotes;
    private DropDown dropDownConsiderarProdutosZerados;
    private Boolean zerarProdutos;
    private Boolean zerarLotes;
    private Boolean considerarProdutosZerados;
    private SubmitButton btnProcessar;
    private PageableTable tblConfirmacaoInventarioProcesso;
    private AutoCompleteConsultaInventario autoCompleteConsultaInventario;

    private InputField dataInventarioInput;
    private Date dataInventario;
    private InputField estabelecimentoInventarioInput;
    private String estabelecimentoInventario;
    private InputField localizacaoInput;
    private String localizacao;

    @Override
    protected void postConstruct() {
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        configuraComponentes();
        
        form.add(autoCompleteConsultaInventario);
        form.add(dataInventarioInput);
        form.add(estabelecimentoInventarioInput);
        form.add(localizacaoInput);
        form.add(dropDownZerarProdutos);
        form.add(dropDownZerarLotes);
        form.add(dropDownConsiderarProdutosZerados);

        form.add(tblConfirmacaoInventarioProcesso = new PageableTable("tblConfirmacaoInventarioProcesso", getColumnsProcesso(), getPagerProviderProcesso()));
        tblConfirmacaoInventarioProcesso.populate();
        
        form.add(btnProcessar);
        
        add(form);
    }
    
    public void configuraComponentes() {

        autoCompleteConsultaInventario = new AutoCompleteConsultaInventario("inventario", true);
        autoCompleteConsultaInventario.setSituacao(Inventario.Situacao.ABERTO.value());
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        if (!isPermissaoEmpresa) {
            try {
                List<Long> empresasUsuario = BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
                List<Empresa> empresas = LoadManager.getInstance(Empresa.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, empresasUsuario))
                        .start().getList();

                autoCompleteConsultaInventario.setEmpresaList(empresas);
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            } catch (ValidacaoException e) {
                Loggable.log.error(e.getMessage());
            }
        }

        autoCompleteConsultaInventario.add(new ConsultaListener<Inventario>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Inventario object) {
                setObject(target, object);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        autoCompleteConsultaInventario.add(new RemoveListener<Inventario>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Inventario object) {
                limpar(target);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        autoCompleteConsultaInventario.addAjaxUpdateValue();
        autoCompleteConsultaInventario.setOutputMarkupId(true);

        dataInventarioInput = new InputField("dataInventario");
        dataInventarioInput.setEnabled(false);

        estabelecimentoInventarioInput = new InputField("estabelecimentoInventario");
        estabelecimentoInventarioInput.setEnabled(false);

        localizacaoInput = new InputField("localizacao");
        localizacaoInput.setEnabled(false);

        dropDownZerarProdutos = DropDownUtil.getNaoSimBooleanDropDown("zerarProdutos");
        dropDownConsiderarProdutosZerados = DropDownUtil.getNaoSimBooleanDropDown("considerarProdutosZerados");

        dropDownZerarProdutos.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if(zerarProdutos){
                    dropDownZerarLotes.limpar(target);
                    dropDownZerarLotes.setEnabled(false);
                }else{
                    dropDownZerarLotes.setEnabled(true);
                }
                target.add(dropDownZerarLotes);
            }
        });
        dropDownZerarLotes = DropDownUtil.getNaoSimBooleanDropDown("zerarLotes");
        
        btnProcessar = new SubmitButton("btnProcessar", new ISubmitAction() {
            
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
                tblConfirmacaoInventarioProcesso.update(target);
                limpar(target);
            }
        });
    }

    private void setObject(AjaxRequestTarget target, Inventario object) {
        Inventario inventario = LoadManager.getInstance(Inventario.class)
                .addProperties(new HQLProperties(Inventario.class).getProperties())
                .addProperties(new HQLProperties(LocalizacaoEstrutura.class, Inventario.PROP_LOCALIZACAO_ESTRUTURA).getProperties())
                .addProperties(new HQLProperties(Empresa.class, Inventario.PROP_EMPRESA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Inventario.PROP_CODIGO, object.getCodigo()))
                .start().getVO();

        if (inventario != null) {
            if (inventario.getEmpresa() != null) {
                estabelecimentoInventarioInput.setComponentValue(inventario.getEmpresa().getDescricaoFormatado());
            }
            if (inventario.getLocalizacaoEstrutura() != null) {
                localizacaoInput.setComponentValue(inventario.getLocalizacaoEstrutura().getDescricaoEstrutura());
            }
            dataInventarioInput.setComponentValue(inventario.getDataInventario());
            this.inventario = inventario;
            target.add(estabelecimentoInventarioInput);
            target.add(dataInventarioInput);
            target.add(localizacaoInput);
        }
    }

    private void limpar(AjaxRequestTarget target) {
        estabelecimentoInventarioInput.limpar(target);
        dataInventarioInput.limpar(target);
        localizacaoInput.limpar(target);
        autoCompleteConsultaInventario.limpar(target);
        dropDownZerarProdutos.limpar(target);
        dropDownZerarLotes.limpar(target);
        dropDownZerarLotes.setEnabled(true);
        dropDownConsiderarProdutosZerados.limpar(target);

        target.add(estabelecimentoInventarioInput);
        target.add(dataInventarioInput);
        target.add(localizacaoInput);
        target.add(autoCompleteConsultaInventario);
        target.add(dropDownZerarProdutos);
        target.add(dropDownZerarLotes);
        target.add(dropDownConsiderarProdutosZerados);
    }

    public List<IColumn> getColumnsProcesso() {
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(ConfirmacaoInventarioProcesso.class);
        
        columns.add(getCustomColumnProcesso());
        columns.add(columnFactory.createSortableColumn(bundle("dataGeracao"), VOUtils.montarPath(ConfirmacaoInventarioProcesso.PROP_DATA_GERACAO)));
        columns.add(columnFactory.createSortableColumn(bundle("inventario"), VOUtils.montarPath(ConfirmacaoInventarioProcesso.PROP_INVENTARIO, Inventario.PROP_DESCRICAO_INVENTARIO)));
        columns.add(getCustomColumnStatus());
        
        return columns;
    }
    
    private CustomColumn getCustomColumnProcesso() {
        return new CustomColumn<ConfirmacaoInventarioProcesso>() {
            
            @Override
            public Component getComponent(String componentId, final ConfirmacaoInventarioProcesso rowObject) {
                return new ConfirmacaoInventarioColumnPanel(componentId, rowObject) {
                    
                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        tblConfirmacaoInventarioProcesso.update(target);
                        target.add(tblConfirmacaoInventarioProcesso);
                    }
                    
                };
            }
        };
    }
    
    public CustomColumn getCustomColumnStatus() {
        return new CustomColumn<ConfirmacaoInventarioProcesso>() {
            
            @Override
            public Component getComponent(String componentId, ConfirmacaoInventarioProcesso rowObject) {
                return new StatusConfirmacaoInventarioProcessoColumnPanel(componentId, rowObject);
            }
        };
    }
    
    public IPagerProvider getPagerProviderProcesso() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaConfirmacaoInventarioProcesso()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ConfirmacaoInventarioProcesso.PROP_CODIGO, false);
            }
        };
    }
    
    private void salvar() throws ValidacaoException, DAOException {
        List<ControleInventario> lstControleInventario = LoadManager.getInstance(ControleInventario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ControleInventario.PROP_ID, ControleInventarioPK.PROP_EMPRESA, Empresa.PROP_CODIGO), inventario.getEmpresa().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ControleInventario.PROP_LOCALIZACAO_ESTRUTURA),
                        BuilderQueryCustom.QueryParameter.IN, InventarioHelper.listaLocalizacaoInventario(inventario)))
                .addParameter(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_STATUS, ControleInventario.STATUS_ABERTO))
                .start().getList();
        
        if (lstControleInventario.isEmpty() && zerarProdutos == false) {
            throw new ValidacaoException(bundle("verifiqueProdutoLancadoOuZereTudo"));
        }
        List<Empresa> lstEmpresas = new ArrayList<Empresa>();
        lstEmpresas.add(inventario.getEmpresa());
        BOFactoryWicket.getBO(ControleInventarioFacade.class).EnviarConfirmacaoInventarioProcessoFila(lstEmpresas, zerarProdutos, inventario.getDataInventario(), zerarLotes, inventario, considerarProdutosZerados);
    }
    
    private boolean verificaDataMaiorAtual(DateChooser data) {
        if (data != null && data.getModelObject() != null) {
            if (data.getModelObject().after(new Date())) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public String getTituloPrograma() {
        return bundle("confirmacaoInventario");
    }
    
    @Override
    public void notifyProcess(AjaxRequestTarget target, AsyncProcess event) {
        tblConfirmacaoInventarioProcesso.update(target);
    }
}
