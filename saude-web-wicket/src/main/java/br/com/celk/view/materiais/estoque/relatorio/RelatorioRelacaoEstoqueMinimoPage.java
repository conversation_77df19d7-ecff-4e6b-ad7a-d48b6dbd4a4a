package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.longfield.LongField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoEstoqueMinimoDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 * Programa - 59
 */
@Private
public class RelatorioRelacaoEstoqueMinimoPage extends RelatorioPage<RelatorioRelacaoEstoqueMinimoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<Long> dropDownVisualizarConsumo;
    private LongField txtMesesCalcMediaConsumo;

    @Override
    public void init(Form form) {
        RelatorioRelacaoEstoqueMinimoDTOParam proxy = on(RelatorioRelacaoEstoqueMinimoDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresas())));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA));
        form.add(getDropDownGrupo(path(proxy.getGrupoProdutoSubGrupo())));
        form.add(getDropDownSubGrupo(path(proxy.getSubGrupo())));
        form.add(new AutoCompleteConsultaLocalizacao(path(proxy.getLocalizacao())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getEstoqueMinimo()), RelatorioRelacaoEstoqueMinimoDTOParam.EstoqueMinimo.values()));
        form.add(DropDownUtil.getNaoSimDropDown(path(proxy.getConsiderarVencidos())));
        form.add(new DoubleField(path(proxy.getPercentual())));
        form.add(getDropDownOrdenacao(path(proxy.getOrdenacao())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoRelatorio()), RelatorioRelacaoEstoqueMinimoDTOParam.TipoRelatorio.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoEstabelecimento()), RelatorioRelacaoEstoqueMinimoDTOParam.TipoEstabelecimento.values()));
        form.add(dropDownVisualizarConsumo = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVisualizarConsumo())));
        dropDownVisualizarConsumo.addAjaxUpdateValue();
        dropDownVisualizarConsumo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (RepositoryComponentDefault.SIM_LONG.equals(dropDownVisualizarConsumo.getComponentValue())) {
                    txtMesesCalcMediaConsumo.setEnabled(true);
                    txtMesesCalcMediaConsumo.setRequired(true);
                } else {
                    txtMesesCalcMediaConsumo.setEnabled(false);
                    txtMesesCalcMediaConsumo.setRequired(false);
                }
                target.add(txtMesesCalcMediaConsumo);
            }
        });

        form.add(txtMesesCalcMediaConsumo = new LongField(path(proxy.getMesesCalculoMediaConsumo())));
        txtMesesCalcMediaConsumo.setLabel(new Model(BundleManager.getString("mesesCalcMediaConsumo")));  
        txtMesesCalcMediaConsumo.setEnabled(false);
    }

    public DropDown getDropDownOrdenacao(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(ReportProperties.ORDENAR_DESCRICAO, BundleManager.getString("descricao"));
        dropDown.addChoice(ReportProperties.ORDENAR_CODIGO, BundleManager.getString("codigo"));

        return dropDown;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo(String id) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>(id);
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo(String id) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>(id);
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        SubGrupo proxy = on(SubGrupo.class);
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(path(proxy.getId().getCodigo()))
                                .addProperty(path(proxy.getId().getCodigoGrupoProduto()))
                                .addProperty(path(proxy.getDescricao()))
                                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getCodigoGrupoProduto()), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDescricao())))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(GrupoProduto.PROP_CODIGO)
                    .addProperty(GrupoProduto.PROP_DESCRICAO)
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoEstoqueMinimoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoEstoqueMinimoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioRelacaoEstoqueMinimo(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("estoqueMinimo");
    }
}
