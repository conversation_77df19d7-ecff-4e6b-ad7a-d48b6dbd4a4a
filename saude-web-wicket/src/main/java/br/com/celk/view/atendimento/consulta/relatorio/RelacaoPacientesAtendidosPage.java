package br.com.celk.view.atendimento.consulta.relatorio;

import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.report.RelatorioPage;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioRelacaoUsuariosAtendidosDTOParam;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.util.Bundle;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import br.com.celk.component.dropdown.DropDown;

/**
 *
 * <AUTHOR>
 * Programa - 137
 */
@Private

public class RelacaoPacientesAtendidosPage extends RelatorioPage<RelatorioRelacaoUsuariosAtendidosDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo;
    private PnlChoicePeriod pnlDatePeriod;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresas"));
        form.add(autoCompleteConsultaTabelaCbo = new AutoCompleteConsultaTabelaCbo("tabelaCbos"));
        form.add(pnlDatePeriod = new RequiredPnlChoicePeriod("periodo"));
        form.add(getCbxFormaApresentacao());
        form.add(getCbxTipoResumo());
        
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaTabelaCbo.setOperadorValor(true);
        autoCompleteConsultaTabelaCbo.setMultiplaSelecao(true);
    }
    
    private DropDown getCbxFormaApresentacao(){
        DropDown cbxFormaApresentacao = new DropDown("formaApresentacao");
        
        cbxFormaApresentacao.addChoice(Bundle.getStringApplication("rotulo_cbo"), BundleManager.getString("cbo"));
        cbxFormaApresentacao.addChoice(Bundle.getStringApplication("rotulo_empresa"), BundleManager.getString("unidade"));
        
        return cbxFormaApresentacao;
    }
    
    private DropDown getCbxTipoResumo(){
        DropDown cbxFormaApresentacao = new DropDown("tipoResumo");
        
        cbxFormaApresentacao.addChoice(Bundle.getStringApplication("rotulo_cbo"), BundleManager.getString("cbo"));
        cbxFormaApresentacao.addChoice(Bundle.getStringApplication("rotulo_empresa"), BundleManager.getString("unidade"));
        
        return cbxFormaApresentacao;
    }

    @Override
    public Class<RelatorioRelacaoUsuariosAtendidosDTOParam> getDTOParamClass() {
        return RelatorioRelacaoUsuariosAtendidosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoUsuariosAtendidosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioRelacaoUsuariosAtendidos(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("pacientesAtendidos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

}
