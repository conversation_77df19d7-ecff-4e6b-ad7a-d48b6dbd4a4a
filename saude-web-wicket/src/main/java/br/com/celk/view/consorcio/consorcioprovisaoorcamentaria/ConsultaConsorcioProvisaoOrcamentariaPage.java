package br.com.celk.view.consorcio.consorcioprovisaoorcamentaria;

import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.EditarActionColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaProvisaoOrcamentariaDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaProvisaoOrcamentariaDTOParam;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Arrays;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 101
 */
@Private
public class ConsultaConsorcioProvisaoOrcamentariaPage extends ConsultaPage<QueryConsultaProvisaoOrcamentariaDTO, QueryConsultaProvisaoOrcamentariaDTOParam> {

    private AutoCompleteConsultaEmpresa pnlConsultaEmpresaConsorcio;
    
    private Empresa consorciado;
    private Empresa consorcio;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(pnlConsultaEmpresaConsorcio = new AutoCompleteConsultaEmpresa("consorcio").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));

        try {
            consorcio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("consorcioPadrao");
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(QueryConsultaProvisaoOrcamentariaDTO.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("consorciado"), QueryConsultaProvisaoOrcamentariaDTO.PROP_CONSORCIADO, VOUtils.montarPath(QueryConsultaProvisaoOrcamentariaDTO.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("consorcio"), QueryConsultaProvisaoOrcamentariaDTO.PROP_CONSORCIO, VOUtils.montarPath(QueryConsultaProvisaoOrcamentariaDTO.PROP_CONSORCIO, Empresa.PROP_DESCRICAO)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<QueryConsultaProvisaoOrcamentariaDTO>() {

            @Override
            public Component getComponent(String componentId, final QueryConsultaProvisaoOrcamentariaDTO rowObject) {
                return new EditarActionColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroConsorcioProvisaoOrcamentariaPage(rowObject.getConsorcio(), rowObject.getConsorciado()));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<QueryConsultaProvisaoOrcamentariaDTO, QueryConsultaProvisaoOrcamentariaDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(ConsorcioFacade.class).consultarProvisaoOrcamentaria(dataPaging);
            }

            @Override
            public void customizeParam(QueryConsultaProvisaoOrcamentariaDTOParam param) {
                param.setSortProp(getSort().getProperty());
                param.setAscending(getSort().isAscending());
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(QueryConsultaProvisaoOrcamentariaDTO.PROP_CONSORCIADO, true);
            }
        };
    }

    @Override
    public QueryConsultaProvisaoOrcamentariaDTOParam getParameters() {
        QueryConsultaProvisaoOrcamentariaDTOParam param = new QueryConsultaProvisaoOrcamentariaDTOParam();
        
        param.setConsorciado(consorciado);
        param.setConsorcio(consorcio);
        
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return NovoConsorcioProvisaoOrcamentariaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaProvisaoOrcamentaria");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlConsultaEmpresaConsorcio.getTxtDescricao().getTextField();
    }

}
