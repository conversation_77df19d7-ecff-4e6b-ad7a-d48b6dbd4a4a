package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioDispensacaoReceituarioParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.io.IOException;

/**
 * Programa - 1184
 */
@Private
public class RelatorioDispensacaoReceitaPage extends RelatorioPage<RelatorioDispensacaoReceituarioParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        form.add(new AutoCompleteConsultaUsuarioCadsus("paciente"));
        form.add(new AutoCompleteConsultaProfissional("profissionalPrescritor"));
        form.add(new InputField("codigoBarras"));
        form.add(new RequiredPnlChoicePeriod("periodo"));

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioDispensacaoReceituarioParam.class;
    }

    @Override
    public void gerarRelatorio(AjaxRequestTarget target) throws IOException, ReportException, ValidacaoException {
        super.gerarRelatorio(target);
    }

    @Override
    public DataReport getDataReport(RelatorioDispensacaoReceituarioParam param) throws ReportException {
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioDispensacaoReceituario(param, true);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoDispensacoesReceituario");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

}
