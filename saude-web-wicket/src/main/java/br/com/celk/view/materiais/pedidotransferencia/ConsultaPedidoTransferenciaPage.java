package br.com.celk.view.materiais.pedidotransferencia;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.estoque.deposito.pnl.PnlConsultaDeposito;
import br.com.celk.view.materiais.pedidotransferencia.customcolumn.ConsultaPedidoTransferenciaColumnPanel;
import br.com.celk.view.materiais.pedidotransferencia.customize.CustomizeConsultaPedidoTransferencia;
import br.com.celk.view.materiais.pedidotransferencia.relatorio.CadastroPedidoTransferenciaVacinaPage;
import br.com.celk.view.materiais.pedidotransferencia.relatorio.DetalhesPedidoAlmoxarifadoVacinaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoTransferenciaFacade;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.OrigemProcessoPedido;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioPedidoTransferenciaDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Deposito;
import br.com.ksisolucoes.vo.entradas.estoque.EmbarquePedidoTransferencia;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * Programa - 106
 */
@Private
public class ConsultaPedidoTransferenciaPage extends BasePage {

    private PageableTable<PedidoTransferencia> pageableTable;
    private CustomizeConsultaPagerProvider<PedidoTransferencia> dataProvider;
    private Long pedido;
    private Empresa unidadeDestino;
    private Deposito deposito;
    private DatePeriod periodo;
    private Long situacao;
    private Long flagVacina;

    public ConsultaPedidoTransferenciaPage(PageParameters parameters) {
        super(parameters);
        init();
    }

    public ConsultaPedidoTransferenciaPage(IModel<?> model) {
        super(model);
        init();
    }

    public ConsultaPedidoTransferenciaPage() {
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));


        form.add(new InputField<Long>("pedido"));
        form.add(new AutoCompleteConsultaEmpresa("unidadeDestino"));
        form.add(new PnlConsultaDeposito("deposito"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(DropDownUtil.getNaoSimLongDropDown("flagVacina", "Ambos"));
        form.add(getDropDownSituacao());

        pageableTable = new PageableTable<PedidoTransferencia>("table", getColumns(), getDataProvider());

        form.add(new ProcurarButton<List<QueryParameter>>("btnProcurar", pageableTable) {
            @Override
            public List<QueryParameter> getParam() {
                return ConsultaPedidoTransferenciaPage.this.getParam();
            }
        });
        pageableTable.setScrollX("1500px");
        form.add(pageableTable);

        add(form);

        add(new BookmarkablePageLink("linkNovo", NovoPedidoTransferenciaPage.class));
    }

    private DropDown<Long> getDropDownSituacao() {
        DropDown<Long> dropDown = new DropDown<Long>("situacao");
        dropDown.addChoice(null, BundleManager.getString("todos"));
        dropDown.addChoice(PedidoTransferencia.STATUS_ABERTO, BundleManager.getString("aberto"));
        dropDown.addChoice(PedidoTransferencia.STATUS_SEPARANDO, BundleManager.getString("separando"));
        dropDown.addChoice(PedidoTransferencia.STATUS_PROCESSADO, BundleManager.getString("processado"));
        dropDown.addChoice(PedidoTransferencia.STATUS_RECEBIDO, BundleManager.getString("recebido"));
        dropDown.addChoice(PedidoTransferencia.STATUS_CANCELADO, BundleManager.getString("cancelado"));
        dropDown.addChoice(PedidoTransferencia.STATUS_NAO_APROVADO, BundleManager.getString("nao_aprovado"));

        return dropDown;
    }

    private CustomizeConsultaPagerProvider<PedidoTransferencia> getDataProvider() {
        if (this.dataProvider == null) {
            this.dataProvider = new CustomizeConsultaPagerProvider<PedidoTransferencia>(new CustomizeConsultaPedidoTransferencia()) {
                @Override
                public SortParam getDefaultSort() {
                    return new SortParam(PedidoTransferencia.PROP_CODIGO, false);
                }
            };
        }

        return this.dataProvider;
    }

    private List<ISortableColumn<PedidoTransferencia>> getColumns() {
        List<ISortableColumn<PedidoTransferencia>> columns = new ArrayList<ISortableColumn<PedidoTransferencia>>();

        ColumnFactory columnFactory = new ColumnFactory(PedidoTransferencia.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("pedido"), VOUtils.montarPath(PedidoTransferencia.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("destino"), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_DESTINO, Empresa.PROP_DESCRICAO), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_DESTINO, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("data_pedido"), VOUtils.montarPath(PedidoTransferencia.PROP_DATA_PEDIDO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(PedidoTransferencia.PROP_STATUS), VOUtils.montarPath(PedidoTransferencia.PROP_DESCRICAO_STATUS)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("origem"), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estoque"), VOUtils.montarPath(PedidoTransferencia.PROP_DEPOSITO, Deposito.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("vacina"), VOUtils.montarPath(PedidoTransferencia.PROP_FLAG_VACINA), VOUtils.montarPath(PedidoTransferencia.PROP_FLAG_VACINA_FORMATADA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("data_embarque"), VOUtils.montarPath(PedidoTransferencia.PROP_DATA_EMBARQUE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("data_recebimento"), VOUtils.montarPath(PedidoTransferencia.PROP_DATA_RECEBIMENTO)));

        return columns;
    }

    private List<QueryParameter> getParam() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_ORIGEM), ApplicationSession.get().getSessaoAplicacao().getEmpresa()));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_DESTINO), unidadeDestino));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_DEPOSITO), deposito));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_FLAG_VACINA), flagVacina));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_CODIGO), pedido));
        if (periodo != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_DATA_PEDIDO), Data.adjustRangeHour(periodo)));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_STATUS), situacao));

        return parameters;
    }

    private CustomColumn<PedidoTransferencia> getCustomColumn() {
        return new CustomColumn<PedidoTransferencia>() {
            @Override
            public Component getComponent(String componentId, final PedidoTransferencia rowObject) {
                return new ConsultaPedidoTransferenciaColumnPanel(componentId, rowObject) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        if (RepositoryComponentDefault.NAO_LONG.equals(rowObject.getFlagVacina())) {
                            setResponsePage(new CadastroPedidoTransferenciaPage(rowObject));
                        } else {
                            setResponsePage(new CadastroPedidoTransferenciaVacinaPage(rowObject));
                        }
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cancelarPedidoTransferencia(rowObject.getCodigo(), rowObject.getVersion(), OrigemProcessoPedido.LANCAMENTO);
                        pageableTable.populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        if (RepositoryComponentDefault.SIM_LONG.equals(rowObject.getFlagVacina())) {
                            setResponsePage(new DetalhesPedidoAlmoxarifadoVacinaPage(rowObject));
                        } else {
                            setResponsePage(new DetalhesPedidoAlmoxarifadoPage(rowObject));
                        }
                    }

                    @Override
                    public DataReport onImprimir() throws ReportException {
                        return imprimir(rowObject);
                    }

                    @Override
                    public void onReabrir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        EmbarquePedidoTransferencia embarquePedidoTransferencia = getEmbarquePedido(rowObject);
                        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).reabrirEmbarquePedidoTransferencia(embarquePedidoTransferencia);
                        pageableTable.populate(target);
                    }
                };
            }
        };
    }

    public EmbarquePedidoTransferencia getEmbarquePedido(PedidoTransferencia pedidoTransferencia) {
        Set<Long> codigos = new HashSet<Long>(1);
        codigos.add(pedidoTransferencia.getCodigo());
        EmbarquePedidoTransferencia embarquePedidoTransferencia = new EmbarquePedidoTransferencia();
        embarquePedidoTransferencia.setCodigoPedidoTransferenciaSet(codigos);
        return embarquePedidoTransferencia;
    }

    private DataReport imprimir( PedidoTransferencia rowObject) throws ReportException {
        RelatorioPedidoTransferenciaDTOParam param = new RelatorioPedidoTransferenciaDTOParam();
//        param.setEmpresaOrigem(rowObject.getEmpresaOrigem());
        param.setFormaApresentacao(ReportProperties.FORMA_APRESENTACAO_GERAL);
        param.setNumeroPedidoTransferencia(rowObject.getCodigo());
        param.setOrdenacao(Produto.PROP_DESCRICAO);
        param.setStatusImpressao(PedidoTransferencia.STATUS_IMPRESSAO_IMPRESSO);

        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioPedidoTransferenciaWeb(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaPedidoTranferencia");
    }
}
