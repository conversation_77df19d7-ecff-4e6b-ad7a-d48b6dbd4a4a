package br.com.celk.view.vigilancia.faturamento.relatorios;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.faturamento.autocomplete.AutoCompleteConsultaAtividadesVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTOParam;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.BasicoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import ch.lambdaj.Lambda;
import org.apache.wicket.markup.html.form.Form;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 * <AUTHOR>
 * Programa - 775
 */
@Private
public class RelatorioProducaoVigilanciaResumidoPage extends RelatorioPage<LancamentoAtividadesVigilanciaDTOParam> {

    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    @Override
    public void init(Form form) {
        LancamentoAtividadesVigilanciaDTOParam proxy = Lambda.on(LancamentoAtividadesVigilanciaDTOParam.class);
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new AutoCompleteConsultaAtividadesVigilancia(path(proxy.getAtividadesVigilancia())));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getFormaApresentacao()), LancamentoAtividadesVigilanciaDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoResumo()), LancamentoAtividadesVigilanciaDTOParam.TipoResumo.values()));
    }

    @Override
    public Class getDTOParamClass() {
        return LancamentoAtividadesVigilanciaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(LancamentoAtividadesVigilanciaDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(BasicoReportFacade.class).relatorioProducaoVigilanciaResumido(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioProducaoResumido");
    }
}
