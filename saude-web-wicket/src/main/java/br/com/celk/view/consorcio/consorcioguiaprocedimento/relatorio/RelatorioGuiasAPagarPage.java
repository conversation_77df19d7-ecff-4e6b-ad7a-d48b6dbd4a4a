package br.com.celk.view.consorcio.consorcioguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.consorcioprocedimento.autocomplete.AutoCompleteConsorcioProcedimento;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioGuiasAPagarPagasDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.util.Arrays;


/**
 *
 * <AUTHOR>
 * Programa - 896
 */
@Private

public class RelatorioGuiasAPagarPage extends RelatorioPage<RelatorioGuiasAPagarPagasDTOParam> {
    
    private AutoCompleteConsorcioProcedimento autoCompleteConsorcioProcedimento;
    private DropDown<Long> dropDownSituacao;
    private DropDown dropDownTipoRelatorio;
    private DropDown dropDownOrdenacao;
    private DropDown dropDownTipoOrdenacao;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(autoCompleteConsorcioProcedimento = new AutoCompleteConsorcioProcedimento("consorcioProcedimento"));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getDropDownSituacao());
        form.add(dropDownTipoRelatorio = DropDownUtil.getEnumDropDown("tipoRelatorio", RelatorioGuiasAPagarPagasDTOParam.TipoRelatorio.values()));
        dropDownTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dropDownOrdenacao.setEnabled(RelatorioGuiasAPagarPagasDTOParam.TipoRelatorio.DETALHADO.equals(param.getTipoRelatorio()));
                dropDownTipoOrdenacao.setEnabled(RelatorioGuiasAPagarPagasDTOParam.TipoRelatorio.DETALHADO.equals(param.getTipoRelatorio()));
                target.add(dropDownOrdenacao);
                target.add(dropDownTipoOrdenacao);
            }
        });
        form.add(dropDownOrdenacao = DropDownUtil.getEnumDropDown("ordenacao", RelatorioGuiasAPagarPagasDTOParam.Ordenacao.values()));
        form.add(dropDownTipoOrdenacao = DropDownUtil.getEnumDropDown("tipoOrdenacao", RelatorioGuiasAPagarPagasDTOParam.TipoOrdenacao.values()));
    }

    public DropDown<Long> getDropDownSituacao() {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>("situacao");
            dropDownSituacao.addChoice(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.A_PAGAR.value(), ConsorcioGuiaProcedimento.StatusGuiaProcedimento.A_PAGAR.descricao());
            dropDownSituacao.addChoice(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.PAGA.value(), ConsorcioGuiaProcedimento.StatusGuiaProcedimento.PAGA.descricao());
            dropDownSituacao.addChoice(null, BundleManager.getString("ambas"));
        }
        return dropDownSituacao;
    }
    
    @Override
    public Class<RelatorioGuiasAPagarPagasDTOParam> getDTOParamClass() {
        return RelatorioGuiasAPagarPagasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioGuiasAPagarPagasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioGuiasAPagarPagas(param);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("guiaAPagarPagas");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsorcioProcedimento;
    }

}
