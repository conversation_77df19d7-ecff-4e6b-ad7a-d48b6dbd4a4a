package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.hospital.convenio.autocomplete.AutoCompleteConsultaConvenio;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRelacaoPrescricoesEmAbertoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 * Programa - 284
 */
@Private

public class RelatorioRelacaoPrescricoesEmAbertoPage extends RelatorioPage<RelatorioRelacaoPrescricoesEmAbertoDTOParam>{
    
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus("paciente"));
        form.add(new AutoCompleteConsultaEmpresa("empresas"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(new AutoCompleteConsultaConvenio("convenio"));
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("tipoRelatorio", RelatorioRelacaoPrescricoesEmAbertoDTOParam.TipoRelatorio.values()));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioRelacaoPrescricoesEmAbertoDTOParam.FormaApresentacao.values()));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoPrescricoesEmAbertoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoPrescricoesEmAbertoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioRelatorioRelacaoPrescricoesEmAberto(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioRelacaoPrescricoesEmAberto");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaUsuarioCadsus.getTxtDescricao().getTextField();
    }
}
