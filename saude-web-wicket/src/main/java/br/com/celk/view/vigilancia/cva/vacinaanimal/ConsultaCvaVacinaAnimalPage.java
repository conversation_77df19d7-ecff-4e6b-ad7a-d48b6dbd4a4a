package br.com.celk.view.vigilancia.cva.vacinaanimal;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.cva.CvaVacinaAnimal;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 925
 */
@Private
public class ConsultaCvaVacinaAnimalPage extends ConsultaPage<CvaVacinaAnimal, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private DropDown dropDownAtivoInativo;
    private Long situacao;

    public ConsultaCvaVacinaAnimalPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        this.dropDownAtivoInativo = new DropDown(CvaVacinaAnimal.PROP_SITUACAO);
        this.dropDownAtivoInativo.addChoice(RepositoryComponentDefault.SIM_LONG, Bundle.getStringApplication("rotulo_ativo"));
        this.dropDownAtivoInativo.addChoice(RepositoryComponentDefault.NAO_LONG, Bundle.getStringApplication("rotulo_inativo"));
        this.dropDownAtivoInativo.addChoice(RepositoryComponentDefault.AMBOS_LONG, Bundle.getStringApplication("rotulo_ambos"));
        form.add(dropDownAtivoInativo);
        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        CvaVacinaAnimal proxy = on(CvaVacinaAnimal.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("descricao"), proxy.getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getSituacaoFormatada()));

        return columns;
    }
    
    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<CvaVacinaAnimal>() {
            @Override
            public void customizeColumn(final CvaVacinaAnimal rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<CvaVacinaAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaVacinaAnimal modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCvaVacinaAnimalPage(rowObject, false, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<CvaVacinaAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaVacinaAnimal modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });

                addAction(ActionType.REATIVAR, rowObject, new IModelAction<CvaVacinaAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaVacinaAnimal modelObject) throws ValidacaoException, DAOException {
                        modelObject.setSituacao(RepositoryComponentDefault.ATIVO);
                        BOFactoryWicket.getBO(CadastroFacade.class).save(rowObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(CvaVacinaAnimal.Situacao.INATIVO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<CvaVacinaAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, CvaVacinaAnimal modelObject) throws ValidacaoException, DAOException {
                        modelObject.setSituacao(RepositoryComponentDefault.INATIVO);
                        BOFactoryWicket.getBO(CadastroFacade.class).save(rowObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(CvaVacinaAnimal.Situacao.ATIVO.value().equals(rowObject.getSituacao()));
            }
        };
    }


    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return CvaVacinaAnimal.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(CvaVacinaAnimal.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CvaVacinaAnimal.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        if (Coalesce.asLong((Long) dropDownAtivoInativo.getComponentValue(), 1L) < 99) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CvaVacinaAnimal.PROP_SITUACAO), Coalesce.asLong((Long) dropDownAtivoInativo.getComponentValue(), 1L)));
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroCvaVacinaAnimalPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaVacinaAnimal");
    }
}
