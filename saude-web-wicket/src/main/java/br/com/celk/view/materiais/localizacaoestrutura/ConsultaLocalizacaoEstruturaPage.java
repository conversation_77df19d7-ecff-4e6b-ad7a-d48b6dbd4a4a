package br.com.celk.view.materiais.localizacaoestrutura;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.localizacaoestrutura.autocomplete.AutoCompleteConsultaLocalizacaoEstrutura;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Localizacao;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 794
 */
@Private
public class ConsultaLocalizacaoEstruturaPage extends ConsultaPage<LocalizacaoEstrutura, List<BuilderQueryCustom.QueryParameter>> {

    private String mascara;
    private String descricao;
    private LocalizacaoEstrutura localizacaoPai;
    private Long visivel;

    public ConsultaLocalizacaoEstruturaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("mascara"));
        form.add(new InputField<String>("descricao"));
        form.add(new AutoCompleteConsultaLocalizacaoEstrutura("localizacaoPai"));
        form.add(DropDownUtil.getSimNaoLongDropDown("visivel", new PropertyModel<Long>(this, "visivel"), true, false));

    }
    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        LocalizacaoEstrutura proxy = on(LocalizacaoEstrutura.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("localizacao"), proxy.getLocalizacao().getDescricao(), proxy.getLocalizacao().getLocalizacaoFormatado()));
        columns.add(createSortableColumn(BundleManager.getString("identificador"), proxy.getMascara()));
        columns.add(createSortableColumn(BundleManager.getString("descricao"), proxy.getDescricaoEstrutura()));
        columns.add(createSortableColumn(BundleManager.getString("visivel"), proxy.getVisivel(), proxy.getVisivelFormatado()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<LocalizacaoEstrutura>() {

            @Override
            public void customizeColumn(LocalizacaoEstrutura rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<LocalizacaoEstrutura>() {
                    @Override
                    public void action(AjaxRequestTarget target, LocalizacaoEstrutura modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLocalizacaoEstruturaPage(modelObject, true));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<LocalizacaoEstrutura>() {
                    @Override
                    public void action(AjaxRequestTarget target, LocalizacaoEstrutura modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(modelObject);
                        getPageableTable().populate(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<LocalizacaoEstrutura>() {
                    @Override
                    public void action(AjaxRequestTarget target, LocalizacaoEstrutura modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLocalizacaoEstruturaPage(modelObject, false));
                    }
                });
                addAction(ActionType.CLONAR, rowObject, new IModelAction<LocalizacaoEstrutura>() {
                    @Override
                    public void action(AjaxRequestTarget target, LocalizacaoEstrutura modelObject) throws ValidacaoException, DAOException {
                        LocalizacaoEstrutura localizacaoEstrutura = VOUtils.cloneObject(modelObject);
                        localizacaoEstrutura.setLocalizacaoEstruturaPai(null);
                        setResponsePage(new CadastroLocalizacaoEstruturaPage(localizacaoEstrutura, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return LocalizacaoEstrutura.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(LocalizacaoEstrutura.class).getProperties(),
                        new HQLProperties(LocalizacaoEstrutura.class, LocalizacaoEstrutura.PROP_LOCALIZACAO_ESTRUTURA_PAI).getProperties(),
                        new String[]{
                                VOUtils.montarPath(LocalizacaoEstrutura.PROP_LOCALIZACAO, Localizacao.PROP_CODIGO),
                                VOUtils.montarPath(LocalizacaoEstrutura.PROP_LOCALIZACAO, Localizacao.PROP_SIGLA),
                                VOUtils.montarPath(LocalizacaoEstrutura.PROP_LOCALIZACAO, Localizacao.PROP_DESCRICAO),
                                VOUtils.montarPath(LocalizacaoEstrutura.PROP_LOCALIZACAO_ESTRUTURA_PAI, LocalizacaoEstrutura.PROP_CODIGO),
                                VOUtils.montarPath(LocalizacaoEstrutura.PROP_LOCALIZACAO_ESTRUTURA_PAI, LocalizacaoEstrutura.PROP_MASCARA),
                                VOUtils.montarPath(LocalizacaoEstrutura.PROP_LOCALIZACAO_ESTRUTURA_PAI, LocalizacaoEstrutura.PROP_LOCALIZACAO, Localizacao.PROP_CODIGO),
                                VOUtils.montarPath(LocalizacaoEstrutura.PROP_LOCALIZACAO_ESTRUTURA_PAI, LocalizacaoEstrutura.PROP_LOCALIZACAO, Localizacao.PROP_DESCRICAO),
                });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(LocalizacaoEstrutura.PROP_CODIGO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LocalizacaoEstrutura.PROP_MASCARA), BuilderQueryCustom.QueryParameter.ILIKE, mascara));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LocalizacaoEstrutura.PROP_DESCRICAO_ESTRUTURA), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LocalizacaoEstrutura.PROP_LOCALIZACAO_ESTRUTURA_PAI), BuilderQueryCustom.QueryParameter.IGUAL, localizacaoPai));

        if(visivel != null){
            parameters.add(new QueryCustom.QueryCustomParameter(LocalizacaoEstrutura.PROP_VISIVEL, visivel));
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroLocalizacaoEstruturaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultarLocalizacaoEstrutura");
    }
}
