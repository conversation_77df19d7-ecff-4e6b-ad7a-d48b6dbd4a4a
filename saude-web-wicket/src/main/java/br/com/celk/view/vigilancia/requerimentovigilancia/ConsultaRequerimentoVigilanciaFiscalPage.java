package br.com.celk.view.vigilancia.requerimentovigilancia;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.boleto.dto.boletocloud.BoletoDTO;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.link.AjaxActionMultiReportLink;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.SelectionPageableTable;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.financeiro.BoletoMultiploVigilanciaPage;
import br.com.celk.view.vigilancia.financeiro.BoletoVigilanciaPage;
import br.com.celk.view.vigilancia.financeiro.DlgEscolherVigilanciaFinanceiro;
import br.com.celk.view.vigilancia.financeiro.dialog.DlgAnexoComprovantePagamentoRequerimento;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.profissional.autocomplete.AutoCompleteConsultaProfissionalVigilancia;
import br.com.celk.view.vigilancia.requerimentos.*;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.*;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.analisehidrossanitariodeclaratorio.RequerimentoHidrossanitarioDeclaratorioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.habitesedeclaratorio.RequerimentoHabiteseDeclaratorioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetoArquitetonicoSanitario.RequerimentoProjetoArquitetonicoParecerTecnicoPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetoArquitetonicoSanitario.RequerimentoProjetoArquitetonicoSanitarioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario.*;
import br.com.celk.view.vigilancia.requerimentos.autorizacaosanitaria.RequerimentoAutorizacaoSanitariaPage;
import br.com.celk.view.vigilancia.requerimentos.baixaveiculo.RequerimentoBaixaVeiculoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoEnderecoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoRazaoSocialPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAtividadeEconomicaPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoRepresentanteLegalPage;
import br.com.celk.view.vigilancia.requerimentos.declaracaoveracidade.RequerimentoDeclaracaoVeracidadePage;
import br.com.celk.view.vigilancia.requerimentos.inspecaosanitaria.RequerimentoInspecaoSanitariaPage;
import br.com.celk.view.vigilancia.requerimentos.inspecaosanitaria.RequerimentoNovaInspecaoSanitariaPage;
import br.com.celk.view.vigilancia.requerimentos.licencatransporte.RequerimentoLicencaTransportePage;
import br.com.celk.view.vigilancia.requerimentos.receituario.RequerimentoReceitaAPage;
import br.com.celk.view.vigilancia.requerimentos.receituario.RequerimentoReceitaTalidomidaPage;
import br.com.celk.view.vigilancia.requerimentovigilancia.customcolumn.ConsultaRequerimentoVigilanciaFiscalColumnPanel;
import br.com.celk.view.vigilancia.requerimentovigilancia.dialog.*;
import br.com.celk.view.vigilancia.responsabilidadetecnica.baixa.RequerimentoBaixaResponsabilidadeTecnicaPage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.inclusao.RequerimentoInclusaoResponsabilidadePage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.nadaconsta.RequerimentoCertidaoNadaConstaPage;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.CadastroAutoIntimacaoPage;
import br.com.celk.view.vigilancia.rotinas.autoinfracao.CadastroAutoInfracaoPage;
import br.com.celk.view.vigilancia.rotinas.automulta.CadastroAutoMultaPage;
import br.com.celk.view.vigilancia.rotinas.autopenalidade.CadastroAutoPenalidadePage;
import br.com.celk.view.vigilancia.rotinas.parecer.CadastroParecerPage;
import br.com.celk.view.vigilancia.rotinas.roteiroinspecao.CadastroRegistroRoteiroInspecaoPage;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.celk.view.vigilancia.tipoenquadramentoprojeto.autocomplete.AutoCompleteConsultaTipoEnquadramentoProjetoMulti;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.VigilanciaAnexosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.UsuarioSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.base.BaseRequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.base.BaseUsuarioSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.TipoEnquadramentoProjeto;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseTipoEnquadramentoProjeto;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;
import org.hamcrest.Matchers;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 29/05/17.
 * Programa - 764
 */
@Private
public class ConsultaRequerimentoVigilanciaFiscalPage extends BasePage {

    public static final String TODOS = "todos";
    public static final String MSG_REQUERIMENTOS_ATUALIZADOS_COM_SUCESSO = "msgRequerimentosAtualizadosComSucesso";
    private Form<RequerimentoVigilanciaDTOParam> form;
    private WebMarkupContainer containerFiltros;
    private SelectionPageableTable<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO> table;
    private QueryPagerProvider<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO, RequerimentoVigilanciaDTOParam> dataProvider;
    private List<UsuarioSetorVigilancia> lstUsuSetor;
    private DropDown ddSituacao;
    private DropDown<Long> ddClassificacaoRisco;
    private DlgCancelamentoFinalizacaoRequerimentoVigilancia dlgCancelamentoRequerimentoVigilancia;
    private DlgCancelamentoFinalizacaoRequerimentoVigilancia dlgFinalizacaoRequerimentoVigilancia;
    private DlgAdicionarFiscaisDataInspecaoConsultaRequerimentoVigilancia dlgAdicionarFiscaisData;
    private DlgAcoesRequerimentoVigilancia dlgAcoesRequerimentoVigilancia;
    private ProcurarButton btnProcurar;
    private br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO requerimentoVigilanciaDTO;
    private DlgAnexoComprovantePagamentoRequerimento dlgAnexoComprovantePagamentoRequerimento;
    private DlgAnexoComprovantePagamentoRequerimento dlgAnexoComprovantePagamentoRequerimentoChoice;
    private DlgEscolherVigilanciaFinanceiro<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO> dlgEscolherVigilanciaFinanceiro;
    private Class classeVoltar;
    private List<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO> requerimentoVigilanciaDTOList = new ArrayList<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>();
    private DlgConfirmacaoSimNao dlgInspecionarRequerimentos;
    private DlgRegistrarVisita dlgRegistrarVisita;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private Long situacao;
    private DlgConfirmacaoSimNao<RequerimentoVigilancia> dlgConfirmacaoSimNaoCancelamento;
    private DlgAdicionarFiscaisRequerimento dlgAdicionarFiscaisRequerimento;
    private CheckBoxLongValue chkTodosEnquadramentos;
    private AutoCompleteConsultaTipoEnquadramentoProjetoMulti accTipoEnquadramentoProjetoMulti;
    private DropDown ddSituacaoAnaliseProjetos;
    private DropDown ddSituacaoParecer;
    private DropDown<Long> ddVisualizartodos;
    private DropDown ddTurno;
    private Button btnPadrao;
    private Button btnTodos;

    public ConsultaRequerimentoVigilanciaFiscalPage() {
        init();
        afterInit();
    }

    public ConsultaRequerimentoVigilanciaFiscalPage(Class classeVoltar) {
        this.classeVoltar = classeVoltar;
        init();
        afterInit();
    }

    public ConsultaRequerimentoVigilanciaFiscalPage(RequerimentoVigilanciaDTOParam param, Class classeVoltar) {
        getForm().getModel().setObject(param);
        this.classeVoltar = classeVoltar;
        init();
        afterInit();
    }

    public ConsultaRequerimentoVigilanciaFiscalPage(Long situacao, Long situacaoOcorrencia, Long situacaoAprovacao, Long situacaoAnaliseProjetos, Class classeVoltar) {
        this.classeVoltar = classeVoltar;
        init();
        afterInit(situacao, situacaoOcorrencia, situacaoAprovacao, situacaoAnaliseProjetos);
    }

    private void init() {
        RequerimentoVigilanciaDTOParam proxy = on(RequerimentoVigilanciaDTOParam.class);

        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoDocumento()), TipoSolicitacao.TipoDocumento.values(), true, "", false, false, true));
        getForm().add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        getForm().add(new AutoCompleteConsultaProfissionalVigilancia(path(proxy.getVigilanciaProfissional())));
        getForm().add(new InputField<String>(path(proxy.getBairro())));
        getForm().add(new InputField<String>(path(proxy.getNome())));
        getForm().add(new InputField<String>(path(proxy.getNumeroProtocolo())));

        containerFiltros = new WebMarkupContainer("containerFiltros");
        containerFiltros.setOutputMarkupId(true);
        containerFiltros.add(new AutoCompleteConsultaSetorVigilancia(path(proxy.getSetorVigilancia())));
        containerFiltros.add(new InputField<String>(path(proxy.getSolicitante())));
        containerFiltros.add(new InputField<String>(path(proxy.getNumeroAutorizacao())));
        containerFiltros.add(new InputField<String>(path(proxy.getCpf())));
        containerFiltros.add(new InputField<String>(path(proxy.getNumeroInscricaoImobiliaria())));

        containerFiltros.add(ddSituacao = DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), RequerimentoVigilancia.Situacao.getDefaultValues(), true, bundle(TODOS)));
        ddSituacao.addAjaxUpdateValue();
        ddSituacao.setOutputMarkupPlaceholderTag(true);

        containerFiltros.add(ddSituacaoAnaliseProjetos = DropDownUtil.getIEnumDropDown(path(proxy.getSituacaoAnaliseProjetos()), RequerimentoVigilancia.SituacaoAnaliseProjetos.getDefaultFilters(), true, bundle(TODOS)));
        ddSituacaoAnaliseProjetos.addAjaxUpdateValue();
        ddSituacaoAnaliseProjetos.setOutputMarkupPlaceholderTag(true);

        containerFiltros.add(ddSituacaoParecer = DropDownUtil.getIEnumDropDown(path(proxy.getSituacaoParecer()), RequerimentoVigilancia.SituacaoOcorrencia.values(), true, bundle(TODOS)));
        ddSituacaoParecer.addAjaxUpdateValue();
        ddSituacaoParecer.setOutputMarkupPlaceholderTag(true);

        containerFiltros.add(new InputField<String>(path(proxy.getNumeroProjetoAprovado())));
        containerFiltros.add(DropDownUtil.getIEnumDropDown(path(proxy.getOrigem()), RequerimentoVigilancia.Origem.values(), true, "", false, false, true));
        containerFiltros.add(ddTurno = DropDownUtil.getEnumDropDown(path(proxy.getTurno()), RequerimentoVigilancia.Turno.values(), bundle(TODOS)));

        containerFiltros.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        containerFiltros.add(ddClassificacaoRisco = DropDownUtil.getIEnumDropDown(path(proxy.getClassificacaoRisco()), RequerimentosProjetosEnums.ClassificacaoRisco.values(), true, bundle(TODOS)));
        ddClassificacaoRisco.addAjaxUpdateValue();
        ddClassificacaoRisco.setOutputMarkupPlaceholderTag(true);

        containerFiltros.add(accTipoEnquadramentoProjetoMulti = new AutoCompleteConsultaTipoEnquadramentoProjetoMulti("tipoEnquadramentoProjetoList"));
        accTipoEnquadramentoProjetoMulti.setOutputMarkupId(true);
        accTipoEnquadramentoProjetoMulti.setOutputMarkupPlaceholderTag(true);

        containerFiltros.add(chkTodosEnquadramentos = new CheckBoxLongValue(path(proxy.getTodosEnquadramentos())));
        chkTodosEnquadramentos.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                accTipoEnquadramentoProjetoMulti.limpar(target);

                if (RepositoryComponentDefault.SIM_LONG.equals(form.getModel().getObject().getTodosEnquadramentos())) {
                    List<TipoEnquadramentoProjeto> tipoEnquadramentoProjetoList = LoadManager.getInstance(TipoEnquadramentoProjeto.class)
                            .addProperties(new HQLProperties(TipoEnquadramentoProjeto.class).getProperties())
                            .addSorter(new QueryCustom.QueryCustomSorter(BaseTipoEnquadramentoProjeto.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                            .start().getList();

                    if (br.com.celk.util.CollectionUtils.isNotNullEmpty(tipoEnquadramentoProjetoList)) {
                        form.getModel().getObject().setTipoEnquadramentoProjetoList(tipoEnquadramentoProjetoList);
                    }
                } else {
                    form.getModel().getObject().setTipoEnquadramentoProjetoList(null);
                }

                target.add(accTipoEnquadramentoProjetoMulti);
            }
        });
        chkTodosEnquadramentos.setOutputMarkupPlaceholderTag(true);
        chkTodosEnquadramentos.setOutputMarkupId(true);
        chkTodosEnquadramentos.addAjaxUpdateValue();

        WebMarkupContainer containerVisualizarTodosRequerimentos = new WebMarkupContainer("containerVisualizarTodosRequerimentos");
        containerVisualizarTodosRequerimentos.setOutputMarkupId(true);
        containerVisualizarTodosRequerimentos.add(ddVisualizartodos = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVisualizarTodosRequerimentos())));
        if (!isActionPermitted(Permissions.VISUALIZAR_TODOS)) {
            containerVisualizarTodosRequerimentos.setVisible(false);
        }

        containerFiltros.add(containerVisualizarTodosRequerimentos);

        getForm().add(containerFiltros);
        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());

        createTable();
        createButtons();
        add(getForm());
    }

    private void afterInit(Long situacao, Long situacaoOcorrencia, Long situacaoAprovacao, Long situacaoAnaliseProjetos) {
        if (situacao != null) {
            this.situacao = situacao;
            getForm().getModel().getObject().setSituacao(situacao);
            ddSituacao.setComponentValue(situacao);
        }
        if (situacaoOcorrencia != null) {
            getForm().getModel().getObject().setSituacaoOcorrencia(situacaoOcorrencia);
            ddSituacaoParecer.setComponentValue(situacaoOcorrencia);
        }
        if (situacaoAprovacao != null) {
            getForm().getModel().getObject().setSituacaoAprovacao(situacaoAprovacao);
        }
        if (situacaoAnaliseProjetos != null) {
            getForm().getModel().getObject().setSituacaoAnaliseProjetos(situacaoAnaliseProjetos);
            ddSituacaoAnaliseProjetos.setComponentValue(situacaoAnaliseProjetos);
        }

        afterInit();
    }

    private void afterInit() {
        carregaLstUsuarioSetor();
        btnProcurar.procurar();
    }

    private void createTable() {
        getForm().add(table = new SelectionPageableTable("table", getColumns(), getDataProvider(), 10));
        table.addSelectionAction(new ISelectionAction<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO object) {
                if (!isPermitidoManipularRequerimentoOutroFiscal(object)) {
                    MessageUtil.modalWarn(target, table, new ValidacaoException(bundle("semPermissaoParaManipularRequerimento")));
                    return;
                }
                table.clearSelection(target);
                table.update(target);
                requerimentoVigilanciaDTO = object;
                initDlgAcoesRequerimentoVigilancia(target, object);
            }
        });
        table.populate();
    }

    private void createButtons() {
        getForm().add(btnPadrao = new AbstractAjaxButton("btnPadrao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                setFiltrosPadrao(target);

            }
        });
        btnPadrao.setEnabled(true);

        getForm().add(btnTodos = new AbstractAjaxButton("btnTodos") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                setFiltrosTodos(target);

            }
        });
        btnTodos.setEnabled(true);

        getForm().add(btnProcurar = new ProcurarButton("btnProcurar", table) {
            @Override
            public void antesProcurar(AjaxRequestTarget target) throws ValidacaoException {
                requerimentoVigilanciaDTOList.clear();
            }

            @Override
            public RequerimentoVigilanciaDTOParam getParam() {
                return (RequerimentoVigilanciaDTOParam) getForm().getModel().getObject();
            }
        });

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar();
            }
        }.setDefaultFormProcessing(false).setVisible(classeVoltar != null));

        getForm().add(new AbstractAjaxButton("btnNovo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new RequerimentosPage(ConsultaRequerimentoVigilanciaFiscalPage.class));
            }

            @Override
            public boolean isVisible() {
                return new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), RequerimentosPage.class.getName());
            }
        });

        getForm().add(new AbstractAjaxButton("btnEmInspecao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarRequerimentosSelecionados(target, true)) {
                    inspecionarRequerimentos(target);
                }
            }
        });

        getForm().add(new AbstractAjaxButton("btnEmAnalise") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarRequerimentosSelecionados(target, false)) {
                    analisarRequerimentos(target);
                }
            }
        }).setVisible(isActionPermitted(Permissions.ENCAMINHAR));

        getForm().add(new AbstractAjaxButton("btnEmReinspecao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarRequerimentosSelecionados(target, true)) {
                    reinspecionarRequerimentos(target);
                }
            }
        });

        getForm().add(new AbstractAjaxButton("btnImprimirListaInspecao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarRequerimentosSelecionados(target, true)) {
                    imprimirListaInspecao(target);
                }
            }
        });
    }


    private void setFiltrosTodos(AjaxRequestTarget target) {
        ComponentUtils.limparForm(getForm(), target);
        ddVisualizartodos.setComponentValue(RepositoryComponentDefault.SIM_LONG);
        ddSituacao.limpar(target);
        ddTurno.limpar(target);
        ddClassificacaoRisco.limpar(target);
        ddSituacaoAnaliseProjetos.limpar(target);
        ddSituacaoParecer.limpar(target);
        chkTodosEnquadramentos.setComponentValue(RepositoryComponentDefault.NAO_LONG);
        accTipoEnquadramentoProjetoMulti.limpar(target);
        target.add(ddVisualizartodos, ddSituacao, ddTurno, ddClassificacaoRisco,
                ddSituacaoAnaliseProjetos, ddSituacaoParecer, chkTodosEnquadramentos,
                accTipoEnquadramentoProjetoMulti, containerFiltros
        );
    }

    private void setFiltrosPadrao(AjaxRequestTarget target) {
        ComponentUtils.limparForm(getForm(), target);
        ddVisualizartodos.setComponentValue(RepositoryComponentDefault.NAO_LONG);
        ddSituacao.setComponentValue(RequerimentoVigilancia.Situacao.FILTRO_PERSONALIZADO_FISCAL.value());
        ddTurno.limpar(target);
        ddClassificacaoRisco.limpar(target);
        ddSituacaoAnaliseProjetos.limpar(target);
        ddSituacaoParecer.limpar(target);
        chkTodosEnquadramentos.setComponentValue(RepositoryComponentDefault.NAO_LONG);
        accTipoEnquadramentoProjetoMulti.limpar(target);
        target.add(ddVisualizartodos, ddSituacao, ddTurno, ddClassificacaoRisco, ddSituacaoAnaliseProjetos,
                ddSituacaoParecer, chkTodosEnquadramentos, accTipoEnquadramentoProjetoMulti, containerFiltros);
    }

    private boolean validarRequerimentosSelecionados(AjaxRequestTarget target, boolean validarTipoSolicitacaoInspeciona) {
        try {
            if (CollectionUtils.isEmpty(requerimentoVigilanciaDTOList)) {
                throw new ValidacaoException(bundle("msgSelecionePeloMenosUmRequerimentoParaContinuar"));
            } else if (validarTipoSolicitacaoInspeciona) {
                Group<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO> tipoSolicitacaoGroup = Lambda.group(requerimentoVigilanciaDTOList, by(on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getTipoSolicitacao()));

                RequerimentoVigilancia rv;
                for (Group<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO> group : tipoSolicitacaoGroup.subgroups()) {
                    rv = group.findAll().get(0).getRequerimentoVigilancia();
                    if (RepositoryComponentDefault.NAO_LONG.equals(rv.getTipoSolicitacao().getFlagInspeciona())) {
                        throw new ValidacaoException(bundle("msgRequerimentoSelecionadoEstaConfiguradoParaNaoInspecionarTipoSolicitaçãoXProtocoloX", rv.getTipoSolicitacao().getDescricao(), rv.getProtocoloFormatado()));
                    }
                }
            }

        } catch (ValidacaoException ex) {
            warn(target, ex.getMessage());
            return false;
        }

        return true;
    }

    private void reinspecionarRequerimentos(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        List<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO> requerimentosDiferenteInspecaoList = Lambda.select(requerimentoVigilanciaDTOList,
                Lambda.having(Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getSituacao(), Matchers.not(RequerimentoVigilancia.Situacao.EM_INSPECAO.value())));

        if (!CollectionUtils.isNotNullEmpty(requerimentosDiferenteInspecaoList)) {
            BOFactoryWicket.getBO(VigilanciaFacade.class).reinspecionarMultiplosRequerimentosVigilancia(requerimentoVigilanciaDTOList);
            requerimentoVigilanciaDTOList.clear();
            table.update(target);
            info(target, bundle(MSG_REQUERIMENTOS_ATUALIZADOS_COM_SUCESSO));
            updateNotificationPanel(target);
            updateNotification(target);
            if (classeVoltar != null && this.situacao != null) {
                setResponsePage(classeVoltar);
            }
        } else {
            warn(target, bundle("msgParaReinspecionarRequerimentoMesmoDeveEstarEmInspecao"));
        }
    }

    private void analisarRequerimentos(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        List<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO> requerimentosDiferentePendenteList = Lambda.select(requerimentoVigilanciaDTOList,
                Lambda.having(Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getSituacao(), Matchers.not(RequerimentoVigilancia.Situacao.PENDENTE.value())));
        requerimentosDiferentePendenteList = Lambda.select(requerimentosDiferentePendenteList,
                Lambda.having(Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getTipoDocumento(), Matchers.not(TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO.value())));

        requerimentosDiferentePendenteList = Lambda.select(requerimentosDiferentePendenteList,
                Lambda.having(Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getTipoDocumento(), Matchers.not(TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value())));

        if (!CollectionUtils.isNotNullEmpty(requerimentosDiferentePendenteList)) {
            for (br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO vigilanciaDTO : requerimentoVigilanciaDTOList) {
                if (!VigilanciaHelper.enableAnalysisRequerimento(vigilanciaDTO.getRequerimentoVigilancia())) {
                    warn(target, bundle("msgRequerimentoXNaoPossuiProcessoAnalise", vigilanciaDTO.getRequerimentoVigilancia().getProtocoloFormatado()));
                    return;
                }

                if (VigilanciaHelper.analisarDocumentosExterno(vigilanciaDTO.getRequerimentoVigilancia())) {
                    warn(target, "Para Requerimentos externos deve-se analisar a documentação de forma individual");
                    return;
                }
            }
            List<RequerimentoVigilancia> retornoRequerimentoVigilanciaList = BOFactoryWicket.getBO(VigilanciaFacade.class).analisarMultiplosRequerimentosVigilancia(requerimentoVigilanciaDTOList);

            requerimentoVigilanciaDTOList.clear();
            table.update(target);
            info(target, bundle(MSG_REQUERIMENTOS_ATUALIZADOS_COM_SUCESSO));
            updateNotificationPanel(target);
            updateNotification(target);
            if (classeVoltar != null && this.situacao != null) {
                setResponsePage(classeVoltar);
            }
        } else {
            warn(target, bundle("msgParaAnalisarRequerimentoMesmoDeveEstarPendente"));
        }
    }


    private void inspecionarRequerimentos(AjaxRequestTarget target) {
        List<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO> requerimentosDiferenteAnaliseList = Lambda.select(requerimentoVigilanciaDTOList,
                Lambda.having(Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getSituacao(), Matchers.not(RequerimentoVigilancia.Situacao.ANALISE.value())));


        if (CollectionUtils.isNotNullEmpty(requerimentosDiferenteAnaliseList)) {
            if (requerimentoVigilanciaDTOList.size() == 1 && requerimentosDiferenteAnaliseList.size() == 1 && RequerimentoVigilancia.Situacao.EM_INSPECAO.value().equals(requerimentosDiferenteAnaliseList.get(0).getRequerimentoVigilancia().getSituacao())) {
                //editar uma inspeçao
                initDlgInspecionarRequerimentosConfirmar(target);

            } else if (requerimentosDiferenteAnaliseList.size() > 1 && Lambda.exists(requerimentosDiferenteAnaliseList, Lambda.having(on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getSituacao(), Matchers.equalTo(RequerimentoVigilancia.Situacao.EM_INSPECAO.value())))) {
                warn(target, bundle("msgSelecioneApenasUmRegistroEmInspecaoParaEditar"));
                return;
            } else {
                warn(target, bundle("msgSelecioneApenasRegistrosEmInspecaoEmAnalise"));
                return;
            }
            warn(target, bundle("msgParaInspecionarRequerimentoMesmoDeveEstarEmAnaliseProtocoloRequerimentoQueNaoEstaEmAnaliseX", requerimentosDiferenteAnaliseList.get(0).getRequerimentoVigilancia().getProtocoloFormatado()));
        } else {
            initDlgInspecionarRequerimentosConfirmar(target);
        }
    }

    private void initDlgInspecionarRequerimentosConfirmar(AjaxRequestTarget target) {
        if (dlgInspecionarRequerimentos == null) {
            addModal(target, dlgInspecionarRequerimentos = new DlgConfirmacaoSimNao(newModalId(), Bundle.getStringApplication("msg_confirma_inspecao_requerimentos_selecionados")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    initDlgInspecionarRequerimentosFiscaisData(target);
                }
            });
        }
        dlgInspecionarRequerimentos.setObject(Lambda.extract(requerimentoVigilanciaDTOList, Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia()));
        dlgInspecionarRequerimentos.show(target);
    }


    private void initDlgInspecionarRequerimentosFiscaisData(AjaxRequestTarget target) {
        addModal(target, dlgAdicionarFiscaisData = new DlgAdicionarFiscaisDataInspecaoConsultaRequerimentoVigilancia(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, CadastroFiscaisDataInspecaoRequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                List<RequerimentoVigilancia> retornoRequerimentoVigilanciaList = BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarSituacaoRequerimentoVigilanciaAdicionarFiscaisDataInspecao(dto);

                requerimentoVigilanciaDTOList.clear();
                table.update(target);
                MessageUtil.info(target, this, bundle(MSG_REQUERIMENTOS_ATUALIZADOS_COM_SUCESSO));
                updateNotificationPanel(target);
                updateNotification(target);
                if (classeVoltar != null && situacao != null) {
                    setResponsePage(classeVoltar);
                }
            }
        });
        dlgAdicionarFiscaisData.show(target, Lambda.extract(requerimentoVigilanciaDTOList, Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia()));
    }

    private void imprimirListaInspecao(AjaxRequestTarget target) {
        List<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO> requerimentosDiferenteAnaliseList = Lambda.select(requerimentoVigilanciaDTOList,
                Lambda.having(Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getSituacao(), Matchers.not(RequerimentoVigilancia.Situacao.EM_INSPECAO.value()))
                        .and(Lambda.having(Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getSituacao(), Matchers.not(RequerimentoVigilancia.Situacao.EM_REINSPECAO.value()))));

        if (CollectionUtils.isNotNullEmpty(requerimentosDiferenteAnaliseList)) {
            warn(target, bundle("msgParaImprimirListaInspecaoRequerimentoDeveEstarEmInspecaoProtocoloRequerimentoQueNaoEstaEmInspecaoX", requerimentosDiferenteAnaliseList.get(0).getRequerimentoVigilancia().getProtocoloFormatado()));
        } else {
            try {
                RelatorioListaInspecaoDTOParam param = new RelatorioListaInspecaoDTOParam();
                param.setCodigoRequerimentoList(Lambda.extract(requerimentoVigilanciaDTOList, Lambda.on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class).getRequerimentoVigilancia().getCodigo()));

                DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioListaInspecao(param);
                File file = File.createTempFile("listaInspecao", ".pdf");
                JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
                IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(file));
                ajaxPreviewBlank.initiate(target, "listaInspecao.pdf", resourceStream);
            } catch (IOException | JRException | ReportException e) {
                Loggable.log.error(e);
            }
        }
    }

    private void updateNotification(AjaxRequestTarget target) {
        INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(ConsultaRequerimentoVigilanciaFiscalPage.this);
        if (findNotificationPanel != null) {
            getSession().getFeedbackMessages().clear();
            if (target != null) {
                findNotificationPanel.updateNotificationPanel(target);
            }
        }
    }

    private void voltar() {
        setResponsePage(classeVoltar);
    }

    private Form<RequerimentoVigilanciaDTOParam> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new RequerimentoVigilanciaDTOParam()));
        }
        return form;
    }

    private List<ISortableColumn<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>> getColumns() {
        List columns = new ArrayList<>();
        br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO proxy = on(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO.class);

        columns.add(getSelectionActionColumn());
        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("dataRequerimento"), proxy.getRequerimentoVigilancia().getDataRequerimento()));
        columns.add(createSortableColumn(BundleManager.getString("dataInspecao"), proxy.getRequerimentoVigilancia().getDataInspecao()));
        columns.add(createSortableColumn(BundleManager.getString("tipoInspecao"), proxy.getDescricaoProximaInspecao()));
        columns.add(createSortableColumn(BundleManager.getString("protocolo"), proxy.getRequerimentoVigilancia().getProtocolo(), proxy.getRequerimentoVigilancia().getProtocoloFormatado()));
        columns.add(createColumn(BundleManager.getString("estabelecimentoPessoaProfissional"), proxy.getRequerimentoVigilancia().getEstabelecimentoPessoaProfissionalFormatado()));
        columns.add(createColumn(BundleManager.getString("funcionamento"), proxy.getRequerimentoVigilancia().getEstabelecimento().getDescricaoFuncionamento()));
        columns.add(createSortableColumn(BundleManager.getString("solicitacao"), proxy.getRequerimentoVigilancia().getTipoDocumento(), proxy.getRequerimentoVigilancia().getDescricaoTipoDocumentoComPlaca()));
        columns.add(createColumn(BundleManager.getString("setorResponsavel"), proxy.getDescricaoSetorResponsavel()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getRequerimentoVigilancia().getSituacao(), proxy.getRequerimentoVigilancia().getDescricaoSituacao()));
        columns.add(createSortableColumn(BundleManager.getString("origem"), proxy.getRequerimentoVigilancia().getOrigem(), proxy.getRequerimentoVigilancia().getDescricaoOrigem()));
        return columns;
    }

    private IColumn getSelectionActionColumn() {
        return new CustomColumn<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>() {

            @Override
            public Component getComponent(String componentId, final br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO rowObject) {
                return new ConsultaRequerimentoVigilanciaFiscalColumnPanel(componentId) {
                    @Override
                    public void onSelectionAction(AjaxRequestTarget target, boolean selectionAction) {
                        if (selectionAction) {
                            requerimentoVigilanciaDTOList.add(rowObject);
                        } else {
                            requerimentoVigilanciaDTOList.remove(rowObject);
                        }
                    }

                    @Override
                    public boolean isEnabled() {
                        return isPermitidoManipularRequerimentoOutroFiscal(rowObject);
                    }
                };
            }
        };
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>() {
            @Override
            public void customizeColumn(final br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO rowObject) {

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO modelObject) throws ValidacaoException, DAOException {
                        responsePage(modelObject.getRequerimentoVigilancia(), false);
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new AjaxActionMultiReportLink<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>() {
                    @Override
                    public List<DataReport> getDataReports(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO modelObject) throws ValidacaoException, DAOException, ReportException {
                        br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO rv = rowObject;
                        Usuario usuarioAlteracao = rv.getRequerimentoVigilancia().getUsuarioAlteracao();
                        if (usuarioAlteracao != null && usuarioAlteracao.getNome() != null) {
                            usuarioAlteracao.setNome(StringUtils.capitalize(usuarioAlteracao.getNome()));
                            rv.getRequerimentoVigilancia().setUsuarioAlteracao(usuarioAlteracao);
                        }
                        return responseImpression(target, rv.getRequerimentoVigilancia());
                    }
                }).setVisible(isVisibleImpressao(rowObject));

                addAction(ActionType.CONJUNTO, rowObject, new IModelAction<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO modelObject) {
                        if (!isPermitidoManipularRequerimentoOutroFiscal(modelObject)) {
                            MessageUtil.modalWarn(target, table, new ValidacaoException(bundle("semPermissaoParaManipularRequerimento")));
                            return;
                        }
                        requerimentoVigilanciaDTO = modelObject;
                        initDlgAcoesRequerimentoVigilancia(target, modelObject);
                    }
                }).setIcon(Icon.ROUND_PLUS).setTitleBundleKey("maisAcoes");
            }
        };
    }

    private boolean isPermitidoManipularRequerimentoOutroFiscal(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO rowObject) {
        try {
            return VigilanciaPageHelper.isPermitidoManipularRequerimentoOutroFiscal(rowObject.getRequerimentoVigilancia(), ConsultaRequerimentoVigilanciaFiscalPage.class);
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }
        return true;
    }

    private boolean isVisibleImpressao(br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO rowObject) {
        return isActionPermitted(Permissions.IMPRIMIR)
                && (enablePrint(rowObject.getRequerimentoVigilancia())
                && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.EM_INSPECAO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.EM_REINSPECAO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
        )
                || habilitarImprimirDiferenteCanceladoIndeferido(rowObject.getRequerimentoVigilancia())
        );
    }

    private boolean enablePrint(RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            List<Long> tiposDocumentos = Arrays.asList(
                    TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_PARTICIPANTE_EVENTO.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_CADASTRO_EVENTO.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value(),
                    TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value(),
                    TipoSolicitacao.TipoDocumento.ABERTURA_LIVRO_CONTROLE.value(),
                    TipoSolicitacao.TipoDocumento.FECHAMENTO_LIVRO_CONTROLE.value(),
                    TipoSolicitacao.TipoDocumento.CERTIDAO_NADA_CONSTA.value(),
                    TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_A.value(),
                    TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_B.value(),
                    TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_TALIDOMIDA.value(),
                    TipoSolicitacao.TipoDocumento.VACINACAO_EXTRAMURO.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETOS.value(),
                    TipoSolicitacao.TipoDocumento.PROJETO_BASICO_ARQUITETURA.value(),
                    TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value(),
                    TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.HABITE_SE_DECLARATORIO.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_VEICULO.value(),
                    TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_ESTABELECIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value(),
                    TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ENDERECO.value(),
                    TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.REVALIDACAO_LICENCA_SANITARIA.value()
            );
            if (tiposDocumentos.contains(td)) {
                return true;
            }
            return false;
        }
        return false;
    }

    private boolean habilitarImprimirDiferenteCanceladoIndeferido(RequerimentoVigilancia rv) {
        return enablePrintDifferentCanceledRejected(rv)
                && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao()) && !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao());
    }

    private boolean enablePrintDifferentCanceledRejected(RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            List<Long> tiposDocumentos = Arrays.asList(
                    TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value(),
                    TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_PRODUTOS.value(),
                    TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_ISENCAO_TAXAS.value(),
                    TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_OUTROS.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_RESPONSABILIDADE_TECNICA.value(),
                    TipoSolicitacao.TipoDocumento.ENTRADA_RESPONSABILIDADE_TECNICA.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ENDERECO.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_RAZAO_SOCIAL.value(),
                    TipoSolicitacao.TipoDocumento.DECLARACAO_CARTORIO.value(),
                    TipoSolicitacao.TipoDocumento.PRORROGACAO_PRAZO.value(),
                    TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_AFE_ANVISA.value(),
                    TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_COMUM.value(),
                    TipoSolicitacao.TipoDocumento.PEDIDO_DOCUMENTO.value(),
                    TipoSolicitacao.TipoDocumento.VACINACAO_EXTRAMURO.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETOS.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value(),
                    TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.HABITE_SE_DECLARATORIO.value(),
                    TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_ESTABELECIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_ESTABELECIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ENDERECO.value()
            );
            if (tiposDocumentos.contains(td)) {
                return true;
            }
            return false;
        }
        return false;
    }

    private void responsePage(RequerimentoVigilancia rv, boolean edicao) {
        Long td = rv.getTipoDocumento();
        rv.getTipoSolicitacao().setTipoDocumento(rv.getTipoDocumento());
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case ANALISE_PROJETOS:
                    setResponsePage(new RequerimentoAnaliseProjetosPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)));
                    break;
                case PROJETO_BASICO_ARQUITETURA:
                    setResponsePage(new RequerimentoAnaliseProjetosPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)));
                    break;
                case EXUMACAO_RESTOS_MORTAIS:
                    setResponsePage(new RequerimentoExumacaoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ALVARA_PARTICIPANTE_EVENTO:
                    setResponsePage(new RequerimentoEventoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                    break;
                case ALVARA_CADASTRO_EVENTO:
                    setResponsePage(new RequerimentoCadastroEventoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                    break;
                case ALVARA_INICIAL:
                    setResponsePage(new RequerimentoAlvaraInicialPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                    break;
                case REVALIDACAO_LICENCA_SANITARIA:
                case ALVARA_REVALIDACAO:
                    setResponsePage(new RequerimentoRevalidacaoAlvaraPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                    break;
                case LICENCA_TRANSPORTE:
                    setResponsePage(new RequerimentoLicencaTransportePage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case AUTORIZACAO_SANITARIA:
                    setResponsePage(new RequerimentoAutorizacaoSanitariaPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ABERTURA_LIVRO_CONTROLE:
                    setResponsePage(new RequerimentoLivroAberturaPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case FECHAMENTO_LIVRO_CONTROLE:
                    setResponsePage(new RequerimentoLivroFechamentoPage(rv, false, false, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case PRORROGACAO_PRAZO:
                    responsePageProrrogacaoPrazo(rv, edicao);
                    break;
                case INSPECAO_SANITARIA_AFE_ANVISA:
                    if(VigilanciaHelper.usarRequerimentoNovaInspecaoSanitaria()){
                        setResponsePage(new RequerimentoNovaInspecaoSanitariaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                    }else{
                        setResponsePage(new RequerimentoInspecaoSanitariaPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, true, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                    }
                    break;
                case INSPECAO_SANITARIA_COMUM:
                    setResponsePage(new RequerimentoInspecaoSanitariaPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, false, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                    break;
                case CERTIDAO_NADA_CONSTA:
                    setResponsePage(new RequerimentoCertidaoNadaConstaPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case DECLARACAO_VISA_PRODUTOS:
                    setResponsePage(new RequerimentoDeclaracaoVisaProdutosPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case DECLARACAO_VISA_ISENCAO_TAXAS:
                    setResponsePage(new RequerimentoDeclaracaoVisaIsencaoTaxasPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case DECLARACAO_VISA_OUTROS:
                    setResponsePage(new RequerimentoDeclaracaoVisaOutrosPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case DECLARACAO_CARTORIO:
                    setResponsePage(new RequerimentoDeclaracaoVeracidadePage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ALTERACAO_RESPONSABILIDADE_LEGAL:
                    setResponsePage(new RequerimentoRepresentanteLegalPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ALTERACAO_ATIVIDADE_ECONOMICA:
                    setResponsePage(new RequerimentoAtividadeEconomicaPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case REQUISICAO_RECEITUARIO_A:
                    setResponsePage(new RequerimentoReceitaAPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case REQUISICAO_RECEITUARIO_TALIDOMIDA:
                    setResponsePage(new RequerimentoReceitaTalidomidaPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case REQUISICAO_RECEITUARIO_B:
                    setResponsePage(new RequerimentoReceitaBPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case BAIXA_RESPONSABILIDADE_TECNICA:
                    setResponsePage(new RequerimentoBaixaResponsabilidadeTecnicaPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ENTRADA_RESPONSABILIDADE_TECNICA:
                    setResponsePage(new RequerimentoInclusaoResponsabilidadePage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ALTERACAO_ENDERECO:
                    setResponsePage(new RequerimentoAlteracaoEnderecoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ALTERACAO_RAZAO_SOCIAL:
                    setResponsePage(new RequerimentoAlteracaoRazaoSocialPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case PEDIDO_DOCUMENTO:
                    setResponsePage(new RequerimentoPedidoDocumentoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case VACINACAO_EXTRAMURO:
                    setResponsePage(new RequerimentoVacinacaoExtramuroPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case BAIXA_ESTABELECIMENTO:
                    setResponsePage(new RequerimentoBaixaEstabelecimentoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case HABITE_SE_DECLARATORIO:
                    setResponsePage(new RequerimentoHabiteseDeclaratorioPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)));
                    break;
                case VISTORIA_LAUDO_CONFORMIDADE_PBA:
                    setResponsePage(new RequerimentoVistoriaProjetoBasicoArquiteturaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)));
                    break;
                case TREINAMENTOS_ALIMENTO:
                    setResponsePage(new RequerimentoTreinamentoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case DENUNCIA_RECLAMACAO:
                    setResponsePage(new RequerimentoDenunciaReclamacaoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case RESTITUICAO_TAXA:
                    setResponsePage(new RequerimentoRestituicaoTaxaPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO:
                    setResponsePage(new RequerimentoHidrossanitarioDeclaratorioPage(rv, (edicao && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case BAIXA_VEICULO:
                    setResponsePage(new RequerimentoBaixaVeiculoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ANALISE_PROJETO_HIDROSSANITARIO:
                    setResponsePage(new RequerimentoProjetoHidrossanitarioPadraoPage(rv, (edicao && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case VISTORIA_HABITESE_SANITARIO:
                    setResponsePage(new RequerimentoHabitesePadraoPage(rv, (edicao && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case PROJETO_ARQUITETONICO_SANITARIO:
                    setResponsePage(new RequerimentoProjetoArquitetonicoSanitarioPage(rv, (edicao && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case LICENCA_SANITARIA:
                    setResponsePage(new RequerimentoAlvaraInicialPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                    break;
                default:
                    break;
            }
        }
    }

    private void responsePageProrrogacaoPrazo(final RequerimentoVigilancia rv, final boolean edicao) {
        RequerimentoProrrogacaoPrazoPage page = new RequerimentoProrrogacaoPrazoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class);
        setResponsePage(page);
    }

    private void initDlgImpressaoConsultaRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        DlgImpressaoConsultaRequerimentoVigilancia dlgImpressaoConsultaRequerimentoVigilancia = new DlgImpressaoConsultaRequerimentoVigilancia(newModalId(), true) {
            @Override
            public DataReport onImprimir(RequerimentoVigilancia requerimentoVigilancia) {
                return null;
            }
        };
        addModal(target, dlgImpressaoConsultaRequerimentoVigilancia);
        dlgImpressaoConsultaRequerimentoVigilancia.show(target, rv);
    }

    private void initDlgAcoesRequerimentoVigilancia(AjaxRequestTarget target, final br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO requerimentoVigilanciaDTO) {
        if (dlgAcoesRequerimentoVigilancia == null) {
            addModal(target, dlgAcoesRequerimentoVigilancia = new DlgAcoesRequerimentoVigilancia(newModalId(), true) {
                @Override
                public void onEditar(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    responsePage(dto.getRequerimentoVigilancia(), true);
                }

                @Override
                public void onAprovar(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    RequerimentoVigilanciaAprovacaoDTO requerimentoVigilanciaAprovacaoDTO = new RequerimentoVigilanciaAprovacaoDTO();
                    requerimentoVigilanciaAprovacaoDTO.setRequerimentoVigilancia(dto.getRequerimentoVigilancia());
                    List<VigilanciaFinanceiro> vigilanciaFinanceiroList = VigilanciaHelper.getVigilanciaFinanceiroList(dto.getRequerimentoVigilancia());
                    requerimentoVigilanciaAprovacaoDTO.setVigilanciaFinanceiroList(vigilanciaFinanceiroList);
                    setResponsePage(new RequerimentoVigilanciaAprovacaoPage(requerimentoVigilanciaAprovacaoDTO, getForm().getModel().getObject(), classeVoltar));
                }

                @Override
                public void onCancelar(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    initDlgCancelamentoRequerimentoVigilancia(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onEmAnalise(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto, String justificativa) throws ValidacaoException, DAOException {
                    analisarRequerimento(target, dto, justificativa);
                }

                @Override
                public void onReverterSituacao(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).reverterSituacaoRequerimentoVigilancia(dto);
                    table.update(target);
                }

                @Override
                public void onReverterFinalizacao(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).reverterSituacaoRequerimentoVigilancia(dto);
                    table.update(target);
                }

                @Override
                public void onLancarOcorrencia(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    initDlgOcorrenciaConsultaRequerimentoVigilancia(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onFinalizar(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    responseConfirm(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onEntregaDocumento(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    initDlgEntregaDocumentoRequerimentoVigilancia(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onConformidadeTecnica(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    redirecionarConformidade(dto.getRequerimentoVigilancia());
                }

                @Override
                public void onEmitirBoleto(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    redirecionarBoletoPage(target, dto);
                }

                @Override
                public void onComprovantePagamento(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    redirecionarComprovantePagamento(target, dto);
                }

                @Override
                public void onInformarFiscais(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    onEncaminharFiscais(target, dto);
                    table.update(target);
                }

                @Override
                public void onRoteiroInspecao(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    setResponsePage(new CadastroRegistroRoteiroInspecaoPage(dto.getRequerimentoVigilancia()) {
                        @Override
                        public Class getResponsePage() {
                            return ConsultaRequerimentoVigilanciaFiscalPage.class;
                        }
                    });
                }

                @Override
                public void onVoltar(AjaxRequestTarget target) {
                    requerimentoVigilanciaDTOList.clear();
                    table.update(target);
                }

                @Override
                public void onRelatorioInspecoes(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    setResponsePage(new CadastroRelatorioInspecaoPage(dto.getRequerimentoVigilancia(), ConsultaRequerimentoVigilanciaFiscalPage.class));
                }

                @Override
                public void onRegistroVisitas(AjaxRequestTarget target, final br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    addModal(target, dlgRegistrarVisita = new DlgRegistrarVisita(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, List<Profissional> profissionalList, Date data, String observacao) throws ValidacaoException, DAOException {
                            if (TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value().equals(requerimentoVigilanciaDTO.getRequerimentoVigilancia().getTipoDocumento())) {
                                BOFactoryWicket.getBO(VigilanciaFacade.class).gerarFaturamentoInspecaoVeiculos(dto.getRequerimentoVigilancia().getEstabelecimento(), profissionalList, data, observacao, requerimentoVigilanciaDTO.getRequerimentoVigilancia());
                            } else {
                                BOFactoryWicket.getBO(VigilanciaFacade.class).gerarFaturamentoInspecao(dto.getRequerimentoVigilancia().getEstabelecimento(), profissionalList, data, observacao, requerimentoVigilanciaDTO.getRequerimentoVigilancia());
                            }

                            MessageUtil.info(target, this, "Inspeção gerada com sucesso!");
                            updateNotificationPanel(target);
                            updateNotification(target);
                        }
                    });
                    dlgRegistrarVisita.show(target);
                }

                @Override
                public void onAutoIntimacao(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    redirecionarAutoIntimacao(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onAutoInfracao(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    redirecionarAutoInfracao(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onParecer(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    redirecionarParecer(dto.getRequerimentoVigilancia());
                }

                @Override
                public void onDocumentosRequerimento(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    imprimirDocumentosRequerimento(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onHistoricoContribuinte(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    imprimirHistorico(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onAutoMulta(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    redirecionarAutoMulta(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onAutoPenalidade(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    redirecionarAutoPenalidade(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onBoletoComplementar(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaFinanceiro vigilanciaFinanceiro = BOFactoryWicket.getBO(VigilanciaFacade.class).gerarBoletoComplementar(dto);
                    if (vigilanciaFinanceiro != null && vigilanciaFinanceiro.getCodigo() != null) {
                        imprimirBoleto(target, vigilanciaFinanceiro);
                    }
                    table.update(target);
                }

            });
        }

        requerimentoVigilanciaDTO.setPermissaoReverterFinalizado(isActionPermitted(Permissions.PERMITIR_REVERTER_FINALIZADO));
        requerimentoVigilanciaDTO.setPermissaoEditarAnaliseProjeto(isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO));
        requerimentoVigilanciaDTO.setPermissaoEditarAlvaras(isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS));
        requerimentoVigilanciaDTO.setPermissaoEditar(isActionPermitted(Permissions.EDITAR));
        requerimentoVigilanciaDTO.setPermissaoImprimir(isVisibleImpressao(requerimentoVigilanciaDTO));
        requerimentoVigilanciaDTO.setPermissaoHistorico(isActionPermitted(Permissions.HISTORICO));
        requerimentoVigilanciaDTO.setPermissaoCancelar(isActionPermitted(Permissions.CANCELAR));
        requerimentoVigilanciaDTO.setPermissaoAnalisar(isActionPermitted(Permissions.ENCAMINHAR));
        requerimentoVigilanciaDTO.setPermissaoReverterSituacao(isActionPermitted(Permissions.ENCAMINHAR));
        requerimentoVigilanciaDTO.setPermissaoLancarOcorrencia(isActionPermitted(Permissions.MANUTENCAO));
        requerimentoVigilanciaDTO.setPermissaoFinalizar(isActionPermitted(Permissions.SALVAR));
        requerimentoVigilanciaDTO.setPermissaoEntregarDocumento(isActionPermitted(Permissions.BAIXA));
        requerimentoVigilanciaDTO.setPermissaoConformidadeTecnica(isActionPermitted(Permissions.CONFORMIDADE_TECNICA));
        requerimentoVigilanciaDTO.setPermissaoRoteiroInspecao(isActionPermitted(Permissions.ROTEIRO_INSPECAO));
        requerimentoVigilanciaDTO.setPermissaoParecer(isActionPermitted(Permissions.PARECER));
        requerimentoVigilanciaDTO.setPermissaoComprovantePagamento(isActionPermitted(Permissions.ANEXAR));
        requerimentoVigilanciaDTO.setPermissaoInformarFiscal(isActionPermitted(Permissions.INFORMAR_FISCAIS));
        requerimentoVigilanciaDTO.setPermissaoBoletoComplementar(isActionPermitted(Permissions.BOLETO_COMPLEMENTAR));
        requerimentoVigilanciaDTO.setPermissaoRegistrarVisita(isActionPermitted(Permissions.REGISTRAR));
        requerimentoVigilanciaDTO.setPermissaoAutoMulta(isActionPermitted(Permissions.AUTO_MULTA));
        requerimentoVigilanciaDTO.setPermissaoAutoInfracao(isActionPermitted(Permissions.AUTO_INFRACAO));
        requerimentoVigilanciaDTO.setPermissaoAutoIntimacao(isActionPermitted(Permissions.AUTO_INTIMACAO));
        requerimentoVigilanciaDTO.setPermissaoAutoPenalidade(false); //rever rotina da funcionalidade
        requerimentoVigilanciaDTO.setVigilanciaFinanceiroList(VigilanciaHelper.getVigilanciaFinanceiroList(requerimentoVigilanciaDTO.getRequerimentoVigilancia()));

        dlgAcoesRequerimentoVigilancia.show(target, requerimentoVigilanciaDTO);
    }

    private void imprimirBoleto(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws ValidacaoException {
        try {
            File boleto = null;
            if (vigilanciaFinanceiro != null && vigilanciaFinanceiro.getAnexoBoleto() != null) {
                boleto = VigilanciaAnexosHelper.getFileAnexoBoleto(vigilanciaFinanceiro);
            } else if (vigilanciaFinanceiro != null && vigilanciaFinanceiro.getTokenBoleto() != null) {
                BoletoDTO boletoDTO = BOFactoryWicket.getBO(BasicoFacade.class).consultarBoleto(vigilanciaFinanceiro.getTokenBoleto());
                boleto = boletoDTO.getBoleto();
                if (boleto != null) {
                    FinanceiroVigilanciaHelper.anexarBoleto(vigilanciaFinanceiro, boleto);
                    BOFactoryWicket.save(vigilanciaFinanceiro);
                }
            } else if (vigilanciaFinanceiro != null) {
                EmissaoBoletoVigilanciaDTO dto = new EmissaoBoletoVigilanciaDTO();
                dto.setVigilanciaFinanceiro(vigilanciaFinanceiro);

                boleto = BOFactoryWicket.getBO(VigilanciaReportFacade.class).gerarBoletoVigilancia(dto);
            }
            if (boleto != null) {
                String fileToBase64 = FileUtils.getFileToBase64(boleto);
                Files.deleteIfExists(boleto.toPath());
                ajaxPreviewBlank.initiatePdfBase64(target, fileToBase64);
            }
        } catch (DAOException | IOException ex) {
            Loggable.log.error(ex);
            throw new ValidacaoException(ex.getMessage());
        }
    }

    private void analisarRequerimento(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto, String justificativa) throws DAOException, ValidacaoException {
        if (VigilanciaHelper.analisarDocumentosExterno(requerimentoVigilanciaDTO.getRequerimentoVigilancia())) {
            setResponsePage(new RequerimentoVigilanciaAnalisarDocumentacaoPage(requerimentoVigilanciaDTO.getRequerimentoVigilancia(), dto, classeVoltar));
        } else {
            commandAnalisarRequerimento(target, dto, justificativa);
        }
    }

    private void commandAnalisarRequerimento(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto, String justificativa) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(VigilanciaFacade.class).analisarRequerimentoVigilancia(dto, justificativa);
        table.update(target);
        if (classeVoltar != null && situacao != null) {
            setResponsePage(classeVoltar);
        }
    }

    private void imprimirHistorico(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        File historico = null;
        if (requerimentoVigilancia.getEstabelecimento() != null && requerimentoVigilancia.getEstabelecimento().getCodigo() != null) {
            historico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).historico(requerimentoVigilancia.getEstabelecimento());
        } else if (requerimentoVigilancia.getVigilanciaPessoa() != null && requerimentoVigilancia.getVigilanciaPessoa().getCodigo() != null) {
            historico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).historico(requerimentoVigilancia.getVigilanciaPessoa());
        } else if (requerimentoVigilancia.getVigilanciaProfissional() != null && requerimentoVigilancia.getVigilanciaProfissional().getCodigo() != null) {
            historico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).historico(requerimentoVigilancia.getVigilanciaProfissional());
        }

        if (historico != null) {
            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(historico));
            ajaxPreviewBlank.initiate(target, "historico.pdf", resourceStream);
        }
    }

    private void imprimirDocumentosRequerimento(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        info(target, "Impressão enviada para processamento e em alguns instantes você receberá uma mensagem com o arquivo gerado");
        BOFactoryWicket.getBO(VigilanciaReportFacade.class).enviarArquivoDocumentosRequerimentoVigilancia(requerimentoVigilancia);
    }

    private void onEncaminharFiscais(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) throws DAOException {
        boolean permissaoRemoverFiscal = new PermissoesWebUtil().isActionPermitted(Permissions.REMOVER_FISCAIS, RequerimentosPage.class);
        if (permissaoRemoverFiscal) {
            //limpar a lista de fiscais do banco e persistir somente os incluidos em tela (encaminhamento)
            initDlgEncaminhamentoFiscaisRequerimentoVigilancia(target, dto.getRequerimentoVigilancia());
        } else {
            // aplica regra de poder devolver somente para o profissional que enviou o protocolo para o profissional que o cadastrou
            initDlgConfirmacaoEncaminhamentoGestor(target, dto.getRequerimentoVigilancia());
        }
    }

    private void initDlgConfirmacaoEncaminhamentoGestor(AjaxRequestTarget target, final RequerimentoVigilancia requerimentoVigilancia) throws DAOException {
        List<RequerimentoVigilanciaFiscal> fiscaisList = VigilanciaHelper.getRequerimentoVigilanciaFiscalList(requerimentoVigilancia);
        Usuario usuarioSessao = SessaoAplicacaoImp.getInstance().getUsuario();
        Profissional profissionalSessao = null;
        if (usuarioSessao.getProfissional() != null) {
            profissionalSessao = usuarioSessao.getProfissional();
        }
        Profissional profissionalCadastro = getProfissionalCadastro(fiscaisList, profissionalSessao);
        if (profissionalCadastro != null) {
            StringBuilder builderMensagem = new StringBuilder("Confirma o encaminhamento do Requerimento/Protocolo para o Profissional " + profissionalCadastro.getDescricaoFormatado());
            builderMensagem.append("? \n");
            builderMensagem.append(" Esta ação irá remover do Requerimento/Protocolo Nº ");
            builderMensagem.append(requerimentoVigilancia.getProtocoloFormatado());
            if (CollectionUtils.isNotNullEmpty(fiscaisList)) {
                builderMensagem.append(" o(s) seguinte(s) profissional(is): \n");
                builderMensagem.append(VigilanciaHelper.getNomeFiscaisList(fiscaisList));
            } else {
                builderMensagem.append("Os fiscais já vinculados");
            }

            RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscal = new RequerimentoVigilanciaFiscal();
            requerimentoVigilanciaFiscal.setProfissional(profissionalCadastro);
            requerimentoVigilanciaFiscal.setRequerimentoVigilancia(requerimentoVigilancia);

            RequerimentoVigilanciaFiscalDTO dto = new RequerimentoVigilanciaFiscalDTO();
            dto.setFiscalList(Arrays.asList(requerimentoVigilanciaFiscal));
            dto.setFiscalExcluirList(fiscaisList);
            dto.setRequerimentoVigilancia(requerimentoVigilancia);

            DlgConfirmacaoSimNao dlgConfirmarEncaminhamentoRequerimento = new DlgConfirmacaoSimNao<RequerimentoVigilanciaFiscalDTO>(newModalId(), 60, 650) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    RequerimentoVigilancia rv = getObject().getRequerimentoVigilancia();
                    BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(rv, getObject().getFiscalList(), getObject().getFiscalExcluirList());
                    rv.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
                    table.update(target);
                    BOFactory.save(rv);
                }
            };
            addModal(target, dlgConfirmarEncaminhamentoRequerimento);
            dlgConfirmarEncaminhamentoRequerimento.setTitle("Devolver Protocolo");

            dlgConfirmarEncaminhamentoRequerimento.setMessage(target, builderMensagem.toString());
            dlgConfirmarEncaminhamentoRequerimento.setObject(dto);
            dlgConfirmarEncaminhamentoRequerimento.show(target);
        } else {
            warn(target, "Não é possível reencaminhar o protocolo, este não foi direcionado ao seu usuário por um profissional");
        }
    }

    private Profissional getProfissionalCadastro(List<RequerimentoVigilanciaFiscal> fiscaisList, Profissional profissionalSessao) {
        Profissional profissionalCadastro = null;
        if (profissionalSessao != null && profissionalSessao.getCodigo() != null && CollectionUtils.isNotNullEmpty(fiscaisList)) {
            RequerimentoVigilanciaFiscal select = Lambda.selectUnique(fiscaisList, Lambda.having(Lambda.on(RequerimentoVigilanciaFiscal.class).getProfissional(), Matchers.equalTo(profissionalSessao)));
            if (select != null && select.getCodigo() != null) {
                profissionalCadastro = select.getProfissionalCadastro();
            }
        }
        return profissionalCadastro;
    }


    private void initDlgEncaminhamentoFiscaisRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        addModal(target, dlgAdicionarFiscaisRequerimento = new DlgAdicionarFiscaisRequerimento(newModalId(), true) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilancia rv, List<RequerimentoVigilanciaFiscal> fiscalList, List<RequerimentoVigilanciaFiscal> fiscalExcluirList) throws ValidacaoException, DAOException {
                // ignora os já persistidos/informados e deixa somente os informados na tela.
                List<RequerimentoVigilanciaFiscal> listFiscal = LoadManager.getInstance(RequerimentoVigilanciaFiscal.class)
                        .addProperties(new HQLProperties(RequerimentoVigilanciaFiscal.class).getProperties())
                        .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(BaseRequerimentoVigilanciaFiscal.PROP_PROFISSIONAL)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(BaseRequerimentoVigilanciaFiscal.PROP_REQUERIMENTO_VIGILANCIA, rv))
                        .start().getList();

                BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(rv, fiscalList, listFiscal);
                rv.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
                BOFactory.save(rv);
                table.update(target);
            }
        });
        requerimentoVigilancia = LoadManager.getInstance(RequerimentoVigilancia.class).setId(requerimentoVigilancia.getCodigo()).start().getVO();
        dlgAdicionarFiscaisRequerimento.show(target, requerimentoVigilancia);
    }

    private void redirecionarAutoIntimacao(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        CadastroAutoIntimacaoPage cadAutoIntimacao = new CadastroAutoIntimacaoPage(requerimentoVigilancia) {
            @Override
            public Class getResponsePage() {
                return ConsultaRequerimentoVigilanciaFiscalPage.class;
            }
        };

        cadAutoIntimacao.instanceFromRequerimento(target);

        setResponsePage(cadAutoIntimacao);
    }

    private void redirecionarAutoInfracao(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        CadastroAutoInfracaoPage cadAutoInfracao = new CadastroAutoInfracaoPage(requerimentoVigilancia) {
            @Override
            public Class getResponsePage() {
                return ConsultaRequerimentoVigilanciaFiscalPage.class;
            }
        };

        cadAutoInfracao.instanceFromRequerimento(target);

        setResponsePage(cadAutoInfracao);
    }

    private void redirecionarAutoPenalidade(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        CadastroAutoPenalidadePage cadastroAutoPenalidadePage = new CadastroAutoPenalidadePage() {
            @Override
            public Class getResponsePage() {
                return ConsultaRequerimentoVigilanciaFiscalPage.class;
            }
        };
        cadastroAutoPenalidadePage.instanceFromRequerimento(target, requerimentoVigilancia);
        setResponsePage(cadastroAutoPenalidadePage);
    }

    private void redirecionarAutoMulta(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        CadastroAutoMultaPage cadastroAutoMulta = new CadastroAutoMultaPage() {
            @Override
            public Class getResponsePage() {
                return ConsultaRequerimentoVigilanciaFiscalPage.class;
            }
        };

        cadastroAutoMulta.instanceFromRequerimento(target, requerimentoVigilancia);

        setResponsePage(cadastroAutoMulta);
    }

    private void redirecionarParecer(RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case PROJETO_BASICO_ARQUITETURA:
                    setResponsePage(new RequerimentoAnaliseProjetosParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ANALISE_PROJETO_HIDROSSANITARIO:
                    setResponsePage(new RequerimentoProjetosHidrossanitarioParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case VISTORIA_HABITESE_SANITARIO:
                    setResponsePage(new RequerimentoVistoriaHidrossanitarioParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO:
                    setResponsePage(new RequerimentoHidrossanitarioDeclaratorioParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case HABITE_SE_DECLARATORIO:
                    setResponsePage(new RequerimentoHabiteseDeclaratorioParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case PROJETO_ARQUITETONICO_SANITARIO:
                    setResponsePage(new RequerimentoProjetoArquitetonicoParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                default:
                    CadastroParecerPage cadParecer = new CadastroParecerPage(rv, getForm().getModel().getObject(), true);
                    cadParecer.instanceFromRequerimento();
                    setResponsePage(cadParecer);
                    break;
            }
        }
    }

    private void redirecionarConformidade(RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case VISTORIA_LAUDO_CONFORMIDADE_PBA:
                    setResponsePage(new RequerimentoAnaliseProjetosConformidadeTecnicaVistoriaPage(rv, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                default:
                    setResponsePage(new RequerimentoAnaliseProjetosConformidadeTecnicaPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
            }
        }
    }

    private void redirecionarBoletoPage(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO dto) {
        List<VigilanciaFinanceiro> vigilanciaFinanceiroList = VigilanciaHelper.getVigilanciaFinanceiroList(dto.getRequerimentoVigilancia());

        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {

            if (vigilanciaFinanceiroList.size() > 1) {
                if (dlgEscolherVigilanciaFinanceiro == null) {
                    addModal(target, dlgEscolherVigilanciaFinanceiro = new DlgEscolherVigilanciaFinanceiro<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>(newModalId()) {
                        @Override
                        public void onEscolherVigilanciaFinanceiro(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws DAOException, ValidacaoException {
                            setResponsePage(new BoletoVigilanciaPage(getObject(), vigilanciaFinanceiro, ConsultaRequerimentoVigilanciaFiscalPage.class));
                        }
                    });
                }

                dlgEscolherVigilanciaFinanceiro.setObject(dto);
                dlgEscolherVigilanciaFinanceiro.show(target, vigilanciaFinanceiroList);
            } else {
                setResponsePage(new BoletoVigilanciaPage(dto, vigilanciaFinanceiroList.get(0), ConsultaRequerimentoVigilanciaFiscalPage.class));
            }
        } else if (VigilanciaHelper.geraFinanceiro(dto.getRequerimentoVigilancia().getTipoDocumento())) {
            ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro = null;
            try {
                configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
            } catch (ValidacaoException e) {
                Loggable.log.error(e.getMessage(), e);
            }
            if (TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())) {
                if (configuracaoVigilanciaFinanceiro != null && ConfiguracaoVigilanciaFinanceiro.TipoCobrancaLicencaTransporte.POR_VEICULO.value().equals(configuracaoVigilanciaFinanceiro.getTipoCobrancaLicencaTransporte())) {
                    setResponsePage(new BoletoMultiploVigilanciaPage(dto.getRequerimentoVigilancia(), ConsultaRequerimentoVigilanciaPage.class));
                } else {
                    setResponsePage(new BoletoVigilanciaPage(dto, ConsultaRequerimentoVigilanciaPage.class));
                }
            } else {
                setResponsePage(new BoletoVigilanciaPage(dto, ConsultaRequerimentoVigilanciaPage.class));
            }
        }
    }

    private void redirecionarComprovantePagamento(AjaxRequestTarget target, br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO requerimentoVigilanciaDTO) {
        List<VigilanciaFinanceiro> vigilanciaFinanceiroList = VigilanciaHelper.getVigilanciaFinanceiroList(requerimentoVigilanciaDTO.getRequerimentoVigilancia());

        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
            if (vigilanciaFinanceiroList.size() > 1) {
                if (dlgEscolherVigilanciaFinanceiro == null) {
                    addModal(target, dlgEscolherVigilanciaFinanceiro = new DlgEscolherVigilanciaFinanceiro<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO>(newModalId()) {
                        @Override
                        public void onEscolherVigilanciaFinanceiro(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws DAOException, ValidacaoException {
                            if (dlgAnexoComprovantePagamentoRequerimentoChoice == null) {
                                addModal(target, dlgAnexoComprovantePagamentoRequerimentoChoice = new DlgAnexoComprovantePagamentoRequerimento(newModalId()) {
                                    @Override
                                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                        dlgEscolherVigilanciaFinanceiro.close(target);
                                        redirecionarComprovantePagamento(target, dlgEscolherVigilanciaFinanceiro.getObject());
                                    }
                                });
                            }
                            dlgAnexoComprovantePagamentoRequerimentoChoice.show(target, vigilanciaFinanceiro.getRequerimentoVigilancia(), vigilanciaFinanceiro);
                        }

                        @Override
                        public void onFechar(AjaxRequestTarget target) {
                            super.onFechar(target);
                            table.update(target);
                        }
                    });
                }
                dlgEscolherVigilanciaFinanceiro.setObject(requerimentoVigilanciaDTO);
                dlgEscolherVigilanciaFinanceiro.show(target, vigilanciaFinanceiroList);
            } else {
                if (dlgAnexoComprovantePagamentoRequerimento == null) {
                    addModal(target, dlgAnexoComprovantePagamentoRequerimento = new DlgAnexoComprovantePagamentoRequerimento(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            table.update(target);
                        }
                    });
                }
                dlgAnexoComprovantePagamentoRequerimento.show(target, requerimentoVigilanciaDTO.getRequerimentoVigilancia(), vigilanciaFinanceiroList.get(0));
            }
        }
    }

    private void initDlgOcorrenciaConsultaRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        DlgOcorrenciaConsultaRequerimentoVigilancia dlgOcorrenciaConsultaRequerimentoVigilancia = new DlgOcorrenciaConsultaRequerimentoVigilancia(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, CadastroOcorrenciaRequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(VigilanciaFacade.class).gerarOcorrenciaRequerimentoVigilanciaProfissional(dto);
                table.update(target);
            }
        };
        addModal(target, dlgOcorrenciaConsultaRequerimentoVigilancia);
        dlgOcorrenciaConsultaRequerimentoVigilancia.show(target, rv);
    }

    private List<DataReport> responseImpression(AjaxRequestTarget target, RequerimentoVigilancia rv) throws ReportException {
        Long td = rv.getTipoDocumento();
        List<DataReport> lstDataReport = new ArrayList<>();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case EXUMACAO_RESTOS_MORTAIS:
                    RelatorioAutorizacaoExumacaoDTOParam paramExumacao = new RelatorioAutorizacaoExumacaoDTOParam();
                    paramExumacao.setCodigoRequerimentoVigilancia(rv.getCodigo());
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRequerimentoExumacao(paramExumacao));
                    break;
                case DECLARACAO_VISA_PRODUTOS:
                    RelatorioRequerimentoVigilanciaComprovanteDTOParam paramVisaProdutos = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                    paramVisaProdutos.setRequerimentoVigilancia(rv);
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoDeclaracaoVisaProdutos(paramVisaProdutos));
                    break;
                case DECLARACAO_VISA_ISENCAO_TAXAS:
                    RelatorioRequerimentoVigilanciaComprovanteDTOParam paramVisaIsencaoTaxas = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                    paramVisaIsencaoTaxas.setRequerimentoVigilancia(rv);
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoDeclaracaoVisaIsencaoTaxas(paramVisaIsencaoTaxas));
                    break;
                case DECLARACAO_VISA_OUTROS:
                    RelatorioRequerimentoVigilanciaComprovanteDTOParam paramVisaOutros = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                    paramVisaOutros.setRequerimentoVigilancia(rv);
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoDeclaracaoVisaOutros(paramVisaOutros));
                    break;
                default:
                    initDlgImpressaoConsultaRequerimentoVigilancia(target, rv);
                    break;
            }

        }
        return lstDataReport;
    }

    private void responseConfirm(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case EXUMACAO_RESTOS_MORTAIS:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALVARA_PARTICIPANTE_EVENTO:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case ALVARA_CADASTRO_EVENTO:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case ALVARA_INICIAL:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case LICENCA_SANITARIA:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case REVALIDACAO_LICENCA_SANITARIA:
                case ALVARA_REVALIDACAO:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case AUTORIZACAO_SANITARIA:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case LICENCA_TRANSPORTE:
                    setResponsePage(new DeferimentoLicencaVeiculosPage(rv, true, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case ABERTURA_LIVRO_CONTROLE:
                    setResponsePage(new RequerimentoLivroFechamentoPage(rv, false, true, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case PRORROGACAO_PRAZO:
                    setResponsePage(new DeferimentoProrrogacaoPrazoPage(rv, true, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case INSPECAO_SANITARIA_AFE_ANVISA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case INSPECAO_SANITARIA_COMUM:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALTERACAO_RESPONSABILIDADE_LEGAL:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALTERACAO_ATIVIDADE_ECONOMICA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case BAIXA_RESPONSABILIDADE_TECNICA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ENTRADA_RESPONSABILIDADE_TECNICA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case REQUISICAO_RECEITUARIO_B:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALTERACAO_ENDERECO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALTERACAO_RAZAO_SOCIAL:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case REQUISICAO_RECEITUARIO_A:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case REQUISICAO_RECEITUARIO_TALIDOMIDA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case DECLARACAO_CARTORIO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case CERTIDAO_NADA_CONSTA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case PEDIDO_DOCUMENTO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case VACINACAO_EXTRAMURO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ANALISE_PROJETOS:
                    setResponsePage(new RequerimentoAnaliseProjetosParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaFiscalPage.class));
                    break;
                case PROJETO_BASICO_ARQUITETURA:
                    setResponsePage(new RequerimentoAnaliseProjetosParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case BAIXA_ESTABELECIMENTO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case BAIXA_VEICULO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case DENUNCIA_RECLAMACAO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case TREINAMENTOS_ALIMENTO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                default:
                    break;
            }
        }
    }

    private void initDlgEntregaDocumentoRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        DlgEntregaDocumentoRequerimentoVigilancia dlgEntregaDocumentoRequerimentoVigilancia = new DlgEntregaDocumentoRequerimentoVigilancia(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaEntregaDocumentoDTO dto) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(VigilanciaFacade.class).entregarDocumentoRequerimentoVigilancia(dto);
                table.update(target);
            }
        };
        addModal(target, dlgEntregaDocumentoRequerimentoVigilancia);
        dlgEntregaDocumentoRequerimentoVigilancia.show(target, rv);
    }

    private void initDlgFinalizacaoRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        addModal(target, dlgFinalizacaoRequerimentoVigilancia = new DlgCancelamentoFinalizacaoRequerimentoVigilancia(newModalId(), BundleManager.getString("finalizacaoRequerimento")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) throws ValidacaoException, DAOException {
                if (dto.getSituacao() == null) {
                    dto.setSituacao(RequerimentoVigilancia.Situacao.FINALIZADO);
                }
                if (RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(dto.getSituacao())) {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                }
                BOFactoryWicket.getBO(VigilanciaFacade.class).cancelarFinalizarRequerimentoVigilancia(dto);
                table.update(target);
            }
        });

        dlgFinalizacaoRequerimentoVigilancia.show(target, rv, true);
    }

    private void initDlgFinalizacaoRequerimentoAlvara(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        addModal(target, dlgFinalizacaoRequerimentoVigilancia = new DlgCancelamentoFinalizacaoRequerimentoVigilancia(newModalId(), BundleManager.getString("finalizacaoRequerimento")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) throws ValidacaoException, DAOException {
                if (dto.getRequerimentoVigilancia() != null && dto.getRequerimentoVigilancia().getSituacao() != null) {
                    dto.getRequerimentoVigilancia().setSituacaoAnteriorConclusao(dto.getRequerimentoVigilancia().getSituacao());
                }
                if (dto.getSituacao() == null) {
                    dto.setSituacao(RequerimentoVigilancia.Situacao.FINALIZADO);
                } else {
                    dto.getRequerimentoVigilancia().setSituacao(dto.getSituacao().value());
                }
                dto.getRequerimentoVigilancia().setDataFinalizacao(dto.getDataFinalizacao());
                if (dto.getRequerimentoVigilancia().getChaveQRcode() != null) {
                    String urlQrcode = new StringBuilder().append(VigilanciaHelper.getURLQRCodePageAlvara()).append("?CHQRC=").append(dto.getRequerimentoVigilancia().getChaveQRcode()).toString();
                    dto.setUrlAlvara(urlQrcode);
                }

                BOFactoryWicket.getBO(VigilanciaFacade.class).finalizarRequerimentoAlvara(dto.getRequerimentoVigilancia(), dto);

                table.update(target);
            }
        });

        dlgFinalizacaoRequerimentoVigilancia.show(target, rv, true);
    }

    private void initDlgCancelamentoRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        addModal(target, dlgCancelamentoRequerimentoVigilancia = new DlgCancelamentoFinalizacaoRequerimentoVigilancia(newModalId(), BundleManager.getString("cancelamentoRequerimento")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) throws ValidacaoException, DAOException {
                if (dto.getSituacao() == null) {
                    dto.setSituacao(RequerimentoVigilancia.Situacao.CANCELADO);
                }
                BOFactoryWicket.getBO(VigilanciaFacade.class).cancelarFinalizarRequerimentoVigilancia(dto);
                table.update(target);
            }
        });

        String validacaoBoletosPendentes = VigilanciaHelper.validacaoBoletosPendentes(rv);
        if (StringUtils.trimToNull(validacaoBoletosPendentes) != null) {
            initDlgConfirmacaoCancelamento(target, validacaoBoletosPendentes.concat(" Deseja realmente cancelar?"), rv);
        } else {
            dlgCancelamentoRequerimentoVigilancia.show(target, rv, false);
        }
    }

    private void initDlgConfirmacaoCancelamento(AjaxRequestTarget target, String mensagemErro, RequerimentoVigilancia requerimentoVigilancia) {
        dlgConfirmacaoSimNaoCancelamento = new DlgConfirmacaoSimNao<RequerimentoVigilancia>(newModalId(), mensagemErro) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgCancelamentoRequerimentoVigilancia.show(target, getObject(), false);
            }
        };
        addModal(target, dlgConfirmacaoSimNaoCancelamento);
        dlgConfirmacaoSimNaoCancelamento.setObject(requerimentoVigilancia);
        dlgConfirmacaoSimNaoCancelamento.show(target);
    }

    private QueryPagerProvider getDataProvider() {
        if (dataProvider == null) {
            dataProvider = new QueryPagerProvider<br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO, RequerimentoVigilanciaDTOParam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging<RequerimentoVigilanciaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                    if (getSort() != null) {
                        getForm().getModel().getObject().setAscending(getSort().isAscending());
                        getForm().getModel().getObject().setPropSort(getSort().getProperty());
                    }
                    getForm().getModel().getObject().setPaginaFiscal(true);
                    getForm().getModel().getObject().setSortDataRequerimento(VigilanciaHelper.getSorterDataRequerimento());
                    return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarRequerimentoVigilancia(dataPaging);
                }
            };
        }
        return dataProvider;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRequerimentosProtocoloFiscal");
    }

    private void carregaLstUsuarioSetor() {
        Usuario usuario = br.com.celk.system.session.ApplicationSession.get().getSession().getUsuario();
        lstUsuSetor = LoadManager.getInstance(UsuarioSetorVigilancia.class)
                .addProperties(new HQLProperties(UsuarioSetorVigilancia.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(BaseUsuarioSetorVigilancia.PROP_USUARIO, usuario))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(lstUsuSetor)) {
            getForm().getModel().getObject().setLstUsuSetor(lstUsuSetor);
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        if (getForm().getModel().getObject().getSituacao() != null && classeVoltar != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerFiltros)));
        }
    }

}