package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.tipodocumento.autocomplete.AutoCompleteConsultaTipoDocumento;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioMovimentacaoDiariaDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import java.util.List;

import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 54
 */
@Private
public class RelatorioRelacaoMovimentacaoDiariaPage extends RelatorioPage<RelatorioMovimentacaoDiariaDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaLocalizacao autoCompleteConsultaLocalizacao;
    private AutoCompleteConsultaCentroCusto autoCompleteConsultaCentroCusto;
    private AutoCompleteConsultaTipoDocumento autoCompleteConsultaTipoDocumento;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<Long> dropDownTipoRelatorio;
    private DropDown<Long> dropDownFormaApresentacao;
    private DropDown<String> dropDownOrdenacao;
    private DropDown dropDownTipoPreco;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresas").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produtos"));
        form.add(autoCompleteConsultaLocalizacao = new AutoCompleteConsultaLocalizacao("localizacao"));
        form.add(autoCompleteConsultaCentroCusto = new AutoCompleteConsultaCentroCusto("centroCustos"));
        form.add(autoCompleteConsultaTipoDocumento = new AutoCompleteConsultaTipoDocumento("tipoDocumentoList"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getDropDownTipoRelatorio());
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownOrdenacao());
        form.add(getDropDownTipoPreco());

        autoCompleteConsultaProduto.setIncluirInativos(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setOperadorValor(true);
        autoCompleteConsultaLocalizacao.setMultiplaSelecao(true);
        autoCompleteConsultaLocalizacao.setOperadorValor(true);
        autoCompleteConsultaCentroCusto.setMultiplaSelecao(true);
        autoCompleteConsultaCentroCusto.setOperadorValor(true);
        autoCompleteConsultaTipoDocumento.setMultiplaSelecao(true);
        autoCompleteConsultaTipoDocumento.setOperadorValor(true);

        dropDownTipoRelatorio.setComponentValue(new Long(ReportProperties.DETALHADO));
        dropDownFormaApresentacao.setComponentValue(new Long(ReportProperties.GERAL));
        dropDownOrdenacao.setComponentValue(MovimentoEstoque.PROP_NUMERO_DOCUMENTO);
        dropDownTipoPreco.setComponentValue(RepositoryComponentDefault.TipoPreco.PRECO_MEDIO);

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioMovimentacaoDiariaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioMovimentacaoDiariaDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioRelacaoMovimentacaoDiaria(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("movimentacaoDiariaProdutos");
    }

    public DropDown getDropDownTipoRelatorio() {
        if (dropDownTipoRelatorio == null) {
            this.dropDownTipoRelatorio = new DropDown("tipoRelatorio");
            dropDownTipoRelatorio.addAjaxUpdateValue();
            dropDownTipoRelatorio.addChoice(new Long(ReportProperties.RESUMIDO_TIPO), BundleManager.getString("resumidoTipo"));
            dropDownTipoRelatorio.addChoice(new Long(ReportProperties.RESUMIDO_PRODUTO), BundleManager.getString("resumidoProduto"));
            dropDownTipoRelatorio.addChoice(new Long(ReportProperties.DETALHADO), BundleManager.getString("detalhado"));
            this.dropDownTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget art) {
                    Long opcao = dropDownTipoRelatorio.getComponentValue();
                    if (opcao != null && !opcao.equals(new Long(ReportProperties.RESUMIDO_TIPO))) {
                        dropDownFormaApresentacao.setEnabled(true);
                        dropDownOrdenacao.setEnabled(true);
                    } else {
                        dropDownFormaApresentacao.setEnabled(false);
                        dropDownOrdenacao.setEnabled(false);
                    }

                    if (new Long(ReportProperties.DETALHADO).equals(opcao)) {
                        dropDownTipoPreco.setEnabled(true);
                    } else {
                        dropDownTipoPreco.setEnabled(false);
                        param.setTipoPreco(RepositoryComponentDefault.TipoPreco.PRECO_MEDIO);
                    }
                    art.add(dropDownFormaApresentacao);
                    art.add(dropDownOrdenacao);
                    art.add(dropDownTipoPreco);
                }
            });
        }
        return dropDownTipoRelatorio;
    }

    private DropDown getDropDownTipoPreco() {
        if (dropDownTipoPreco == null) {
            dropDownTipoPreco = DropDownUtil.getEnumDropDown("tipoPreco", RepositoryComponentDefault.TipoPreco.values());
            dropDownTipoPreco.add(new Tooltip().setText("msgTooltipTipoPreco"));
        }
        return dropDownTipoPreco;
    }

    public DropDown getDropDownOrdenacao() {
        if (dropDownOrdenacao == null) {
            this.dropDownOrdenacao = new DropDown("ordenacao");
            dropDownOrdenacao.addAjaxUpdateValue();
            dropDownOrdenacao.addChoice(Produto.PROP_DESCRICAO, BundleManager.getString("descricaoProduto"));
            dropDownOrdenacao.addChoice(Produto.PROP_CODIGO, BundleManager.getString("codigoProduto"));
            dropDownOrdenacao.addChoice(MovimentoEstoque.PROP_NUMERO_DOCUMENTO, BundleManager.getString("documentoItem"));
        }
        return dropDownOrdenacao;
    }

    public DropDown getDropDownFormaApresentacao() {
        if (dropDownFormaApresentacao == null) {
            this.dropDownFormaApresentacao = new DropDown("formaApresentacao");
            dropDownFormaApresentacao.addAjaxUpdateValue();
            dropDownFormaApresentacao.addChoice(new Long(ReportProperties.AGRUPAR_GRUPO), BundleManager.getString("grupoProduto"));
            dropDownFormaApresentacao.addChoice(new Long(ReportProperties.GERAL), BundleManager.getString("geral"));
            dropDownFormaApresentacao.addChoice(new Long(ReportProperties.AGRUPAR_CENTRO_CUSTO), BundleManager.getString("centroCusto"));
        }

        return dropDownFormaApresentacao;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }
}
