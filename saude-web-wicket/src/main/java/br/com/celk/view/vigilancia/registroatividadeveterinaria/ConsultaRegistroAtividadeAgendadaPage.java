package br.com.celk.view.vigilancia.registroatividadeveterinaria;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dialog.DlgMotivoArea;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaAtividadeVeterinaria;
import br.com.celk.vigilancia.dto.CadastroRegistroAtividadeVeterinariaDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.AgendamentoSolicitacaoCVADTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 577
 */
@Private
public class ConsultaRegistroAtividadeAgendadaPage extends ConsultaPage<SolicitacaoAgendamentoCVA, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa estabelecimento;
    private Profissional profissional;
    private AtividadeVeterinaria atividadeVeterinaria;
    private Long status = SolicitacaoAgendamentoCVA.Status.AGENDADO.value();
    private DlgMotivoArea dlgMotivo;

    @Override
    public void initForm(Form form) {

        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new AutoCompleteConsultaEmpresa("estabelecimento"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(new AutoCompleteConsultaAtividadeVeterinaria("atividadeVeterinaria"));
        form.add(getDropDownStatus());

        getLinkNovo().setVisible(false);
    }

    @Override
    public List getColumns(List columns) {
        columns = new ArrayList();

        SolicitacaoAgendamentoCVA proxy = on(SolicitacaoAgendamentoCVA.class);

        columns.add(getCustomActionColumn());
        columns.add(new DateTimeColumn(bundle("dataAgendamento"), path(proxy.getDataAgendamento()), path(proxy.getDataAgendamento())).setPattern("dd/MM/yyyy - HH:mm"));
        columns.add(createSortableColumn(bundle("estabelecimento"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissionalAgendamento().getNome()));
        columns.add(createSortableColumn(bundle("proprietarioResponsavel"), proxy.getResponsavel()));
        columns.add(createSortableColumn(bundle("atividade"), proxy.getAtividadeVeterinaria().getDescricao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));


        return columns;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<SolicitacaoAgendamentoCVA>() {
            @Override
            public void customizeColumn(final SolicitacaoAgendamentoCVA rowObject) {

                addAction(ActionType.CANCELAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        rowObject.setStatus(SolicitacaoAgendamentoCVA.Status.NAO_COMPARECEU.value());
                        BOFactoryWicket.save(rowObject);
                        updateTable(target);
                    }
                }).setQuestionDialogBundleKey("registrarNaoComparecimento")
                        .setTitleBundleKey("naoComparecimento")
                        .setEnabled(!SolicitacaoAgendamentoCVA.Status.NAO_COMPARECEU.value().equals(rowObject.getStatus())
                                && !SolicitacaoAgendamentoCVA.Status.CONFIRMADO.value().equals(rowObject.getStatus()));

                addAction(ActionType.REVERTER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        if (SolicitacaoAgendamentoCVA.Status.NAO_COMPARECEU.value().equals(rowObject.getStatus())) {
                            rowObject.setStatus(SolicitacaoAgendamentoCVA.Status.AGENDADO.value());
                            BOFactoryWicket.save(rowObject);
                            updateTable(target);
                        } else if (SolicitacaoAgendamentoCVA.Status.CONFIRMADO.value().equals(rowObject.getStatus())) {
                            reverter(target, rowObject);
                        } else if (SolicitacaoAgendamentoCVA.Status.AGENDADO.value().equals(rowObject.getStatus())) {
                            if (dlgMotivo == null) {
                                addModal(target, dlgMotivo = new DlgMotivoArea<SolicitacaoAgendamentoCVA>(newModalId(), bundle("motivoReversao")) {
                                    @Override
                                    public void onConfirmar(AjaxRequestTarget target, String motivo, SolicitacaoAgendamentoCVA object) throws ValidacaoException, DAOException {
                                        AgendamentoSolicitacaoCVADTO dto = new AgendamentoSolicitacaoCVADTO();
                                        dto.setSolicitacaoAgendamentoCVA(object);

                                        SolicitacaoAgendamentoCVAOcorrencia solicitacaoAgendamentoCVAOcorrencia = new SolicitacaoAgendamentoCVAOcorrencia();
                                        solicitacaoAgendamentoCVAOcorrencia.setDataOcorrencia(DataUtil.getDataAtual());
                                        solicitacaoAgendamentoCVAOcorrencia.setSolicitacaoAgendamentoCVA(rowObject);
                                        solicitacaoAgendamentoCVAOcorrencia.setDescricaoOcorrencia(bundle("prefixoReversaoAgendamentoMotivo") + " "+motivo);
                                        solicitacaoAgendamentoCVAOcorrencia.setUsuarioCadastro(SessaoAplicacaoImp.getInstance().getUsuario());

                                        dto.setOcorrenciaList(Arrays.asList(solicitacaoAgendamentoCVAOcorrencia));

                                        BOFactoryWicket.getBO(VigilanciaFacade.class).reverterSolicitacaoAgendamentoCVA(dto);

                                        updateTable(target);
                                    }

                                    @Override
                                    public Long getMaxLengthMotivo() {
                                        return getTamanhoMaximoMotivo();
                                    }
                                });
                            }

                            dlgMotivo.setObject(rowObject);
                            dlgMotivo.show(target);


                        }
                    }
                }).setEnabled(SolicitacaoAgendamentoCVA.Status.NAO_COMPARECEU.value().equals(rowObject.getStatus())
                        || SolicitacaoAgendamentoCVA.Status.CONFIRMADO.value().equals(rowObject.getStatus())
                        || (SolicitacaoAgendamentoCVA.Status.AGENDADO.value().equals(rowObject.getStatus()) && Data.adjustRangeHour(rowObject.getDataAgendamento()).getDataInicial().compareTo(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial()) <= 0));

                addAction(ActionType.CONFIRMAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        confirmar(target, rowObject);
                    }
                }).setEnabled(SolicitacaoAgendamentoCVA.Status.AGENDADO.value().equals(rowObject.getStatus()));
            }
        };
    }

    public Long getTamanhoMaximoMotivo() {
        Long tamanho = 500L;
        int tamanhoPrefixo = bundle("prefixoReversaoAgendamentoMotivo").length();

        return tamanho - tamanhoPrefixo - 1; //menos 1 por causa do espaço
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return SolicitacaoAgendamentoCVA.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(SolicitacaoAgendamentoCVA.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_EMPRESA, Empresa.PROP_CODIGO),
                                VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
                                VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_PROFISSIONAL_AGENDAMENTO, Profissional.PROP_CODIGO),
                                VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_PROFISSIONAL_AGENDAMENTO, Profissional.PROP_NOME),
                                VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_ATIVIDADE_VETERINARIA, AtividadeVeterinaria.PROP_CODIGO),
                                VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_ATIVIDADE_VETERINARIA, AtividadeVeterinaria.PROP_DESCRICAO),
                                VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_ATIVIDADE_VETERINARIA, AtividadeVeterinaria.PROP_INFORMAR_ANIMAIS),});
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(SolicitacaoAgendamentoCVA.PROP_DATA_AGENDAMENTO), true);
            }
        };
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaRegistroAtividadeAgendadaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("registroAtividadeAgendada");
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (SolicitacaoAgendamentoCVA.Status.AGENDADO.value().equals(status)) {
            parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_STATUS, SolicitacaoAgendamentoCVA.Status.AGENDADO.value()));
            parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_DATA_AGENDAMENTO, QueryCustom.QueryCustomParameter.MENOR_IGUAL, DataUtil.getDataHora(DataUtil.getDataAtual(), "23:59")));
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_STATUS, status));
            parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_DATA_AGENDAMENTO, Data.adjustRangeHour(DataUtil.getDataAtual())));
        }

        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_EMPRESA, estabelecimento));
        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_PROFISSIONAL_AGENDAMENTO, profissional));
        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVA.PROP_ATIVIDADE_VETERINARIA, atividadeVeterinaria));

        return parameters;
    }

    public void updateTable(AjaxRequestTarget target) {
        getPageableTable().populate();
        getPageableTable().update(target);
    }

    public void confirmar(AjaxRequestTarget target, SolicitacaoAgendamentoCVA sac) {
        CadastroRegistroAtividadeVeterinariaDTO dto = new CadastroRegistroAtividadeVeterinariaDTO();
        RegistroAtividadeVeterinaria rav = getRegistroAtividadeVeterinaria(sac);

        if (rav == null) {
            rav = new RegistroAtividadeVeterinaria();
            rav.setAtividadeVeterinaria(sac.getAtividadeVeterinaria());
            rav.setDescricao(sac.getObservacao());
            rav.setEmpresa(sac.getEmpresa());
            rav.setProfissional(sac.getProfissionalAgendamento());
        }

        List<SolicitacaoAgendamentoCVAAnimal> lstSolicitacaoAgendamentoCVAAnimal = LoadManager.getInstance(SolicitacaoAgendamentoCVAAnimal.class)
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoCVAAnimal.PROP_SOLICITACAO_AGENDAMENTO_C_V_A, sac))
                .start().getList();
        List<RegistroAtividadeAnimal> listaAnimal = new ArrayList<RegistroAtividadeAnimal>();

        for (SolicitacaoAgendamentoCVAAnimal item : lstSolicitacaoAgendamentoCVAAnimal) {
            RegistroAtividadeAnimal raa = new RegistroAtividadeAnimal();
            raa.setEspecieAnimal(item.getEspecieAnimal());
            raa.setSexo(item.getSexo());
            raa.setQuantidade(item.getQuantidade());
            listaAnimal.add(raa);
        }

        dto.setRegistroAtividadeVeterinaria(rav);
        dto.setListaRegistroAtividadeAnimal(listaAnimal);

        setResponsePage(new CadastroRegistroAtividadeVeterinariaPage(sac, dto, true));
    }

    private void reverter(AjaxRequestTarget target, SolicitacaoAgendamentoCVA sacva) throws DAOException, ValidacaoException {
        RegistroAtividadeVeterinaria rav = getRegistroAtividadeVeterinaria(sacva);
        if (rav != null) {
            rav.setStatus(RegistroAtividadeVeterinaria.Status.CANCELADO.value());
            BOFactoryWicket.save(rav);
        }

        sacva.setStatus(SolicitacaoAgendamentoCVA.Status.AGENDADO.value());
        BOFactoryWicket.save(sacva);

        updateTable(target);
    }

    private RegistroAtividadeVeterinaria getRegistroAtividadeVeterinaria(SolicitacaoAgendamentoCVA sacva) {
        RegistroAtividadeVeterinaria rav = LoadManager.getInstance(RegistroAtividadeVeterinaria.class)
                .addProperties(new HQLProperties(RegistroAtividadeVeterinaria.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RegistroAtividadeVeterinaria.PROP_SOLICITACAO_AGENDAMENTO_CVA, sacva))
                .addParameter(new QueryCustom.QueryCustomParameter(RegistroAtividadeVeterinaria.PROP_STATUS, RegistroAtividadeVeterinaria.Status.ATIVO.value()))
                .start().getVO();

        return rav;
    }

    public DropDown getDropDownStatus() {
        DropDown dropownStaus = new DropDown("status");

        dropownStaus.addChoice(SolicitacaoAgendamentoCVA.Status.AGENDADO.value(), SolicitacaoAgendamentoCVA.Status.AGENDADO.descricao());
        dropownStaus.addChoice(SolicitacaoAgendamentoCVA.Status.NAO_COMPARECEU.value(), SolicitacaoAgendamentoCVA.Status.NAO_COMPARECEU.descricao());
        dropownStaus.addChoice(SolicitacaoAgendamentoCVA.Status.CONFIRMADO.value(), SolicitacaoAgendamentoCVA.Status.CONFIRMADO.descricao());

        return dropownStaus;
    }
}
