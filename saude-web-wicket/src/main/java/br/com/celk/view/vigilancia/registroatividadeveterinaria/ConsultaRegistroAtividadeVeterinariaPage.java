package br.com.celk.view.vigilancia.registroatividadeveterinaria;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaAtividadeVeterinaria;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.AtividadeVeterinaria;
import br.com.ksisolucoes.vo.vigilancia.RegistroAtividadeVeterinaria;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 567
 */
public class ConsultaRegistroAtividadeVeterinariaPage extends ConsultaPage<RegistroAtividadeVeterinaria, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa unidade;
    private Profissional profissional;
    private AtividadeVeterinaria atividadeVeterinaria;
    private DatePeriod periodo;
    private Long situacao = RegistroAtividadeVeterinaria.Status.ATIVO.value();

    private DlgMotivoObject<RegistroAtividadeVeterinaria> dlgMotivo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new PnlDatePeriod("periodo"));
        form.add(new AutoCompleteConsultaEmpresa("unidade"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(new AutoCompleteConsultaAtividadeVeterinaria("atividadeVeterinaria"));
        form.add(DropDownUtil.getIEnumDropDown("situacao", RegistroAtividadeVeterinaria.Status.values()));

        setExibeExpandir(true);
    }

    @Override
    public List getColumns(List columns) {
        columns = new ArrayList();

        RegistroAtividadeVeterinaria proxy = on(RegistroAtividadeVeterinaria.class);

        columns.add(getCustomActionColumn());
        columns.add(createSortableColumn(bundle("dataAtividade"), proxy.getDataAtividade()));
        columns.add(createSortableColumn(bundle("unidade"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("atividade"), proxy.getAtividadeVeterinaria().getDescricao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));

        return columns;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<RegistroAtividadeVeterinaria>() {
            @Override
            public void customizeColumn(final RegistroAtividadeVeterinaria rowObject) {

                addAction(ActionType.EDITAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroAtividadeVeterinariaPage(rowObject));
                    }
                }).setEnabled(rowObject.getStatus().equals(RegistroAtividadeVeterinaria.Status.ATIVO.value()) && rowObject.getSolicitacaoAgendamentoCva() == null);

                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroAtividadeVeterinariaPage(rowObject, true));
                    }
                });

                addAction(ActionType.CANCELAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        viewDialogMotivo(target, rowObject);
                    }
                }).setQuestionDialogBundleKey(null).setEnabled(rowObject.getStatus().equals(RegistroAtividadeVeterinaria.Status.ATIVO.value()));
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return RegistroAtividadeVeterinaria.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(RegistroAtividadeVeterinaria.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(RegistroAtividadeVeterinaria.PROP_EMPRESA, Empresa.PROP_CODIGO),
                            VOUtils.montarPath(RegistroAtividadeVeterinaria.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
                            VOUtils.montarPath(RegistroAtividadeVeterinaria.PROP_PROFISSIONAL, Profissional.PROP_CODIGO),
                            VOUtils.montarPath(RegistroAtividadeVeterinaria.PROP_PROFISSIONAL, Profissional.PROP_NOME),
                            VOUtils.montarPath(RegistroAtividadeVeterinaria.PROP_ATIVIDADE_VETERINARIA, AtividadeVeterinaria.PROP_CODIGO),
                            VOUtils.montarPath(RegistroAtividadeVeterinaria.PROP_ATIVIDADE_VETERINARIA, AtividadeVeterinaria.PROP_DESCRICAO),});
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(RegistroAtividadeVeterinaria.PROP_DATA_ATIVIDADE), false);
            }
        };
    }

    public void viewDialogMotivo(AjaxRequestTarget target, RegistroAtividadeVeterinaria raa) {
        if (dlgMotivo == null) {
            addModal(target, dlgMotivo = new DlgMotivoObject<RegistroAtividadeVeterinaria>(newModalId(), bundle("motivo")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, RegistroAtividadeVeterinaria object) throws ValidacaoException, DAOException {
                    object.setStatus(RegistroAtividadeVeterinaria.Status.CANCELADO.value());
                    object.setMotivo(motivo);
                    object.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                    object.setDataCancelamento(DataUtil.getDataAtual());
                    BOFactoryWicket.save(object);
                    getPageableTable().populate();
                    getPageableTable().update(target);
                }
            });
        }

        dlgMotivo.setObject(raa);
        dlgMotivo.show(target);
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRegistroAtividadeVeterinariaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("registroAtividadeVeterinaria");
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(RegistroAtividadeVeterinaria.PROP_STATUS, situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroAtividadeVeterinaria.PROP_DATA_ATIVIDADE, periodo));
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroAtividadeVeterinaria.PROP_EMPRESA, unidade));
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroAtividadeVeterinaria.PROP_PROFISSIONAL, profissional));
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroAtividadeVeterinaria.PROP_ATIVIDADE_VETERINARIA, atividadeVeterinaria));

        return parameters;
    }
}
