package br.com.celk.view.consorcio.pedidotransferencialicitacaoentrega;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.importacaopedido.dlg.DlgInformarGuia;
import br.com.celk.view.consorcio.pedidotransferencialicitacaoentrega.dialog.DlgCancelarGuiaSaida;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioReciboEntregaProdutosDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.Licitacao;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoEntrega;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 289
 */
@Private
public class ConsultaGuiasSaidaPage extends ConsultaPage<PedidoTransferenciaLicitacaoEntrega, List<BuilderQueryCustom.QueryParameter>>{

    private Empresa consorciado;
    private DatePeriod periodo;
    private Long situacao;
    private DlgCancelarGuiaSaida dlgCancelarGuiaSaida;
    private DropDown<Long> dropDownStatus;
    private TipoConta tipoConta;
    private Long codigo;
    
    private DlgInformarGuia dlgInformarGuia;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField<Long>("codigo"));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)));
        form.add(new PnlChoicePeriod("periodo"));
        form.add(getDropDownTipoConta());
        form.add(getDropDownStatus());
        getLinkNovo().setVisible(false);
        
        situacao = PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.ENVIADA.value();
        periodo = Data.adjustRangeDay(new Date());

        setExibeExpandir(true);


    }

    private DropDown<TipoConta> getDropDownTipoConta() {
        DropDown<TipoConta> dropDownTipoConta = new DropDown<TipoConta>("tipoConta");

        List<TipoConta> tiposConta = LoadManager.getInstance(TipoConta.class)
                .addProperties(new HQLProperties(TipoConta.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(TipoConta.PROP_DESCRICAO), QueryCustom.QueryCustomSorter.DECRESCENTE))
                .start().getList();

        dropDownTipoConta.addChoice(null, "");
        for (TipoConta tipoConta : tiposConta) {
            dropDownTipoConta.addChoice(tipoConta, tipoConta.getDescricao());
        }
        return dropDownTipoConta;
    }
    
    private DropDown<Long> getDropDownStatus(){
        if (this.dropDownStatus == null) {
            this.dropDownStatus = new DropDown<Long>("situacao");
            dropDownStatus.addChoice(PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.ENVIADA.value(), BundleManager.getString("enviada"));
            dropDownStatus.addChoice(PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.CANCELADA.value(), BundleManager.getString("cancelada"));
            dropDownStatus.addChoice(null, BundleManager.getString("todas"));
        }
        
        return this.dropDownStatus;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        PedidoTransferenciaLicitacaoEntrega proxy = on(PedidoTransferenciaLicitacaoEntrega.class);
        
        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("nGuia"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("dataEntrega"), proxy.getDataEntrega()));
        columns.add(createSortableColumn(bundle("consorciado"), proxy.getPedidoTransferenciaLicitacao().getEmpresaConsorciado().getDescricao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getStatusFormatado()));
        columns.add(createSortableColumn(bundle("totalGuia"), proxy.getTotalGuia()));
        
        return columns;
    }
    
    private IColumn getActionColumn(){
        return new MultipleActionCustomColumn<PedidoTransferenciaLicitacaoEntrega>() {

            @Override
            public void customizeColumn(PedidoTransferenciaLicitacaoEntrega rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<PedidoTransferenciaLicitacaoEntrega>() {

                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferenciaLicitacaoEntrega modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesGuiaSaidaPage(modelObject));
                    }
                }).setVisible(isActionPermitted(Permissions.CONSULTAR));
                
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<PedidoTransferenciaLicitacaoEntrega>() {

                    @Override
                    public DataReport action(PedidoTransferenciaLicitacaoEntrega modelObject) throws ReportException {
                        RelatorioReciboEntregaProdutosDTOParam param = new RelatorioReciboEntregaProdutosDTOParam();
                        param.setCodigoPedidoTransferenciaEntrega(modelObject.getCodigo());
                        return BOFactoryWicket.getBO(ConsorcioFacade.class).impressaoReciboEntregaProdutos(param);
                    }
                });
                
                addAction(ActionType.CANCELAR, rowObject, new IModelAction<PedidoTransferenciaLicitacaoEntrega>() {

                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferenciaLicitacaoEntrega modelObject) throws ValidacaoException, DAOException {
                        cancelarGuiaSaida(target, modelObject);
                    }
                }).setTitleBundleKey("cancelar")
                        .setQuestionDialogBundleKey(null)
                        .setEnabled(PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.CANCELADA.value().equals(rowObject.getStatus()) ? false : true)
                        .setVisible(isActionPermitted(Permissions.CANCELAR))
                ;
            }
        };
    }
    
    public void cancelarGuiaSaida(AjaxRequestTarget target, PedidoTransferenciaLicitacaoEntrega object){
        if (dlgCancelarGuiaSaida==null) {
            addModal(target, dlgCancelarGuiaSaida = new DlgCancelarGuiaSaida(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, PedidoTransferenciaLicitacaoEntrega object) throws ValidacaoException, DAOException {

                    BOFactoryWicket.getBO(ConsorcioFacade.class).confirmarCancelamentoGuiaSaida(object);
                   getPageableTable().update(target);
                }
            });
        }
        dlgCancelarGuiaSaida.show(target, object);
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter(){

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(PedidoTransferenciaLicitacaoEntrega.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(PedidoTransferenciaLicitacaoEntrega.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO, Empresa.PROP_CODIGO),
                            VOUtils.montarPath(PedidoTransferenciaLicitacaoEntrega.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO, Empresa.PROP_DESCRICAO)
                        });
            }

            @Override
            public Class getClassConsulta() {
                return PedidoTransferenciaLicitacaoEntrega.class;
            }
        }){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(PedidoTransferenciaLicitacaoEntrega.PROP_DATA_ENTREGA, false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        if(PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.ENVIADA.value().equals(situacao)){
            parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoEntrega.PROP_STATUS, PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.ENVIADA.value()));
        } else if(PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.CANCELADA.value().equals(situacao)){
            parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoEntrega.PROP_STATUS, PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.CANCELADA.value()));
        } else{
            parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoEntrega.PROP_STATUS, BuilderQueryCustom.QueryParameter.IN, Arrays.asList(PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.ENVIADA.value(), PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.CANCELADA.value())));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoEntrega.PROP_CODIGO, codigo));
        parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoEntrega.PROP_DATA_ENTREGA, periodo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoEntrega.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO), consorciado));
        if (tipoConta != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoEntrega.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_LICITACAO, Licitacao.PROP_TIPO_CONTA, TipoConta.PROP_CODIGO), tipoConta.getCodigo()));
        }

        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaGuiasSaidaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaGuiasSaida");
    }

}
