package br.com.celk.view.materiais.dispensacao.impressaoprescricao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AjaxReportLink;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.dispensacao.impressaoprescricao.dlg.DlgConsultarPrescricao;
import br.com.celk.view.materiais.dispensacao.prescricaoatendimento.DispensacaoPrescricaoAtendimentoPage;
import br.com.celk.view.materiais.dispensacao.prescricaoatendimento.DispensacaoReceituarioPage;
import br.com.celk.view.unidadesaude.receituario.DlgSelecionaEmpresa;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.dto.QueryConsultaImpressaoPrescricoesDTOParam;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.facade.DispensacaoMedicamentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.AntimicrobianoDtoParam;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoFormularioAntimicrobianoDtoParam;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioFarmaciaDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.facade.ProcedimentoReportFacade;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 476
 */
@Private
public class ConsultaImpressaoPrescricaoPage extends BasePage {

    private Form form;

    private Button btnProcurar;

    private MultiSelectionTableOld<Receituario> tblReceituario;
    private List<Receituario> lstReceituario;

    private List<Long> lstStatus;
    private Long validadePrescricaoInterna;
    private Date dataValidadePrescricao;
    private Long tipoData;

    private String nomePaciente;
    private DatePeriod periodo;
    private PnlChoicePeriod pnlChoicePeriod;
    private Empresa empresaDispensacao;
    private Empresa empresa;
    private Long impressao;

    private Long receituario;

    private DropDown dropDownTipoReceita;
    private TipoReceita tipoReceita;

    private DlgConsultarPrescricao dlgConsultarPrescricao;
    private DlgSelecionaEmpresa dlgSelecionaEmpresa;

    private boolean permissaoVisualizarApenasPrescricaoEstabelecimento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaDispensacao;

    public ConsultaImpressaoPrescricaoPage() {
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(this));

        permissaoVisualizarApenasPrescricaoEstabelecimento = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);

        form.add(autoCompleteConsultaEmpresaDispensacao = new AutoCompleteConsultaEmpresa("empresaDispensacao", new PropertyModel<Empresa>(this, "empresaDispensacao")));
        if(permissaoVisualizarApenasPrescricaoEstabelecimento) {
            autoCompleteConsultaEmpresaDispensacao.setValidaUsuarioEmpresa(true);
            if(!SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster()) {
                autoCompleteConsultaEmpresaDispensacao.setComponentValue(SessaoAplicacaoImp.getInstance().getEmpresa());
            }
        }

        form.add(new InputField("nomePaciente", new PropertyModel<UsuarioCadsus>(this, "nomePaciente")));
        form.add(new InputField("receituario", new PropertyModel(this, "receituario")));
        form.add(pnlChoicePeriod = new PnlChoicePeriod("periodo", new PropertyModel<DatePeriod>(this, "periodo")));
        pnlChoicePeriod.setDefaultOutro();
        form.add(DropDownUtil.getIEnumDropDown("tipoData", new PropertyModel(this, "tipoData"), QueryConsultaImpressaoPrescricoesDTOParam.TipoData.values()));
        form.add(new AutoCompleteConsultaEmpresa("empresa", new PropertyModel<Empresa>(this, "empresa")));
        form.add(DropDownUtil.getIEnumDropDown("impressao", new PropertyModel(this, "impressao"), Receituario.ImpressaoPrescricao.values()));
        impressao = Receituario.ImpressaoPrescricao.PENDENTE.value();

        lstStatus = new ArrayList<Long>();
        lstStatus.add(Receituario.Situacao.PRESCRITO.value());
        lstStatus.add(Receituario.Situacao.NAO_DISPENSADO.value());

        calculaDataValidadePrescricao();

        form.add(btnProcurar = new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tblReceituario.updateAndClearSelection(target);
            }
        });

        form.add(tblReceituario = new MultiSelectionTableOld("tblReceituario", getColumns(), getCollectionProvider()));
        tblReceituario.populate();

        form.add(new AjaxReportLink("btnImprimir") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                if (!tblReceituario.getSelectedObjects().isEmpty()) {
                    ImpressaoReceituarioFarmaciaDTOParam param = new ImpressaoReceituarioFarmaciaDTOParam();
                    for (Receituario selectedObject : tblReceituario.getSelectedObjects()) {
                        Receituario receituario = BOFactoryWicket.getBO(DispensacaoMedicamentoFacade.class).setarImpressoReceituario(selectedObject);
                        param.getLstCodigoReceituario().add(receituario.getCodigo());
                        param.setTipoReceita(selectedObject.getTipoReceita().getTipoReceita());
                    }
                    tblReceituario.updateAndClearSelection(target);

                    return BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoPrescricaoInternaFarmacia(param);
                }
                throw new ValidacaoException(bundle("porFavorSelecioneUmItem"));
            }
        });

        form.add(getDropDownTipoReceita("tipoReceita"));

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        Receituario proxy = on(Receituario.class);
        Atendimento onAtendimento = on(Atendimento.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("prescricao"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("dataCadastro"), proxy.getDataCadastro(), proxy.getDataHoraCadastro()));
        columns.add(createSortableColumn(bundle("dataPrescricao"), proxy.getDataReceituario()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getUsuarioCadsus().getNome(),proxy.getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("unidade"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("leito"), onAtendimento.getLeitoQuarto().getDescricao(), proxy.getAtendimento().getLeitoQuarto().getDescricaoQuarto()));
        columns.add(createSortableColumn(bundle("tipo"), proxy.getTipoReceita().getDescricao()));
        columns.add(createSortableColumn(bundle("farmaciaDispensacao"), proxy.getEmpresaDispensacao().getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                procurar(getSort());
                return lstReceituario;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Receituario.PROP_DATA_CADASTRO, false);
            }
        };
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<Receituario>() {
            @Override
            public void customizeColumn(Receituario rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<Receituario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Receituario modelObject) throws ValidacaoException, DAOException {
                        consultarReceituario(target, modelObject);
                    }
                }).setTitleBundleKey("detalhar");

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<Receituario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Receituario modelObject) throws ValidacaoException, DAOException {
                        redirecionarDispensacao(target, modelObject);
                    }
                }).setIcon(Icon.PILL).setTitleBundleKey("dispensar");

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<Receituario>() {
                    @Override
                    public DataReport action(Receituario modelObject) throws ReportException {

                        ImpressaoFormularioAntimicrobianoDtoParam param = new ImpressaoFormularioAntimicrobianoDtoParam();
                        param.setNomePaciente(modelObject.getUsuarioCadsus().getNome());
                        param.setNumeroProntuario(modelObject.getUsuarioCadsus().getCodigo());
                        param.setDataNascimento(modelObject.getUsuarioCadsus().getDataNascimento());
                        param.setNomeMae(modelObject.getUsuarioCadsus().getNomeMae());
                        param.setTelefone(modelObject.getUsuarioCadsus().getTelefone());
                        param.setSexo(modelObject.getUsuarioCadsus().getSexo());
                        param.setUtilizaNomeSocial(modelObject.getUsuarioCadsus().getUtilizaNomeSocial());
                        param.setDescricao(modelObject.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao());
                        param.setAntimicrobianoMap(carregaAntimicrobiano(modelObject.getReceituarioItemList()));

                        return BOFactoryWicket.getBO(ProcedimentoReportFacade.class)
                                .relatorioImpressaoFormularioAntimicrobiano(param);
                    }
                }).setTitleBundleKey("imprimirAntimicrobiano").setVisible(!listaComAntimicrobiano(rowObject.getReceituarioItemList()).isEmpty());

            }
        };
    }
    private List<ReceituarioItem> listaComAntimicrobiano(List<ReceituarioItem> receituarioItemList) {
        if (receituarioItemList == null) {
            return new ArrayList<>();
        }
        List<ReceituarioItem> listaComAntimicrobiano = new ArrayList<>();
        for (ReceituarioItem ri : receituarioItemList) {
            if (ri.getProduto() != null && ri.getProduto().getSubGrupo() != null
                    && RepositoryComponentDefault.SIM.equals(ri.getProduto().getSubGrupo().getFlagAntimicrobiano())) {
                listaComAntimicrobiano.add(ri);
            }
        }
        return listaComAntimicrobiano;
    }

    private Map<Integer, AntimicrobianoDtoParam> carregaAntimicrobiano(List<ReceituarioItem> riList) {
        Map<Integer, AntimicrobianoDtoParam> map = new HashMap<>();
        int indice = 1;
        if (riList != null) {
            for (ReceituarioItem ri : listaComAntimicrobiano(riList)) {
                AntimicrobianoDtoParam antimicrobianoDtoParam = new AntimicrobianoDtoParam();
                antimicrobianoDtoParam.setDescricaoMedicamento(ri.getNomeProduto());
                antimicrobianoDtoParam.setVia(ri.getTipoViaMedicamento().getDescricao());
                antimicrobianoDtoParam.setDose(ri.getQuantidade());
                antimicrobianoDtoParam.setPosologia(ri.getPosologia());
                antimicrobianoDtoParam.setIntervalo(ri.getIntervalo());
                antimicrobianoDtoParam.setDiasTratamento(ri.getDiasTratamento());
                antimicrobianoDtoParam.setDiagnosticoAntimicrobiano(ri.getDiagnosticoAntimicrobiano());
                antimicrobianoDtoParam.setMotivoAntibiotico(ri.getMotivoAntibiotico());
                antimicrobianoDtoParam.setMotivoOutro(ri.getMotivoOutro());
                antimicrobianoDtoParam.setAntibioticoterapiaPrevia(ri.getAntibioticoterapiaPrevia());
                antimicrobianoDtoParam.setAntibioticos(ri.getAntibioticos());
                antimicrobianoDtoParam.setTratamentoBaseado(ri.getTratamentoBaseado());
                antimicrobianoDtoParam.setMicroorganismoIdentificado(ri.getMicroorganismoIdentificado());
                antimicrobianoDtoParam.setMicroorganismo(ri.getMicroorganismo());
                map.put(indice, antimicrobianoDtoParam);
                indice++;
            }
        }
        return map;
    }

    private void redirecionarDispensacao(AjaxRequestTarget target, Receituario receituario) throws ValidacaoException, DAOException {
        if (dlgSelecionaEmpresa == null) {
            addModal(target, dlgSelecionaEmpresa = new DlgSelecionaEmpresa(newModalId(), bundle("selecioneFarmaciaSaida")) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, Receituario receituario, Empresa empresa) throws ValidacaoException, DAOException {
                    boolean contemSaldo = false;

                    List<ReceituarioItem> lstReceituarioItem = LoadManager.getInstance(ReceituarioItem.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_CODIGO), receituario.getCodigo()))
                            .start().getList();
                    if (contemSaldoDisponivelDispensar(lstReceituarioItem)) {
                        contemSaldo = true;
                    }

                    String validaSaldo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ValidaSaldoDispensacaoPrescricao");

                    if (RepositoryComponentDefault.SIM.equals(validaSaldo)) {
                        if (!contemSaldo) {
                            throw new ValidacaoException(bundle("todosItensPrescricaoDispensados"));
                        }
                        setResponsePage(new DispensacaoReceituarioPage(receituario, new ConsultaImpressaoPrescricaoPage(), empresa));
                    } else {
                        setResponsePage(new DispensacaoPrescricaoAtendimentoPage(receituario, new ConsultaImpressaoPrescricaoPage(), empresa));
                    }
                }
            });
        }
        dlgSelecionaEmpresa.show(target, receituario);

    }

    private boolean contemSaldoDisponivelDispensar(List<ReceituarioItem> list) {
        for (ReceituarioItem receituarioItem : list) {
            if (receituarioItem.getProduto() == null) {
                List<ReceituarioItemComponente> lstComponentes = LoadManager.getInstance(ReceituarioItemComponente.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItemComponente.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_CODIGO), receituarioItem.getCodigo()))
                        .start().getList();
                if (lstComponentes != null && !lstComponentes.isEmpty()) {
                    for (ReceituarioItemComponente componente : lstComponentes) {
                        if (componente.getQuantidadeDispensada() == null || componente.getQuantidadeDispensada() < componente.getQuantidadePrescrita()) {
                            return true;
                        }
                    }
                }
            } else if (receituarioItem.getQuantidadeDispensada() == null || receituarioItem.getQuantidadeDispensada() < receituarioItem.getQuantidadePrescrita()) {
                return true;
            }
        }
        return false;
    }

    private void procurar(SortParam sortParam) throws DAOException, ValidacaoException {
        QueryConsultaImpressaoPrescricoesDTOParam param = new QueryConsultaImpressaoPrescricoesDTOParam();
        param.setNomePaciente(nomePaciente);
        param.setEmpresa(empresa);
        param.setPeriodo(periodo);
        param.setTipoData(tipoData);
        param.setImpressao(impressao);
        param.setLstStatus(lstStatus);
        param.setDataValidadePrescricao(dataValidadePrescricao);
        param.setTipoReceita(tipoReceita);
        param.setCodigoReceituario(receituario);
        param.setPropSort(sortParam.getProperty().toString());
        param.setAscending(sortParam.isAscending());
        param.setEmpresaDispensacao(empresaDispensacao);
        param.setVisualizarApenasPrescricaoEstabelecimento(permissaoVisualizarApenasPrescricaoEstabelecimento);
        lstReceituario = BOFactoryWicket.getBO(DispensacaoMedicamentoFacade.class).consultaImpressaoPrescricoes(param);
        carregarReceituarioItem();
    }
    private void carregarReceituarioItem() {
        if (lstReceituario != null) {
            for (Receituario receituario : lstReceituario) {
                List<ReceituarioItem> receituarioItemList = LoadManager.getInstance(ReceituarioItem.class)
                        .addProperties(new HQLProperties(Produto.class, path(on(ReceituarioItem.class).getProduto())).getProperties())
                        .addProperties(new HQLProperties(TipoViaMedicamento.class, path(on(ReceituarioItem.class).getTipoViaMedicamento())).getProperties())
                        .addProperties(new HQLProperties(SubGrupo.class, path(on(ReceituarioItem.class).getProduto().getSubGrupo())).getProperties())
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_NOME_PRODUTO))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_QUANTIDADE))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_INTERVALO))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_POSOLOGIA))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_DIAS_TRATAMENTO))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_DIAGNOSTICO_ANTIMICROBIANO))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_MOTIVO_ANTIBIOTICO))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_MOTIVO_OUTRO))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_ANTIBIOTICOTERAPIA_PREVIA))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_ANTIBIOTICOS))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_TRATAMENTO_BASEADO))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_MICROORGANISMO_IDENTIFICADO))
                        .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_MICROORGANISMO))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO), receituario.getCodigo()))
                        .start()
                        .getList();
                receituario.setReceituarioItemList(receituarioItemList);
            }
        }
    }

    public void consultarReceituario(AjaxRequestTarget target, Receituario model) {
        if (dlgConsultarPrescricao == null) {
            addModal(target, dlgConsultarPrescricao = new DlgConsultarPrescricao(newModalId()) {
            });
        }
        dlgConsultarPrescricao.show(target, model.getCodigo());
    }

    private void calculaDataValidadePrescricao() {
        try {
            validadePrescricaoInterna = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ValidadePrescricaoInterna");
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage());
        }
        if (validadePrescricaoInterna != null) {
            dataValidadePrescricao = Data.adjustRangeHour(Data.removeMinutos(DataUtil.getDataAtual(), (validadePrescricaoInterna.intValue() * 60))).getDataInicial();
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("impressaoPrescricoes");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        Long tempo = 30L;
        boolean atualizaPagina;
        try {
          atualizaPagina = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("AtualizaPaginaImpressaoPrescricao"));
        } catch (DAOException e) {
            throw new RuntimeException(e);
        }
        if (atualizaPagina) {
            response.render(OnLoadHeaderItem.forScript(""
                    + " setInterval(function(){"
                    + "     if($('.wicket-mask-dark').size() == 0){"
                    + "         $('#" + btnProcurar.getMarkupId() + "').click();"
                    + "     }"
                    + " }, " + tempo * 1000 + ");"));
        }
    }

    public DropDown getDropDownTipoReceita(String id) {
        if (dropDownTipoReceita == null) {
            dropDownTipoReceita = new DropDown<TipoReceita>(id, new PropertyModel(this, "tipoReceita"));
            List<TipoReceita> tipoReceitaList = LoadManager.getInstance(TipoReceita.class)
                    .addProperties(new HQLProperties(TipoReceita.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(TipoReceita.PROP_TIPO_RECEITA, QueryCustom.QueryCustomParameter.IN, Arrays.asList(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS, TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO)))
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoReceita.PROP_DESCRICAO))
                    .start().getList();

            dropDownTipoReceita.addChoice(null, "");
            for (TipoReceita tipoReceita : tipoReceitaList) {
                dropDownTipoReceita.addChoice(tipoReceita, tipoReceita.getDescricao());
            }
        }
        return dropDownTipoReceita;
    }
}
