package br.com.celk.view.materiais.kitpedidopaciente;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.materiais.kitpedidopaciente.customize.CustomizeConsultaKitPedidoPaciente;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.KitPedidoPaciente;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 859
 */
@Private
public class ConsultaKitPedidoPacientePage extends ConsultaPage<KitPedidoPaciente, List<QueryParameter>> {

    private String descricao;
    
    public ConsultaKitPedidoPacientePage() {
        super();
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        
        ColumnFactory columnFactory = new ColumnFactory(KitPedidoPaciente.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(KitPedidoPaciente.PROP_DESCRICAO)));

        return columns;
    }
    
    private CustomColumn<KitPedidoPaciente> getCustomColumn(){
        return new CustomColumn<KitPedidoPaciente>() {

            @Override
            public Component getComponent(String componentId, final KitPedidoPaciente rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroKitPedidoPacientePage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(MaterialBasicoFacade.class).deletarKitPedidoPaciente(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroKitPedidoPacientePage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaKitPedidoPaciente()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(KitPedidoPaciente.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(KitPedidoPaciente.PROP_DESCRICAO), QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroKitPedidoPacientePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaKitPedidoPaciente");
    }

}
