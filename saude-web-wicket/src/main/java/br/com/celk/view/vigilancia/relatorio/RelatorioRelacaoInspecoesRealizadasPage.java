package br.com.celk.view.vigilancia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoInspecoesRealizadasDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import ch.lambdaj.Lambda;
import org.apache.wicket.markup.html.form.Form;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 * <AUTHOR>
 * Programa - 902
 */
@Private
public class RelatorioRelacaoInspecoesRealizadasPage extends RelatorioPage<RelatorioRelacaoInspecoesRealizadasDTOParam> {

    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    @Override
    public void init(Form form) {
        RelatorioRelacaoInspecoesRealizadasDTOParam proxy = Lambda.on(RelatorioRelacaoInspecoesRealizadasDTOParam.class);

        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getVigilanciaPessoa())));
        form.add(new PnlAtividadeEstabelecimento(path(proxy.getAtividadeEstabelecimento())));
        form.add(new AutoCompleteConsultaSetorVigilancia(path(proxy.getSetorVigilancia())).add(new Tooltip().setHtmlText("Ao filtrar ou selecionar forma de apresentação pelo Setor Responsável é exibido somente registros que possuem um Requerimento/Protocolo vinculado.")));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioRelacaoInspecoesRealizadasDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoRelatorio()), RelatorioRelacaoInspecoesRealizadasDTOParam.TipoRelatorio.values()));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoInspecoesRealizadasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoInspecoesRealizadasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRelacaoInspecoesRealizadas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoInspecoesRealizadas");
    }
}
