package br.com.celk.view.consorcio.consorcioguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.behavior.AjaxDownload;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.atendimento.procedimentogrupo.pnl.PnlConsultaProcedimentoGrupo;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.consorcioprocedimento.autocomplete.AutoCompleteConsorcioProcedimento;
import br.com.celk.view.consorcio.procedimento.autocomplete.AutoCompleteConsultaConsorcioGrupoMulti;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioProcedimentoPrestadorDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioProcedimentoPrestadorDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.export.ExportListToXls;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.File;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static ch.lambdaj.Lambda.on;


/**
 * <AUTHOR>
 * Programa - 883
 */
@Private

public class RelatorioProcedimentoPrestadorPage extends RelatorioPage<RelatorioProcedimentoPrestadorDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaPrestador;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaConsorciado;
    private AutoCompleteConsorcioProcedimento autoCompleteConsorcioProcedimento;
    private AutoCompleteConsultaConsorcioGrupoMulti autoCompleteConsultaConsorcioGrupoMulti;
    private DropDown<ProcedimentoSubGrupo> cbxProcedimentoSubGrupo;
    private DropDown<ProcedimentoFormaOrganizacao> cbxProcedimentoFormaOrganizacao;
    private DropDown<Long> dropDownGrupoProcedimentoDiferenteSolicitado;
    private DropDown<Long> dropDownSemPrestadores;
    private AjaxDownload ajaxDownload;
    private AbstractAjaxButton btnImprimeRelatorioXLS;
    private PnlConsultaProcedimentoGrupo pnlConsultaProcedimentoGrupo;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsorcioProcedimento = new AutoCompleteConsorcioProcedimento("consorcioProcedimento"));
        form.add(autoCompleteConsultaPrestador = new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(autoCompleteConsultaConsorciado = new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        autoCompleteConsultaPrestador.setOutputMarkupPlaceholderTag(true);
        form.add(autoCompleteConsultaConsorcioGrupoMulti = new AutoCompleteConsultaConsorcioGrupoMulti("consorcioGrupoList"));
//        autoCompleteConsultaConsorcioGrupoMulti.add(new AjaxFormComponentUpdatingBehavior("onchange") {
//            @Override
//            protected void onUpdate(AjaxRequestTarget target) {
//                if (autoCompleteConsultaConsorciado.get(0) != null) {
//                    cbxProcedimentoSubGrupo.setEnabled(false);
//                    cbxProcedimentoFormaOrganizacao.setEnabled(false);
//                    pnlConsultaProcedimentoGrupo.setEnabled(false);
//                } else {
//                    cbxProcedimentoSubGrupo.setEnabled(true);
//                    cbxProcedimentoFormaOrganizacao.setEnabled(true);
//                    pnlConsultaProcedimentoGrupo.setEnabled(true);
//                }
//                target.add(cbxProcedimentoSubGrupo);
//                target.add(cbxProcedimentoFormaOrganizacao);
//                target.add(pnlConsultaProcedimentoGrupo);
//                target.add(autoCompleteConsultaConsorciado);
//            }
//        });
        form.add(pnlConsultaProcedimentoGrupo = new PnlConsultaProcedimentoGrupo("procedimentoGrupo", false));
        form.add(cbxProcedimentoSubGrupo = new DropDown("procedimentoSubGrupo"));
        form.add(cbxProcedimentoFormaOrganizacao = new DropDown("procedimentoFormaOrganizacao"));
        form.add(DropDownUtil.getIEnumDropDown("formaApresentacao", RelatorioProcedimentoPrestadorDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getNaoSimLongDropDown("comCads"));
        form.add(dropDownSemPrestadores = DropDownUtil.getNaoSimLongDropDown("semPrestadores"));
        dropDownSemPrestadores.add(new Tooltip().setText("msgFiltroListarApenasProcedimentosSemPrestadores"));

        dropDownSemPrestadores.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownSemPrestadores.getComponentValue() != null && RepositoryComponentDefault.SIM_LONG.equals(dropDownSemPrestadores.getComponentValue())) {
                    autoCompleteConsultaPrestador.limpar(target);
                    autoCompleteConsultaPrestador.setEnabled(false);
                } else {
                    autoCompleteConsultaPrestador.setEnabled(true);
                }
                target.add(autoCompleteConsultaPrestador);
            }
        });

        form.add(dropDownGrupoProcedimentoDiferenteSolicitado = DropDownUtil.getNaoSimLongDropDown("grupoProcedimentoDiferenteSolicitado"));
        dropDownGrupoProcedimentoDiferenteSolicitado.add(new Tooltip().setText("grupoProcedimentoDiferenteTooltip"));

        pnlConsultaProcedimentoGrupo.add(new ConsultaListener<ProcedimentoGrupo>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoGrupo object) {
                eventoProcedimentoGrupo(target, object);
            }
        });
        pnlConsultaProcedimentoGrupo.setLabel(Model.of(BundleManager.getString("grupo")));

        cbxProcedimentoSubGrupo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                eventoProcedimentoSubGrupo(target, cbxProcedimentoSubGrupo.getModelObject());
            }
        });

        add(ajaxDownload = new AjaxDownload());

        getControls().add(btnImprimeRelatorioXLS = new AbstractAjaxButton("btnImprimeRelatorioXLS") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                List<ConsorcioProcedimentoPrestadorDTO> relatorioProcedimentoPrestadorDTOList =
                        BOFactoryWicket.getBO(ConsorcioFacade.class)
                                .gerarXLSConsorcioProcedimento(param);

                if (CollectionUtils.isNotNullEmpty(relatorioProcedimentoPrestadorDTOList)) {
                    ConsorcioProcedimentoPrestadorDTO proxy = on(ConsorcioProcedimentoPrestadorDTO.class);

                    LinkedHashMap<String, Object> titleProperties = new LinkedHashMap<>();
                    titleProperties.put(bundle("prestador"), proxy.getConsorcioPrestadorServico().getConsorcioPrestador().getEmpresaPrestador().getDescricao());
                    titleProperties.put(bundle("cidade"), proxy.getConsorcioPrestadorServico().getConsorcioPrestador().getEmpresaPrestador().getCidade().getDescricao());
                    titleProperties.put(bundle("contato"), proxy.getConsorcioPrestadorServico().getConsorcioPrestador().getEmpresaPrestador().getTelefoneFormatado());
                    titleProperties.put(bundle("grupoProcedimento"), proxy.getConsorcioPrestadorServico().getConsorcioProcedimento().getProcedimento().getProcedimentoFormaOrganizacao().getRoProcedimentoSubGrupo().getRoGrupo().getDescricao());
                    titleProperties.put(bundle("codigoProcedimento"), proxy.getConsorcioPrestadorServico().getConsorcioProcedimento().getReferencia());
                    titleProperties.put(bundle("procedimento"), proxy.getConsorcioPrestadorServico().getConsorcioProcedimento().getProcedimento().getDescricao());
                    titleProperties.put(bundle("descricaoProcedimento"), proxy.getConsorcioPrestadorServico().getConsorcioProcedimento().getDescricaoProcedimento());
                    titleProperties.put(bundle("valor"), proxy.getValor());

                    Workbook workbook = ExportListToXls.getInstance().gerarPlanilha(relatorioProcedimentoPrestadorDTOList, titleProperties, "Procedimento por Prestador");
                    File file = ExportListToXls.getInstance().extrairPlanilhaParaArquivoXls(workbook, "procedimentosPrestador");

                    if (file != null) {
                        IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(file));
                        ajaxDownload.initiate(target, file.getName(), resourceStream);
                    }
                } else {
                    MessageUtil.warn(target, this, bundle("msgNaoHaRegistroaParaFiltrosAplicados"));
                }
            }
        });
        btnImprimeRelatorioXLS.add(new AttributeModifier("value",BundleManager.getString("gerar_relatorio_xls")));
    }

    private void eventoProcedimentoSubGrupo(AjaxRequestTarget target, ProcedimentoSubGrupo object) {
        if (target != null) {
            cbxProcedimentoFormaOrganizacao.limpar(target);
            cbxProcedimentoFormaOrganizacao.removeAllChoices();
        }
        if (object != null) {
            cbxProcedimentoFormaOrganizacao.addChoice(null, BundleManager.getString("todos"));
            List<ProcedimentoFormaOrganizacao> formaOrganizacoes = LoadManager.getInstance(ProcedimentoFormaOrganizacao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoFormaOrganizacao.PROP_ID, ProcedimentoFormaOrganizacaoPK.PROP_CODIGO_PROCEDIMENTO_GRUPO), object.getId().getCodigoGrupo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoFormaOrganizacao.PROP_ID, ProcedimentoFormaOrganizacaoPK.PROP_CODIGO_PROCEDIMENTO_SUB_GRUPO), object.getId().getCodigo()))
                    .start().getList();
            for (ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao1 : formaOrganizacoes) {
                cbxProcedimentoFormaOrganizacao.addChoice(procedimentoFormaOrganizacao1, procedimentoFormaOrganizacao1.getDescricaoFormatado());
            }
        }
    }

    private void eventoProcedimentoGrupo(AjaxRequestTarget target, ProcedimentoGrupo object) {
        if (target != null) {
            cbxProcedimentoSubGrupo.limpar(target);
            cbxProcedimentoSubGrupo.removeAllChoices();
        }

        if (object != null) {
            cbxProcedimentoSubGrupo.addChoice(null, BundleManager.getString("todos"));
            List<ProcedimentoSubGrupo> subGrupos = LoadManager.getInstance(ProcedimentoSubGrupo.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoSubGrupo.PROP_ID, ProcedimentoSubGrupoPK.PROP_CODIGO_GRUPO), object.getCodigo()))
                    .start().getList();
            for (ProcedimentoSubGrupo procedimentoSubGrupo1 : subGrupos) {
                cbxProcedimentoSubGrupo.addChoice(procedimentoSubGrupo1, procedimentoSubGrupo1.getDescricaoFormatado());
            }
        }
    }

    @Override
    public Class<RelatorioProcedimentoPrestadorDTOParam> getDTOParamClass() {
        return RelatorioProcedimentoPrestadorDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioProcedimentoPrestadorDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioProcedimentoPrestador(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioProcedimentoPrestador");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsorcioProcedimento.getTxtDescricao().getTextField();
    }
}