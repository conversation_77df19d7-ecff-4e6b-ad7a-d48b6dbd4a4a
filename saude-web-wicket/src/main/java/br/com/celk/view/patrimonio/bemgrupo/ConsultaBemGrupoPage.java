package br.com.celk.view.patrimonio.bemgrupo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.view.patrimonio.bemgrupo.customize.CustomizeConsultaBemGrupo;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.patrimonio.BemGrupo;
import static ch.lambdaj.Lambda.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 376
 */
@Private
public class ConsultaBemGrupoPage extends ConsultaPage<BemGrupo, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField("descricao"));
        
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        BemGrupo proxy = on(BemGrupo.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<BemGrupo>() {
            @Override
            public void customizeColumn(BemGrupo rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<BemGrupo>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemGrupo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroBemGrupoPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<BemGrupo>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemGrupo modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().update(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaBemGrupo()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(BemGrupo.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(BemGrupo.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroBemGrupoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaBemGrupo");
    }
}
