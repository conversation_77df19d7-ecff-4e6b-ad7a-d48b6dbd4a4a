package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.report.RelatorioPage;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioProdutoParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 29
 */
@Private
public class RelatorioFichaProdutoPage extends RelatorioPage<RelatorioProdutoParam> {

    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("empresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto", true));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(DropDownUtil.getSimNaoDropDown("exibirLotes"));
    }

    @Override
    public Class<RelatorioProdutoParam> getDTOParamClass() {
        return RelatorioProdutoParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioProdutoParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioProduto(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioFichaProduto");
    }

}
