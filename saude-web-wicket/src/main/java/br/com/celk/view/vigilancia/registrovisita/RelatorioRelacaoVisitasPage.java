package br.com.celk.view.vigilancia.registrovisita;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.motivovisita.AutoCompleteConsultaMotivoVista;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryRelatorioRelacaoVisitasDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 483
 */
@Private
public class RelatorioRelacaoVisitasPage extends RelatorioPage<QueryRelatorioRelacaoVisitasDTOParam> {

    @Override
    public void init(Form<QueryRelatorioRelacaoVisitasDTOParam> form) {
        QueryRelatorioRelacaoVisitasDTOParam proxy = on(QueryRelatorioRelacaoVisitasDTOParam.class);

        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new AutoCompleteConsultaMotivoVista(path(proxy.getMotivoVisita())));
        form.add(DropDownUtil.getNaoSimDropDown(path(proxy.getVerDescricaoVisita())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), QueryRelatorioRelacaoVisitasDTOParam.FormaApresentacao.values()));
        form.add(new PnlChoicePeriod(path(proxy.getPeriodo())));
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioRelacaoMotivo");
    }

    @Override
    public Class<QueryRelatorioRelacaoVisitasDTOParam> getDTOParamClass() {
        return QueryRelatorioRelacaoVisitasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(QueryRelatorioRelacaoVisitasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRelacaoVisita(param);
    }

}
