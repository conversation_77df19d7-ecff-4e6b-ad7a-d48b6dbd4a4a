package br.com.celk.view.vigilancia.autodepenalidade.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.report.vigilancia.autopenalidade.dto.RelatorioAutoPenalidadeDTOParam;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.autodepenalidade.autocomplete.AutoCompleteConsultaTipoPenalidade;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.grupoestabelecimento.autocomplete.AutoCompleteConsultaGrupoEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 619
 */
@Private
public class RelatorioAutoPenalidadePage extends RelatorioPage<RelatorioAutoPenalidadeDTOParam> {

    private DropDown dropDownTipoDenunciado;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private PnlAtividadeEstabelecimento pnlAtividadeEstabelecimento;
    private AutoCompleteConsultaGrupoEstabelecimento autoCompleteConsultaGrupoEstabelecimento;
    private DropDown dropDownSituacao;

    @Override
    public void init(Form<RelatorioAutoPenalidadeDTOParam> form) {
        RelatorioAutoPenalidadeDTOParam proxy = on(RelatorioAutoPenalidadeDTOParam.class);

        form.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getVigilanciaPessoa())));
        form.add(dropDownTipoDenunciado = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAutuado()), AutoPenalidade.TipoAutuado.values(), true, bundle("ambos")));
        form.add(autoCompleteConsultaGrupoEstabelecimento = new AutoCompleteConsultaGrupoEstabelecimento(path(proxy.getGrupoEstabelecimento())));
        form.add(pnlAtividadeEstabelecimento = new PnlAtividadeEstabelecimento(path(proxy.getAtividadeEstabelecimento())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new AutoCompleteConsultaTipoPenalidade(path(proxy.getTipoPenalidade())));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(dropDownSituacao = DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), AutoPenalidade.Situacao.values(), true));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioAutoPenalidadeDTOParam.FormaApresentacao.values()));
        enableCampos(null, null);
        dropDownTipoDenunciado.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCampos(target, (Long) dropDownTipoDenunciado.getComponentValue());
            }
        });
    }

    public void enableCampos(AjaxRequestTarget target, Long tipoDenunciado) {
        if (tipoDenunciado != null) {
            Boolean controle = false;
            if (AutoPenalidade.TipoAutuado.PESSOA.value().equals(tipoDenunciado)) {
                controle = false;
            } else {
                controle = true;
            }

            pnlAtividadeEstabelecimento.setEnabled(controle);
            autoCompleteConsultaGrupoEstabelecimento.setEnabled(controle);
            autoCompleteConsultaEstabelecimento.setEnabled(controle);
            autoCompleteConsultaVigilanciaPessoa.setEnabled(!controle);

        } else {
            pnlAtividadeEstabelecimento.setEnabled(true);
            autoCompleteConsultaGrupoEstabelecimento.setEnabled(true);
            autoCompleteConsultaEstabelecimento.setEnabled(true);
            autoCompleteConsultaVigilanciaPessoa.setEnabled(true);
        }
        if (target != null) {
            pnlAtividadeEstabelecimento.limpar(target);
            autoCompleteConsultaEstabelecimento.limpar(target);
            autoCompleteConsultaGrupoEstabelecimento.limpar(target);
            autoCompleteConsultaVigilanciaPessoa.limpar(target);
            target.add(autoCompleteConsultaVigilanciaPessoa);
            target.add(pnlAtividadeEstabelecimento);
            target.add(autoCompleteConsultaEstabelecimento);
            target.add(autoCompleteConsultaGrupoEstabelecimento);
        }
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioAutoPenalidade");
    }

    @Override
    public Class<RelatorioAutoPenalidadeDTOParam> getDTOParamClass() {
        return RelatorioAutoPenalidadeDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioAutoPenalidadeDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioAutoPenalidade(param);
    }

}
