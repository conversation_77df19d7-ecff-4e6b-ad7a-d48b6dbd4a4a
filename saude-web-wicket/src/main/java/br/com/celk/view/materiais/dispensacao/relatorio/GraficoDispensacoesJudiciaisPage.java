package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioGraficoDemonstrativoDispensacaoJudicialDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.materiais.grupoproduto.autocomplete.AutoCompleteConsultaGrupoProduto;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;


/**
 *
 * <AUTHOR>
 * Programa - 135
 */
@Private

public class GraficoDispensacoesJudiciaisPage extends RelatorioPage<RelatorioGraficoDemonstrativoDispensacaoJudicialDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private InputField txtPeriodoInicial;
    private InputField txtPeriodoFinal;
    private InputField txtQuantidade;
    
    private String periodoInicial;
    private String periodoFinal;
    
    private Integer periodo;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresas"));
        form.add(new AutoCompleteConsultaGrupoProduto("grupoProduto"));
        form.add(getDropDownPeriodo());
        form.add(txtPeriodoInicial = new RequiredInputField("periodoInicial", new PropertyModel(this, "periodoInicial")));
        form.add(txtPeriodoFinal = new RequiredInputField("periodoFinal", new PropertyModel(this, "periodoFinal")));
        form.add(txtQuantidade = new RequiredInputField("quantidade"));
        form.add(getDropDownTipoGrafico());
        form.add(getDropDownFormaApresentacao());
        
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        
        periodo = 12;
        DatePeriod datePeriod = new DatePeriod(Data.removeMeses(Data.getDataAtual(), periodo), Data.getDataAtual());
        periodoInicial = Data.formatarMesAno(datePeriod.getDataInicial());
        periodoFinal = Data.formatarMesAno(datePeriod.getDataFinal());
        txtPeriodoInicial.setEnabled(false);
        txtPeriodoFinal.setEnabled(false);
        txtQuantidade.setEnabled(false);
    }
    
    private DropDown getDropDownPeriodo(){
        DropDown dropDown = new DropDown("cbxPeriodo", new PropertyModel(this, "periodo"));
        
        dropDown.addChoice(3, BundleManager.getString("ultimos3meses"));
        dropDown.addChoice(6, BundleManager.getString("ultimos6meses"));
        dropDown.addChoice(12, BundleManager.getString("ultimoAno"));
        dropDown.addChoice(999, BundleManager.getString("outro"));
        
        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (periodo!=999) {
                    DatePeriod datePeriod = new DatePeriod(Data.removeMeses(Data.getDataAtual(), periodo), Data.getDataAtual());
                    periodoInicial = Data.formatarMesAno(datePeriod.getDataInicial());
                    periodoFinal = Data.formatarMesAno(datePeriod.getDataFinal());
                    txtPeriodoInicial.setEnabled(false);
                    txtPeriodoFinal.setEnabled(false);
                } else {
                    txtPeriodoInicial.setEnabled(true);
                    txtPeriodoFinal.setEnabled(true);
                }
                target.add(txtPeriodoInicial);
                target.add(txtPeriodoFinal);
            }
        });
        
        return dropDown;
    }
    
    private DropDown getDropDownTipoGrafico(){
        DropDown dropDown = new DropDown("tipoGrafico");
        
        dropDown.addChoice(RelatorioGraficoDemonstrativoDispensacaoJudicialDTOParam.TIPO_LINHAS, BundleManager.getString("linhas"));
        dropDown.addChoice(RelatorioGraficoDemonstrativoDispensacaoJudicialDTOParam.TIPO_BARRAS, BundleManager.getString("barras"));
        
        return dropDown;
    }
    
    private DropDown getDropDownFormaApresentacao(){
        DropDown dropDown = new DropDown("formaApresentacao");
        
        dropDown.addChoice(null, BundleManager.getString("geral"));
        dropDown.addChoice(DispensacaoMedicamentoItem.PROP_PRODUTO, BundleManager.getString("produto"));
        dropDown.addChoice(VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), BundleManager.getString("grupoSubGrupo"));

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                txtQuantidade.limpar(target);
                txtQuantidade.setEnabled(param.getFormaApresentacao()!=null);
            }
        });
        
        return dropDown;
    }

    @Override
    public Class<RelatorioGraficoDemonstrativoDispensacaoJudicialDTOParam> getDTOParamClass() {
        return RelatorioGraficoDemonstrativoDispensacaoJudicialDTOParam.class;
    }

    @Override
    public void customDTOParam(RelatorioGraficoDemonstrativoDispensacaoJudicialDTOParam param) {
        param.setTipoDado(RelatorioGraficoDemonstrativoDispensacaoJudicialDTOParam.TIPO_VALOR);
        param.setTipoPreco(EstoqueEmpresa.PROP_ULTIMO_PRECO);
    }

    @Override
    public DataReport getDataReport(RelatorioGraficoDemonstrativoDispensacaoJudicialDTOParam param) throws ReportException {
        if (param.getQuantidade()!=null && param.getQuantidade()==0L) {
            throw new ReportException(BundleManager.getString("quantidadeDeveSerMaiorZero"));
        }
        DatePeriod datePeriod = new DatePeriod();
        datePeriod.setDataInicial(Data.adjustRangeHour(Data.parserMounthYear(periodoInicial)).getDataInicial());
        datePeriod.setDataFinal(Data.adjustRangeHour(Data.adjustRangeDay(Data.parserMounthYear(periodoFinal)).getDataFinal()).getDataFinal());
        param.setDataInicial(datePeriod.getDataInicial());
        param.setDataFinal(datePeriod.getDataFinal());
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioGraficoDemonstrativoDispensacaoJudicial(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("graficoDispensacoesJudiciais");
    }

}
