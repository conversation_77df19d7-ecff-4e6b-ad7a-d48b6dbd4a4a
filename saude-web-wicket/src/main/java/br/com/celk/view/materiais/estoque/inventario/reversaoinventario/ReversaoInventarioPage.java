package br.com.celk.view.materiais.estoque.inventario.reversaoinventario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.asyncprocess.interfaces.IAsyncProcessNotification;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.materiais.estoque.inventario.reversaoinventario.customcolumn.StatusReversaoInventarioProcessoColumnPanel;
import br.com.celk.view.materiais.estoque.inventario.reversaoinventario.customize.CustomizeConsultaReversaoInventarioProcesso;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ControleInventarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import br.com.ksisolucoes.vo.entradas.estoque.ReversaoInventarioProcesso;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 * Programa - 393
 */
@Private
public class ReversaoInventarioPage extends BasePage implements IAsyncProcessNotification {

    private Form form;
    private InputField inventarioInput;
    private InputField dataInput;
    private InputField estabelecimentoInput;
    private InputField localizacaoInput;
    private PageableTable tblReversaoInventarioProcesso;
    private SubmitButton processarButton;
    private Inventario inventario;

    @Override
    protected void postConstruct() {
        instanciarFormulario();
        popularFormulario();
        add(form);
    }

    public void instanciarFormulario() {
        form = new Form("form", new CompoundPropertyModel(this));

        inventarioInput = new InputField("inventario", new Model());
        inventarioInput.setEnabled(false);

        estabelecimentoInput = new InputField("estabelecimento", new Model());
        estabelecimentoInput.setEnabled(false);

        dataInput = new InputField("data", new Model());
        dataInput.setEnabled(false);

        localizacaoInput = new InputField("localizacao", new Model());
        localizacaoInput.setEnabled(false);

        processarButton = new SubmitButton("processar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
                tblReversaoInventarioProcesso.update(target);
                limpar(target);
            }
        });

        processarButton.add(new Tooltip().setText("instrucoesReversaoInventario"));

        form.add(inventarioInput);
        form.add(estabelecimentoInput);
        form.add(dataInput);
        form.add(localizacaoInput);

        form.add(tblReversaoInventarioProcesso = new PageableTable("tblReversaoInventarioProcesso", getColumnsProcesso(), getPagerProviderProcesso()));
        tblReversaoInventarioProcesso.populate();

        form.add(processarButton);
    }

    private void limpar(AjaxRequestTarget target) {
        inventarioInput.limpar(target);
        estabelecimentoInput.limpar(target);
        dataInput.limpar(target);
        localizacaoInput.limpar(target);

        target.add(inventarioInput);
        target.add(estabelecimentoInput);
        target.add(dataInput);
        target.add(localizacaoInput);
    }

    public List<IColumn> getColumnsProcesso() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(ReversaoInventarioProcesso.class);

        columns.add(getCustomColumnProcesso());
        columns.add(columnFactory.createSortableColumn(bundle("dataGeracao"), VOUtils.montarPath(ReversaoInventarioProcesso.PROP_DATA_GERACAO)));
        columns.add(getCustomColumnStatus());

        return columns;
    }

    private CustomColumn getCustomColumnProcesso() {
        return new CustomColumn<ReversaoInventarioProcesso>() {

            @Override
            public Component getComponent(String componentId, final ReversaoInventarioProcesso rowObject) {
                return new RemoverActionColumnPanel(componentId) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(rowObject);
                        tblReversaoInventarioProcesso.update(target);
                    }
                };
            }
        };
    }

    public CustomColumn getCustomColumnStatus() {
        return new CustomColumn<ReversaoInventarioProcesso>() {

            @Override
            public Component getComponent(String componentId, ReversaoInventarioProcesso rowObject) {
                return new StatusReversaoInventarioProcessoColumnPanel(componentId, rowObject);
            }
        };
    }

    public IPagerProvider getPagerProviderProcesso() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaReversaoInventarioProcesso()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ReversaoInventarioProcesso.PROP_CODIGO, false);
            }
        };
    }

    private void salvar() throws ValidacaoException, DAOException {
        if (inventario != null && inventario.getEmpresa() != null) {
            List<Inventario> inv;
            if (inventario.getLocalizacaoEstrutura() != null) {
                inv = LoadManager.getInstance(Inventario.class)
                        .addProperties(new HQLProperties(Inventario.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(Inventario.PROP_EMPRESA, inventario.getEmpresa()))
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                new BuilderQueryCustom.QueryGroupAnd(
                                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                                new QueryCustom.QueryCustomParameter(Inventario.PROP_LOCALIZACAO_ESTRUTURA, BuilderQueryCustom.QueryParameter.IGUAL, inventario.getLocalizacaoEstrutura()))),
                                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                                new QueryCustom.QueryCustomParameter(Inventario.PROP_LOCALIZACAO_ESTRUTURA, BuilderQueryCustom.QueryParameter.IS_NULL))))))
                        .addParameter(new QueryCustom.QueryCustomParameter(Inventario.PROP_SITUACAO, Inventario.Situacao.ABERTO.value()))
                        .start().getList();
            } else {
                inv = LoadManager.getInstance(Inventario.class)
                        .addProperties(new HQLProperties(Inventario.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(Inventario.PROP_EMPRESA, inventario.getEmpresa()))
                        .addParameter(new QueryCustom.QueryCustomParameter(Inventario.PROP_SITUACAO, Inventario.Situacao.ABERTO.value()))
                        .start().getList();
            }
            if (CollectionUtils.isNotNullEmpty(inv) && inv.size() > 0) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_e_possivel_movimentar_estoque_com_inventario_aberto_para_empresa_X", inventario.getEmpresa().getDescricaoFormatado()));
            }
        }
        List<Empresa> lstEmpresas = new ArrayList<Empresa>();
        lstEmpresas.add(inventario.getEmpresa());
        BOFactoryWicket.getBO(ControleInventarioFacade.class).EnviarReversaoInventarioProcessoFila(lstEmpresas, inventario.getDataInventario(), inventario);

    }

    @Override
    public String getTituloPrograma() {
        return bundle("reversaoInventario");
    }

    @Override
    public void notifyProcess(AjaxRequestTarget target, AsyncProcess event) {
        tblReversaoInventarioProcesso.update(target);
    }

    private void popularFormulario() {
        final Inventario inventario = buscarUltimoInventarioProcessado();

        if (inventario != null) {
            if (inventario.getDescricaoInventario() != null) {
                inventarioInput.setComponentValue(inventario.getDescricaoInventario());
            }
            if (inventario.getEmpresa() != null && inventario.getEmpresa().getDescricaoFormatado() != null) {
                estabelecimentoInput.setComponentValue(inventario.getEmpresa().getDescricaoFormatado());
            }
            if (inventario.getDataInventario() != null) {
                dataInput.setComponentValue(inventario.getDataInventario());
            }
            if (inventario.getLocalizacaoEstrutura() != null && inventario.getLocalizacaoEstrutura().getDescricaoEstrutura() != null) {
                localizacaoInput.setComponentValue(inventario.getLocalizacaoEstrutura().getDescricaoEstrutura());
            }
            this.inventario = inventario;
        }
    }

    private Inventario buscarUltimoInventarioProcessado() {
        return LoadManager.getInstance(Inventario.class)
                .addProperties(new HQLProperties(Inventario.class).getProperties())
                .addProperties(new HQLProperties(LocalizacaoEstrutura.class, Inventario.PROP_LOCALIZACAO_ESTRUTURA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Inventario.PROP_SITUACAO, Inventario.Situacao.PROCESSADO.value()))
                .addSorter(new QueryCustom.QueryCustomSorter(Inventario.PROP_CODIGO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .setMaxResults(1)
                .start()
                .getVO();
    }

}
