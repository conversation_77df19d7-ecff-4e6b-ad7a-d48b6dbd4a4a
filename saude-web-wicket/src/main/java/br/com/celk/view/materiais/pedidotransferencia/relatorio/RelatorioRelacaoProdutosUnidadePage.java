package br.com.celk.view.materiais.pedidotransferencia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoProdutosUnidadeDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.List;


/**
 *
 * <AUTHOR>
 * Programa - 116
 */
@Private

public class RelatorioRelacaoProdutosUnidadePage extends RelatorioPage<RelatorioRelacaoProdutosUnidadeDTOParam> {
        
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresaList"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownOrdenacao());
        form.add(DropDownUtil.getSimNaoDropDown("exibirQtdPadrao"));
        form.add(getDropDownTipoProduto());
        form.add(DropDownUtil.getNaoSimDropDown("iniciarNovaPaginaUnidade"));

        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoProdutosUnidadeDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoProdutosUnidadeDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioRelacaoProdutosUnidade(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoProdutosUnidade");
    }
    
    public DropDown getDropDownOrdenacao(){
        DropDown dropDown = new DropDown("ordenacao");
        dropDown.addChoice(Produto.PROP_CODIGO, BundleManager.getString("codigo"));
        dropDown.addChoice(Produto.PROP_DESCRICAO, BundleManager.getString("descricao"));
        
        return dropDown;
    }
    
    public DropDown getDropDownTipoProduto(){
        DropDown dropDown = new DropDown("tipoProduto");
        dropDown.addChoice(null, BundleManager.getString("todos"));
        dropDown.addChoice(TipoProduto.TIPO_PRODUTO_MATERIAL, BundleManager.getString("material"));
        dropDown.addChoice(TipoProduto.TIPO_PRODUTO_MEDICAMENTO, BundleManager.getString("medicamento"));
        dropDown.addChoice(TipoProduto.TIPO_PRODUTO_VACINA, BundleManager.getString("vacina"));
        
        return dropDown;
    }
    
    public DropDown getDropDownFormaApresentacao(){
        DropDown dropDown = new DropDown("formaApresentacao");
        dropDown.addChoice(new Long(ReportProperties.GERAL), BundleManager.getString("geral"));
        dropDown.addChoice(new Long(ReportProperties.AGRUPAR_GRUPO), BundleManager.getString("grupo"));
        
        return dropDown;
    }
    
    private DropDown<SubGrupo> getDropDownSubGrupo(){
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
            
        }
        
        return this.dropDownSubGrupo;
    }
    
    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto!=null) {
                            List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_RO_GRUPO_PRODUTO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                    .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                    .start().getList();

                            if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                                dropDownSubGrupo.removeAllChoices();
                                dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                                for (SubGrupo subGrupo : subGrupos) {
                                    dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                                }
                            }
                            param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });
            
                List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                        .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                        .start().getList();

                dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));
                
                if (CollectionUtils.isNotNullEmpty(grupos)) {
                    for (GrupoProduto grupoProduto : grupos) {
                        dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                    }
                }
        }
        return this.dropDownGrupoProduto;
    }
}
