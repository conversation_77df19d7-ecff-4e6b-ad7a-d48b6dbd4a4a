package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.DlgInformarTipoProcedimento;
import br.com.celk.view.vacina.produtovacina.autocomplete.AutoCompleteConsultaVacinaAplicada;
import br.com.celk.view.vacina.vacinaaplicacao.AplicarVacinaForaEsquemaVacinalPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.entradas.estoque.FabricanteMedicamento;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.vacina.Calendario;
import br.com.ksisolucoes.vo.vacina.ProdutoVacina;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoCovid19;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 960
 */
@Private
public class FichaInvestigacaoAgravoPage extends BasePage {

    private Form<FichaInvestigacaoAgravoDTO> form;
    private RegistroAgravo registroAgravo;

    private AutoCompleteConsultaVacinaAplicada autoCompleteConsultaVacinaAplicada;
    private AbstractAjaxLink btnAplicarVacina;
    private InputField txtViaAdministracao;
    private InputField txtLocalAplicacao;
    private InputField txtIndicacao;
    private Table<InvestigacaoVacinaAplicada> tableVacinaAplicada;
    private List<InvestigacaoVacinaAplicada> vacinaAplicacaoList = new ArrayList<InvestigacaoVacinaAplicada>();
    private Table<InvestigacaoEapvPresente> tableEapv;
    private List<InvestigacaoEapvPresente> eapvList = new ArrayList<InvestigacaoEapvPresente>();
    private RadioButtonGroup radioGroupClassificacao;
    private RadioButtonGroup radioGroupEAPVPresente;
    private RadioButtonGroup radioGroupDoencas;
    private WebMarkupContainer containerEapv;
    private DateChooser txtDataOcorencia;
    private InputField txtConduta;
    private InputField txtImunobiologico;
    private InputField txtEventoAdversoPosVacinacao;

    private WebMarkupContainer containerDoencas;

    private CompoundPropertyModel<InvestigacaoEapvPresente> modelInvestigacaoEapvPresente;
    private CheckBoxLongValue checkBoxDoencaAids;
    private CheckBoxLongValue checkBoxDoencaAlergiaMedicamento;
    private CheckBoxLongValue checkBoxDoencaAlergiaAlimentar;
    private CheckBoxLongValue checkBoxDoencaDiabetes;
    private CheckBoxLongValue checkBoxDoencaAutoimune;
    private CheckBoxLongValue checkBoxDoencaCardiaca;
    private CheckBoxLongValue checkBoxDoencaHepatica;
    private CheckBoxLongValue checkBoxDoencaNeurologicaPsiquiatrica;
    private CheckBoxLongValue checkBoxDoencaPulmonar;
    private CheckBoxLongValue checkBoxDoencaRenal;
    private CheckBoxLongValue checkBoxDoencaOutras;
    private List<CheckBoxLongValue> lstCheckBoxDoencas = new ArrayList<CheckBoxLongValue>();
    private InputField txtDoencasOutras;

    private WebMarkupContainer containerMedicacao;
    private RadioButtonGroup radioGroupMedicacao;
    private CompoundPropertyModel<InvestigacaoMedicacaoUso> modelInvestigacaoMedicacaoUso;
    private Table<InvestigacaoMedicacaoUso> tableMedicacaoUso;
    private List<InvestigacaoMedicacaoUso> investigacaoMedicacaoUsoList = new ArrayList<InvestigacaoMedicacaoUso>();
    private DropDown dropDownTipoMedicacao;
    private InputField txtDescricaoTipoOutros;
    private InputField txtNomeGenerico;
    private InputField txtViaAdministracaoMedicacao;
    private DateChooser txtDataInicio;
    private CheckBoxLongValue chkUsoContinuo;
    private DateChooser txtDataTermino;

    private RadioButtonGroup radioGroupTransfusao;
    private DateChooser txtDataTransfusaoUltimoMes;

    private RadioButtonGroup radioGroupConvulsao;

    private RadioButtonGroup radioGroupViajou;
    private WebMarkupContainer containerViajou;
    private DateChooser txtDataInicioViagem;
    private DateChooser txtDataTerminoViagem;
    private InputField txtPaisViagem;
    private InputField txtLovalViagem;
    private InputField txtUfViagem;
    private InputField txtMunicipioViagem;

    private WebMarkupContainer containerManifestacoesLocais;
    private CompoundPropertyModel<InvestigacaoManifestacoes> modelManifestacoesLocais;
    private Table<InvestigacaoManifestacoes> tableManifestacoesLocais;
    private List<InvestigacaoManifestacoes> investigacaoManifestacoesLocaisList = new ArrayList<InvestigacaoManifestacoes>();
    private DropDown dropDownTipoManifestacoesLocais;
    private InputField txtDescricaoTipoOutrosManifestacoesLocais;
    private DateChooser txtDataInicioManifestacoesLocais;
    private DateChooser txtDataTerminoManifestacoesLocais;
    private InputField txtTempoInicioManifestacoesLocais;
    private CheckBoxLongValue chkEmAcompanhamentoManifestacoesLocais;

    private WebMarkupContainer containerManifestacoesPeleMucosa;
    private CompoundPropertyModel<InvestigacaoManifestacoes> modelManifestacoesPeleMucosa;
    private Table<InvestigacaoManifestacoes> tableManifestacoesPeleMucosa;
    private List<InvestigacaoManifestacoes> investigacaoManifestacoesPeleMucosaList = new ArrayList<InvestigacaoManifestacoes>();
    private DropDown dropDownTipoManifestacoesPeleMucosa;
    private InputField txtDescricaoTipoOutrosManifestacoesPeleMucosa;
    private DateChooser txtDataInicioManifestacoesPeleMucosa;
    private DateChooser txtDataTerminoManifestacoesPeleMucosa;
    private InputField txtTempoInicioManifestacoesPeleMucosa;
    private CheckBoxLongValue chkEmAcompanhamentoManifestacoesPeleMucosa;

    private WebMarkupContainer containerManifestacoesCardiovasculares;
    private CompoundPropertyModel<InvestigacaoManifestacoes> modelManifestacoesCardiovasculares;
    private Table<InvestigacaoManifestacoes> tableManifestacoesCardiovasculares;
    private List<InvestigacaoManifestacoes> investigacaoManifestacoesCardiovascularesList = new ArrayList<InvestigacaoManifestacoes>();
    private DropDown dropDownTipoManifestacoesCardiovasculares;
    private InputField txtDescricaoTipoOutrosManifestacoesCardiovasculares;
    private DateChooser txtDataInicioManifestacoesCardiovasculares;
    private DateChooser txtDataTerminoManifestacoesCardiovasculares;
    private InputField txtTempoInicioManifestacoesCardiovasculares;
    private CheckBoxLongValue chkEmAcompanhamentoManifestacoesCardiovasculares;

    private WebMarkupContainer containerManifestacoesRespiratoria;
    private CompoundPropertyModel<InvestigacaoManifestacoes> modelManifestacoesRespiratoria;
    private Table<InvestigacaoManifestacoes> tableManifestacoesRespiratoria;
    private List<InvestigacaoManifestacoes> investigacaoManifestacoesRespiratoriaList = new ArrayList<InvestigacaoManifestacoes>();
    private DropDown dropDownTipoManifestacoesRespiratoria;
    private InputField txtDescricaoTipoOutrosManifestacoesRespiratoria;
    private DateChooser txtDataInicioManifestacoesRespiratoria;
    private DateChooser txtDataTerminoManifestacoesRespiratoria;
    private InputField txtTempoInicioManifestacoesRespiratoria;
    private CheckBoxLongValue chkEmAcompanhamentoManifestacoesRespiratoria;

    private WebMarkupContainer containerManifestacoesNeurologicas;
    private CompoundPropertyModel<InvestigacaoManifestacoes> modelManifestacoesNeurologicas;
    private Table<InvestigacaoManifestacoes> tableManifestacoesNeurologicas;
    private List<InvestigacaoManifestacoes> investigacaoManifestacoesNeurologicasList = new ArrayList<InvestigacaoManifestacoes>();
    private DropDown dropDownTipoManifestacoesNeurologicas;
    private InputField txtDescricaoTipoOutrosManifestacoesNeurologicas;
    private DateChooser txtDataInicioManifestacoesNeurologicas;
    private DateChooser txtDataTerminoManifestacoesNeurologicas;
    private InputField txtTempoInicioManifestacoesNeurologicas;
    private CheckBoxLongValue chkEmAcompanhamentoManifestacoesNeurologicas;

    private WebMarkupContainer containerManifestacoesGastrintestinais;
    private CompoundPropertyModel<InvestigacaoManifestacoes> modelManifestacoesGastrintestinais;
    private Table<InvestigacaoManifestacoes> tableManifestacoesGastrintestinais;
    private List<InvestigacaoManifestacoes> investigacaoManifestacoesGastrintestinaisList = new ArrayList<InvestigacaoManifestacoes>();
    private DropDown dropDownTipoManifestacoesGastrintestinais;
    private InputField txtDescricaoTipoOutrosManifestacoesGastrintestinais;
    private DateChooser txtDataInicioManifestacoesGastrintestinais;
    private DateChooser txtDataTerminoManifestacoesGastrintestinais;
    private InputField txtTempoInicioManifestacoesGastrintestinais;
    private CheckBoxLongValue chkEmAcompanhamentoManifestacoesGastrintestinais;

    private WebMarkupContainer containerManifestacoesOutras;
    private CompoundPropertyModel<InvestigacaoManifestacoes> modelManifestacoesOutras;
    private Table<InvestigacaoManifestacoes> tableManifestacoesOutras;
    private List<InvestigacaoManifestacoes> investigacaoManifestacoesOutrasList = new ArrayList<InvestigacaoManifestacoes>();
    private DropDown dropDownTipoManifestacoesOutras;
    private InputField txtDescricaoTipoOutrosManifestacoesOutras;
    private DateChooser txtDataInicioManifestacoesOutras;
    private DateChooser txtDataTerminoManifestacoesOutras;
    private InputField txtTempoInicioManifestacoesOutras;
    private CheckBoxLongValue chkEmAcompanhamentoManifestacoesOutras;

    private RadioButtonGroup radioGroupAtendimento;
    private RadioButtonGroup radioGroupTipoAtendimentoMedico;
    private WebMarkupContainer containerAtendimento;
    private InputField txCNESHospital;
    private InputField txtNomeHospital;
    private DateChooser txtDataInternacao;
    private DateChooser txtDataAlta;
    private InputField txtMunicipioAtendimento;
    private InputField txtUfAtendimento;

    private RadioButtonGroup radioGroupEvolucao;

    private RadioButtonGroup radioGroupClassificacaoFinal;

    private RadioButtonGroup radioGroupErrosImunizacao;
    private InputArea txtDescricaoOutrosErrosImunizacao;

    private RadioButtonGroup radioGroupCondutaErrosImunizacao;
    private WebMarkupContainer containerDoseConsideradaInvalida;
    private RadioButtonGroup radioGroupDoseConsideradaInvalida;
    private InputArea txtDescricaoOutrosCondutaErroImunizacaoItem;

    private WebMarkupContainer containerHemograma;
    private CompoundPropertyModel<InvestigacaoAgravoHemograma> modelHemograma;
    private Table<InvestigacaoAgravoHemograma> tableHemograma;
    private List<InvestigacaoAgravoHemograma> investigacaoHemogramaList = new ArrayList<InvestigacaoAgravoHemograma>();
    private DateChooser txtDataColeta;
    private InputField txtHemacias;
    private InputField txtHemoglobina;
    private InputField txtHematocrito;
    private InputField txtPlaquetas;
    private InputField txtBastoes;
    private InputField txtNeutrofilos;
    private InputField txtLinfocitos;
    private InputField txtLeucocitos;
    private InputField txtEosinofitos;
    private InputField txtMonocitos;

    private WebMarkupContainer containerBioquimica;
    private CompoundPropertyModel<InvestigacaoAgravoBioquimica> modelBioquimica;
    private Table<InvestigacaoAgravoBioquimica> tableBioquimica;
    private List<InvestigacaoAgravoBioquimica> investigacaoBioquimicaList = new ArrayList<InvestigacaoAgravoBioquimica>();
    private DateChooser txtDataColetaBioquimica;
    private InputField txtBd;
    private InputField txtBi;
    private InputField txtBt;
    private InputField txtUreia;
    private InputField txtCreatina;
    private InputField txtAstTgo;
    private InputField txtAltTg;
    private InputField txtGgt;
    private InputField txtFa;
    private InputField txtRni;
    private InputField txtPt;
    private InputField txtPtt;

    private WebMarkupContainer containerPuncaoLombar;
    private CompoundPropertyModel<InvestigacaoAgravoPuncaoLombar> modelPuncaoLombar;
    private Table<InvestigacaoAgravoPuncaoLombar> tablePuncaoLombar;
    private List<InvestigacaoAgravoPuncaoLombar> investigacaoPuncaoLombarList = new ArrayList<InvestigacaoAgravoPuncaoLombar>();
    private DateChooser txtDataColetaPuncaoLombar;
    private InputField txtLeucocitosPl;
    private InputField txtNeutrofilosPl;
    private InputField txtLinfocitosPl;
    private InputField txtGlicosePl;
    private InputField txtProteinasPl;
    private InputField txtBacterioscopiaPl;
    private InputField txtCulturaLiquorPl;

    private WebMarkupContainer containerUrina;
    private CompoundPropertyModel<InvestigacaoAgravoUrina> modelUrina;
    private Table<InvestigacaoAgravoUrina> tableUrina;
    private List<InvestigacaoAgravoUrina> investigacaoUrinaList = new ArrayList<InvestigacaoAgravoUrina>();
    private DropDown dropDownTipoUrina;
    private DateChooser txtDataColetaUrina;
    private InputField txtDescricaoUrina;

    private WebMarkupContainer containerDeteccaoViral;
    private CompoundPropertyModel<InvestigacaoAgravoDeteccaoViral> modelDeteccaoViral;
    private Table<InvestigacaoAgravoDeteccaoViral> tableDeteccaoViral;
    private List<InvestigacaoAgravoDeteccaoViral> investigacaoDeteccaoViralList = new ArrayList<InvestigacaoAgravoDeteccaoViral>();
    private DateChooser txtDataColetaDeteccaoViral;
    private DropDown dropDownAmostraDeteccaoViral;
    private InputField txtDescricaoOutrosAmostraDeteccaoViral;
    private InputField txtResultadoDeteccaoViral;

    private WebMarkupContainer containerImunologia;
    private CompoundPropertyModel<InvestigacaoAgravoImunologia> modelImunologia;
    private Table<InvestigacaoAgravoImunologia> tableImunologia;
    private List<InvestigacaoAgravoImunologia> investigacaoImunologiaList = new ArrayList<InvestigacaoAgravoImunologia>();
    private DropDown dropDownAgravoImunologia;
    private InputField txtDescricaoOutrosAgravoImunologia;
    private DateChooser txtDataColetaSorologiaImunologia;
    private DateChooser txtDataColetaPcrImunologia;
    private InputField txtSangueSorologiaImunologia;
    private InputField txtSanguePcrImunologia;
    private InputField txtLiquorSorologiaImunologia;
    private InputField txtLiquorPcrImunologia;

    private WebMarkupContainer containerAdicional;
    private CompoundPropertyModel<InvestigacaoAgravoAdicional> modelAdicional;
    private Table<InvestigacaoAgravoAdicional> tableAdicional;
    private List<InvestigacaoAgravoAdicional> investigacaoAdicionalList = new ArrayList<InvestigacaoAgravoAdicional>();
    private DateChooser txtDataColetaAdicional;
    private DropDown dropDownExameAdicional;
    private InputField txtDescricaoOutrosExameAdicional;
    private InputField txtResultadoAdicional;

    private WebMarkupContainer containerOutros;
    private CompoundPropertyModel<InvestigacaoAgravoOutros> modelOutros;
    private Table<InvestigacaoAgravoOutros> tableOutros;
    private List<InvestigacaoAgravoOutros> investigacaoOutrosList = new ArrayList<InvestigacaoAgravoOutros>();
    private DateChooser txtDataColetaOutros;
    private InputField txtMacroscopiaOutros;
    private InputField txtMicroscopiaOutros;
    private InputField txtAnatomopatologicoOutros;

    private WebMarkupContainer containerEncerramentoFinal;
    private CompoundPropertyModel<InvestigacaoEncerramentoFinal> modelEncerramentoFinal;
    private Table<InvestigacaoEncerramentoFinal> tableEncerramentoFinal;
    private List<InvestigacaoEncerramentoFinal> investigacaoEncerramentoFinalList = new ArrayList<InvestigacaoEncerramentoFinal>();
    private DropDown<VacinaAplicacao> dropDownVacinaAplicadaEncerramentoFinal;
    private InputField txtDiagnostico;
    private DropDown dropDownConduta;
    private InputField txtDescricaoOutrasCondutas;
    private DropDown dropDownCausalidadesUm;
    private DropDown dropDownCausalidadesDois;
    private DropDown dropDownCausalidadesTres;
    private DropDown dropDownCausalidadesQuatro;
    private InputField txtDescricaoOutrasCausalidades;
    private DlgConfirmacaoSimNao dlgConfirmacaoEncerramento;
    private VoltarButton btnVoltar;

    private CheckBoxLongValue checkBoxCriancaAleitamento;
    private RequiredDateChooser dchDataPrimeirosSintomas;
    private InputArea inputAreaEventoAdverso;

    private DropDown<Long> dropDownGestante;
    private DropDown dropDownIdadeGestacional;
    private DropDown dropDownMulherAmamentando;
    private WebMarkupContainer containerIdadeGestacional;

    public FichaInvestigacaoAgravoPage(RegistroAgravo registroAgravo) {
        if (ApplicationSession.get().getSessaoAplicacao().getUsuario().getProfissional() != null) {
            registroAgravo.setProfissionalInvestigacao(ApplicationSession.get().getSessaoAplicacao().getUsuario().getProfissional());
        } else {
            registroAgravo.setProfissionalInvestigacao(registroAgravo.getProfissional());
        }
        this.registroAgravo = registroAgravo;
        init();
    }

    private void init() {
        FichaInvestigacaoAgravoDTO proxy = on(FichaInvestigacaoAgravoDTO.class);

        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getCodigoNotificacao())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getDescricaoSocialFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getDataNascimento())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getSexoFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getNomeMae())));

        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getTelefoneFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getTelefone2Formatado())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getTelefone3())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getTelefone4())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getCelularFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadsus().getEmail())));

        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getDataRegistro())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getDescricaoGestante())));
        getForm().add(new DateChooser(path(proxy.getRegistroAgravo().getDataEncerramento())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getDescricaoStatus())));
        getForm().add(dchDataPrimeirosSintomas = new RequiredDateChooser(path(proxy.getRegistroAgravo().getDataPrimeirosSintomas())));


        dchDataPrimeirosSintomas.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dchDataPrimeirosSintomas.setLabel((new Model(bundle("dataPrimeirosSintomas"))));
        if (registroAgravo != null && registroAgravo.getUsuarioCadsus() != null && registroAgravo.getUsuarioCadsus().getDataNascimento() != null) {
            dchDataPrimeirosSintomas.getData().setMaxDate(new DateOption(registroAgravo.getUsuarioCadsus().getDataNascimento()));
        }


        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getUsuarioCadastro().getNome())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getCid().getDescricaoFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getProfissionalInvestigacao().getNome())));
        getForm().add(new DisabledInputField(path(proxy.getRegistroAgravo().getEmpresa().getDescricao())));
        getForm().add(new DisabledInputArea(path(proxy.getRegistroAgravo().getObservacao())));

        grupoVacina(proxy);

        grupoDadosGestante(proxy);

        idadeGestacional(proxy);

        dadosAleitamento(proxy);

        getForm().add(inputAreaEventoAdverso = new InputArea(path(proxy.getInvestigacaoAgravo().getEventoAdverso())));

        grupoClassificacao(proxy);

        grupoEAPV(proxy);

        grupoDoencas(proxy);

        grupoMedicacaoUso(proxy);

        grupoTransfusao(proxy);

        grupoHistoriaPrevia(proxy);

        grupoViajou(proxy);

        grupoManifestacoesLocais();

        grupoManifestacoesPeleMucosa();

        grupoManifestacoesCardiovasculares();

        grupoManifestacoesRespiratoria();

        grupoManifestacoesNeurologicas();

        grupoManifestacoesGastrintestinais();

        grupoManifestacoesClinicasOutras();

        grupoAtendimentoMedico(proxy);

        getForm().add(new InputArea(path(proxy.getInvestigacaoAgravo().getInformacoesComplementares())));

        getForm().add(new InputArea(path(proxy.getInvestigacaoAgravo().getDiagnostico())));

        grupoEvolucao(proxy);

        grupoClassificacaoFinal(proxy);

        grupoErrosIMunizacao(proxy);

        grupoCondutaFrenteErrosImunizacao(proxy);

        grupoEncerramentoFinal();

        grupoHemograma();

        grupoBioquimica();

        grupoPuncaoLombar();

        grupoUrina();

        grupoDeteccaoViral();

        grupoImunologia();

        grupoAdicional();

        grupoOutros();

        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        form.add(new SubmitButton("btnEncerrar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                encerrar(target);
            }
        }));

        carregaInformacoesFichaInvestigacao();

        defineCamposObrigatorios(null);

        if (RegistroAgravo.Status.INVESTIGACAO_CONCLUIDA.value().equals(registroAgravo.getStatus()) && !registroAgravo.isPermiteAlterar()) {
            ComponentUtils.disableComponentsContainer(form);
            tableVacinaAplicada.setEnabled(false);
            tableHemograma.setEnabled(false);
            tableManifestacoesRespiratoria.setEnabled(false);
            tableManifestacoesLocais.setEnabled(false);
            tableMedicacaoUso.setEnabled(false);
            tableEapv.setEnabled(false);
            tableAdicional.setEnabled(false);
            tableBioquimica.setEnabled(false);
            tableDeteccaoViral.setEnabled(false);
            tableEncerramentoFinal.setEnabled(false);
            tableImunologia.setEnabled(false);
            tableManifestacoesCardiovasculares.setEnabled(false);
            tableManifestacoesGastrintestinais.setEnabled(false);
            tableUrina.setEnabled(false);
            tablePuncaoLombar.setEnabled(false);
            tableOutros.setEnabled(false);
            tableManifestacoesPeleMucosa.setEnabled(false);
            tableManifestacoesNeurologicas.setEnabled(false);
            tableManifestacoesOutras.setEnabled(false);
        }
        getForm().add(new VoltarButton("btnVoltar"));

        add(getForm());
    }

    private void dadosAleitamento(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(dropDownMulherAmamentando = DropDownUtil.getNaoSimLongDropDown(path(proxy.getInvestigacaoAgravo().getFlagMulherAmamentando()), true, false));

        getForm().add(checkBoxCriancaAleitamento = new CheckBoxLongValue(path(proxy.getInvestigacaoAgravo().getFlagCriancaAleitamento())));
        if (getForm().getModel().getObject().getRegistroAgravo() != null && RepositoryComponentDefault.NAO_LONG.equals(getForm().getModel().getObject().getRegistroAgravo().getGestante())) {
            checkBoxCriancaAleitamento.setEnabled(false);
        }
    }

    private void grupoDadosGestante(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(dropDownGestante = DropDownUtil.getNaoSimLongDropDown(path(proxy.getRegistroAgravo().getGestante()), false, true));
        dropDownGestante.setComponentValue(getForm().getModel().getObject().getRegistroAgravo().getGestante());
        dropDownGestante.setOutputMarkupPlaceholderTag(true);
        dropDownGestante.addAjaxUpdateValue();
        dropDownGestante.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dropDownIdadeGestacional.setEnabled(RepositoryComponentDefault.SIM_LONG.equals(dropDownGestante.getComponentValue()));
                if (RepositoryComponentDefault.SIM_LONG.equals(dropDownGestante.getComponentValue())) {
                    checkBoxCriancaAleitamento.setEnabled(true);
                }
                target.add(containerIdadeGestacional);
            }

            @Override
            public void onConfigure(Component component) {
                dropDownIdadeGestacional.setEnabled(RepositoryComponentDefault.SIM_LONG.equals(dropDownGestante.getComponentValue()));
            }
        });
    }

    private void idadeGestacional(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(containerIdadeGestacional = new WebMarkupContainer("containerIdadeGestacional"));
        containerIdadeGestacional.setOutputMarkupPlaceholderTag(true);
        containerIdadeGestacional.add(dropDownIdadeGestacional = DropDownUtil.getIEnumDropDown(path(proxy.getRegistroAgravo().getIdadeGestacional()), RegistroAgravo.IdadeGestacional.values()));
    }

    private void defineCamposObrigatorios(AjaxRequestTarget target) {
        if (CollectionUtils.isEmpty(vacinaAplicacaoList)) {
            txtIndicacao.addRequiredClass();
            txtLocalAplicacao.addRequiredClass();
            txtViaAdministracao.addRequiredClass();
        }
        if (target != null) {
            target.add(txtIndicacao);
            target.add(txtLocalAplicacao);
            target.add(txtViaAdministracao);
        }
        inputAreaEventoAdverso.addRequiredClass();
        dropDownIdadeGestacional.addRequiredClass();
    }

    private void grupoVacina(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(tableVacinaAplicada = new Table("tableVacinaAplicada", getColumns(), getCollectionProvider()));
        getForm().add(autoCompleteConsultaVacinaAplicada = new AutoCompleteConsultaVacinaAplicada(path(proxy.getInvestigacaoVacinaAplicada().getVacinaAplicacao())));
        autoCompleteConsultaVacinaAplicada.setOutputMarkupId(true);
        form.add(btnAplicarVacina = new AbstractAjaxLink("btnAplicarVacina") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                FichaInvestigacaoAgravoPage.this.setResponsePage(new AplicarVacinaForaEsquemaVacinalPage(registroAgravo.getUsuarioCadsus(), registroAgravo));
            }
        });
        autoCompleteConsultaVacinaAplicada.setObjetos(registroAgravo.getUsuarioCadsus(), (getForm().getModel().getObject().getInvestigacaoAgravo() != null ? getForm().getModel().getObject().getInvestigacaoAgravo().getCodigo() : null));
        getForm().add(txtViaAdministracao = new InputField(path(proxy.getInvestigacaoVacinaAplicada().getViaAdministracao())));
        getForm().add(txtLocalAplicacao = new InputField(path(proxy.getInvestigacaoVacinaAplicada().getLocalAplicacao())));
        getForm().add(txtIndicacao = new InputField(path(proxy.getInvestigacaoVacinaAplicada().getIndicacao())));

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarVacinaAplicada(target);
            }
        });
    }

    private void grupoClassificacao(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupClassificacao = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getClassificacao())));
        radioGroupClassificacao.add(new AjaxRadio("naoGraveEANG",
                new Model(InvestigacaoAgravo.TipoClassificacao.NAO_GRAVE_EANG.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
            }
        });
        radioGroupClassificacao.add(new AjaxRadio("graveEAG",
                new Model(InvestigacaoAgravo.TipoClassificacao.GRAVE_EAG.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
            }
        });
        radioGroupClassificacao.add(new AjaxRadio("erroImunizacaoEI",
                new Model(InvestigacaoAgravo.TipoClassificacao.ERRO_IMUNIZACAO_EI.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
            }
        });
    }

    private void grupoEAPV(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupEAPVPresente = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getEapvPresente())));
        radioGroupEAPVPresente.add(new AjaxRadio("simEAPV", new Model(InvestigacaoAgravo.TipoSelecionado.SIM.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerEapv(target, true);
            }
        });

        radioGroupEAPVPresente.add(new AjaxRadio("naoEAPV", new Model(InvestigacaoAgravo.TipoSelecionado.NAO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerEapv(target, false);
            }
        });

        radioGroupEAPVPresente.add(new AjaxRadio("ignoradoEAPV", new Model(InvestigacaoAgravo.TipoSelecionado.IGNORADO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerEapv(target, false);
            }
        });

        InvestigacaoEapvPresente proxyEapv = on(InvestigacaoEapvPresente.class);
        containerEapv = new WebMarkupContainer("containerEapv", modelInvestigacaoEapvPresente = new CompoundPropertyModel(new InvestigacaoEapvPresente()));
        containerEapv.setOutputMarkupPlaceholderTag(true);
        containerEapv.setEnabled(false);
        getForm().add(containerEapv);

        containerEapv.add(txtDataOcorencia = new DateChooser(path(proxyEapv.getDataOcorrencia())));
        containerEapv.add(txtConduta = new InputField(path(proxyEapv.getConduta())));
        containerEapv.add(txtImunobiologico = new InputField(path(proxyEapv.getImunobiologico())));
        containerEapv.add(txtEventoAdversoPosVacinacao = new InputField(path(proxyEapv.getEventoAdversoPosVacinacao())));
        containerEapv.add(new AbstractAjaxButton("btnAdicionarEapv") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarEapv(target);
            }
        });

        containerEapv.add(tableEapv = new Table("tableEapv", getColumnsEapv(), getCollectionProviderEapv()));
        tableEapv.populate();
    }

    private void grupoDoencas(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupDoencas = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getDoencaPreExistentes())));
        radioGroupDoencas.add(new AjaxRadio("simDoencas", new Model(InvestigacaoAgravo.TipoSelecionado.SIM.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerDoencas(target, true);
            }
        });

        radioGroupDoencas.add(new AjaxRadio("naoDoencas", new Model(InvestigacaoAgravo.TipoSelecionado.NAO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerDoencas(target, false);
            }
        });

        radioGroupDoencas.add(new AjaxRadio("ignoradoDoencas", new Model(InvestigacaoAgravo.TipoSelecionado.IGNORADO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerDoencas(target, false);
            }
        });

        containerDoencas = new WebMarkupContainer("containerDoencas");
        containerDoencas.setOutputMarkupPlaceholderTag(true);
        containerDoencas.setEnabled(false);
        getForm().add(containerDoencas);
        containerDoencas.add(txtDoencasOutras = new InputField(path(proxy.getInvestigacaoAgravo().getDescricaoOutrosDoencasExistentes())));
        txtDoencasOutras.addAjaxUpdateValue();
        txtDoencasOutras.setEnabled(false);
        addCheckboxDoencas();
    }

    private void grupoMedicacaoUso(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupMedicacao = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getMedicacaoUso())));
        radioGroupMedicacao.add(new AjaxRadio("simMedicacao", new Model(InvestigacaoAgravo.TipoSelecionado.SIM.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerMedicacaoUso(target, true);
            }
        });

        radioGroupMedicacao.add(new AjaxRadio("naoMedicacao", new Model(InvestigacaoAgravo.TipoSelecionado.NAO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerMedicacaoUso(target, false);
            }
        });

        radioGroupMedicacao.add(new AjaxRadio("ignoradoMedicacao", new Model(InvestigacaoAgravo.TipoSelecionado.IGNORADO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerMedicacaoUso(target, false);
            }
        });

        InvestigacaoMedicacaoUso proxyMedUso = on(InvestigacaoMedicacaoUso.class);
        containerMedicacao = new WebMarkupContainer("containerMedicacao", modelInvestigacaoMedicacaoUso = new CompoundPropertyModel(new InvestigacaoMedicacaoUso()));
        containerMedicacao.setOutputMarkupPlaceholderTag(true);
        containerMedicacao.setEnabled(false);
        getForm().add(containerMedicacao);

        containerMedicacao.add(dropDownTipoMedicacao = DropDownUtil.getIEnumDropDown(path(proxyMedUso.getTipoMedicacao()), InvestigacaoMedicacaoUso.TipoMedicacao.values(), true));
        dropDownTipoMedicacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoMedicacaoUso.TipoMedicacao.OUTROS.value().equals(modelInvestigacaoMedicacaoUso.getObject().getTipoMedicacao())) {
                    txtDescricaoTipoOutros.setEnabled(true);
                } else {
                    txtDescricaoTipoOutros.setEnabled(false);
                }
                target.add(txtDescricaoTipoOutros);
            }
        });
        containerMedicacao.add(txtDescricaoTipoOutros = new InputField(path(proxyMedUso.getDescricaoTipoOutros())));
        containerMedicacao.add(txtNomeGenerico = new InputField(path(proxyMedUso.getNomeGenerico())));
        containerMedicacao.add(txtViaAdministracaoMedicacao = new InputField(path(proxyMedUso.getViaAdministracao())));
        containerMedicacao.add(txtDataInicio = new DateChooser(path(proxyMedUso.getDataInicio())));
        containerMedicacao.add(chkUsoContinuo = new CheckBoxLongValue(path(proxyMedUso.getFlagUsoContinuo())));
        containerMedicacao.add(txtDataTermino = new DateChooser(path(proxyMedUso.getDataTermino())));
        containerMedicacao.add(new AbstractAjaxButton("btnAdicionarMedicacao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarMedicacaoUso(target);
            }
        });

        containerMedicacao.add(tableMedicacaoUso = new Table("tableMedicacaoUso", getColumnsMedicacaoUso(), getCollectionProviderMedicacaoUso()));
        tableMedicacaoUso.populate();
    }

    private void grupoTransfusao(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupTransfusao = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getTransfusaoUltimoMes())));
        getForm().add(txtDataTransfusaoUltimoMes = new DateChooser(path(proxy.getInvestigacaoAgravo().getDataTransfusaoUltimoMes())));
        txtDataTransfusaoUltimoMes.setEnabled(false);
        radioGroupTransfusao.add(new AjaxRadio("simTransfusao", new Model(InvestigacaoAgravo.TipoSelecionado.SIM.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                txtDataTransfusaoUltimoMes.setEnabled(true);
                txtDataTransfusaoUltimoMes.limpar(target);
                target.add(txtDataTransfusaoUltimoMes);
            }
        });

        radioGroupTransfusao.add(new AjaxRadio("naoTransfusao", new Model(InvestigacaoAgravo.TipoSelecionado.NAO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                txtDataTransfusaoUltimoMes.setEnabled(false);
                txtDataTransfusaoUltimoMes.limpar(target);
                target.add(txtDataTransfusaoUltimoMes);
            }
        });

        radioGroupTransfusao.add(new AjaxRadio("ignoradoTransfusao", new Model(InvestigacaoAgravo.TipoSelecionado.IGNORADO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                txtDataTransfusaoUltimoMes.setEnabled(false);
                txtDataTransfusaoUltimoMes.limpar(target);
                target.add(txtDataTransfusaoUltimoMes);
            }
        });
    }

    private void grupoHistoriaPrevia(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupConvulsao = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getHistoriaPreviaConvulsao())));
        radioGroupConvulsao.add(new AjaxRadio("semHistoriaConvulsao", new Model(InvestigacaoAgravo.TipoHistoricoConvulsao.SEM_HISTORIA.getValue())));
        radioGroupConvulsao.add(new AjaxRadio("convulsaoAfebril", new Model(InvestigacaoAgravo.TipoHistoricoConvulsao.AFEBRIL.getValue())));
        radioGroupConvulsao.add(new AjaxRadio("convulsaoFebril", new Model(InvestigacaoAgravo.TipoHistoricoConvulsao.FEBRIL.getValue())));
        radioGroupConvulsao.add(new AjaxRadio("ignoradoConvulsao", new Model(InvestigacaoAgravo.TipoHistoricoConvulsao.IGNORADO.getValue())));
    }

    private void grupoViajou(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupViajou = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getViajouUltimosDias())));
        radioGroupViajou.add(new AjaxRadio("simViajou", new Model(InvestigacaoAgravo.TipoSelecionado.SIM.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerViajou(target, true);
            }
        });

        radioGroupViajou.add(new AjaxRadio("naoViajou", new Model(InvestigacaoAgravo.TipoSelecionado.NAO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerViajou(target, false);
            }
        });

        radioGroupViajou.add(new AjaxRadio("ignoradoViajou", new Model(InvestigacaoAgravo.TipoSelecionado.IGNORADO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerViajou(target, false);
            }
        });
        containerViajou = new WebMarkupContainer("containerViajou");
        containerViajou.setOutputMarkupPlaceholderTag(true);
        containerViajou.setEnabled(false);
        getForm().add(containerViajou);

        containerViajou.add(txtDataInicioViagem = new DateChooser(path(proxy.getInvestigacaoAgravo().getDataInicioViagem())));
        containerViajou.add(txtDataTerminoViagem = new DateChooser(path(proxy.getInvestigacaoAgravo().getDataTerminoViagem())));
        containerViajou.add(txtPaisViagem = new InputField(path(proxy.getInvestigacaoAgravo().getPaisViagem())));
        containerViajou.add(txtLovalViagem = new InputField(path(proxy.getInvestigacaoAgravo().getLocalViagem())));
        containerViajou.add(txtUfViagem = new InputField(path(proxy.getInvestigacaoAgravo().getUfViagem())));
        containerViajou.add(txtMunicipioViagem = new InputField(path(proxy.getInvestigacaoAgravo().getMunicipioViagem())));
    }

    private void grupoManifestacoesLocais() {
        InvestigacaoManifestacoes proxyManLocais = on(InvestigacaoManifestacoes.class);
        containerManifestacoesLocais = new WebMarkupContainer("containerManifestacoesLocais", modelManifestacoesLocais = new CompoundPropertyModel(new InvestigacaoManifestacoes()));
        containerManifestacoesLocais.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerManifestacoesLocais);

        containerManifestacoesLocais.add(dropDownTipoManifestacoesLocais = DropDownUtil.getIEnumDropDown(path(proxyManLocais.getTipoEventoAdverso()), InvestigacaoManifestacoes.EventoAdversoManLocal.values(), true));
        dropDownTipoManifestacoesLocais.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(modelManifestacoesLocais.getObject().getTipoEventoAdverso())) {
                    txtDescricaoTipoOutrosManifestacoesLocais.setEnabled(true);
                } else {
                    txtDescricaoTipoOutrosManifestacoesLocais.setEnabled(false);
                }
                target.add(txtDescricaoTipoOutrosManifestacoesLocais);
            }
        });
        containerManifestacoesLocais.add(txtDescricaoTipoOutrosManifestacoesLocais = new InputField(path(proxyManLocais.getDescricaoOutrosEventoAdverso())));
        txtDescricaoTipoOutrosManifestacoesLocais.setEnabled(false);
        containerManifestacoesLocais.add(txtDataInicioManifestacoesLocais = new DateChooser(path(proxyManLocais.getDataInicioManifestacao())));
        containerManifestacoesLocais.add(txtDataTerminoManifestacoesLocais = new DateChooser(path(proxyManLocais.getDataTerminoManifestacao())));
        txtDataTerminoManifestacoesLocais.addAjaxUpdateValue();
        txtDataTerminoManifestacoesLocais.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (modelManifestacoesLocais.getObject().getDataTerminoManifestacao() != null) {
                    chkEmAcompanhamentoManifestacoesLocais.limpar(target);
                    chkEmAcompanhamentoManifestacoesLocais.setEnabled(false);
                } else {
                    chkEmAcompanhamentoManifestacoesLocais.setEnabled(true);
                }
                target.add(chkEmAcompanhamentoManifestacoesLocais);
            }
        });
        containerManifestacoesLocais.add(txtTempoInicioManifestacoesLocais = new InputField(path(proxyManLocais.getTempoInicio())));
        containerManifestacoesLocais.add(chkEmAcompanhamentoManifestacoesLocais = new CheckBoxLongValue(path(proxyManLocais.getFlagEmAcompanhamento())));

        containerManifestacoesLocais.add(new AbstractAjaxButton("btnAdicionarManifestacoesLocais") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarManifestacoesLocais(target);
            }
        });

        containerManifestacoesLocais.add(tableManifestacoesLocais = new Table("tableManifestacoesLocais", getColumnsManifestacoesLocais(), getCollectionProviderManifestacoesLocais()));
        tableManifestacoesLocais.populate();
    }

    private void grupoManifestacoesPeleMucosa() {
        InvestigacaoManifestacoes proxyManPeleMucosa = on(InvestigacaoManifestacoes.class);
        containerManifestacoesPeleMucosa = new WebMarkupContainer("containerManifestacoesPeleMucosa", modelManifestacoesPeleMucosa = new CompoundPropertyModel(new InvestigacaoManifestacoes()));
        containerManifestacoesPeleMucosa.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerManifestacoesPeleMucosa);

        containerManifestacoesPeleMucosa.add(dropDownTipoManifestacoesPeleMucosa = DropDownUtil.getIEnumDropDown(path(proxyManPeleMucosa.getTipoEventoAdverso()), InvestigacaoManifestacoes.EventoAdversoSistemciaPeleMucosa.values(), true));
        dropDownTipoManifestacoesPeleMucosa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(modelManifestacoesPeleMucosa.getObject().getTipoEventoAdverso())) {
                    txtDescricaoTipoOutrosManifestacoesPeleMucosa.setEnabled(true);
                } else {
                    txtDescricaoTipoOutrosManifestacoesPeleMucosa.setEnabled(false);
                }
                target.add(txtDescricaoTipoOutrosManifestacoesPeleMucosa);
            }
        });
        containerManifestacoesPeleMucosa.add(txtDescricaoTipoOutrosManifestacoesPeleMucosa = new InputField(path(proxyManPeleMucosa.getDescricaoOutrosEventoAdverso())));
        txtDescricaoTipoOutrosManifestacoesPeleMucosa.setEnabled(false);
        containerManifestacoesPeleMucosa.add(txtDataInicioManifestacoesPeleMucosa = new DateChooser(path(proxyManPeleMucosa.getDataInicioManifestacao())));
        containerManifestacoesPeleMucosa.add(txtDataTerminoManifestacoesPeleMucosa = new DateChooser(path(proxyManPeleMucosa.getDataTerminoManifestacao())));
        txtDataTerminoManifestacoesPeleMucosa.addAjaxUpdateValue();
        txtDataTerminoManifestacoesPeleMucosa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (modelManifestacoesPeleMucosa.getObject().getDataTerminoManifestacao() != null) {
                    chkEmAcompanhamentoManifestacoesPeleMucosa.limpar(target);
                    chkEmAcompanhamentoManifestacoesPeleMucosa.setEnabled(false);
                } else {
                    chkEmAcompanhamentoManifestacoesPeleMucosa.setEnabled(true);
                }
                target.add(chkEmAcompanhamentoManifestacoesPeleMucosa);
            }
        });
        containerManifestacoesPeleMucosa.add(txtTempoInicioManifestacoesPeleMucosa = new InputField(path(proxyManPeleMucosa.getTempoInicio())));
        containerManifestacoesPeleMucosa.add(chkEmAcompanhamentoManifestacoesPeleMucosa = new CheckBoxLongValue(path(proxyManPeleMucosa.getFlagEmAcompanhamento())));

        containerManifestacoesPeleMucosa.add(new AbstractAjaxButton("btnAdicionarManifestacoesPeleMucosa") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarManifestacoesPeleMucosa(target);
            }
        });

        containerManifestacoesPeleMucosa.add(tableManifestacoesPeleMucosa = new Table("tableManifestacoesPeleMucosa", getColumnsManifestacoesPeleMucosa(), getCollectionProviderManifestacoesPeleMucosa()));
        tableManifestacoesPeleMucosa.populate();
    }

    private void grupoManifestacoesCardiovasculares() {
        InvestigacaoManifestacoes proxyManCardiovasculares = on(InvestigacaoManifestacoes.class);
        containerManifestacoesCardiovasculares = new WebMarkupContainer("containerManifestacoesCardiovasculares", modelManifestacoesCardiovasculares = new CompoundPropertyModel(new InvestigacaoManifestacoes()));
        containerManifestacoesCardiovasculares.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerManifestacoesCardiovasculares);

        containerManifestacoesCardiovasculares.add(dropDownTipoManifestacoesCardiovasculares = DropDownUtil.getIEnumDropDown(path(proxyManCardiovasculares.getTipoEventoAdverso()), InvestigacaoManifestacoes.EventoAdversoSistemciaCardiovasculares.values(), true));
        dropDownTipoManifestacoesCardiovasculares.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(modelManifestacoesCardiovasculares.getObject().getTipoEventoAdverso())) {
                    txtDescricaoTipoOutrosManifestacoesCardiovasculares.setEnabled(true);
                } else {
                    txtDescricaoTipoOutrosManifestacoesCardiovasculares.setEnabled(false);
                }
                target.add(txtDescricaoTipoOutrosManifestacoesCardiovasculares);
            }
        });
        containerManifestacoesCardiovasculares.add(txtDescricaoTipoOutrosManifestacoesCardiovasculares = new InputField(path(proxyManCardiovasculares.getDescricaoOutrosEventoAdverso())));
        txtDescricaoTipoOutrosManifestacoesCardiovasculares.setEnabled(false);
        containerManifestacoesCardiovasculares.add(txtDataInicioManifestacoesCardiovasculares = new DateChooser(path(proxyManCardiovasculares.getDataInicioManifestacao())));
        containerManifestacoesCardiovasculares.add(txtDataTerminoManifestacoesCardiovasculares = new DateChooser(path(proxyManCardiovasculares.getDataTerminoManifestacao())));
        txtDataTerminoManifestacoesCardiovasculares.addAjaxUpdateValue();
        txtDataTerminoManifestacoesCardiovasculares.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (modelManifestacoesCardiovasculares.getObject().getDataTerminoManifestacao() != null) {
                    chkEmAcompanhamentoManifestacoesCardiovasculares.limpar(target);
                    chkEmAcompanhamentoManifestacoesCardiovasculares.setEnabled(false);
                } else {
                    chkEmAcompanhamentoManifestacoesCardiovasculares.setEnabled(true);
                }
                target.add(chkEmAcompanhamentoManifestacoesCardiovasculares);
            }
        });
        containerManifestacoesCardiovasculares.add(txtTempoInicioManifestacoesCardiovasculares = new InputField(path(proxyManCardiovasculares.getTempoInicio())));
        containerManifestacoesCardiovasculares.add(chkEmAcompanhamentoManifestacoesCardiovasculares = new CheckBoxLongValue(path(proxyManCardiovasculares.getFlagEmAcompanhamento())));

        containerManifestacoesCardiovasculares.add(new AbstractAjaxButton("btnAdicionarManifestacoesCardiovasculares") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarManifestacoesCardiovasculares(target);
            }
        });

        containerManifestacoesCardiovasculares.add(tableManifestacoesCardiovasculares = new Table("tableManifestacoesCardiovasculares", getColumnsManifestacoesCardiovasculares(), getCollectionProviderManifestacoesCardiovasculares()));
        tableManifestacoesCardiovasculares.populate();
    }

    private void grupoManifestacoesRespiratoria() {
        InvestigacaoManifestacoes proxyManRespiratoria = on(InvestigacaoManifestacoes.class);
        containerManifestacoesRespiratoria = new WebMarkupContainer("containerManifestacoesRespiratoria", modelManifestacoesRespiratoria = new CompoundPropertyModel(new InvestigacaoManifestacoes()));
        containerManifestacoesRespiratoria.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerManifestacoesRespiratoria);

        containerManifestacoesRespiratoria.add(dropDownTipoManifestacoesRespiratoria = DropDownUtil.getIEnumDropDown(path(proxyManRespiratoria.getTipoEventoAdverso()), InvestigacaoManifestacoes.EventoAdversoSistemciaRespiratoria.values(), true));
        dropDownTipoManifestacoesRespiratoria.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(modelManifestacoesRespiratoria.getObject().getTipoEventoAdverso())) {
                    txtDescricaoTipoOutrosManifestacoesRespiratoria.setEnabled(true);
                } else {
                    txtDescricaoTipoOutrosManifestacoesRespiratoria.setEnabled(false);
                }
                target.add(txtDescricaoTipoOutrosManifestacoesRespiratoria);
            }
        });
        containerManifestacoesRespiratoria.add(txtDescricaoTipoOutrosManifestacoesRespiratoria = new InputField(path(proxyManRespiratoria.getDescricaoOutrosEventoAdverso())));
        txtDescricaoTipoOutrosManifestacoesRespiratoria.setEnabled(false);
        containerManifestacoesRespiratoria.add(txtDataInicioManifestacoesRespiratoria = new DateChooser(path(proxyManRespiratoria.getDataInicioManifestacao())));
        containerManifestacoesRespiratoria.add(txtDataTerminoManifestacoesRespiratoria = new DateChooser(path(proxyManRespiratoria.getDataTerminoManifestacao())));
        txtDataTerminoManifestacoesRespiratoria.addAjaxUpdateValue();
        txtDataTerminoManifestacoesRespiratoria.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (modelManifestacoesRespiratoria.getObject().getDataTerminoManifestacao() != null) {
                    chkEmAcompanhamentoManifestacoesRespiratoria.limpar(target);
                    chkEmAcompanhamentoManifestacoesRespiratoria.setEnabled(false);
                } else {
                    chkEmAcompanhamentoManifestacoesRespiratoria.setEnabled(true);
                }
                target.add(chkEmAcompanhamentoManifestacoesRespiratoria);
            }
        });
        containerManifestacoesRespiratoria.add(txtTempoInicioManifestacoesRespiratoria = new InputField(path(proxyManRespiratoria.getTempoInicio())));
        containerManifestacoesRespiratoria.add(chkEmAcompanhamentoManifestacoesRespiratoria = new CheckBoxLongValue(path(proxyManRespiratoria.getFlagEmAcompanhamento())));

        containerManifestacoesRespiratoria.add(new AbstractAjaxButton("btnAdicionarManifestacoesRespiratoria") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarManifestacoesRespiratoria(target);
            }
        });

        containerManifestacoesRespiratoria.add(tableManifestacoesRespiratoria = new Table("tableManifestacoesRespiratoria", getColumnsManifestacoesRespiratoria(), getCollectionProviderManifestacoesRespiratoria()));
        tableManifestacoesRespiratoria.populate();
    }

    private void grupoManifestacoesNeurologicas() {
        InvestigacaoManifestacoes proxyManNeurologicas = on(InvestigacaoManifestacoes.class);
        containerManifestacoesNeurologicas = new WebMarkupContainer("containerManifestacoesNeurologicas", modelManifestacoesNeurologicas = new CompoundPropertyModel(new InvestigacaoManifestacoes()));
        containerManifestacoesNeurologicas.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerManifestacoesNeurologicas);

        containerManifestacoesNeurologicas.add(dropDownTipoManifestacoesNeurologicas = DropDownUtil.getIEnumDropDown(path(proxyManNeurologicas.getTipoEventoAdverso()), InvestigacaoManifestacoes.EventoAdversoSistemciaNeurologicas.values(), true));
        dropDownTipoManifestacoesNeurologicas.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(modelManifestacoesNeurologicas.getObject().getTipoEventoAdverso())) {
                    txtDescricaoTipoOutrosManifestacoesNeurologicas.setEnabled(true);
                } else {
                    txtDescricaoTipoOutrosManifestacoesNeurologicas.setEnabled(false);
                }
                target.add(txtDescricaoTipoOutrosManifestacoesNeurologicas);
            }
        });
        containerManifestacoesNeurologicas.add(txtDescricaoTipoOutrosManifestacoesNeurologicas = new InputField(path(proxyManNeurologicas.getDescricaoOutrosEventoAdverso())));
        txtDescricaoTipoOutrosManifestacoesNeurologicas.setEnabled(false);
        containerManifestacoesNeurologicas.add(txtDataInicioManifestacoesNeurologicas = new DateChooser(path(proxyManNeurologicas.getDataInicioManifestacao())));
        containerManifestacoesNeurologicas.add(txtDataTerminoManifestacoesNeurologicas = new DateChooser(path(proxyManNeurologicas.getDataTerminoManifestacao())));
        txtDataTerminoManifestacoesNeurologicas.addAjaxUpdateValue();
        txtDataTerminoManifestacoesNeurologicas.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (modelManifestacoesNeurologicas.getObject().getDataTerminoManifestacao() != null) {
                    chkEmAcompanhamentoManifestacoesNeurologicas.limpar(target);
                    chkEmAcompanhamentoManifestacoesNeurologicas.setEnabled(false);
                } else {
                    chkEmAcompanhamentoManifestacoesNeurologicas.setEnabled(true);
                }
                target.add(chkEmAcompanhamentoManifestacoesNeurologicas);
            }
        });
        containerManifestacoesNeurologicas.add(txtTempoInicioManifestacoesNeurologicas = new InputField(path(proxyManNeurologicas.getTempoInicio())));
        containerManifestacoesNeurologicas.add(chkEmAcompanhamentoManifestacoesNeurologicas = new CheckBoxLongValue(path(proxyManNeurologicas.getFlagEmAcompanhamento())));

        containerManifestacoesNeurologicas.add(new AbstractAjaxButton("btnAdicionarManifestacoesNeurologicas") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarManifestacoesNeurologicas(target);
            }
        });

        containerManifestacoesNeurologicas.add(tableManifestacoesNeurologicas = new Table("tableManifestacoesNeurologicas", getColumnsManifestacoesNeurologicas(), getCollectionProviderManifestacoesNeurologicas()));
        tableManifestacoesNeurologicas.populate();
    }

    private void grupoManifestacoesGastrintestinais() {
        InvestigacaoManifestacoes proxyManGastrintestinais = on(InvestigacaoManifestacoes.class);
        containerManifestacoesGastrintestinais = new WebMarkupContainer("containerManifestacoesGastrintestinais", modelManifestacoesGastrintestinais = new CompoundPropertyModel(new InvestigacaoManifestacoes()));
        containerManifestacoesGastrintestinais.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerManifestacoesGastrintestinais);

        containerManifestacoesGastrintestinais.add(dropDownTipoManifestacoesGastrintestinais = DropDownUtil.getIEnumDropDown(path(proxyManGastrintestinais.getTipoEventoAdverso()), InvestigacaoManifestacoes.EventoAdversoSistemciaGastrintestinais.values(), true));
        dropDownTipoManifestacoesGastrintestinais.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(modelManifestacoesGastrintestinais.getObject().getTipoEventoAdverso())) {
                    txtDescricaoTipoOutrosManifestacoesGastrintestinais.setEnabled(true);
                } else {
                    txtDescricaoTipoOutrosManifestacoesGastrintestinais.setEnabled(false);
                }
                target.add(txtDescricaoTipoOutrosManifestacoesGastrintestinais);
            }
        });
        containerManifestacoesGastrintestinais.add(txtDescricaoTipoOutrosManifestacoesGastrintestinais = new InputField(path(proxyManGastrintestinais.getDescricaoOutrosEventoAdverso())));
        txtDescricaoTipoOutrosManifestacoesGastrintestinais.setEnabled(false);
        containerManifestacoesGastrintestinais.add(txtDataInicioManifestacoesGastrintestinais = new DateChooser(path(proxyManGastrintestinais.getDataInicioManifestacao())));
        containerManifestacoesGastrintestinais.add(txtDataTerminoManifestacoesGastrintestinais = new DateChooser(path(proxyManGastrintestinais.getDataTerminoManifestacao())));
        txtDataTerminoManifestacoesGastrintestinais.addAjaxUpdateValue();
        txtDataTerminoManifestacoesGastrintestinais.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (modelManifestacoesGastrintestinais.getObject().getDataTerminoManifestacao() != null) {
                    chkEmAcompanhamentoManifestacoesGastrintestinais.limpar(target);
                    chkEmAcompanhamentoManifestacoesGastrintestinais.setEnabled(false);
                } else {
                    chkEmAcompanhamentoManifestacoesGastrintestinais.setEnabled(true);
                }
                target.add(chkEmAcompanhamentoManifestacoesGastrintestinais);
            }
        });
        containerManifestacoesGastrintestinais.add(txtTempoInicioManifestacoesGastrintestinais = new InputField(path(proxyManGastrintestinais.getTempoInicio())));
        containerManifestacoesGastrintestinais.add(chkEmAcompanhamentoManifestacoesGastrintestinais = new CheckBoxLongValue(path(proxyManGastrintestinais.getFlagEmAcompanhamento())));

        containerManifestacoesGastrintestinais.add(new AbstractAjaxButton("btnAdicionarManifestacoesGastrintestinais") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarManifestacoesGastrintestinais(target);
            }
        });

        containerManifestacoesGastrintestinais.add(tableManifestacoesGastrintestinais = new Table("tableManifestacoesGastrintestinais", getColumnsManifestacoesGastrintestinais(), getCollectionProviderManifestacoesGastrintestinais()));
        tableManifestacoesGastrintestinais.populate();
    }

    private void grupoManifestacoesClinicasOutras() {
        InvestigacaoManifestacoes proxyManOutras = on(InvestigacaoManifestacoes.class);
        containerManifestacoesOutras = new WebMarkupContainer("containerManifestacoesOutras", modelManifestacoesOutras = new CompoundPropertyModel(new InvestigacaoManifestacoes()));
        containerManifestacoesOutras.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerManifestacoesOutras);

        containerManifestacoesOutras.add(dropDownTipoManifestacoesOutras = DropDownUtil.getIEnumDropDown(path(proxyManOutras.getTipoEventoAdverso()), InvestigacaoManifestacoes.EventoAdversoSistemciaOutras.values(), true));
        dropDownTipoManifestacoesOutras.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(modelManifestacoesOutras.getObject().getTipoEventoAdverso())) {
                    txtDescricaoTipoOutrosManifestacoesOutras.setEnabled(true);
                } else {
                    txtDescricaoTipoOutrosManifestacoesOutras.setEnabled(false);
                }
                target.add(txtDescricaoTipoOutrosManifestacoesOutras);
            }
        });
        containerManifestacoesOutras.add(txtDescricaoTipoOutrosManifestacoesOutras = new InputField(path(proxyManOutras.getDescricaoOutrosEventoAdverso())));
        txtDescricaoTipoOutrosManifestacoesOutras.setEnabled(false);
        containerManifestacoesOutras.add(txtDataInicioManifestacoesOutras = new DateChooser(path(proxyManOutras.getDataInicioManifestacao())));
        containerManifestacoesOutras.add(txtDataTerminoManifestacoesOutras = new DateChooser(path(proxyManOutras.getDataTerminoManifestacao())));
        txtDataTerminoManifestacoesOutras.addAjaxUpdateValue();
        txtDataTerminoManifestacoesOutras.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (modelManifestacoesOutras.getObject().getDataTerminoManifestacao() != null) {
                    chkEmAcompanhamentoManifestacoesOutras.limpar(target);
                    chkEmAcompanhamentoManifestacoesOutras.setEnabled(false);
                } else {
                    chkEmAcompanhamentoManifestacoesOutras.setEnabled(true);
                }
                target.add(chkEmAcompanhamentoManifestacoesOutras);
            }
        });
        containerManifestacoesOutras.add(txtTempoInicioManifestacoesOutras = new InputField(path(proxyManOutras.getTempoInicio())));
        containerManifestacoesOutras.add(chkEmAcompanhamentoManifestacoesOutras = new CheckBoxLongValue(path(proxyManOutras.getFlagEmAcompanhamento())));

        containerManifestacoesOutras.add(new AbstractAjaxButton("btnAdicionarManifestacoesOutras") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarManifestacoesOutras(target);
            }
        });

        containerManifestacoesOutras.add(tableManifestacoesOutras = new Table("tableManifestacoesOutras", getColumnsManifestacoesOutras(), getCollectionProviderManifestacoesOutras()));
        tableManifestacoesOutras.populate();
    }

    private void grupoAtendimentoMedico(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupAtendimento = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getAtendimentoMedico())));
        radioGroupAtendimento.add(new AjaxRadio("simAtendimento", new Model(InvestigacaoAgravo.TipoSelecionado.SIM.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerAtendimento(target, true);
            }
        });

        radioGroupAtendimento.add(new AjaxRadio("naoAtendimento", new Model(InvestigacaoAgravo.TipoSelecionado.NAO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerAtendimento(target, false);
            }
        });

        radioGroupAtendimento.add(new AjaxRadio("ignoradoAtendimento", new Model(InvestigacaoAgravo.TipoSelecionado.IGNORADO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                enableContainerAtendimento(target, false);
            }
        });

        containerAtendimento = new WebMarkupContainer("containerAtendimento");
        containerAtendimento.setOutputMarkupPlaceholderTag(true);
        containerAtendimento.setEnabled(false);
        getForm().add(containerAtendimento);

        containerAtendimento.add(radioGroupTipoAtendimentoMedico = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getTipoAtendimentoMedico())));
        radioGroupTipoAtendimentoMedico.add(new AjaxRadio("ambulatorioConsultorio", new Model(InvestigacaoAgravo.TipoAtendimentoMedico.AMBULATORIO_CONSULTORIO.getValue())));
        radioGroupTipoAtendimentoMedico.add(new AjaxRadio("observacaoPermanenciaUnidade", new Model(InvestigacaoAgravo.TipoAtendimentoMedico.OBSERVACAO_PERMANENCIA_UNIDADE.getValue())));
        radioGroupTipoAtendimentoMedico.add(new AjaxRadio("hospitalizacaoPermanciaUnidade", new Model(InvestigacaoAgravo.TipoAtendimentoMedico.HOSPITALIZACAO_PERMANCIA_UNIDADE.getValue())));

        containerAtendimento.add(txCNESHospital = new InputField(path(proxy.getInvestigacaoAgravo().getCnesHospitalAtendimentoMedico())));
        containerAtendimento.add(txtNomeHospital = new InputField(path(proxy.getInvestigacaoAgravo().getNomeHospitalAtendimentoMedico())));
        containerAtendimento.add(txtDataInternacao = new DateChooser(path(proxy.getInvestigacaoAgravo().getDataInternacaoAtendimentoMedico())));
        containerAtendimento.add(txtDataAlta = new DateChooser(path(proxy.getInvestigacaoAgravo().getDataAltaAtendimentoMedico())));
        containerAtendimento.add(txtMunicipioAtendimento = new InputField(path(proxy.getInvestigacaoAgravo().getMunicipioAtendimentoMedico())));
        containerAtendimento.add(txtUfAtendimento = new InputField(path(proxy.getInvestigacaoAgravo().getUfAtendimentoMedico())));
    }

    private void grupoEvolucao(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupEvolucao = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getEvolucao())));
        radioGroupEvolucao.add(new AjaxRadio("eventoAdversoBCGSemNecessidadeAdmIso", new Model(InvestigacaoAgravo.Evolucao.EVENTO_ADVERSO_BCG_SEM_NECESSIDADE_ADM_ISO.getValue())));
        radioGroupEvolucao.add(new AjaxRadio("eventoAdversoBCGComNecessidadeAdmIso", new Model(InvestigacaoAgravo.Evolucao.EVENTO_ADVERSO_BCG_COM_NECESSIDADE_ADM_ISO.getValue())));
        radioGroupEvolucao.add(new AjaxRadio("eventoAdversoBCGComNecessidadeAdmEsqTriQua", new Model(InvestigacaoAgravo.Evolucao.EVENTO_ADVERSO_BCG_COM_NECESSIDADE_ADM_ESQ_TRI_QUA.getValue())));
        radioGroupEvolucao.add(new AjaxRadio("curaSemSequelas", new Model(InvestigacaoAgravo.Evolucao.CURA_SEM_SEQUELAS.getValue())));
        radioGroupEvolucao.add(new AjaxRadio("curaComSequelas", new Model(InvestigacaoAgravo.Evolucao.CURA_COM_SEQUELAS.getValue())));
        radioGroupEvolucao.add(new AjaxRadio("obito", new Model(InvestigacaoAgravo.Evolucao.OBITO.getValue())));
        radioGroupEvolucao.add(new AjaxRadio("naoEAPV", new Model(InvestigacaoAgravo.Evolucao.NAO_EAPV.getValue())));
        radioGroupEvolucao.add(new AjaxRadio("perdaSeguimento", new Model(InvestigacaoAgravo.Evolucao.PERDA_SEGUIMENTO.getValue())));
        radioGroupEvolucao.add(new AjaxRadio("emAcompanhamento", new Model(InvestigacaoAgravo.Evolucao.EM_ACOMPANHAMENTO.getValue())));
    }

    private void grupoClassificacaoFinal(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupClassificacaoFinal = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getClassificacaoFinal())));
        radioGroupClassificacaoFinal.add(new AjaxRadio("eaNaoGrave", new Model(InvestigacaoAgravo.ClassificacaoFinal.EA_NAO_GRAVE.getValue())));
        radioGroupClassificacaoFinal.add(new AjaxRadio("eaGrave", new Model(InvestigacaoAgravo.ClassificacaoFinal.EA_GRAVE.getValue())));
        radioGroupClassificacaoFinal.add(new AjaxRadio("erroImunizacao", new Model(InvestigacaoAgravo.ClassificacaoFinal.ERRO_IMUNIZACAO.getValue())));
        radioGroupClassificacaoFinal.add(new AjaxRadio("inclassificavel", new Model(InvestigacaoAgravo.ClassificacaoFinal.INCLASSIFICAVEL.getValue())));
    }

    private void grupoErrosIMunizacao(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupErrosImunizacao = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getErrosImunizacao())));
        radioGroupErrosImunizacao.add(new AjaxRadio("tipoImunibiologicoUtilizado", new Model(InvestigacaoAgravo.ErroImunizacao.TIPO_IMUNUBIOLOGICO_UTILIZADO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCamposOutrosErrosImunizacao(target, false);
            }
        });
        radioGroupErrosImunizacao.add(new AjaxRadio("errosAdministracaoTecnicaAplicacao", new Model(InvestigacaoAgravo.ErroImunizacao.ERROS_ADMINISTRACAO_TECNICA_APLICACAO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCamposOutrosErrosImunizacao(target, false);
            }
        });
        radioGroupErrosImunizacao.add(new AjaxRadio("errosAdministracaoUsoIncorreto", new Model(InvestigacaoAgravo.ErroImunizacao.ERROS_ADMINISTRACAO_USO_INCORRETO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCamposOutrosErrosImunizacao(target, false);
            }
        });
        radioGroupErrosImunizacao.add(new AjaxRadio("errosManuseio", new Model(InvestigacaoAgravo.ErroImunizacao.ERROS_MANUSEIO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCamposOutrosErrosImunizacao(target, false);
            }
        });
        radioGroupErrosImunizacao.add(new AjaxRadio("intervaloInadequadoEntreDoses", new Model(InvestigacaoAgravo.ErroImunizacao.INTERVALO_INADEQUADO_ENTRE_DOSES.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCamposOutrosErrosImunizacao(target, false);
            }
        });
        radioGroupErrosImunizacao.add(new AjaxRadio("validadeVencida", new Model(InvestigacaoAgravo.ErroImunizacao.VALIDADE_VENCIDA.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCamposOutrosErrosImunizacao(target, false);
            }
        });
        radioGroupErrosImunizacao.add(new AjaxRadio("errosPrescricaoIndicacoes", new Model(InvestigacaoAgravo.ErroImunizacao.ERROS_PRESCRICAO_INDICACOES.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCamposOutrosErrosImunizacao(target, false);
            }
        });
        radioGroupErrosImunizacao.add(new AjaxRadio("naoAvaliacaoContraindicacoesPrecaucoes", new Model(InvestigacaoAgravo.ErroImunizacao.NAO_AVALIACAO_CONTRAINDICACOES_PRECAUCOES.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCamposOutrosErrosImunizacao(target, false);
            }
        });
        radioGroupErrosImunizacao.add(new AjaxRadio("outrosDescreverDetalhadamenteErros", new Model(InvestigacaoAgravo.ErroImunizacao.OUTROS.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCamposOutrosErrosImunizacao(target, true);
            }
        });

        getForm().add(txtDescricaoOutrosErrosImunizacao = new InputArea(path(proxy.getInvestigacaoAgravo().getDescricaoOutrosErrosImunizacao())));
        txtDescricaoOutrosErrosImunizacao.setEnabled(false);
    }

    private void grupoCondutaFrenteErrosImunizacao(FichaInvestigacaoAgravoDTO proxy) {
        getForm().add(radioGroupCondutaErrosImunizacao = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getCondutaErrosImunizacao())));
        radioGroupCondutaErrosImunizacao.add(new AjaxRadio("doseConsideradaValida", new Model(InvestigacaoAgravo.CondutaErroImunizacao.DOSE_CONSIDERADA_VALIDA.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarDoseConsideradaInvalida(target, false);
            }
        });

        radioGroupCondutaErrosImunizacao.add(new AjaxRadio("doseConsideradaInvalida", new Model(InvestigacaoAgravo.CondutaErroImunizacao.DOSE_CONSIDERADA_INVALIDA.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarDoseConsideradaInvalida(target, true);
            }
        });

        radioGroupCondutaErrosImunizacao.add(containerDoseConsideradaInvalida = new WebMarkupContainer("containerDoseConsideradaInvalida"));
        containerDoseConsideradaInvalida.setOutputMarkupId(true);
        containerDoseConsideradaInvalida.setEnabled(false);
        containerDoseConsideradaInvalida.add(radioGroupDoseConsideradaInvalida = new RadioButtonGroup(path(proxy.getInvestigacaoAgravo().getCondutaErrosImunizacaoItem())));
        radioGroupDoseConsideradaInvalida.add(new AjaxRadio("repetirDoseUnica", new Model(InvestigacaoAgravo.DoseConsideradaInvalida.REPETIR_DOSE_UNICA_MAIS_RAPIDO_POSSIVEL.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarDoseConsideradaInvalidaOutros(target, false);
            }
        });
        radioGroupDoseConsideradaInvalida.add(new AjaxRadio("repetirDoseIntervaloMinimo", new Model(InvestigacaoAgravo.DoseConsideradaInvalida.REPETIR_DOSE_MAIS_RAPIDO_POSSIVEL_CONSIDERANDO_INTERVALO_MINIMO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarDoseConsideradaInvalidaOutros(target, false);
            }
        });
        radioGroupDoseConsideradaInvalida.add(new AjaxRadio("repetirDoseAprazamentoReforco", new Model(InvestigacaoAgravo.DoseConsideradaInvalida.REPETIR_DOSE_COM_APRAZAMENTO_REFORCO.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarDoseConsideradaInvalidaOutros(target, false);
            }
        });
        radioGroupDoseConsideradaInvalida.add(new AjaxRadio("acompanhamentoDosagemAnticorpos", new Model(InvestigacaoAgravo.DoseConsideradaInvalida.ACOMPANHAMENTO_DOSAGEM_ANTICORPOS.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarDoseConsideradaInvalidaOutros(target, false);
            }
        });
        radioGroupDoseConsideradaInvalida.add(new AjaxRadio("outrosEspecificar", new Model(InvestigacaoAgravo.DoseConsideradaInvalida.OUTROS.getValue())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarDoseConsideradaInvalidaOutros(target, true);
            }
        });

        containerDoseConsideradaInvalida.add(txtDescricaoOutrosCondutaErroImunizacaoItem = new InputArea(path(proxy.getInvestigacaoAgravo().getDescricaoOutrosCondutaErrosImunizacaoItem())));
        txtDescricaoOutrosCondutaErroImunizacaoItem.setEnabled(false);
    }

    private void grupoEncerramentoFinal() {
        InvestigacaoEncerramentoFinal proxyEncerramentoFinal = on(InvestigacaoEncerramentoFinal.class);
        containerEncerramentoFinal = new WebMarkupContainer("containerEncerramentoFinal", modelEncerramentoFinal = new CompoundPropertyModel(new InvestigacaoEncerramentoFinal()));
        containerEncerramentoFinal.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerEncerramentoFinal);

        dropDownVacinaAplicadaEncerramentoFinal = new DropDown(path(proxyEncerramentoFinal.getVacinaAplicacao()));
        dropDownVacinaAplicadaEncerramentoFinal.addRequiredClass();
        containerEncerramentoFinal.add(dropDownVacinaAplicadaEncerramentoFinal);
        containerEncerramentoFinal.add(txtDiagnostico = new InputField(path(proxyEncerramentoFinal.getDiagnostico())));
        txtDiagnostico.addRequiredClass();
        containerEncerramentoFinal.add(dropDownConduta = DropDownUtil.getIEnumDropDown(path(proxyEncerramentoFinal.getConduta()), InvestigacaoEncerramentoFinal.Conduta.values(), true));
        dropDownConduta.addRequiredClass();
        dropDownConduta.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoEncerramentoFinal.Conduta.OUTROS_ESPECIFICAR.value().equals(modelEncerramentoFinal.getObject().getConduta())) {
                    txtDescricaoOutrasCondutas.setEnabled(true);
                } else {
                    txtDescricaoOutrasCondutas.setEnabled(false);
                }
                txtDescricaoOutrasCondutas.limpar(target);
            }
        });
        containerEncerramentoFinal.add(txtDescricaoOutrasCondutas = new InputField(path(proxyEncerramentoFinal.getDescricaoOutrosConduta())));
        txtDescricaoOutrasCondutas.setEnabled(false);
        containerEncerramentoFinal.add(dropDownCausalidadesUm = getDropDownCausalidadesUm(path(proxyEncerramentoFinal.getCausalidadeUm())));
        containerEncerramentoFinal.add(dropDownCausalidadesDois = getDropDownCausalidadesDois(path(proxyEncerramentoFinal.getCausalidadeDois())));
        containerEncerramentoFinal.add(dropDownCausalidadesTres = getDropDownCausalidadesTres(path(proxyEncerramentoFinal.getCausalidadeTres())));
        containerEncerramentoFinal.add(dropDownCausalidadesQuatro = getDropDownCausalidadesQuatro(path(proxyEncerramentoFinal.getCausalidadeQuatro())));
        containerEncerramentoFinal.add(txtDescricaoOutrasCausalidades = new InputField(path(proxyEncerramentoFinal.getDescricaoOutrosCausalidade())));
        txtDescricaoOutrasCausalidades.setEnabled(false);
        containerEncerramentoFinal.add(new AbstractAjaxButton("btnAdicionarEncerramentoFinal") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarEncerramentoFinal(target);
            }
        });

        containerEncerramentoFinal.add(tableEncerramentoFinal = new Table("tableEncerramentoFinal", getColumnsEncerramentoFinal(), getCollectionProviderEncerramentoFinal()));
        tableEncerramentoFinal.populate();
    }

    private void removeItemDropDownVacinaAplicadaEncerramentoFinal(AjaxRequestTarget target, InvestigacaoVacinaAplicada investigacaoVacinaAplicada) {
        if (investigacaoVacinaAplicada.getCodigo() != null) {
            dropDownVacinaAplicadaEncerramentoFinal.removeChoice(investigacaoVacinaAplicada.getVacinaAplicacao());
            removerEncerramentoFinal(target, investigacaoVacinaAplicada.getVacinaAplicacao());
        } else {
            Map<VacinaAplicacao, String> values = dropDownVacinaAplicadaEncerramentoFinal.getValues();
            for (Map.Entry<VacinaAplicacao, String> entry : values.entrySet()) {
                if (entry.getKey() == null) continue;
                if (entry.getKey().equals(investigacaoVacinaAplicada.getVacinaAplicacao())) {
                    dropDownVacinaAplicadaEncerramentoFinal.removeChoice(entry.getKey());
                    removerEncerramentoFinal(target, entry.getKey());
                    tableEncerramentoFinal.update(target);
                }
            }
        }

        target.add(dropDownVacinaAplicadaEncerramentoFinal);
    }

    private void addItemDropDownVacinaAplicadaEncerramentoFinal(AjaxRequestTarget target, InvestigacaoVacinaAplicada investigacaoVacinaAplicada) {
        dropDownVacinaAplicadaEncerramentoFinal.addChoice(investigacaoVacinaAplicada.getVacinaAplicacao(), investigacaoVacinaAplicada.getVacinaAplicacao().getDescricaoVacina());
        target.add(dropDownVacinaAplicadaEncerramentoFinal);
    }

    private void carregarDropDownVacinaAplicadaEncerramentoFinal(InvestigacaoAgravo investigacaoAgravo) {
        dropDownVacinaAplicadaEncerramentoFinal.removeAllChoices();

        List<InvestigacaoVacinaAplicada> list = LoadManager.getInstance(InvestigacaoVacinaAplicada.class)
                .addProperties(new HQLProperties(InvestigacaoVacinaAplicada.class).getProperties())
                .addProperties(new HQLProperties(VacinaAplicacao.class, InvestigacaoVacinaAplicada.PROP_VACINA_APLICACAO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoVacinaAplicada.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                .start().getList();

        dropDownVacinaAplicadaEncerramentoFinal.addChoice(null, "");
        for (InvestigacaoVacinaAplicada vacinaAplicada : list) {
            dropDownVacinaAplicadaEncerramentoFinal.addChoice(vacinaAplicada.getVacinaAplicacao(), vacinaAplicada.getVacinaAplicacao().getDescricaoVacina());
        }
    }

    public DropDown getDropDownCausalidadesUm(String id) {
        if (dropDownCausalidadesUm == null) {
            dropDownCausalidadesUm = DropDownUtil.getIEnumDropDown(id, InvestigacaoEncerramentoFinal.CausalidadeUm.values(), true);
        }
        dropDownCausalidadesUm.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dropDownCausalidadesDois.removeAllChoices();
                dropDownCausalidadesTres.removeAllChoices();
                dropDownCausalidadesQuatro.removeAllChoices();
                if (InvestigacaoEncerramentoFinal.CausalidadeUm.INFORMACAO_DISPONIVEL_ADEQUADA.value().equals(modelEncerramentoFinal.getObject().getCausalidadeUm())) {
                    dropDownCausalidadesDois.setEnabled(true);
                    DropDownUtil.setIEnumChoices(dropDownCausalidadesDois, InvestigacaoEncerramentoFinal.CausalidadeDoisAdequada.values(), true);
                } else if (InvestigacaoEncerramentoFinal.CausalidadeUm.INFORMACAO_DISPONIVEL_INADEQUADA.value().equals(modelEncerramentoFinal.getObject().getCausalidadeUm())) {
                    dropDownCausalidadesDois.setEnabled(true);
                    DropDownUtil.setIEnumChoices(dropDownCausalidadesDois, InvestigacaoEncerramentoFinal.CausalidadeDoisInadequada.values(), true);
                } else {
                    dropDownCausalidadesDois.setEnabled(false);
                }
                dropDownCausalidadesDois.limpar(target);
                dropDownCausalidadesTres.limpar(target);
                dropDownCausalidadesTres.setEnabled(false);
                dropDownCausalidadesQuatro.limpar(target);
                dropDownCausalidadesQuatro.setEnabled(false);
            }
        });
        return dropDownCausalidadesUm;
    }

    public DropDown getDropDownCausalidadesDois(String id) {
        if (dropDownCausalidadesDois == null) {
            dropDownCausalidadesDois = new DropDown(id);
        }
        dropDownCausalidadesDois.setEnabled(false);
        dropDownCausalidadesDois.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dropDownCausalidadesTres.removeAllChoices();
                dropDownCausalidadesQuatro.removeAllChoices();
                if (InvestigacaoEncerramentoFinal.CausalidadeUm.INFORMACAO_DISPONIVEL_ADEQUADA.value().equals(modelEncerramentoFinal.getObject().getCausalidadeUm())) {
                    if (InvestigacaoEncerramentoFinal.CausalidadeDoisAdequada.A_CONSISTENTE.value().equals(modelEncerramentoFinal.getObject().getCausalidadeDois())) {
                        dropDownCausalidadesTres.setEnabled(true);
                        DropDownUtil.setIEnumChoices(dropDownCausalidadesTres, InvestigacaoEncerramentoFinal.CausalidadeTresAdequadaConsistente.values(), true);
                    } else if (InvestigacaoEncerramentoFinal.CausalidadeDoisAdequada.B_INDETERMINADA.value().equals(modelEncerramentoFinal.getObject().getCausalidadeDois())) {
                        dropDownCausalidadesTres.setEnabled(true);
                        DropDownUtil.setIEnumChoices(dropDownCausalidadesTres, InvestigacaoEncerramentoFinal.CausalidadeTresAdequadaIndeterminada.values(), true);
                    } else if (InvestigacaoEncerramentoFinal.CausalidadeDoisAdequada.C_INCONSISTENTE.value().equals(modelEncerramentoFinal.getObject().getCausalidadeDois())) {
                        dropDownCausalidadesTres.setEnabled(true);
                        DropDownUtil.setIEnumChoices(dropDownCausalidadesTres, InvestigacaoEncerramentoFinal.CausalidadeTresAdequadaInconsistente.values(), true);
                    } else {
                        dropDownCausalidadesTres.setEnabled(false);
                    }
                    dropDownCausalidadesTres.limpar(target);
                    dropDownCausalidadesQuatro.limpar(target);
                    dropDownCausalidadesQuatro.setEnabled(false);
                }
            }
        });
        return dropDownCausalidadesDois;
    }

    public DropDown getDropDownCausalidadesTres(String id) {
        if (dropDownCausalidadesTres == null) {
            dropDownCausalidadesTres = new DropDown(id);
        }
        dropDownCausalidadesTres.setEnabled(false);
        dropDownCausalidadesTres.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dropDownCausalidadesQuatro.removeAllChoices();
                if (InvestigacaoEncerramentoFinal.CausalidadeUm.INFORMACAO_DISPONIVEL_ADEQUADA.value().equals(modelEncerramentoFinal.getObject().getCausalidadeUm())) {
                    if (InvestigacaoEncerramentoFinal.CausalidadeTresAdequadaConsistente.A_UM_REACOES.value().equals(modelEncerramentoFinal.getObject().getCausalidadeTres())) {
                        dropDownCausalidadesQuatro.setEnabled(true);
                        DropDownUtil.setIEnumChoices(dropDownCausalidadesQuatro, InvestigacaoEncerramentoFinal.CausalidadeTresAdequadaConsistenteAUm.values(), true);
                    } else if (InvestigacaoEncerramentoFinal.CausalidadeTresAdequadaConsistente.A_TRES_ERROS.value().equals(modelEncerramentoFinal.getObject().getCausalidadeTres())) {
                        dropDownCausalidadesQuatro.setEnabled(true);
                        DropDownUtil.setIEnumChoices(dropDownCausalidadesQuatro, InvestigacaoEncerramentoFinal.CausalidadeTresAdequadaConsistenteATres.values(), true);
                    } else {
                        dropDownCausalidadesQuatro.setEnabled(false);
                    }
                    dropDownCausalidadesQuatro.limpar(target);
                }
            }
        });
        return dropDownCausalidadesTres;
    }

    public DropDown getDropDownCausalidadesQuatro(String id) {
        if (dropDownCausalidadesQuatro == null) {
            dropDownCausalidadesQuatro = new DropDown(id);
        }
        dropDownCausalidadesQuatro.setEnabled(false);
        dropDownCausalidadesQuatro.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoEncerramentoFinal.CausalidadeTresAdequadaConsistenteATres.A_TRES_NOVE_OUTROS.value().equals(modelEncerramentoFinal.getObject().getCausalidadeQuatro())) {
                    txtDescricaoOutrasCausalidades.setEnabled(true);
                } else {
                    txtDescricaoOutrasCausalidades.setEnabled(false);
                }
                txtDescricaoOutrasCausalidades.limpar(target);
            }
        });
        return dropDownCausalidadesQuatro;
    }

    private void grupoHemograma() {
        InvestigacaoAgravoHemograma proxyHemograma = on(InvestigacaoAgravoHemograma.class);
        containerHemograma = new WebMarkupContainer("containerHemograma", modelHemograma = new CompoundPropertyModel(new InvestigacaoAgravoHemograma()));
        containerHemograma.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerHemograma);

        containerHemograma.add(txtDataColeta = new DateChooser(path(proxyHemograma.getDataColeta())));
        containerHemograma.add(txtHemacias = new InputField(path(proxyHemograma.getHemacias())));
        containerHemograma.add(txtHemoglobina = new InputField(path(proxyHemograma.getHemoglobina())));
        containerHemograma.add(txtHematocrito = new InputField(path(proxyHemograma.getHematocrito())));
        containerHemograma.add(txtPlaquetas = new InputField(path(proxyHemograma.getPlaquetas())));
        containerHemograma.add(txtBastoes = new InputField(path(proxyHemograma.getBastoes())));
        containerHemograma.add(txtNeutrofilos = new InputField(path(proxyHemograma.getNeutrofilos())));
        containerHemograma.add(txtLinfocitos = new InputField(path(proxyHemograma.getLinfocitos())));
        containerHemograma.add(txtLeucocitos = new InputField(path(proxyHemograma.getLeucocitos())));
        containerHemograma.add(txtEosinofitos = new InputField(path(proxyHemograma.getEosinofitos())));
        containerHemograma.add(txtMonocitos = new InputField(path(proxyHemograma.getMonocitos())));

        containerHemograma.add(new AbstractAjaxButton("btnAdicionarHemograma") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarHemograma(target);
            }
        });

        containerHemograma.add(tableHemograma = new Table("tableHemograma", getColumnsHemograma(), getCollectionProviderHemograma()));
        tableHemograma.populate();
    }

    private void grupoBioquimica() {
        InvestigacaoAgravoBioquimica proxyBioquimica = on(InvestigacaoAgravoBioquimica.class);
        containerBioquimica = new WebMarkupContainer("containerBioquimica", modelBioquimica = new CompoundPropertyModel(new InvestigacaoAgravoBioquimica()));
        containerBioquimica.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerBioquimica);

        containerBioquimica.add(txtDataColetaBioquimica = new DateChooser(path(proxyBioquimica.getDataColeta())));
        containerBioquimica.add(txtBd = new InputField(path(proxyBioquimica.getBd())));
        containerBioquimica.add(txtBi = new InputField(path(proxyBioquimica.getBi())));
        containerBioquimica.add(txtBt = new InputField(path(proxyBioquimica.getBt())));
        containerBioquimica.add(txtUreia = new InputField(path(proxyBioquimica.getUreia())));
        containerBioquimica.add(txtCreatina = new InputField(path(proxyBioquimica.getCreatinina())));
        containerBioquimica.add(txtAstTgo = new InputField(path(proxyBioquimica.getAstTgo())));
        containerBioquimica.add(txtAltTg = new InputField(path(proxyBioquimica.getAltTgp())));
        containerBioquimica.add(txtGgt = new InputField(path(proxyBioquimica.getGgt())));
        containerBioquimica.add(txtFa = new InputField(path(proxyBioquimica.getFa())));
        containerBioquimica.add(txtRni = new InputField(path(proxyBioquimica.getRni())));
        containerBioquimica.add(txtPt = new InputField(path(proxyBioquimica.getPt())));
        containerBioquimica.add(txtPtt = new InputField(path(proxyBioquimica.getPtt())));

        containerBioquimica.add(new AbstractAjaxButton("btnAdicionarBioquimica") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarBioquimica(target);
            }
        });

        containerBioquimica.add(tableBioquimica = new Table("tableBioquimica", getColumnsBioquimica(), getCollectionProviderBioquimica()));
        tableBioquimica.populate();
    }

    private void grupoPuncaoLombar() {
        InvestigacaoAgravoPuncaoLombar proxyPuncaoLombar = on(InvestigacaoAgravoPuncaoLombar.class);
        containerPuncaoLombar = new WebMarkupContainer("containerPuncaoLombar", modelPuncaoLombar = new CompoundPropertyModel(new InvestigacaoAgravoPuncaoLombar()));
        containerPuncaoLombar.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerPuncaoLombar);

        containerPuncaoLombar.add(txtDataColetaPuncaoLombar = new DateChooser(path(proxyPuncaoLombar.getDataColeta())));
        containerPuncaoLombar.add(txtLeucocitosPl = new InputField(path(proxyPuncaoLombar.getLeucocitos())));
        containerPuncaoLombar.add(txtNeutrofilosPl = new InputField(path(proxyPuncaoLombar.getNeutrofilos())));
        containerPuncaoLombar.add(txtLinfocitosPl = new InputField(path(proxyPuncaoLombar.getLinfocitos())));
        containerPuncaoLombar.add(txtGlicosePl = new InputField(path(proxyPuncaoLombar.getGlicose())));
        containerPuncaoLombar.add(txtProteinasPl = new InputField(path(proxyPuncaoLombar.getProteinas())));
        containerPuncaoLombar.add(txtBacterioscopiaPl = new InputField(path(proxyPuncaoLombar.getBacterioscopia())));
        containerPuncaoLombar.add(txtCulturaLiquorPl = new InputField(path(proxyPuncaoLombar.getCulturaLiquor())));

        containerPuncaoLombar.add(new AbstractAjaxButton("btnAdicionarPuncaoLombar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarPuncaoLombar(target);
            }
        });

        containerPuncaoLombar.add(tablePuncaoLombar = new Table("tablePuncaoLombar", getColumnsPuncaoLombar(), getCollectionProviderPuncaoLombar()));
        tablePuncaoLombar.populate();
    }

    private void grupoUrina() {
        InvestigacaoAgravoUrina proxyUrina = on(InvestigacaoAgravoUrina.class);
        containerUrina = new WebMarkupContainer("containerUrina", modelUrina = new CompoundPropertyModel(new InvestigacaoAgravoUrina()));
        containerUrina.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerUrina);

        containerUrina.add(txtDataColetaUrina = new DateChooser(path(proxyUrina.getDataColeta())));
        containerUrina.add(dropDownTipoUrina = DropDownUtil.getIEnumDropDown(path(proxyUrina.getTipo()), InvestigacaoAgravoUrina.Tipo.values(), true));
        containerUrina.add(txtDescricaoUrina = new InputField(path(proxyUrina.getDescricao())));

        containerUrina.add(new AbstractAjaxButton("btnAdicionarUrina") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarUrina(target);
            }
        });

        containerUrina.add(tableUrina = new Table("tableUrina", getColumnsUrina(), getCollectionProviderUrina()));
        tableUrina.populate();
    }

    private void grupoDeteccaoViral() {
        InvestigacaoAgravoDeteccaoViral proxyDeteccaoViral = on(InvestigacaoAgravoDeteccaoViral.class);
        containerDeteccaoViral = new WebMarkupContainer("containerDeteccaoViral", modelDeteccaoViral = new CompoundPropertyModel(new InvestigacaoAgravoDeteccaoViral()));
        containerDeteccaoViral.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerDeteccaoViral);

        containerDeteccaoViral.add(txtDataColetaDeteccaoViral = new DateChooser(path(proxyDeteccaoViral.getDataColeta())));
        containerDeteccaoViral.add(dropDownAmostraDeteccaoViral = DropDownUtil.getIEnumDropDown(path(proxyDeteccaoViral.getAmostra()), InvestigacaoAgravoDeteccaoViral.Amostra.values(), true));
        dropDownAmostraDeteccaoViral.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoAgravoDeteccaoViral.Amostra.OUTROS.value().equals(modelDeteccaoViral.getObject().getAmostra())) {
                    txtDescricaoOutrosAmostraDeteccaoViral.setEnabled(true);
                } else {
                    txtDescricaoOutrosAmostraDeteccaoViral.setEnabled(false);
                }
                target.add(txtDescricaoOutrosAmostraDeteccaoViral);
            }
        });
        containerDeteccaoViral.add(txtDescricaoOutrosAmostraDeteccaoViral = new InputField(path(proxyDeteccaoViral.getDescricaoOutrasAmostra())));
        txtDescricaoOutrosAmostraDeteccaoViral.setEnabled(false);
        containerDeteccaoViral.add(txtResultadoDeteccaoViral = new InputField(path(proxyDeteccaoViral.getResultado())));

        containerDeteccaoViral.add(new AbstractAjaxButton("btnAdicionarDeteccaoViral") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarDeteccaoViral(target);
            }
        });

        containerDeteccaoViral.add(tableDeteccaoViral = new Table("tableDeteccaoViral", getColumnsDeteccaoViral(), getCollectionProviderDeteccaoViral()));
        tableDeteccaoViral.populate();
    }

    private void grupoAdicional() {
        InvestigacaoAgravoAdicional proxyAdicional = on(InvestigacaoAgravoAdicional.class);
        containerAdicional = new WebMarkupContainer("containerAdicional", modelAdicional = new CompoundPropertyModel(new InvestigacaoAgravoAdicional()));
        containerAdicional.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerAdicional);

        containerAdicional.add(txtDataColetaAdicional = new DateChooser(path(proxyAdicional.getDataColeta())));
        containerAdicional.add(dropDownExameAdicional = DropDownUtil.getIEnumDropDown(path(proxyAdicional.getExame()), InvestigacaoAgravoAdicional.Exame.values(), true));
        dropDownExameAdicional.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoAgravoAdicional.Exame.OUTROS.value().equals(modelAdicional.getObject().getExame())) {
                    txtDescricaoOutrosExameAdicional.setEnabled(true);
                } else {
                    txtDescricaoOutrosExameAdicional.setEnabled(false);
                }
                target.add(txtDescricaoOutrosExameAdicional);
            }
        });
        containerAdicional.add(txtDescricaoOutrosExameAdicional = new InputField(path(proxyAdicional.getDescricaoOutrosExame())));
        txtDescricaoOutrosExameAdicional.setEnabled(false);
        containerAdicional.add(txtResultadoAdicional = new InputField(path(proxyAdicional.getResultado())));

        containerAdicional.add(new AbstractAjaxButton("btnAdicionarAdicional") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarAdicional(target);
            }
        });

        containerAdicional.add(tableAdicional = new Table("tableAdicional", getColumnsAdicional(), getCollectionProviderAdicional()));
        tableAdicional.populate();
    }

    private void grupoOutros() {
        InvestigacaoAgravoOutros proxyOutros = on(InvestigacaoAgravoOutros.class);
        containerOutros = new WebMarkupContainer("containerOutros", modelOutros = new CompoundPropertyModel(new InvestigacaoAgravoOutros()));
        containerOutros.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerOutros);

        containerOutros.add(txtDataColetaOutros = new DateChooser(path(proxyOutros.getDataColeta())));
        containerOutros.add(txtMacroscopiaOutros = new InputField(path(proxyOutros.getMacroscopia())));
        containerOutros.add(txtMicroscopiaOutros = new InputField(path(proxyOutros.getMicroscopia())));
        containerOutros.add(txtAnatomopatologicoOutros = new InputField(path(proxyOutros.getAnatomopatologico())));

        containerOutros.add(new AbstractAjaxButton("btnAdicionarOutros") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarOutros(target);
            }
        });

        containerOutros.add(tableOutros = new Table("tableOutros", getColumnsOutros(), getCollectionProviderOutros()));
        tableOutros.populate();
    }

    private void grupoImunologia() {
        InvestigacaoAgravoImunologia proxyImunologia = on(InvestigacaoAgravoImunologia.class);
        containerImunologia = new WebMarkupContainer("containerImunologia", modelImunologia = new CompoundPropertyModel(new InvestigacaoAgravoImunologia()));
        containerImunologia.setOutputMarkupPlaceholderTag(true);
        getForm().add(containerImunologia);

        containerImunologia.add(dropDownAgravoImunologia = DropDownUtil.getIEnumDropDown(path(proxyImunologia.getAgravo()), InvestigacaoAgravoImunologia.Agravo.values(), true));
        dropDownAgravoImunologia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (InvestigacaoAgravoImunologia.Agravo.OUTROS.value().equals(modelImunologia.getObject().getAgravo())) {
                    txtDescricaoOutrosAgravoImunologia.setEnabled(true);
                } else {
                    txtDescricaoOutrosAgravoImunologia.setEnabled(false);
                }
                target.add(txtDescricaoOutrosAgravoImunologia);
            }
        });
        containerImunologia.add(txtDescricaoOutrosAgravoImunologia = new InputField(path(proxyImunologia.getDescricaoOutrosAgravo())));
        txtDescricaoOutrosAgravoImunologia.setEnabled(false);

        containerImunologia.add(txtDataColetaSorologiaImunologia = new DateChooser(path(proxyImunologia.getDataColetaSorologia())));
        containerImunologia.add(txtSangueSorologiaImunologia = new InputField(path(proxyImunologia.getSangueSorologia())));
        containerImunologia.add(txtLiquorSorologiaImunologia = new InputField(path(proxyImunologia.getLiquorSorologia())));

        containerImunologia.add(txtDataColetaPcrImunologia = new DateChooser(path(proxyImunologia.getDataColetaPcr())));
        containerImunologia.add(txtSanguePcrImunologia = new InputField(path(proxyImunologia.getSanguePcr())));
        containerImunologia.add(txtLiquorPcrImunologia = new InputField(path(proxyImunologia.getLiquorPcr())));

        containerImunologia.add(new AbstractAjaxButton("btnAdicionarImunologia") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarImunologia(target);
            }
        });

        containerImunologia.add(tableImunologia = new Table("tableImunologia", getColumnsImunologia(), getCollectionProviderImunologia()));
        tableImunologia.populate();
    }

    private void habilitarDoseConsideradaInvalidaOutros(AjaxRequestTarget target, boolean habilitar) {
        txtDescricaoOutrosCondutaErroImunizacaoItem.limpar(target);
        txtDescricaoOutrosCondutaErroImunizacaoItem.setEnabled(habilitar);
        target.add(txtDescricaoOutrosCondutaErroImunizacaoItem);
    }


    private void habilitarDoseConsideradaInvalida(AjaxRequestTarget target, boolean habilitar) {
        limparDoseConsideradaInvalida(target);
        containerDoseConsideradaInvalida.setEnabled(habilitar);
        habilitarDoseConsideradaInvalidaOutros(target, false);
        target.add(containerDoseConsideradaInvalida);
    }

    private void limparDoseConsideradaInvalida(AjaxRequestTarget target) {
        radioGroupDoseConsideradaInvalida.limpar();
        txtDescricaoOutrosCondutaErroImunizacaoItem.limpar(target);
    }

    private void habilitarCamposOutrosErrosImunizacao(AjaxRequestTarget target, boolean habilitar) {
        txtDescricaoOutrosErrosImunizacao.limpar(target);
        txtDescricaoOutrosErrosImunizacao.setEnabled(habilitar);
        target.add(txtDescricaoOutrosErrosImunizacao);
    }

    private void adicionarMedicacaoUso(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoMedicacaoUso investigacaoMedicacaoUso = modelInvestigacaoMedicacaoUso.getObject();

        if (InvestigacaoMedicacaoUso.TipoMedicacao.OUTROS.value().equals(investigacaoMedicacaoUso.getTipoMedicacao())
                && investigacaoMedicacaoUso.getDescricaoTipoOutros() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoTipoMedicamento"));
        }
        if (investigacaoMedicacaoUso == null
                || investigacaoMedicacaoUso.getTipoMedicacao() == null) {
            throw new ValidacaoException(BundleManager.getString("informeAoMenosUmDosCamposDaMedicacaoEmUso"));
        }

        for (InvestigacaoMedicacaoUso _investigacaoMedicacaoUso : investigacaoMedicacaoUsoList) {
            if (Coalesce.asString(_investigacaoMedicacaoUso.getTipoMedicacao()).equals(Coalesce.asString(investigacaoMedicacaoUso.getTipoMedicacao()))) {
                limparMedicacao(target);
                throw new ValidacaoException(BundleManager.getString("itemDoTipoMedicamentoJaAdicionado"));
            }
        }

        investigacaoMedicacaoUsoList.add(investigacaoMedicacaoUso);
        tableMedicacaoUso.update(target);
        modelInvestigacaoMedicacaoUso.setObject(new InvestigacaoMedicacaoUso());

        limparMedicacao(target);
        target.focusComponent(txtDataOcorencia);
    }

    private void limparMedicacao(AjaxRequestTarget target) {
        dropDownTipoMedicacao.limpar(target);
        txtDescricaoTipoOutros.limpar(target);
        txtNomeGenerico.limpar(target);
        txtViaAdministracaoMedicacao.limpar(target);
        txtDataInicio.limpar(target);
        chkUsoContinuo.limpar(target);
        txtDataTermino.limpar(target);
        txtDescricaoTipoOutros.setEnabled(false);
    }

    private void adicionarManifestacoesLocais(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoManifestacoes investigacaoManifestacoes = modelManifestacoesLocais.getObject();

        if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(investigacaoManifestacoes.getTipoEventoAdverso())
                && investigacaoManifestacoes.getDescricaoOutrosEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoEventoAdverso"));
        }
        if (investigacaoManifestacoes == null
                || investigacaoManifestacoes.getTipoEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosManifestacoesLocal"));
        }

        if (investigacaoManifestacoes.getDataInicioManifestacao() != null && investigacaoManifestacoes.getDataTerminoManifestacao() != null
                && investigacaoManifestacoes.getDataTerminoManifestacao().compareTo(investigacaoManifestacoes.getDataInicioManifestacao()) < 0) {
            throw new ValidacaoException(BundleManager.getString("msgDataInicioNaoPodeMaiorDataTermino"));
        }

        for (InvestigacaoManifestacoes _investigacaoManifestacoes : investigacaoManifestacoesLocaisList) {
            if (Coalesce.asString(_investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()))
                    && Coalesce.asString(_investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()))) {
                limparManifestacoesLocais(target);
                throw new ValidacaoException(BundleManager.getString("itemDoTipoMedicamentoJaAdicionado"));
            }
        }

        investigacaoManifestacoes.setTipoManifestacao(InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACAO_LOCAL.value());
        if (investigacaoManifestacoes.getFlagEmAcompanhamento() == null) {
            investigacaoManifestacoes.setFlagEmAcompanhamento(RepositoryComponentDefault.NAO_LONG);
        }
        investigacaoManifestacoesLocaisList.add(investigacaoManifestacoes);
        tableManifestacoesLocais.update(target);
        modelManifestacoesLocais.setObject(new InvestigacaoManifestacoes());

        limparManifestacoesLocais(target);
        target.focusComponent(txtDataOcorencia);
    }

    private void limparManifestacoesLocais(AjaxRequestTarget target) {
        dropDownTipoManifestacoesLocais.limpar(target);
        txtDescricaoTipoOutrosManifestacoesLocais.limpar(target);
        txtDataInicioManifestacoesLocais.limpar(target);
        txtDataTerminoManifestacoesLocais.limpar(target);
        txtTempoInicioManifestacoesLocais.limpar(target);
        txtDescricaoTipoOutrosManifestacoesLocais.setEnabled(false);
        chkEmAcompanhamentoManifestacoesLocais.limpar(target);
        chkEmAcompanhamentoManifestacoesLocais.setEnabled(true);
    }

    private void limparHemograma(AjaxRequestTarget target) {
        txtDataColeta.limpar(target);
        txtHemacias.limpar(target);
        txtHemoglobina.limpar(target);
        txtHematocrito.limpar(target);
        txtPlaquetas.limpar(target);
        txtBastoes.limpar(target);
        txtNeutrofilos.limpar(target);
        txtLinfocitos.limpar(target);
        txtLeucocitos.limpar(target);
        txtEosinofitos.limpar(target);
        txtMonocitos.limpar(target);
    }

    private void limparEncerramentoFinal(AjaxRequestTarget target) {
        dropDownVacinaAplicadaEncerramentoFinal.limpar(target);
        txtDiagnostico.limpar(target);
        dropDownConduta.limpar(target);
        txtDescricaoOutrasCondutas.limpar(target);
        dropDownCausalidadesUm.limpar(target);
        dropDownCausalidadesDois.limpar(target);
        dropDownCausalidadesDois.setEnabled(false);
        dropDownCausalidadesTres.limpar(target);
        dropDownCausalidadesTres.setEnabled(false);
        dropDownCausalidadesQuatro.limpar(target);
        dropDownCausalidadesQuatro.setEnabled(false);
        target.add(dropDownCausalidadesDois);
        target.add(dropDownCausalidadesTres);
        target.add(dropDownCausalidadesTres);
    }

    private void limparBioquimica(AjaxRequestTarget target) {
        txtDataColetaBioquimica.limpar(target);
        txtBd.limpar(target);
        txtBi.limpar(target);
        txtBt.limpar(target);
        txtUreia.limpar(target);
        txtCreatina.limpar(target);
        txtAstTgo.limpar(target);
        txtAltTg.limpar(target);
        txtGgt.limpar(target);
        txtFa.limpar(target);
        txtRni.limpar(target);
        txtPt.limpar(target);
        txtPtt.limpar(target);
    }

    private void limparPuncaoLombar(AjaxRequestTarget target) {
        txtDataColetaPuncaoLombar.limpar(target);
        txtLeucocitosPl.limpar(target);
        txtNeutrofilosPl.limpar(target);
        txtLinfocitosPl.limpar(target);
        txtGlicosePl.limpar(target);
        txtProteinasPl.limpar(target);
        txtBacterioscopiaPl.limpar(target);
        txtCulturaLiquorPl.limpar(target);
    }

    private void limparUrina(AjaxRequestTarget target) {
        txtDataColetaUrina.limpar(target);
        dropDownTipoUrina.limpar(target);
        txtDescricaoUrina.limpar(target);
    }

    private void limparDeteccaoViral(AjaxRequestTarget target) {
        txtDataColetaDeteccaoViral.limpar(target);
        dropDownAmostraDeteccaoViral.limpar(target);
        txtResultadoDeteccaoViral.limpar(target);
        txtDescricaoOutrosAmostraDeteccaoViral.limpar(target);
        txtDescricaoOutrosAmostraDeteccaoViral.setEnabled(false);
        target.add(txtDescricaoOutrosAmostraDeteccaoViral);
    }

    private void limparAdicional(AjaxRequestTarget target) {
        txtDataColetaAdicional.limpar(target);
        dropDownExameAdicional.limpar(target);
        txtResultadoAdicional.limpar(target);
        txtDescricaoOutrosExameAdicional.limpar(target);
        txtDescricaoOutrosExameAdicional.setEnabled(false);
        target.add(txtDescricaoOutrosExameAdicional);
    }

    private void limparOutros(AjaxRequestTarget target) {
        txtDataColetaOutros.limpar(target);
        txtMacroscopiaOutros.limpar(target);
        txtMicroscopiaOutros.limpar(target);
        txtAnatomopatologicoOutros.limpar(target);
    }

    private void limparImunologia(AjaxRequestTarget target) {
        dropDownAgravoImunologia.limpar(target);
        txtDescricaoOutrosAgravoImunologia.limpar(target);
        txtDescricaoOutrosAgravoImunologia.setEnabled(false);
        txtDataColetaSorologiaImunologia.limpar(target);
        txtDataColetaPcrImunologia.limpar(target);
        txtSangueSorologiaImunologia.limpar(target);
        txtSanguePcrImunologia.limpar(target);
        txtLiquorSorologiaImunologia.limpar(target);
        txtLiquorPcrImunologia.limpar(target);
        target.add(txtDescricaoOutrosAgravoImunologia);
    }

    private List<IColumn> getColumnsEncerramentoFinal() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoEncerramentoFinal proxy = on(InvestigacaoEncerramentoFinal.class);

        columns.add(getCustomColumnEncerramentoFinal());
        columns.add(createColumn(BundleManager.getString("imunobiologico"), proxy.getVacinaAplicacao().getDescricaoVacina()));
        columns.add(createColumn(BundleManager.getString("diagnostico"), proxy.getDiagnostico()));
        columns.add(createColumn(BundleManager.getString("conduta"), proxy.getDescricaoConduta()));
        columns.add(createColumn(BundleManager.getString("causalidades"), proxy.getDescricaoCausalidade()));
        return columns;
    }

    private CustomColumn<InvestigacaoEncerramentoFinal> getCustomColumnEncerramentoFinal() {
        return new CustomColumn<InvestigacaoEncerramentoFinal>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoEncerramentoFinal rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerEncerramentoFinal(target, rowObject);
                    }
                };
            }
        };
    }

    private void removerEncerramentoFinal(AjaxRequestTarget target, VacinaAplicacao vacinaAplicacao) {
        for (InvestigacaoEncerramentoFinal encerramentoFinal : investigacaoEncerramentoFinalList) {
            if (encerramentoFinal.getVacinaAplicacao().equals(vacinaAplicacao)) {
                removerEncerramentoFinal(target, encerramentoFinal);
            }
        }
    }

    private void removerEncerramentoFinal(AjaxRequestTarget target, InvestigacaoEncerramentoFinal rowObject) {
        int i = 0;
        for (InvestigacaoEncerramentoFinal investigacaoEncerramentoFinal : investigacaoEncerramentoFinalList) {
            if (Coalesce.asString(rowObject.getVacinaAplicacao().getCodigo()).equals(Coalesce.asString(investigacaoEncerramentoFinal.getVacinaAplicacao().getCodigo()))
                    && Coalesce.asString(rowObject.getDiagnostico()).equals(Coalesce.asString(investigacaoEncerramentoFinal.getDiagnostico()))
                    && Coalesce.asString(rowObject.getConduta()).equals(Coalesce.asString(investigacaoEncerramentoFinal.getConduta()))
                    && Coalesce.asString(rowObject.getCausalidadeUm()).equals(Coalesce.asString(investigacaoEncerramentoFinal.getCausalidadeUm()))
                    && Coalesce.asString(rowObject.getCausalidadeDois()).equals(Coalesce.asString(investigacaoEncerramentoFinal.getCausalidadeDois()))
                    && Coalesce.asString(rowObject.getCausalidadeTres()).equals(Coalesce.asString(investigacaoEncerramentoFinal.getCausalidadeTres()))
                    && Coalesce.asString(rowObject.getCausalidadeQuatro()).equals(Coalesce.asString(investigacaoEncerramentoFinal.getCausalidadeQuatro()))
            ) {
                investigacaoEncerramentoFinalList.remove(i);
                break;
            }
            i++;
        }
        tableEncerramentoFinal.update(target);
    }

    public ICollectionProvider getCollectionProviderEncerramentoFinal() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoEncerramentoFinalList;
            }
        };
    }

    private List<IColumn> getColumnsHemograma() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoAgravoHemograma proxy = on(InvestigacaoAgravoHemograma.class);

        columns.add(getCustomColumnHemograma());
        columns.add(createColumn(BundleManager.getString("dataColeta"), proxy.getDataColeta()));
        columns.add(createColumn(BundleManager.getString("hemacias"), proxy.getHemacias()));
        columns.add(createColumn(BundleManager.getString("hemoglobina"), proxy.getHemoglobina()));
        columns.add(createColumn(BundleManager.getString("hematocrito"), proxy.getHematocrito()));
        columns.add(createColumn(BundleManager.getString("plaquetas"), proxy.getPlaquetas()));
        columns.add(createColumn(BundleManager.getString("bastoes"), proxy.getBastoes()));
        columns.add(createColumn(BundleManager.getString("neutrofilos"), proxy.getNeutrofilos()));
        columns.add(createColumn(BundleManager.getString("linfocitos"), proxy.getLinfocitos()));
        columns.add(createColumn(BundleManager.getString("leucocitos"), proxy.getLeucocitos()));
        columns.add(createColumn(BundleManager.getString("eosinofitos"), proxy.getEosinofitos()));
        columns.add(createColumn(BundleManager.getString("monocitos"), proxy.getMonocitos()));
        return columns;
    }

    private CustomColumn<InvestigacaoAgravoHemograma> getCustomColumnHemograma() {
        return new CustomColumn<InvestigacaoAgravoHemograma>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoAgravoHemograma rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerHemograma(rowObject);
                        tableHemograma.update(target);
                    }
                };
            }
        };
    }

    private void removerHemograma(InvestigacaoAgravoHemograma rowObject) {
        int i = 0;
        for (InvestigacaoAgravoHemograma investigacaoAgravoHemograma : investigacaoHemogramaList) {
            if ((rowObject.getDataColeta() == null && investigacaoAgravoHemograma.getDataColeta() == null || rowObject.getDataColeta().equals(investigacaoAgravoHemograma.getDataColeta()))
                    && Coalesce.asString(rowObject.getHemacias()).equals(Coalesce.asString(investigacaoAgravoHemograma.getHemacias()))
                    && Coalesce.asString(rowObject.getHemoglobina()).equals(Coalesce.asString(investigacaoAgravoHemograma.getHemoglobina()))
                    && Coalesce.asString(rowObject.getHematocrito()).equals(Coalesce.asString(investigacaoAgravoHemograma.getHematocrito()))
                    && Coalesce.asString(rowObject.getPlaquetas()).equals(Coalesce.asString(investigacaoAgravoHemograma.getPlaquetas()))
                    && Coalesce.asString(rowObject.getBastoes()).equals(Coalesce.asString(investigacaoAgravoHemograma.getBastoes()))
                    && Coalesce.asString(rowObject.getNeutrofilos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getNeutrofilos()))
                    && Coalesce.asString(rowObject.getLinfocitos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getLinfocitos()))
                    && Coalesce.asString(rowObject.getEosinofitos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getEosinofitos()))
                    && Coalesce.asString(rowObject.getLeucocitos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getLeucocitos()))
                    && Coalesce.asString(rowObject.getMonocitos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getMonocitos()))
            ) {
                investigacaoHemogramaList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderHemograma() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoHemogramaList;
            }
        };
    }

    private List<IColumn> getColumnsBioquimica() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoAgravoBioquimica proxy = on(InvestigacaoAgravoBioquimica.class);

        columns.add(getCustomColumnBioquimica());
        columns.add(createColumn(BundleManager.getString("dataColeta"), proxy.getDataColeta()));
        columns.add(createColumn(BundleManager.getString("bdMgdl"), proxy.getBd()));
        columns.add(createColumn(BundleManager.getString("biMgdl"), proxy.getBi()));
        columns.add(createColumn(BundleManager.getString("btMgdl"), proxy.getBt()));
        columns.add(createColumn(BundleManager.getString("ureiaMgdl"), proxy.getUreia()));
        columns.add(createColumn(BundleManager.getString("creatininaMgdl"), proxy.getCreatinina()));
        columns.add(createColumn(BundleManager.getString("astTGO"), proxy.getAstTgo()));
        columns.add(createColumn(BundleManager.getString("altTGP"), proxy.getAltTgp()));
        columns.add(createColumn(BundleManager.getString("ggt"), proxy.getGgt()));
        columns.add(createColumn(BundleManager.getString("fa"), proxy.getFa()));
        columns.add(createColumn(BundleManager.getString("rni"), proxy.getRni()));
        columns.add(createColumn(BundleManager.getString("pt"), proxy.getPt()));
        columns.add(createColumn(BundleManager.getString("ptt"), proxy.getPtt()));
        return columns;
    }

    private CustomColumn<InvestigacaoAgravoBioquimica> getCustomColumnBioquimica() {
        return new CustomColumn<InvestigacaoAgravoBioquimica>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoAgravoBioquimica rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerBioquimica(rowObject);
                        tableBioquimica.update(target);
                    }
                };
            }
        };
    }

    private void removerBioquimica(InvestigacaoAgravoBioquimica rowObject) {
        int i = 0;
        for (InvestigacaoAgravoBioquimica investigacaoAgravoBioquimica : investigacaoBioquimicaList) {
            if ((rowObject.getDataColeta() == null && investigacaoAgravoBioquimica.getDataColeta() == null || rowObject.getDataColeta().equals(investigacaoAgravoBioquimica.getDataColeta()))
                    && Coalesce.asString(rowObject.getBd()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getBd()))
                    && Coalesce.asString(rowObject.getBi()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getBi()))
                    && Coalesce.asString(rowObject.getBt()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getBt()))
                    && Coalesce.asString(rowObject.getUreia()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getUreia()))
                    && Coalesce.asString(rowObject.getCreatinina()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getCreatinina()))
                    && Coalesce.asString(rowObject.getAstTgo()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getAstTgo()))
                    && Coalesce.asString(rowObject.getAltTgp()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getAltTgp()))
                    && Coalesce.asString(rowObject.getGgt()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getGgt()))
                    && Coalesce.asString(rowObject.getFa()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getFa()))
                    && Coalesce.asString(rowObject.getRni()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getRni()))
                    && Coalesce.asString(rowObject.getPt()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getPt()))
                    && Coalesce.asString(rowObject.getPtt()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getPtt()))
            ) {
                investigacaoBioquimicaList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderBioquimica() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoBioquimicaList;
            }
        };
    }

    private List<IColumn> getColumnsPuncaoLombar() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoAgravoPuncaoLombar proxy = on(InvestigacaoAgravoPuncaoLombar.class);
        columns.add(getCustomColumnPuncaoLombar());
        columns.add(createColumn(BundleManager.getString("dataColeta"), proxy.getDataColeta()));
        columns.add(createColumn(BundleManager.getString("leucocitosMC"), proxy.getLeucocitos()));
        columns.add(createColumn(BundleManager.getString("neutrofilos"), proxy.getNeutrofilos()));
        columns.add(createColumn(BundleManager.getString("linfocitos"), proxy.getLinfocitos()));
        columns.add(createColumn(BundleManager.getString("glicoseMg"), proxy.getGlicose()));
        columns.add(createColumn(BundleManager.getString("proteinas"), proxy.getProteinas()));
        columns.add(createColumn(BundleManager.getString("bacterioscopiaGRAM"), proxy.getBacterioscopia()));
        columns.add(createColumn(BundleManager.getString("culturaLiquorLeuco"), proxy.getCulturaLiquor()));
        return columns;
    }

    private CustomColumn<InvestigacaoAgravoPuncaoLombar> getCustomColumnPuncaoLombar() {
        return new CustomColumn<InvestigacaoAgravoPuncaoLombar>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoAgravoPuncaoLombar rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerPuncaoLombar(rowObject);
                        tablePuncaoLombar.update(target);
                    }
                };
            }
        };
    }

    private void removerPuncaoLombar(InvestigacaoAgravoPuncaoLombar rowObject) {
        int i = 0;
        for (InvestigacaoAgravoPuncaoLombar investigacaoAgravoPuncaoLombar : investigacaoPuncaoLombarList) {
            if ((rowObject.getDataColeta() == null && investigacaoAgravoPuncaoLombar.getDataColeta() == null || rowObject.getDataColeta().equals(investigacaoAgravoPuncaoLombar.getDataColeta()))
                    && Coalesce.asString(rowObject.getLeucocitos()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getLeucocitos()))
                    && Coalesce.asString(rowObject.getNeutrofilos()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getNeutrofilos()))
                    && Coalesce.asString(rowObject.getLinfocitos()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getLinfocitos()))
                    && Coalesce.asString(rowObject.getGlicose()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getGlicose()))
                    && Coalesce.asString(rowObject.getProteinas()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getProteinas()))
                    && Coalesce.asString(rowObject.getBacterioscopia()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getBacterioscopia()))
                    && Coalesce.asString(rowObject.getCulturaLiquor()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getCulturaLiquor()))
            ) {
                investigacaoPuncaoLombarList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderPuncaoLombar() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoPuncaoLombarList;
            }
        };
    }

    private List<IColumn> getColumnsUrina() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoAgravoUrina proxy = on(InvestigacaoAgravoUrina.class);
        columns.add(getCustomColumnUrina());
        columns.add(createColumn(BundleManager.getString("dataColeta"), proxy.getDataColeta()));
        columns.add(createColumn(BundleManager.getString("tipo"), proxy.getDescricaoTipo()));
        columns.add(createColumn(BundleManager.getString("descricao"), proxy.getDescricao()));
        return columns;
    }

    private CustomColumn<InvestigacaoAgravoUrina> getCustomColumnUrina() {
        return new CustomColumn<InvestigacaoAgravoUrina>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoAgravoUrina rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerUrina(rowObject);
                        tableUrina.update(target);
                    }
                };
            }
        };
    }

    private void removerUrina(InvestigacaoAgravoUrina rowObject) {
        int i = 0;
        for (InvestigacaoAgravoUrina investigacaoAgravoUrina : investigacaoUrinaList) {
            if ((rowObject.getDataColeta() == null && investigacaoAgravoUrina.getDataColeta() == null || rowObject.getDataColeta().equals(investigacaoAgravoUrina.getDataColeta()))
                    && Coalesce.asString(rowObject.getTipo()).equals(Coalesce.asString(investigacaoAgravoUrina.getTipo()))
                    && Coalesce.asString(rowObject.getDescricao()).equals(Coalesce.asString(investigacaoAgravoUrina.getDescricao()))
            ) {
                investigacaoUrinaList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderUrina() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoUrinaList;
            }
        };
    }

    private List<IColumn> getColumnsDeteccaoViral() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoAgravoDeteccaoViral proxy = on(InvestigacaoAgravoDeteccaoViral.class);
        columns.add(getCustomColumnDeteccaoViral());
        columns.add(createColumn(BundleManager.getString("dataColeta"), proxy.getDataColeta()));
        columns.add(createColumn(BundleManager.getString("amostra"), proxy.getDescricaoAmostra()));
        columns.add(createColumn(BundleManager.getString("resultado"), proxy.getResultado()));
        return columns;
    }

    private CustomColumn<InvestigacaoAgravoDeteccaoViral> getCustomColumnDeteccaoViral() {
        return new CustomColumn<InvestigacaoAgravoDeteccaoViral>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoAgravoDeteccaoViral rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerDeteccaoViral(rowObject);
                        tableDeteccaoViral.update(target);
                    }
                };
            }
        };
    }

    private void removerDeteccaoViral(InvestigacaoAgravoDeteccaoViral rowObject) {
        int i = 0;
        for (InvestigacaoAgravoDeteccaoViral investigacaoAgravoDeteccaoViral : investigacaoDeteccaoViralList) {
            if ((rowObject.getDataColeta() == null && investigacaoAgravoDeteccaoViral.getDataColeta() == null || rowObject.getDataColeta().equals(investigacaoAgravoDeteccaoViral.getDataColeta()))
                    && Coalesce.asString(rowObject.getAmostra()).equals(Coalesce.asString(investigacaoAgravoDeteccaoViral.getAmostra()))
                    && Coalesce.asString(rowObject.getDescricaoOutrasAmostra()).equals(Coalesce.asString(investigacaoAgravoDeteccaoViral.getDescricaoOutrasAmostra()))
                    && Coalesce.asString(rowObject.getResultado()).equals(Coalesce.asString(investigacaoAgravoDeteccaoViral.getResultado()))
            ) {
                investigacaoDeteccaoViralList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderDeteccaoViral() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoDeteccaoViralList;
            }
        };
    }

    private List<IColumn> getColumnsAdicional() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoAgravoAdicional proxy = on(InvestigacaoAgravoAdicional.class);
        columns.add(getCustomColumnAdicional());
        columns.add(createColumn(BundleManager.getString("dataColeta"), proxy.getDataColeta()));
        columns.add(createColumn(BundleManager.getString("exame"), proxy.getDescricaoExame()));
        columns.add(createColumn(BundleManager.getString("resultado"), proxy.getResultado()));
        return columns;
    }

    private CustomColumn<InvestigacaoAgravoAdicional> getCustomColumnAdicional() {
        return new CustomColumn<InvestigacaoAgravoAdicional>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoAgravoAdicional rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerAdicional(rowObject);
                        tableAdicional.update(target);
                    }
                };
            }
        };
    }

    private void removerAdicional(InvestigacaoAgravoAdicional rowObject) {
        int i = 0;
        for (InvestigacaoAgravoAdicional investigacaoAgravoAdicional : investigacaoAdicionalList) {
            if ((rowObject.getDataColeta() == null && investigacaoAgravoAdicional.getDataColeta() == null || rowObject.getDataColeta().equals(investigacaoAgravoAdicional.getDataColeta()))
                    && Coalesce.asString(rowObject.getExame()).equals(Coalesce.asString(investigacaoAgravoAdicional.getExame()))
                    && Coalesce.asString(rowObject.getDescricaoOutrosExame()).equals(Coalesce.asString(investigacaoAgravoAdicional.getDescricaoOutrosExame()))
                    && Coalesce.asString(rowObject.getResultado()).equals(Coalesce.asString(investigacaoAgravoAdicional.getResultado()))
            ) {
                investigacaoAdicionalList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderAdicional() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoAdicionalList;
            }
        };
    }


    private List<IColumn> getColumnsOutros() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoAgravoOutros proxy = on(InvestigacaoAgravoOutros.class);
        columns.add(getCustomColumnOutros());
        columns.add(createColumn(BundleManager.getString("dataColeta"), proxy.getDataColeta()));
        columns.add(createColumn(BundleManager.getString("macroscopia"), proxy.getMacroscopia()));
        columns.add(createColumn(BundleManager.getString("microscopia"), proxy.getMicroscopia()));
        columns.add(createColumn(BundleManager.getString("anatomopatologico"), proxy.getAnatomopatologico()));
        return columns;
    }

    private CustomColumn<InvestigacaoAgravoOutros> getCustomColumnOutros() {
        return new CustomColumn<InvestigacaoAgravoOutros>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoAgravoOutros rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerOutros(rowObject);
                        tableOutros.update(target);
                    }
                };
            }
        };
    }

    private void removerOutros(InvestigacaoAgravoOutros rowObject) {
        int i = 0;
        for (InvestigacaoAgravoOutros investigacaoAgravoOutros : investigacaoOutrosList) {
            if ((rowObject.getDataColeta() == null && investigacaoAgravoOutros.getDataColeta() == null || rowObject.getDataColeta().equals(investigacaoAgravoOutros.getDataColeta()))
                    && Coalesce.asString(rowObject.getMacroscopia()).equals(Coalesce.asString(investigacaoAgravoOutros.getMacroscopia()))
                    && Coalesce.asString(rowObject.getMicroscopia()).equals(Coalesce.asString(investigacaoAgravoOutros.getMicroscopia()))
                    && Coalesce.asString(rowObject.getAnatomopatologico()).equals(Coalesce.asString(investigacaoAgravoOutros.getAnatomopatologico()))
            ) {
                investigacaoOutrosList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderOutros() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoOutrosList;
            }
        };
    }

    private List<IColumn> getColumnsImunologia() {
        List<IColumn> columns = new ArrayList<IColumn>();
        InvestigacaoAgravoImunologia proxy = on(InvestigacaoAgravoImunologia.class);
        columns.add(getCustomColumnImunologia());
        columns.add(createColumn(BundleManager.getString("agravo"), proxy.getDescricaoAgravo()));
        columns.add(createColumn(BundleManager.getString("dataColetaSorologia"), proxy.getDataColetaSorologia()));
        columns.add(createColumn(BundleManager.getString("sangueSorologia"), proxy.getSangueSorologia()));
        columns.add(createColumn(BundleManager.getString("liquorSorologia"), proxy.getLiquorSorologia()));
        columns.add(createColumn(BundleManager.getString("dataColetaPcr"), proxy.getDataColetaPcr()));
        columns.add(createColumn(BundleManager.getString("sanguePcr"), proxy.getSanguePcr()));
        columns.add(createColumn(BundleManager.getString("liquorPcr"), proxy.getLiquorPcr()));
        return columns;
    }

    private CustomColumn<InvestigacaoAgravoImunologia> getCustomColumnImunologia() {
        return new CustomColumn<InvestigacaoAgravoImunologia>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoAgravoImunologia rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerImunologia(rowObject);
                        tableImunologia.update(target);
                    }
                };
            }
        };
    }

    private void removerImunologia(InvestigacaoAgravoImunologia rowObject) {
        int i = 0;
        for (InvestigacaoAgravoImunologia investigacaoAgravoImunologia : investigacaoImunologiaList) {
            if ((rowObject.getDataColetaSorologia() == null && investigacaoAgravoImunologia.getDataColetaSorologia() == null || rowObject.getDataColetaSorologia().equals(investigacaoAgravoImunologia.getDataColetaSorologia()))
                    && (rowObject.getDataColetaPcr() == null && investigacaoAgravoImunologia.getDataColetaPcr() == null || rowObject.getDataColetaPcr().equals(investigacaoAgravoImunologia.getDataColetaPcr()))
                    && Coalesce.asString(rowObject.getAgravo()).equals(Coalesce.asString(investigacaoAgravoImunologia.getAgravo()))
                    && Coalesce.asString(rowObject.getSangueSorologia()).equals(Coalesce.asString(investigacaoAgravoImunologia.getSangueSorologia()))
                    && Coalesce.asString(rowObject.getSanguePcr()).equals(Coalesce.asString(investigacaoAgravoImunologia.getSanguePcr()))
                    && Coalesce.asString(rowObject.getLiquorSorologia()).equals(Coalesce.asString(investigacaoAgravoImunologia.getLiquorSorologia()))
                    && Coalesce.asString(rowObject.getLiquorPcr()).equals(Coalesce.asString(investigacaoAgravoImunologia.getLiquorPcr()))
            ) {
                investigacaoImunologiaList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderImunologia() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoImunologiaList;
            }
        };
    }

    private List<IColumn> getColumnsManifestacoesLocais() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoManifestacoes proxy = on(InvestigacaoManifestacoes.class);

        columns.add(getCustomColumnManifestacoesLocais());
        columns.add(createColumn(BundleManager.getString("eventoAdverso"), proxy.getDescricaoEventoAdverso()));
        columns.add(createColumn(BundleManager.getString("dataInicio"), proxy.getDataInicioManifestacao()));
        columns.add(createColumn(BundleManager.getString("dataTermino"), proxy.getDataTerminoManifestacao()));
        columns.add(createColumn(BundleManager.getString("tempoInicio"), proxy.getTempoInicio()));
        columns.add(createColumn(BundleManager.getString("emAcompanhamento"), proxy.getDescricaoEmAcompanhamento()));
        return columns;
    }

    private CustomColumn<InvestigacaoManifestacoes> getCustomColumnManifestacoesLocais() {
        return new CustomColumn<InvestigacaoManifestacoes>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoManifestacoes rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerManifestacoesLocais(rowObject);
                        tableManifestacoesLocais.update(target);
                    }
                };
            }
        };
    }

    private void removerManifestacoesLocais(InvestigacaoManifestacoes rowObject) {
        int i = 0;
        for (InvestigacaoManifestacoes investigacaoManifestacoes : investigacaoManifestacoesLocaisList) {
            if (Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(rowObject.getTipoEventoAdverso()))
                    && Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(rowObject.getTipoManifestacao()))) {
                investigacaoManifestacoesLocaisList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderManifestacoesLocais() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoManifestacoesLocaisList;
            }
        };
    }

    private List<IColumn> getColumnsMedicacaoUso() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoMedicacaoUso proxy = on(InvestigacaoMedicacaoUso.class);

        columns.add(getCustomColumnMedicacaoUso());
        columns.add(createColumn(BundleManager.getString("tipoMedicamento"), proxy.getDescricaoTipoMedicamento()));
        columns.add(createColumn(BundleManager.getString("nomeGenerico"), proxy.getNomeGenerico()));
        columns.add(createColumn(BundleManager.getString("viaAdministracao"), proxy.getViaAdministracao()));
        columns.add(createColumn(BundleManager.getString("dataInicio"), proxy.getDataInicio()));
        columns.add(createColumn(BundleManager.getString("usoContinuo"), proxy.getDescricaoUsoContinuo()));
        columns.add(createColumn(BundleManager.getString("dataTermino"), proxy.getDataTermino()));
        return columns;
    }

    private CustomColumn<InvestigacaoMedicacaoUso> getCustomColumnMedicacaoUso() {
        return new CustomColumn<InvestigacaoMedicacaoUso>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoMedicacaoUso rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerMedicacaoUso(rowObject);
                        tableMedicacaoUso.update(target);
                    }
                };
            }
        };
    }

    private void enableContainerMedicacaoUso(AjaxRequestTarget target, boolean enabled) {
        containerMedicacao.setEnabled(enabled);
        dropDownTipoMedicacao.limpar(target);
        limparMedicacao(target);
        investigacaoMedicacaoUsoList = new ArrayList<>();
        target.add(containerMedicacao);
    }

    private void removerMedicacaoUso(InvestigacaoMedicacaoUso rowObject) {
        int i = 0;
        for (InvestigacaoMedicacaoUso investigacaoMedicacaoUso : investigacaoMedicacaoUsoList) {
            if (Coalesce.asString(investigacaoMedicacaoUso.getTipoMedicacao()).equals(Coalesce.asString(rowObject.getTipoMedicacao()))) {
                investigacaoMedicacaoUsoList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderMedicacaoUso() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoMedicacaoUsoList;
            }
        };
    }

    private void addCheckboxDoencas() {
        containerDoencas.add(checkBoxDoencaAids = new CheckBoxLongValue("doencaAids", InvestigacaoAgravo.DoencasPreExistentes.AIDS.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaAlergiaMedicamento = new CheckBoxLongValue("doencaAlergiaMedicamento", InvestigacaoAgravo.DoencasPreExistentes.ALERGIA_MEDICAMENTO.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaAlergiaAlimentar = new CheckBoxLongValue("doencaAlergiaAlimentar", InvestigacaoAgravo.DoencasPreExistentes.ALERGIA_ALIMENTAR.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaDiabetes = new CheckBoxLongValue("doencaDiabetes", InvestigacaoAgravo.DoencasPreExistentes.DIABETES.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaAutoimune = new CheckBoxLongValue("doencaAutoimune", InvestigacaoAgravo.DoencasPreExistentes.DOENCA_AUTOIMUNE.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaCardiaca = new CheckBoxLongValue("doencaCardiaca", InvestigacaoAgravo.DoencasPreExistentes.DOENCA_CARDIACA.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaHepatica = new CheckBoxLongValue("doencaHepatica", InvestigacaoAgravo.DoencasPreExistentes.DOENCA_HEPATICA.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaNeurologicaPsiquiatrica = new CheckBoxLongValue("doencaNeurologicaPsiquiatrica", InvestigacaoAgravo.DoencasPreExistentes.DOENCA_NEUROLOGICA_PSIQUIATRICA.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaPulmonar = new CheckBoxLongValue("doencaPulmonar", InvestigacaoAgravo.DoencasPreExistentes.DOENCA_PULMONAR.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaRenal = new CheckBoxLongValue("doencaRenal", InvestigacaoAgravo.DoencasPreExistentes.DOENCA_RENAL.sum(), new Model<Long>()));
        containerDoencas.add(checkBoxDoencaOutras = new CheckBoxLongValue("doencaOutras", InvestigacaoAgravo.DoencasPreExistentes.DOENCA_OUTRAS.sum(), new Model<Long>()));
        checkBoxDoencaOutras.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                habilitarCamposOutrasDoencas(art);
            }
        });
        lstCheckBoxDoencas.add(checkBoxDoencaAids);
        lstCheckBoxDoencas.add(checkBoxDoencaAlergiaMedicamento);
        lstCheckBoxDoencas.add(checkBoxDoencaAlergiaAlimentar);
        lstCheckBoxDoencas.add(checkBoxDoencaDiabetes);
        lstCheckBoxDoencas.add(checkBoxDoencaAutoimune);
        lstCheckBoxDoencas.add(checkBoxDoencaCardiaca);
        lstCheckBoxDoencas.add(checkBoxDoencaHepatica);
        lstCheckBoxDoencas.add(checkBoxDoencaNeurologicaPsiquiatrica);
        lstCheckBoxDoencas.add(checkBoxDoencaPulmonar);
        lstCheckBoxDoencas.add(checkBoxDoencaRenal);
        lstCheckBoxDoencas.add(checkBoxDoencaOutras);

    }

    private void habilitarCamposOutrasDoencas(AjaxRequestTarget art) {
        txtDoencasOutras.limpar(art);
        if (RepositoryComponentDefault.NAO_LONG.equals(checkBoxDoencaOutras.getComponentValue())) {
            txtDoencasOutras.setEnabled(false);
        } else {
            txtDoencasOutras.setEnabled(true);
        }
        art.add(txtDoencasOutras);
    }

    private void enableContainerEapv(AjaxRequestTarget target, boolean enabled) {
        containerEapv.setEnabled(enabled);
        txtDataOcorencia.limpar(target);
        txtConduta.limpar(target);
        txtEventoAdversoPosVacinacao.limpar(target);
        txtImunobiologico.limpar(target);
        eapvList = new ArrayList<>();
        target.add(containerEapv);
    }

    private void enableContainerDoencas(AjaxRequestTarget target, boolean enabled) {
        containerDoencas.setEnabled(enabled);
        checkBoxDoencaAids.limpar(target);
        checkBoxDoencaAlergiaMedicamento.limpar(target);
        checkBoxDoencaAlergiaAlimentar.limpar(target);
        checkBoxDoencaDiabetes.limpar(target);
        checkBoxDoencaAutoimune.limpar(target);
        checkBoxDoencaCardiaca.limpar(target);
        checkBoxDoencaHepatica.limpar(target);
        checkBoxDoencaNeurologicaPsiquiatrica.limpar(target);
        checkBoxDoencaPulmonar.limpar(target);
        checkBoxDoencaRenal.limpar(target);
        checkBoxDoencaOutras.limpar(target);
        txtDoencasOutras.limpar(target);
        target.add(containerEapv);
    }

    private void enableContainerViajou(AjaxRequestTarget target, boolean enabled) {
        containerViajou.setEnabled(enabled);
        txtDataInicioViagem.limpar(target);
        txtDataTerminoViagem.limpar(target);
        txtPaisViagem.limpar(target);
        txtLovalViagem.limpar(target);
        txtUfViagem.limpar(target);
        txtMunicipioViagem.limpar(target);
        target.add(containerViajou);
    }

    private void enableContainerAtendimento(AjaxRequestTarget target, boolean enabled) {
        containerAtendimento.setEnabled(enabled);
        limparAtendimentoMedico(target);
        target.add(containerAtendimento);
    }

    private void limparAtendimentoMedico(AjaxRequestTarget target) {
        txCNESHospital.limpar(target);
        txtNomeHospital.limpar(target);
        txtDataInternacao.limpar(target);
        txtDataAlta.limpar(target);
        txtMunicipioAtendimento.limpar(target);
        txtUfAtendimento.limpar(target);
        radioGroupTipoAtendimentoMedico.limpar();
    }

    private void adicionarVacinaAplicada(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoVacinaAplicada formInvestigacaoVacinaAplicada = getForm().getModel().getObject().getInvestigacaoVacinaAplicada();
        if (formInvestigacaoVacinaAplicada == null
                || formInvestigacaoVacinaAplicada.getVacinaAplicacao() == null
                || formInvestigacaoVacinaAplicada.getViaAdministracao() == null
                || formInvestigacaoVacinaAplicada.getLocalAplicacao() == null
                || formInvestigacaoVacinaAplicada.getIndicacao() == null) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosVacina"));
        }
        if (CollectionUtils.isNotNullEmpty(vacinaAplicacaoList)) {
            for (InvestigacaoVacinaAplicada _investigacaoVacinaAplicada : vacinaAplicacaoList) {
                if (_investigacaoVacinaAplicada.getVacinaAplicacao().getCodigo().equals(formInvestigacaoVacinaAplicada.getVacinaAplicacao().getCodigo())) {
                    autoCompleteConsultaVacinaAplicada.limpar(target);
                    txtViaAdministracao.limpar(target);
                    txtLocalAplicacao.limpar(target);
                    txtIndicacao.limpar(target);
                    throw new ValidacaoException(BundleManager.getString("vacinaJaAdicionada"));
                }
            }
        }
        InvestigacaoVacinaAplicada investigacaoVacinaAplicada = new InvestigacaoVacinaAplicada();
        investigacaoVacinaAplicada.setVacinaAplicacao(carregarVacinaAplicacao(formInvestigacaoVacinaAplicada.getVacinaAplicacao().getCodigo()));
        investigacaoVacinaAplicada.setViaAdministracao(formInvestigacaoVacinaAplicada.getViaAdministracao());
        investigacaoVacinaAplicada.setLocalAplicacao(formInvestigacaoVacinaAplicada.getLocalAplicacao());
        investigacaoVacinaAplicada.setIndicacao(formInvestigacaoVacinaAplicada.getIndicacao());

        addItemDropDownVacinaAplicadaEncerramentoFinal(target, investigacaoVacinaAplicada);

        vacinaAplicacaoList.add(investigacaoVacinaAplicada);
        tableVacinaAplicada.update(target);
        tableVacinaAplicada.populate();
        autoCompleteConsultaVacinaAplicada.limpar(target);
        txtViaAdministracao.limpar(target);
        txtIndicacao.limpar(target);
        txtLocalAplicacao.limpar(target);
        autoCompleteConsultaVacinaAplicada.focus(target);

        txtIndicacao.removeRequiredClass();
        txtLocalAplicacao.removeRequiredClass();
        txtViaAdministracao.removeRequiredClass();
        autoCompleteConsultaVacinaAplicada.setRequired(target, false);
    }

    private void adicionarEncerramentoFinal(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoEncerramentoFinal investigacaoEncerramentoFinal = modelEncerramentoFinal.getObject();
        if (investigacaoEncerramentoFinal == null
                || (investigacaoEncerramentoFinal.getVacinaAplicacao() == null
                || investigacaoEncerramentoFinal.getDiagnostico() == null
                || investigacaoEncerramentoFinal.getConduta() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosEncerramentoFinal"));
        }

        for (InvestigacaoEncerramentoFinal _investigacaoEncerramentoFinal : investigacaoEncerramentoFinalList) {
            if (Coalesce.asString(_investigacaoEncerramentoFinal.getVacinaAplicacao().getCodigo()).equals(Coalesce.asString(investigacaoEncerramentoFinal.getVacinaAplicacao().getCodigo()))) {
                limparEncerramentoFinal(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        investigacaoEncerramentoFinalList.add(investigacaoEncerramentoFinal);
        tableEncerramentoFinal.update(target);
        modelEncerramentoFinal.setObject(new InvestigacaoEncerramentoFinal());

        limparEncerramentoFinal(target);
        target.focusComponent(txtDataColeta);
    }

    private void adicionarHemograma(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoHemograma investigacaoAgravoHemograma = modelHemograma.getObject();

        if (investigacaoAgravoHemograma == null
                || (investigacaoAgravoHemograma.getDataColeta() == null
                && investigacaoAgravoHemograma.getHemacias() == null
                && investigacaoAgravoHemograma.getHemoglobina() == null
                && investigacaoAgravoHemograma.getHematocrito() == null
                && investigacaoAgravoHemograma.getPlaquetas() == null
                && investigacaoAgravoHemograma.getBastoes() == null
                && investigacaoAgravoHemograma.getNeutrofilos() == null
                && investigacaoAgravoHemograma.getLinfocitos() == null
                && investigacaoAgravoHemograma.getEosinofitos() == null
                && investigacaoAgravoHemograma.getLeucocitos() == null
                && investigacaoAgravoHemograma.getMonocitos() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosHemograma"));
        }

        for (InvestigacaoAgravoHemograma _investigacaoAgravoHemograma : investigacaoHemogramaList) {
            if ((_investigacaoAgravoHemograma.getDataColeta() == null && investigacaoAgravoHemograma.getDataColeta() == null || _investigacaoAgravoHemograma.getDataColeta().equals(investigacaoAgravoHemograma.getDataColeta()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getHemacias()).equals(Coalesce.asString(investigacaoAgravoHemograma.getHemacias()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getHemoglobina()).equals(Coalesce.asString(investigacaoAgravoHemograma.getHemoglobina()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getHematocrito()).equals(Coalesce.asString(investigacaoAgravoHemograma.getHematocrito()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getPlaquetas()).equals(Coalesce.asString(investigacaoAgravoHemograma.getPlaquetas()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getBastoes()).equals(Coalesce.asString(investigacaoAgravoHemograma.getBastoes()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getNeutrofilos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getNeutrofilos()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getLinfocitos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getLinfocitos()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getEosinofitos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getEosinofitos()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getLeucocitos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getLeucocitos()))
                    && Coalesce.asString(_investigacaoAgravoHemograma.getMonocitos()).equals(Coalesce.asString(investigacaoAgravoHemograma.getMonocitos()))
            ) {
                limparHemograma(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        investigacaoHemogramaList.add(investigacaoAgravoHemograma);
        tableHemograma.update(target);
        modelHemograma.setObject(new InvestigacaoAgravoHemograma());

        limparHemograma(target);
        target.focusComponent(txtDataColeta);
    }

    private void adicionarBioquimica(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoBioquimica investigacaoAgravoBioquimica = modelBioquimica.getObject();

        if (investigacaoAgravoBioquimica == null
                || (investigacaoAgravoBioquimica.getDataColeta() == null
                && investigacaoAgravoBioquimica.getBd() == null
                && investigacaoAgravoBioquimica.getBi() == null
                && investigacaoAgravoBioquimica.getBt() == null
                && investigacaoAgravoBioquimica.getUreia() == null
                && investigacaoAgravoBioquimica.getCreatinina() == null
                && investigacaoAgravoBioquimica.getAstTgo() == null
                && investigacaoAgravoBioquimica.getAltTgp() == null
                && investigacaoAgravoBioquimica.getGgt() == null
                && investigacaoAgravoBioquimica.getFa() == null
                && investigacaoAgravoBioquimica.getRni() == null
                && investigacaoAgravoBioquimica.getPt() == null
                && investigacaoAgravoBioquimica.getPtt() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosBioquimica"));
        }

        for (InvestigacaoAgravoBioquimica _investigacaoAgravoBioquimica : investigacaoBioquimicaList) {
            if ((_investigacaoAgravoBioquimica.getDataColeta() == null && investigacaoAgravoBioquimica.getDataColeta() == null || _investigacaoAgravoBioquimica.getDataColeta().equals(investigacaoAgravoBioquimica.getDataColeta()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getBd()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getBd()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getBi()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getBi()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getBt()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getBt()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getUreia()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getUreia()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getCreatinina()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getCreatinina()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getAstTgo()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getAstTgo()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getAltTgp()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getAltTgp()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getGgt()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getGgt()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getFa()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getFa()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getRni()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getRni()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getPt()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getPt()))
                    && Coalesce.asString(_investigacaoAgravoBioquimica.getPtt()).equals(Coalesce.asString(investigacaoAgravoBioquimica.getPtt()))
            ) {
                limparBioquimica(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        investigacaoBioquimicaList.add(investigacaoAgravoBioquimica);
        tableBioquimica.update(target);
        modelBioquimica.setObject(new InvestigacaoAgravoBioquimica());

        limparBioquimica(target);
        target.focusComponent(txtDataColeta);
    }

    private void adicionarPuncaoLombar(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoPuncaoLombar investigacaoAgravoPuncaoLombar = modelPuncaoLombar.getObject();

        if (investigacaoAgravoPuncaoLombar == null
                || (investigacaoAgravoPuncaoLombar.getDataColeta() == null
                && investigacaoAgravoPuncaoLombar.getLeucocitos() == null
                && investigacaoAgravoPuncaoLombar.getNeutrofilos() == null
                && investigacaoAgravoPuncaoLombar.getLinfocitos() == null
                && investigacaoAgravoPuncaoLombar.getGlicose() == null
                && investigacaoAgravoPuncaoLombar.getProteinas() == null
                && investigacaoAgravoPuncaoLombar.getBacterioscopia() == null
                && investigacaoAgravoPuncaoLombar.getCulturaLiquor() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosPuncaoLombar"));
        }

        for (InvestigacaoAgravoPuncaoLombar _investigacaoAgravoPuncaoLombar : investigacaoPuncaoLombarList) {
            if ((_investigacaoAgravoPuncaoLombar.getDataColeta() == null && investigacaoAgravoPuncaoLombar.getDataColeta() == null || _investigacaoAgravoPuncaoLombar.getDataColeta().equals(investigacaoAgravoPuncaoLombar.getDataColeta()))
                    && Coalesce.asString(_investigacaoAgravoPuncaoLombar.getLeucocitos()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getLeucocitos()))
                    && Coalesce.asString(_investigacaoAgravoPuncaoLombar.getNeutrofilos()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getNeutrofilos()))
                    && Coalesce.asString(_investigacaoAgravoPuncaoLombar.getLinfocitos()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getLinfocitos()))
                    && Coalesce.asString(_investigacaoAgravoPuncaoLombar.getGlicose()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getGlicose()))
                    && Coalesce.asString(_investigacaoAgravoPuncaoLombar.getProteinas()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getProteinas()))
                    && Coalesce.asString(_investigacaoAgravoPuncaoLombar.getBacterioscopia()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getBacterioscopia()))
                    && Coalesce.asString(_investigacaoAgravoPuncaoLombar.getCulturaLiquor()).equals(Coalesce.asString(investigacaoAgravoPuncaoLombar.getCulturaLiquor()))
            ) {
                limparPuncaoLombar(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        investigacaoPuncaoLombarList.add(investigacaoAgravoPuncaoLombar);
        tablePuncaoLombar.update(target);
        modelPuncaoLombar.setObject(new InvestigacaoAgravoPuncaoLombar());

        limparPuncaoLombar(target);
        target.focusComponent(txtDataColeta);
    }

    private void adicionarUrina(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoUrina investigacaoAgravoUrina = modelUrina.getObject();

        if (investigacaoAgravoUrina == null
                || (investigacaoAgravoUrina.getDataColeta() == null
                && investigacaoAgravoUrina.getTipo() == null
                && investigacaoAgravoUrina.getDescricao() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosUrina"));
        }

        for (InvestigacaoAgravoUrina _investigacaoAgravoUrina : investigacaoUrinaList) {
            if ((_investigacaoAgravoUrina.getDataColeta() == null && investigacaoAgravoUrina.getDataColeta() == null || _investigacaoAgravoUrina.getDataColeta().equals(investigacaoAgravoUrina.getDataColeta()))
                    && Coalesce.asString(_investigacaoAgravoUrina.getTipo()).equals(Coalesce.asString(investigacaoAgravoUrina.getTipo()))
                    && Coalesce.asString(_investigacaoAgravoUrina.getDescricao()).equals(Coalesce.asString(investigacaoAgravoUrina.getDescricao()))
            ) {
                limparUrina(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        investigacaoUrinaList.add(investigacaoAgravoUrina);
        tableUrina.update(target);
        modelUrina.setObject(new InvestigacaoAgravoUrina());

        limparUrina(target);
        target.focusComponent(txtDataColeta);
    }

    private void adicionarDeteccaoViral(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoDeteccaoViral investigacaoAgravoDeteccaoViral = modelDeteccaoViral.getObject();

        if (investigacaoAgravoDeteccaoViral == null
                || (investigacaoAgravoDeteccaoViral.getDataColeta() == null
                && investigacaoAgravoDeteccaoViral.getAmostra() == null
                && investigacaoAgravoDeteccaoViral.getResultado() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosDeteccaoViral"));
        }

        if (InvestigacaoAgravoDeteccaoViral.Amostra.OUTROS.value().equals(investigacaoAgravoDeteccaoViral.getAmostra())
                && investigacaoAgravoDeteccaoViral.getDescricaoOutrasAmostra() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoOutrasAmostra"));
        }
        for (InvestigacaoAgravoDeteccaoViral _investigacaoAgravoDeteccaoViral : investigacaoDeteccaoViralList) {
            if ((_investigacaoAgravoDeteccaoViral.getDataColeta() == null && investigacaoAgravoDeteccaoViral.getDataColeta() == null || _investigacaoAgravoDeteccaoViral.getDataColeta().equals(investigacaoAgravoDeteccaoViral.getDataColeta()))
                    && Coalesce.asString(_investigacaoAgravoDeteccaoViral.getAmostra()).equals(Coalesce.asString(investigacaoAgravoDeteccaoViral.getAmostra()))
                    && Coalesce.asString(_investigacaoAgravoDeteccaoViral.getDescricaoOutrasAmostra()).equals(Coalesce.asString(investigacaoAgravoDeteccaoViral.getDescricaoOutrasAmostra()))
                    && Coalesce.asString(_investigacaoAgravoDeteccaoViral.getResultado()).equals(Coalesce.asString(investigacaoAgravoDeteccaoViral.getResultado()))
            ) {
                limparDeteccaoViral(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        investigacaoDeteccaoViralList.add(investigacaoAgravoDeteccaoViral);
        tableDeteccaoViral.update(target);
        modelDeteccaoViral.setObject(new InvestigacaoAgravoDeteccaoViral());

        limparDeteccaoViral(target);
        target.focusComponent(txtDataColeta);
    }

    private void adicionarAdicional(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoAdicional investigacaoAgravoAdicional = modelAdicional.getObject();

        if (investigacaoAgravoAdicional == null
                || (investigacaoAgravoAdicional.getDataColeta() == null
                && investigacaoAgravoAdicional.getExame() == null
                && investigacaoAgravoAdicional.getResultado() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosAdicional"));
        }

        if (InvestigacaoAgravoAdicional.Exame.OUTROS.value().equals(investigacaoAgravoAdicional.getExame())
                && investigacaoAgravoAdicional.getDescricaoOutrosExame() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoOutrosExame"));
        }
        for (InvestigacaoAgravoAdicional _investigacaoAgravoAdicional : investigacaoAdicionalList) {
            if ((_investigacaoAgravoAdicional.getDataColeta() == null && investigacaoAgravoAdicional.getDataColeta() == null || _investigacaoAgravoAdicional.getDataColeta().equals(investigacaoAgravoAdicional.getDataColeta()))
                    && Coalesce.asString(_investigacaoAgravoAdicional.getExame()).equals(Coalesce.asString(investigacaoAgravoAdicional.getExame()))
                    && Coalesce.asString(_investigacaoAgravoAdicional.getDescricaoOutrosExame()).equals(Coalesce.asString(investigacaoAgravoAdicional.getDescricaoOutrosExame()))
                    && Coalesce.asString(_investigacaoAgravoAdicional.getResultado()).equals(Coalesce.asString(investigacaoAgravoAdicional.getResultado()))
            ) {
                limparAdicional(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        investigacaoAdicionalList.add(investigacaoAgravoAdicional);
        tableAdicional.update(target);
        modelAdicional.setObject(new InvestigacaoAgravoAdicional());

        limparAdicional(target);
        target.focusComponent(txtDataColeta);
    }

    private void adicionarOutros(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoOutros investigacaoAgravoOutros = modelOutros.getObject();

        if (investigacaoAgravoOutros == null
                || (investigacaoAgravoOutros.getDataColeta() == null
                && investigacaoAgravoOutros.getMacroscopia() == null
                && investigacaoAgravoOutros.getMicroscopia() == null
                && investigacaoAgravoOutros.getAnatomopatologico() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosOutrosViscerotomiaNecropsiaAnatomopatologico"));
        }

        for (InvestigacaoAgravoOutros _investigacaoAgravoOutros : investigacaoOutrosList) {
            if ((_investigacaoAgravoOutros.getDataColeta() == null && investigacaoAgravoOutros.getDataColeta() == null || _investigacaoAgravoOutros.getDataColeta().equals(investigacaoAgravoOutros.getDataColeta()))
                    && Coalesce.asString(_investigacaoAgravoOutros.getMacroscopia()).equals(Coalesce.asString(investigacaoAgravoOutros.getMacroscopia()))
                    && Coalesce.asString(_investigacaoAgravoOutros.getMicroscopia()).equals(Coalesce.asString(investigacaoAgravoOutros.getMicroscopia()))
                    && Coalesce.asString(_investigacaoAgravoOutros.getAnatomopatologico()).equals(Coalesce.asString(investigacaoAgravoOutros.getAnatomopatologico()))
            ) {
                limparOutros(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        investigacaoOutrosList.add(investigacaoAgravoOutros);
        tableOutros.update(target);
        modelOutros.setObject(new InvestigacaoAgravoOutros());

        limparOutros(target);
        target.focusComponent(txtDataColeta);
    }

    private void adicionarImunologia(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoImunologia investigacaoAgravoImunologia = modelImunologia.getObject();

        if (investigacaoAgravoImunologia == null
                || (investigacaoAgravoImunologia.getDataColetaSorologia() == null
                && investigacaoAgravoImunologia.getDataColetaPcr() == null
                && investigacaoAgravoImunologia.getAgravo() == null
                && investigacaoAgravoImunologia.getSangueSorologia() == null
                && investigacaoAgravoImunologia.getSanguePcr() == null
                && investigacaoAgravoImunologia.getLiquorSorologia() == null
                && investigacaoAgravoImunologia.getLiquorPcr() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosImunologia"));
        }

        if (InvestigacaoAgravoImunologia.Agravo.OUTROS.value().equals(investigacaoAgravoImunologia.getAgravo())
                && investigacaoAgravoImunologia.getDescricaoOutrosAgravo() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoOutrasImunologia"));
        }
        for (InvestigacaoAgravoImunologia _investigacaoAgravoImunologia : investigacaoImunologiaList) {
            if ((_investigacaoAgravoImunologia.getDataColetaSorologia() == null && investigacaoAgravoImunologia.getDataColetaSorologia() == null || _investigacaoAgravoImunologia.getDataColetaSorologia().equals(investigacaoAgravoImunologia.getDataColetaSorologia()))
                    && (_investigacaoAgravoImunologia.getDataColetaPcr() == null && investigacaoAgravoImunologia.getDataColetaPcr() == null || _investigacaoAgravoImunologia.getDataColetaPcr().equals(investigacaoAgravoImunologia.getDataColetaPcr()))
                    && Coalesce.asString(_investigacaoAgravoImunologia.getAgravo()).equals(Coalesce.asString(investigacaoAgravoImunologia.getAgravo()))
                    && Coalesce.asString(_investigacaoAgravoImunologia.getSangueSorologia()).equals(Coalesce.asString(investigacaoAgravoImunologia.getSangueSorologia()))
                    && Coalesce.asString(_investigacaoAgravoImunologia.getSanguePcr()).equals(Coalesce.asString(investigacaoAgravoImunologia.getSanguePcr()))
                    && Coalesce.asString(_investigacaoAgravoImunologia.getLiquorSorologia()).equals(Coalesce.asString(investigacaoAgravoImunologia.getLiquorSorologia()))
                    && Coalesce.asString(_investigacaoAgravoImunologia.getLiquorPcr()).equals(Coalesce.asString(investigacaoAgravoImunologia.getLiquorPcr()))
            ) {
                limparImunologia(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        investigacaoImunologiaList.add(investigacaoAgravoImunologia);
        tableImunologia.update(target);
        modelImunologia.setObject(new InvestigacaoAgravoImunologia());

        limparImunologia(target);
        target.focusComponent(txtDataColeta);
    }

    private void adicionarEapv(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoEapvPresente investigacaoEapvPresente = modelInvestigacaoEapvPresente.getObject();

        if (investigacaoEapvPresente == null
                || (investigacaoEapvPresente.getDataOcorrencia() == null
                && investigacaoEapvPresente.getConduta() == null
                && investigacaoEapvPresente.getEventoAdversoPosVacinacao() == null
                && investigacaoEapvPresente.getImunobiologico() == null)) {
            throw new ValidacaoException(BundleManager.getString("informeAoMenosUmDosCamposDaEAPVAnterior"));
        }

        for (InvestigacaoEapvPresente _investigacaoEapvPresente : eapvList) {
            if (Coalesce.asString(_investigacaoEapvPresente.getConduta()).equals(Coalesce.asString(investigacaoEapvPresente.getConduta()))
                    && Coalesce.asString(_investigacaoEapvPresente.getImunobiologico()).equals(Coalesce.asString(investigacaoEapvPresente.getImunobiologico()))
                    && Coalesce.asString(_investigacaoEapvPresente.getEventoAdversoPosVacinacao()).equals(Coalesce.asString(investigacaoEapvPresente.getEventoAdversoPosVacinacao()))
                    && Coalesce.asString(_investigacaoEapvPresente.getDataOcorrencia()).equals(Coalesce.asString(investigacaoEapvPresente.getDataOcorrencia()))) {
                txtImunobiologico.limpar(target);
                txtEventoAdversoPosVacinacao.limpar(target);
                txtConduta.limpar(target);
                txtDataOcorencia.limpar(target);
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        eapvList.add(investigacaoEapvPresente);
        tableEapv.update(target);

        modelInvestigacaoEapvPresente.setObject(new InvestigacaoEapvPresente());

        txtImunobiologico.limpar(target);
        txtEventoAdversoPosVacinacao.limpar(target);
        txtConduta.limpar(target);
        txtDataOcorencia.limpar(target);
        target.focusComponent(txtDataOcorencia);
    }

    private List<IColumn> getColumnsEapv() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoEapvPresente proxy = on(InvestigacaoEapvPresente.class);

        columns.add(getCustomColumnEapv());
        columns.add(createColumn(BundleManager.getString("dataOcorrencia"), proxy.getDataOcorrencia()));
        columns.add(createColumn(BundleManager.getString("imunobiologico"), proxy.getImunobiologico()));
        columns.add(createColumn(BundleManager.getString("eventoAdversoPosVacinacao"), proxy.getEventoAdversoPosVacinacao()));
        columns.add(createColumn(BundleManager.getString("conduta"), proxy.getConduta()));
        return columns;
    }

    private CustomColumn<InvestigacaoEapvPresente> getCustomColumnEapv() {
        return new CustomColumn<InvestigacaoEapvPresente>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoEapvPresente rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerEapv(rowObject);
                        tableEapv.update(target);
                    }
                };
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoVacinaAplicada proxy = on(InvestigacaoVacinaAplicada.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(BundleManager.getString("dataAplicacao"), proxy.getVacinaAplicacao().getDataAplicacao()));
        columns.add(createColumn(BundleManager.getString("estrategia"), proxy.getVacinaAplicacao().getEstrategia().getDescricao()));
        columns.add(createColumn(BundleManager.getString("imunobiologico"), proxy.getVacinaAplicacao().getDescricaoVacina()));
        columns.add(createColumn(BundleManager.getString("dose"), proxy.getVacinaAplicacao().getDescricaoDoses()));
        columns.add(createColumn(BundleManager.getString("laboratorio"), proxy.getVacinaAplicacao().getProdutoVacina().getProduto().getFabricante().getDescricao()));
        columns.add(createColumn(BundleManager.getString("lote"), proxy.getVacinaAplicacao().getLote()));
        columns.add(createColumn(BundleManager.getString("viaAdministracao"), proxy.getViaAdministracao()));
        columns.add(createColumn(BundleManager.getString("localAplicacao"), proxy.getLocalAplicacao()));
        columns.add(createColumn(BundleManager.getString("indicacao"), proxy.getIndicacao()));
        columns.add(createColumn(BundleManager.getString("especialidade"), proxy.getVacinaAplicacao().getProfissionalAplicacao().getCboProfissional(proxy.getVacinaAplicacao().getEmpresa()).getCbo()));
        columns.add(createColumn(BundleManager.getString("estabelecimento"), proxy.getVacinaAplicacao().getEmpresa().getDescricao()));
        return columns;
    }

    private CustomColumn<InvestigacaoVacinaAplicada> getCustomColumn() {
        return new CustomColumn<InvestigacaoVacinaAplicada>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoVacinaAplicada rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        remover(rowObject);
                        tableVacinaAplicada.update(target);
                        defineCamposObrigatorios(target);
                        removeItemDropDownVacinaAplicadaEncerramentoFinal(target, rowObject);
                    }
                };
            }
        };
    }

    private void remover(InvestigacaoVacinaAplicada rowObject) {
        int i = 0;
        for (InvestigacaoVacinaAplicada dto : vacinaAplicacaoList) {
            if (dto.getVacinaAplicacao().getCodigo().equals(rowObject.getVacinaAplicacao().getCodigo())) {
                vacinaAplicacaoList.remove(i);
                break;
            }
            i++;
        }
    }

    private void removerEapv(InvestigacaoEapvPresente rowObject) {
        int i = 0;
        for (InvestigacaoEapvPresente eapvPresente : eapvList) {
            if (Coalesce.asString(eapvPresente.getConduta()).equals(Coalesce.asString(rowObject.getConduta()))
                    && Coalesce.asString(eapvPresente.getImunobiologico()).equals(Coalesce.asString(rowObject.getImunobiologico()))
                    && Coalesce.asString(eapvPresente.getEventoAdversoPosVacinacao()).equals(Coalesce.asString(rowObject.getEventoAdversoPosVacinacao()))
                    && Coalesce.asString(eapvPresente.getDataOcorrencia()).equals(Coalesce.asString(rowObject.getDataOcorrencia()))) {
                eapvList.remove(i);
                break;
            }
            i++;
        }
    }

    private void adicionarManifestacoesPeleMucosa(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoManifestacoes investigacaoManifestacoes = modelManifestacoesPeleMucosa.getObject();

        if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(investigacaoManifestacoes.getTipoEventoAdverso())
                && investigacaoManifestacoes.getDescricaoOutrosEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoEventoAdverso"));
        }
        if (investigacaoManifestacoes == null
                || investigacaoManifestacoes.getTipoEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosManifestacoesLocal"));
        }

        if (investigacaoManifestacoes.getDataInicioManifestacao() != null && investigacaoManifestacoes.getDataTerminoManifestacao() != null
                && investigacaoManifestacoes.getDataTerminoManifestacao().compareTo(investigacaoManifestacoes.getDataInicioManifestacao()) < 0) {
            throw new ValidacaoException(BundleManager.getString("msgDataInicioNaoPodeMaiorDataTermino"));
        }

        for (InvestigacaoManifestacoes _investigacaoManifestacoes : investigacaoManifestacoesPeleMucosaList) {
            if (Coalesce.asString(_investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()))
                    && Coalesce.asString(_investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()))) {
                limparManifestacoesPeleMucosa(target);
                throw new ValidacaoException(BundleManager.getString("itemDoTipoMedicamentoJaAdicionado"));
            }
        }

        investigacaoManifestacoes.setTipoManifestacao(InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_PELE_MUCOSA.value());
        if (investigacaoManifestacoes.getFlagEmAcompanhamento() == null) {
            investigacaoManifestacoes.setFlagEmAcompanhamento(RepositoryComponentDefault.NAO_LONG);
        }
        investigacaoManifestacoesPeleMucosaList.add(investigacaoManifestacoes);
        tableManifestacoesPeleMucosa.update(target);
        modelManifestacoesPeleMucosa.setObject(new InvestigacaoManifestacoes());

        limparManifestacoesPeleMucosa(target);
        target.focusComponent(txtDataOcorencia);
    }

    private void limparManifestacoesPeleMucosa(AjaxRequestTarget target) {
        dropDownTipoManifestacoesPeleMucosa.limpar(target);
        txtDescricaoTipoOutrosManifestacoesPeleMucosa.limpar(target);
        txtDataInicioManifestacoesPeleMucosa.limpar(target);
        txtDataTerminoManifestacoesPeleMucosa.limpar(target);
        txtTempoInicioManifestacoesPeleMucosa.limpar(target);
        txtDescricaoTipoOutrosManifestacoesPeleMucosa.setEnabled(false);
        chkEmAcompanhamentoManifestacoesPeleMucosa.limpar(target);
        chkEmAcompanhamentoManifestacoesPeleMucosa.setEnabled(true);
    }

    private List<IColumn> getColumnsManifestacoesPeleMucosa() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoManifestacoes proxy = on(InvestigacaoManifestacoes.class);

        columns.add(getCustomColumnManifestacoesPeleMucosa());
        columns.add(createColumn(BundleManager.getString("eventoAdverso"), proxy.getDescricaoEventoAdverso()));
        columns.add(createColumn(BundleManager.getString("dataInicio"), proxy.getDataInicioManifestacao()));
        columns.add(createColumn(BundleManager.getString("dataTermino"), proxy.getDataTerminoManifestacao()));
        columns.add(createColumn(BundleManager.getString("tempoInicio"), proxy.getTempoInicio()));
        columns.add(createColumn(BundleManager.getString("emAcompanhamento"), proxy.getDescricaoEmAcompanhamento()));
        return columns;
    }

    private CustomColumn<InvestigacaoManifestacoes> getCustomColumnManifestacoesPeleMucosa() {
        return new CustomColumn<InvestigacaoManifestacoes>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoManifestacoes rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerManifestacoesPeleMucosa(rowObject);
                        tableManifestacoesPeleMucosa.update(target);
                    }
                };
            }
        };
    }

    private void removerManifestacoesPeleMucosa(InvestigacaoManifestacoes rowObject) {
        int i = 0;
        for (InvestigacaoManifestacoes investigacaoManifestacoes : investigacaoManifestacoesPeleMucosaList) {
            if (Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(rowObject.getTipoEventoAdverso()))
                    && Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(rowObject.getTipoManifestacao()))) {
                investigacaoManifestacoesPeleMucosaList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderManifestacoesPeleMucosa() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoManifestacoesPeleMucosaList;
            }
        };
    }

    private void adicionarManifestacoesCardiovasculares(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoManifestacoes investigacaoManifestacoes = modelManifestacoesCardiovasculares.getObject();

        if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(investigacaoManifestacoes.getTipoEventoAdverso())
                && investigacaoManifestacoes.getDescricaoOutrosEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoEventoAdverso"));
        }
        if (investigacaoManifestacoes == null
                || investigacaoManifestacoes.getTipoEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosManifestacoesLocal"));
        }
        if (investigacaoManifestacoes.getDataInicioManifestacao() != null && investigacaoManifestacoes.getDataTerminoManifestacao() != null
                && investigacaoManifestacoes.getDataTerminoManifestacao().compareTo(investigacaoManifestacoes.getDataInicioManifestacao()) < 0) {
            throw new ValidacaoException(BundleManager.getString("msgDataInicioNaoPodeMaiorDataTermino"));
        }

        for (InvestigacaoManifestacoes _investigacaoManifestacoes : investigacaoManifestacoesCardiovascularesList) {
            if (Coalesce.asString(_investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()))
                    && Coalesce.asString(_investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()))) {
                limparManifestacoesCardiovasculares(target);
                throw new ValidacaoException(BundleManager.getString("itemDoTipoMedicamentoJaAdicionado"));
            }
        }

        investigacaoManifestacoes.setTipoManifestacao(InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_CARDIOVASCULARES.value());
        if (investigacaoManifestacoes.getFlagEmAcompanhamento() == null) {
            investigacaoManifestacoes.setFlagEmAcompanhamento(RepositoryComponentDefault.NAO_LONG);
        }
        investigacaoManifestacoesCardiovascularesList.add(investigacaoManifestacoes);
        tableManifestacoesCardiovasculares.update(target);
        modelManifestacoesCardiovasculares.setObject(new InvestigacaoManifestacoes());

        limparManifestacoesCardiovasculares(target);
        target.focusComponent(txtDataOcorencia);
    }

    private void limparManifestacoesCardiovasculares(AjaxRequestTarget target) {
        dropDownTipoManifestacoesCardiovasculares.limpar(target);
        txtDescricaoTipoOutrosManifestacoesCardiovasculares.limpar(target);
        txtDataInicioManifestacoesCardiovasculares.limpar(target);
        txtDataTerminoManifestacoesCardiovasculares.limpar(target);
        txtTempoInicioManifestacoesCardiovasculares.limpar(target);
        txtDescricaoTipoOutrosManifestacoesCardiovasculares.setEnabled(false);
        chkEmAcompanhamentoManifestacoesCardiovasculares.limpar(target);
        chkEmAcompanhamentoManifestacoesCardiovasculares.setEnabled(true);
    }

    private List<IColumn> getColumnsManifestacoesCardiovasculares() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoManifestacoes proxy = on(InvestigacaoManifestacoes.class);

        columns.add(getCustomColumnManifestacoesCardiovasculares());
        columns.add(createColumn(BundleManager.getString("eventoAdverso"), proxy.getDescricaoEventoAdverso()));
        columns.add(createColumn(BundleManager.getString("dataInicio"), proxy.getDataInicioManifestacao()));
        columns.add(createColumn(BundleManager.getString("dataTermino"), proxy.getDataTerminoManifestacao()));
        columns.add(createColumn(BundleManager.getString("tempoInicio"), proxy.getTempoInicio()));
        columns.add(createColumn(BundleManager.getString("emAcompanhamento"), proxy.getDescricaoEmAcompanhamento()));
        return columns;
    }

    private CustomColumn<InvestigacaoManifestacoes> getCustomColumnManifestacoesCardiovasculares() {
        return new CustomColumn<InvestigacaoManifestacoes>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoManifestacoes rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerManifestacoesCardiovasculares(rowObject);
                        tableManifestacoesCardiovasculares.update(target);
                    }
                };
            }
        };
    }

    private void removerManifestacoesCardiovasculares(InvestigacaoManifestacoes rowObject) {
        int i = 0;
        for (InvestigacaoManifestacoes investigacaoManifestacoes : investigacaoManifestacoesCardiovascularesList) {
            if (Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(rowObject.getTipoEventoAdverso()))
                    && Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(rowObject.getTipoManifestacao()))) {
                investigacaoManifestacoesCardiovascularesList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderManifestacoesCardiovasculares() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoManifestacoesCardiovascularesList;
            }
        };
    }

    private void adicionarManifestacoesRespiratoria(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoManifestacoes investigacaoManifestacoes = modelManifestacoesRespiratoria.getObject();

        if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(investigacaoManifestacoes.getTipoEventoAdverso())
                && investigacaoManifestacoes.getDescricaoOutrosEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoEventoAdverso"));
        }
        if (investigacaoManifestacoes == null
                || investigacaoManifestacoes.getTipoEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosManifestacoesLocal"));
        }

        if (investigacaoManifestacoes.getDataInicioManifestacao() != null && investigacaoManifestacoes.getDataTerminoManifestacao() != null
                && investigacaoManifestacoes.getDataTerminoManifestacao().compareTo(investigacaoManifestacoes.getDataInicioManifestacao()) < 0) {
            throw new ValidacaoException(BundleManager.getString("msgDataInicioNaoPodeMaiorDataTermino"));
        }

        for (InvestigacaoManifestacoes _investigacaoManifestacoes : investigacaoManifestacoesRespiratoriaList) {
            if (Coalesce.asString(_investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()))
                    && Coalesce.asString(_investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()))) {
                limparManifestacoesRespiratoria(target);
                throw new ValidacaoException(BundleManager.getString("itemDoTipoMedicamentoJaAdicionado"));
            }
        }

        investigacaoManifestacoes.setTipoManifestacao(InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_RESPIRATORIA.value());
        if (investigacaoManifestacoes.getFlagEmAcompanhamento() == null) {
            investigacaoManifestacoes.setFlagEmAcompanhamento(RepositoryComponentDefault.NAO_LONG);
        }
        investigacaoManifestacoesRespiratoriaList.add(investigacaoManifestacoes);
        tableManifestacoesRespiratoria.update(target);
        modelManifestacoesRespiratoria.setObject(new InvestigacaoManifestacoes());

        limparManifestacoesRespiratoria(target);
        target.focusComponent(txtDataOcorencia);
    }

    private void limparManifestacoesRespiratoria(AjaxRequestTarget target) {
        dropDownTipoManifestacoesRespiratoria.limpar(target);
        txtDescricaoTipoOutrosManifestacoesRespiratoria.limpar(target);
        txtDataInicioManifestacoesRespiratoria.limpar(target);
        txtDataTerminoManifestacoesRespiratoria.limpar(target);
        txtTempoInicioManifestacoesRespiratoria.limpar(target);
        txtDescricaoTipoOutrosManifestacoesRespiratoria.setEnabled(false);
        chkEmAcompanhamentoManifestacoesRespiratoria.limpar(target);
        chkEmAcompanhamentoManifestacoesRespiratoria.setEnabled(true);
    }

    private List<IColumn> getColumnsManifestacoesRespiratoria() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoManifestacoes proxy = on(InvestigacaoManifestacoes.class);

        columns.add(getCustomColumnManifestacoesRespiratoria());
        columns.add(createColumn(BundleManager.getString("eventoAdverso"), proxy.getDescricaoEventoAdverso()));
        columns.add(createColumn(BundleManager.getString("dataInicio"), proxy.getDataInicioManifestacao()));
        columns.add(createColumn(BundleManager.getString("dataTermino"), proxy.getDataTerminoManifestacao()));
        columns.add(createColumn(BundleManager.getString("tempoInicio"), proxy.getTempoInicio()));
        columns.add(createColumn(BundleManager.getString("emAcompanhamento"), proxy.getDescricaoEmAcompanhamento()));
        return columns;
    }

    private CustomColumn<InvestigacaoManifestacoes> getCustomColumnManifestacoesRespiratoria() {
        return new CustomColumn<InvestigacaoManifestacoes>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoManifestacoes rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerManifestacoesRespiratoria(rowObject);
                        tableManifestacoesRespiratoria.update(target);
                    }
                };
            }
        };
    }

    private void removerManifestacoesRespiratoria(InvestigacaoManifestacoes rowObject) {
        int i = 0;
        for (InvestigacaoManifestacoes investigacaoManifestacoes : investigacaoManifestacoesRespiratoriaList) {
            if (Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(rowObject.getTipoEventoAdverso()))
                    && Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(rowObject.getTipoManifestacao()))) {
                investigacaoManifestacoesRespiratoriaList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderManifestacoesRespiratoria() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoManifestacoesRespiratoriaList;
            }
        };
    }

    private void adicionarManifestacoesNeurologicas(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoManifestacoes investigacaoManifestacoes = modelManifestacoesNeurologicas.getObject();

        if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(investigacaoManifestacoes.getTipoEventoAdverso())
                && investigacaoManifestacoes.getDescricaoOutrosEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoEventoAdverso"));
        }
        if (investigacaoManifestacoes == null
                || investigacaoManifestacoes.getTipoEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosManifestacoesLocal"));
        }

        if (investigacaoManifestacoes.getDataInicioManifestacao() != null && investigacaoManifestacoes.getDataTerminoManifestacao() != null
                && investigacaoManifestacoes.getDataTerminoManifestacao().compareTo(investigacaoManifestacoes.getDataInicioManifestacao()) < 0) {
            throw new ValidacaoException(BundleManager.getString("msgDataInicioNaoPodeMaiorDataTermino"));
        }

        for (InvestigacaoManifestacoes _investigacaoManifestacoes : investigacaoManifestacoesNeurologicasList) {
            if (Coalesce.asString(_investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()))
                    && Coalesce.asString(_investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()))) {
                limparManifestacoesNeurologicas(target);
                throw new ValidacaoException(BundleManager.getString("itemDoTipoMedicamentoJaAdicionado"));
            }
        }

        investigacaoManifestacoes.setTipoManifestacao(InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_NEUROLOGICAS.value());
        if (investigacaoManifestacoes.getFlagEmAcompanhamento() == null) {
            investigacaoManifestacoes.setFlagEmAcompanhamento(RepositoryComponentDefault.NAO_LONG);
        }
        investigacaoManifestacoesNeurologicasList.add(investigacaoManifestacoes);
        tableManifestacoesNeurologicas.update(target);
        modelManifestacoesNeurologicas.setObject(new InvestigacaoManifestacoes());

        limparManifestacoesNeurologicas(target);
        target.focusComponent(txtDataOcorencia);
    }

    private void limparManifestacoesNeurologicas(AjaxRequestTarget target) {
        dropDownTipoManifestacoesNeurologicas.limpar(target);
        txtDescricaoTipoOutrosManifestacoesNeurologicas.limpar(target);
        txtDataInicioManifestacoesNeurologicas.limpar(target);
        txtDataTerminoManifestacoesNeurologicas.limpar(target);
        txtTempoInicioManifestacoesNeurologicas.limpar(target);
        txtDescricaoTipoOutrosManifestacoesNeurologicas.setEnabled(false);
        chkEmAcompanhamentoManifestacoesNeurologicas.limpar(target);
        chkEmAcompanhamentoManifestacoesNeurologicas.setEnabled(true);
    }

    private List<IColumn> getColumnsManifestacoesNeurologicas() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoManifestacoes proxy = on(InvestigacaoManifestacoes.class);

        columns.add(getCustomColumnManifestacoesNeurologicas());
        columns.add(createColumn(BundleManager.getString("eventoAdverso"), proxy.getDescricaoEventoAdverso()));
        columns.add(createColumn(BundleManager.getString("dataInicio"), proxy.getDataInicioManifestacao()));
        columns.add(createColumn(BundleManager.getString("dataTermino"), proxy.getDataTerminoManifestacao()));
        columns.add(createColumn(BundleManager.getString("tempoInicio"), proxy.getTempoInicio()));
        columns.add(createColumn(BundleManager.getString("emAcompanhamento"), proxy.getDescricaoEmAcompanhamento()));
        return columns;
    }

    private CustomColumn<InvestigacaoManifestacoes> getCustomColumnManifestacoesNeurologicas() {
        return new CustomColumn<InvestigacaoManifestacoes>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoManifestacoes rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerManifestacoesNeurologicas(rowObject);
                        tableManifestacoesNeurologicas.update(target);
                    }
                };
            }
        };
    }

    private void removerManifestacoesNeurologicas(InvestigacaoManifestacoes rowObject) {
        int i = 0;
        for (InvestigacaoManifestacoes investigacaoManifestacoes : investigacaoManifestacoesNeurologicasList) {
            if (Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(rowObject.getTipoEventoAdverso()))
                    && Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(rowObject.getTipoManifestacao()))) {
                investigacaoManifestacoesNeurologicasList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderManifestacoesNeurologicas() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoManifestacoesNeurologicasList;
            }
        };
    }

    private void adicionarManifestacoesGastrintestinais(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoManifestacoes investigacaoManifestacoes = modelManifestacoesGastrintestinais.getObject();

        if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(investigacaoManifestacoes.getTipoEventoAdverso())
                && investigacaoManifestacoes.getDescricaoOutrosEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoEventoAdverso"));
        }
        if (investigacaoManifestacoes == null
                || investigacaoManifestacoes.getTipoEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosManifestacoesLocal"));
        }

        if (investigacaoManifestacoes.getDataInicioManifestacao() != null && investigacaoManifestacoes.getDataTerminoManifestacao() != null
                && investigacaoManifestacoes.getDataTerminoManifestacao().compareTo(investigacaoManifestacoes.getDataInicioManifestacao()) < 0) {
            throw new ValidacaoException(BundleManager.getString("msgDataInicioNaoPodeMaiorDataTermino"));
        }

        for (InvestigacaoManifestacoes _investigacaoManifestacoes : investigacaoManifestacoesGastrintestinaisList) {
            if (Coalesce.asString(_investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()))
                    && Coalesce.asString(_investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()))) {
                limparManifestacoesGastrintestinais(target);
                throw new ValidacaoException(BundleManager.getString("itemDoTipoMedicamentoJaAdicionado"));
            }
        }

        investigacaoManifestacoes.setTipoManifestacao(InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_GASTRINTESTINAIS.value());
        if (investigacaoManifestacoes.getFlagEmAcompanhamento() == null) {
            investigacaoManifestacoes.setFlagEmAcompanhamento(RepositoryComponentDefault.NAO_LONG);
        }
        if (investigacaoManifestacoes.getFlagEmAcompanhamento() == null) {
            investigacaoManifestacoes.setFlagEmAcompanhamento(RepositoryComponentDefault.NAO_LONG);
        }
        investigacaoManifestacoesGastrintestinaisList.add(investigacaoManifestacoes);
        tableManifestacoesGastrintestinais.update(target);
        modelManifestacoesGastrintestinais.setObject(new InvestigacaoManifestacoes());

        limparManifestacoesGastrintestinais(target);
        target.focusComponent(txtDataOcorencia);
    }

    private void limparManifestacoesGastrintestinais(AjaxRequestTarget target) {
        dropDownTipoManifestacoesGastrintestinais.limpar(target);
        txtDescricaoTipoOutrosManifestacoesGastrintestinais.limpar(target);
        txtDataInicioManifestacoesGastrintestinais.limpar(target);
        txtDataTerminoManifestacoesGastrintestinais.limpar(target);
        txtTempoInicioManifestacoesGastrintestinais.limpar(target);
        txtDescricaoTipoOutrosManifestacoesGastrintestinais.setEnabled(false);
        chkEmAcompanhamentoManifestacoesGastrintestinais.limpar(target);
        chkEmAcompanhamentoManifestacoesGastrintestinais.setEnabled(true);
    }

    private List<IColumn> getColumnsManifestacoesGastrintestinais() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoManifestacoes proxy = on(InvestigacaoManifestacoes.class);

        columns.add(getCustomColumnManifestacoesGastrintestinais());
        columns.add(createColumn(BundleManager.getString("eventoAdverso"), proxy.getDescricaoEventoAdverso()));
        columns.add(createColumn(BundleManager.getString("dataInicio"), proxy.getDataInicioManifestacao()));
        columns.add(createColumn(BundleManager.getString("dataTermino"), proxy.getDataTerminoManifestacao()));
        columns.add(createColumn(BundleManager.getString("tempoInicio"), proxy.getTempoInicio()));
        columns.add(createColumn(BundleManager.getString("emAcompanhamento"), proxy.getDescricaoEmAcompanhamento()));
        return columns;
    }

    private CustomColumn<InvestigacaoManifestacoes> getCustomColumnManifestacoesGastrintestinais() {
        return new CustomColumn<InvestigacaoManifestacoes>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoManifestacoes rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerManifestacoesGastrintestinais(rowObject);
                        tableManifestacoesGastrintestinais.update(target);
                    }
                };
            }
        };
    }

    private void removerManifestacoesGastrintestinais(InvestigacaoManifestacoes rowObject) {
        int i = 0;
        for (InvestigacaoManifestacoes investigacaoManifestacoes : investigacaoManifestacoesGastrintestinaisList) {
            if (Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(rowObject.getTipoEventoAdverso()))
                    && Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(rowObject.getTipoManifestacao()))) {
                investigacaoManifestacoesGastrintestinaisList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderManifestacoesGastrintestinais() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoManifestacoesGastrintestinaisList;
            }
        };
    }

    private void adicionarManifestacoesOutras(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoManifestacoes investigacaoManifestacoes = modelManifestacoesOutras.getObject();

        if (InvestigacaoManifestacoes.EventoAdverso.OUTROS_MAN_LOCAL.value().equals(investigacaoManifestacoes.getTipoEventoAdverso())
                && investigacaoManifestacoes.getDescricaoOutrosEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDescricaoEventoAdverso"));
        }
        if (investigacaoManifestacoes == null
                || investigacaoManifestacoes.getTipoEventoAdverso() == null) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosManifestacoesLocal"));
        }

        if (investigacaoManifestacoes.getDataInicioManifestacao() != null && investigacaoManifestacoes.getDataTerminoManifestacao() != null
                && investigacaoManifestacoes.getDataTerminoManifestacao().compareTo(investigacaoManifestacoes.getDataInicioManifestacao()) < 0) {
            throw new ValidacaoException(BundleManager.getString("msgDataInicioNaoPodeMaiorDataTermino"));
        }

        for (InvestigacaoManifestacoes _investigacaoManifestacoes : investigacaoManifestacoesOutrasList) {
            if (Coalesce.asString(_investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()))
                    && Coalesce.asString(_investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()))) {
                limparManifestacoesOutras(target);
                throw new ValidacaoException(BundleManager.getString("itemDoTipoMedicamentoJaAdicionado"));
            }
        }

        investigacaoManifestacoes.setTipoManifestacao(InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_OUTRAS.value());
        if (investigacaoManifestacoes.getFlagEmAcompanhamento() == null) {
            investigacaoManifestacoes.setFlagEmAcompanhamento(RepositoryComponentDefault.NAO_LONG);
        }
        investigacaoManifestacoesOutrasList.add(investigacaoManifestacoes);
        tableManifestacoesOutras.update(target);
        modelManifestacoesOutras.setObject(new InvestigacaoManifestacoes());

        limparManifestacoesOutras(target);
        target.focusComponent(txtDataOcorencia);
    }

    private void limparManifestacoesOutras(AjaxRequestTarget target) {
        dropDownTipoManifestacoesOutras.limpar(target);
        txtDescricaoTipoOutrosManifestacoesOutras.limpar(target);
        txtDataInicioManifestacoesOutras.limpar(target);
        txtDataTerminoManifestacoesOutras.limpar(target);
        txtTempoInicioManifestacoesOutras.limpar(target);
        txtDescricaoTipoOutrosManifestacoesOutras.setEnabled(false);
        chkEmAcompanhamentoManifestacoesOutras.limpar(target);
        chkEmAcompanhamentoManifestacoesOutras.setEnabled(true);
    }

    private List<IColumn> getColumnsManifestacoesOutras() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InvestigacaoManifestacoes proxy = on(InvestigacaoManifestacoes.class);

        columns.add(getCustomColumnManifestacoesOutras());
        columns.add(createColumn(BundleManager.getString("eventoAdverso"), proxy.getDescricaoEventoAdverso()));
        columns.add(createColumn(BundleManager.getString("dataInicio"), proxy.getDataInicioManifestacao()));
        columns.add(createColumn(BundleManager.getString("dataTermino"), proxy.getDataTerminoManifestacao()));
        columns.add(createColumn(BundleManager.getString("tempoInicio"), proxy.getTempoInicio()));
        columns.add(createColumn(BundleManager.getString("emAcompanhamento"), proxy.getDescricaoEmAcompanhamento()));
        return columns;
    }

    private CustomColumn<InvestigacaoManifestacoes> getCustomColumnManifestacoesOutras() {
        return new CustomColumn<InvestigacaoManifestacoes>() {
            @Override
            public Component getComponent(String componentId, final InvestigacaoManifestacoes rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerManifestacoesOutras(rowObject);
                        tableManifestacoesOutras.update(target);
                    }
                };
            }
        };
    }

    private void removerManifestacoesOutras(InvestigacaoManifestacoes rowObject) {
        int i = 0;
        for (InvestigacaoManifestacoes investigacaoManifestacoes : investigacaoManifestacoesOutrasList) {
            if (Coalesce.asString(investigacaoManifestacoes.getTipoEventoAdverso()).equals(Coalesce.asString(rowObject.getTipoEventoAdverso()))
                    && Coalesce.asString(investigacaoManifestacoes.getTipoManifestacao()).equals(Coalesce.asString(rowObject.getTipoManifestacao()))) {
                investigacaoManifestacoesOutrasList.remove(i);
                break;
            }
            i++;
        }
    }

    public ICollectionProvider getCollectionProviderManifestacoesOutras() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return investigacaoManifestacoesOutrasList;
            }
        };
    }

    public ICollectionProvider getCollectionProviderEapv() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return eapvList;
            }
        };
    }

    public ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return vacinaAplicacaoList;
            }
        };
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        validaCamposObrigatorios(getForm().getModel().getObject(), target);
        registroAgravo.setStatus(RegistroAgravo.Status.EM_INVESTIGACAO.value());
        salvarRegistro();
    }

    private void encerrar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        validaCamposObrigatorios(getForm().getModel().getObject(), target);
        initDlgConfirmacao(target);
    }

    private void validaCamposObrigatorios(FichaInvestigacaoAgravoDTO object, AjaxRequestTarget target) throws ValidacaoException {
        if (object.getInvestigacaoAgravo() == null) {
            throw new ValidacaoException(BundleManager.getString("informeOsDadosComplementaresParaGerarFicha"));
        }
        if (CollectionUtils.isEmpty(vacinaAplicacaoList)) {
            autoCompleteConsultaVacinaAplicada.limpar(target);
            this.validarObjetoObrigatorio(null, autoCompleteConsultaVacinaAplicada.getTxtDescricao().getTextField(), "vacinaAplicada");
        }
        this.validarObjetoObrigatorio(object.getInvestigacaoAgravo().getEventoAdverso(), inputAreaEventoAdverso, "eventoAdverso");
        this.validarObjetoObrigatorio(object.getInvestigacaoAgravo().getFlagMulherAmamentando(), dropDownMulherAmamentando, "mulherAmamentando");
        this.validarObjetoObrigatorio(object.getInvestigacaoAgravo().getClassificacao(), radioGroupClassificacao.get(0), "classificacao");

        if (object.getRegistroAgravo().getDataPrimeirosSintomas().getTime() > DataUtil.getDataAtual().getTime() ||
                object.getRegistroAgravo().getDataPrimeirosSintomas().getTime() < object.getRegistroAgravo().getUsuarioCadsus().getDataNascimento().getTime()) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_validacao_data_primeiros_sintomas"));
        }
    }

    private void salvarRegistro() throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoDTO fichaInvestigacaoAgravoDTO = getForm().getModel().getObject();
        fichaInvestigacaoAgravoDTO.getInvestigacaoAgravo().setSomatorioDoencasExistentes(CheckBoxUtil.getSomatorio(lstCheckBoxDoencas));
        fichaInvestigacaoAgravoDTO.setInvestigacaoVacinaAplicadaList(vacinaAplicacaoList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoEapvPresenteList(eapvList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoMedicacaoUsoList(investigacaoMedicacaoUsoList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoManifestacoesLocaisList(investigacaoManifestacoesLocaisList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoManifestacoesPeleMucosaList(investigacaoManifestacoesPeleMucosaList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoManifestacoesCardiovascularesList(investigacaoManifestacoesCardiovascularesList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoManifestacoesRespiratoriaList(investigacaoManifestacoesRespiratoriaList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoManifestacoesNeurologicasList(investigacaoManifestacoesNeurologicasList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoManifestacoesGastrintestinaisList(investigacaoManifestacoesGastrintestinaisList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoManifestacoesOutrasList(investigacaoManifestacoesOutrasList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoHemogramaList(investigacaoHemogramaList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoBioquimicaList(investigacaoBioquimicaList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoPuncaoLombarList(investigacaoPuncaoLombarList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoUrinaList(investigacaoUrinaList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoDeteccaoViralList(investigacaoDeteccaoViralList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoImunologiaList(investigacaoImunologiaList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoAdicionalList(investigacaoAdicionalList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoOutrosList(investigacaoOutrosList);
        fichaInvestigacaoAgravoDTO.setInvestigacaoEncerramentoFinalList(investigacaoEncerramentoFinalList);

        if (!isTemFichas(fichaInvestigacaoAgravoDTO.getInvestigacaoAgravo(), registroAgravo)) {
            BOFactory.save(registroAgravo);
            BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravo(fichaInvestigacaoAgravoDTO);

            Page page = new ConsultaRegistroAgravoPage();
            getSession().getFeedbackMessages().info(page, BundleManager.getString("registroSalvoSucesso"));
            setResponsePage(page);
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_um_registro_cadastrado_com_o_mesmo_registro_agravo"));
        }
    }

    private boolean isTemFichas(InvestigacaoAgravo investigacaoAgravo, RegistroAgravo registroAgravo) {
        LoadManager loadManager = LoadManager.getInstance(InvestigacaoAgravoCovid19.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoCovid19.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO), registroAgravo.getCodigo()));
        if (investigacaoAgravo.getCodigo() != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoCovid19.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, investigacaoAgravo.getCodigo()));
        }
        return loadManager.start().exists();
    }

    private void initDlgConfirmacao(AjaxRequestTarget target) {
        addModal(target, dlgConfirmacaoEncerramento = new DlgConfirmacaoSimNao(newModalId(), BundleManager.getString("msgDesejaConfirmarEncerramentoInvestigacao")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                encerramentoRegistro(true);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                super.onFechar(target);
            }
        });
        dlgConfirmacaoEncerramento.show(target);
    }

    private void encerramentoRegistro(boolean encerrar) throws ValidacaoException, DAOException {
        RegistroAgravo registroAgravoObject = getForm().getModel().getObject().getRegistroAgravo();
        if (encerrar) {
            if (registroAgravo.getDataEncerramento() == null) {
                throw new ValidacaoException(BundleManager.getString("msgPreencherDataEncerramento"));
            }
            if (VigilanciaHelper.isMonitoramentoContinuo(registroAgravoObject)) {
                registroAgravo.setStatus(RegistroAgravo.Status.INVESTIGACAO_CONCLUIDA.value());
            } else {
                registroAgravo.setStatus(RegistroAgravo.Status.CONCLUIDO.value());
            }
        } else if (RegistroAgravo.Status.CONCLUIDO.value().equals(registroAgravoObject.getStatus())) {
            getForm().getModel().getObject().getRegistroAgravo().setDataEncerramento(null);
            getForm().getModel().getObject().getRegistroAgravo().setStatus(RegistroAgravo.Status.EM_INVESTIGACAO.value());
        }
        salvarRegistro();
    }

    private Form<FichaInvestigacaoAgravoDTO> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<FichaInvestigacaoAgravoDTO>(new FichaInvestigacaoAgravoDTO()));
            this.form.getModel().getObject().setRegistroAgravo(registroAgravo);
        }
        return this.form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("investigacaoAgravo");
    }

    private void carregaInformacoesFichaInvestigacao() {
        InvestigacaoAgravo investigacaoAgravo = LoadManager.getInstance(InvestigacaoAgravo.class)
                .addProperties(new HQLProperties(InvestigacaoAgravo.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravo.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO), registroAgravo.getCodigo()))
                .start().getVO();
        if (investigacaoAgravo != null) {
            getForm().getModel().getObject().setInvestigacaoAgravo(investigacaoAgravo);
            carregarDropDownVacinaAplicadaEncerramentoFinal(investigacaoAgravo);
            if (InvestigacaoAgravo.TipoSelecionado.SIM.getValue().equals(investigacaoAgravo.getAtendimentoMedico())) {
                containerAtendimento.setEnabled(true);
            }
            if (InvestigacaoAgravo.TipoSelecionado.SIM.getValue().equals(investigacaoAgravo.getViajouUltimosDias())) {
                containerViajou.setEnabled(true);
            }
            if (InvestigacaoAgravo.TipoSelecionado.SIM.getValue().equals(investigacaoAgravo.getTransfusaoUltimoMes())) {
                txtDataTransfusaoUltimoMes.setEnabled(true);
            }
            if (InvestigacaoAgravo.TipoSelecionado.SIM.getValue().equals(investigacaoAgravo.getMedicacaoUso())) {
                containerMedicacao.setEnabled(true);
                txtDescricaoTipoOutros.setEnabled(false);
            }
            if (InvestigacaoAgravo.TipoSelecionado.SIM.getValue().equals(investigacaoAgravo.getDoencaPreExistentes())) {
                containerDoencas.setEnabled(true);
            }
            if (InvestigacaoAgravo.TipoSelecionado.SIM.getValue().equals(investigacaoAgravo.getEapvPresente())) {
                containerEapv.setEnabled(true);
            }
            if (InvestigacaoAgravo.CondutaErroImunizacao.DOSE_CONSIDERADA_INVALIDA.getValue().equals(investigacaoAgravo.getCondutaErrosImunizacao())) {
                containerDoseConsideradaInvalida.setEnabled(true);
            }
            if (InvestigacaoAgravo.DoseConsideradaInvalida.OUTROS.getValue().equals(investigacaoAgravo.getCondutaErrosImunizacaoItem())) {
                txtDescricaoOutrosCondutaErroImunizacaoItem.setEnabled(true);
            }
            if (InvestigacaoAgravo.ErroImunizacao.OUTROS.getValue().equals(investigacaoAgravo.getErrosImunizacao())) {
                txtDescricaoOutrosErrosImunizacao.setEnabled(true);
            }
            CheckBoxUtil.selecionarSomatorio(Arrays.asList(checkBoxDoencaAids, checkBoxDoencaAlergiaMedicamento, checkBoxDoencaAlergiaAlimentar, checkBoxDoencaDiabetes,
                    checkBoxDoencaAutoimune, checkBoxDoencaCardiaca, checkBoxDoencaHepatica, checkBoxDoencaNeurologicaPsiquiatrica, checkBoxDoencaPulmonar,
                    checkBoxDoencaRenal, checkBoxDoencaOutras), investigacaoAgravo.getSomatorioDoencasExistentes());
            if (checkBoxDoencaOutras.getComponentValue() != null) {
                txtDoencasOutras.setEnabled(true);
            }
            vacinaAplicacaoList = LoadManager.getInstance(InvestigacaoVacinaAplicada.class)
                    .addProperties(new HQLProperties(InvestigacaoVacinaAplicada.class).getProperties())
                    .addProperties(new HQLProperties(VacinaAplicacao.class, InvestigacaoVacinaAplicada.PROP_VACINA_APLICACAO).getProperties())
                    .addProperty(VOUtils.montarPath(InvestigacaoVacinaAplicada.PROP_VACINA_APLICACAO, VacinaAplicacao.PROP_PRODUTO_VACINA, ProdutoVacina.PROP_PRODUTO, Produto.PROP_FABRICANTE, FabricanteMedicamento.PROP_DESCRICAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoVacinaAplicada.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();
            tableVacinaAplicada.populate();

            eapvList = LoadManager.getInstance(InvestigacaoEapvPresente.class)
                    .addProperties(new HQLProperties(InvestigacaoEapvPresente.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoEapvPresente.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoMedicacaoUsoList = LoadManager.getInstance(InvestigacaoMedicacaoUso.class)
                    .addProperties(new HQLProperties(InvestigacaoMedicacaoUso.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoMedicacaoUso.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoManifestacoesLocaisList = LoadManager.getInstance(InvestigacaoManifestacoes.class)
                    .addProperties(new HQLProperties(InvestigacaoManifestacoes.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_TIPO_MANIFESTACAO, InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACAO_LOCAL.value()))
                    .start().getList();

            investigacaoManifestacoesPeleMucosaList = LoadManager.getInstance(InvestigacaoManifestacoes.class)
                    .addProperties(new HQLProperties(InvestigacaoManifestacoes.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_TIPO_MANIFESTACAO, InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_PELE_MUCOSA.value()))
                    .start().getList();

            investigacaoManifestacoesCardiovascularesList = LoadManager.getInstance(InvestigacaoManifestacoes.class)
                    .addProperties(new HQLProperties(InvestigacaoManifestacoes.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_TIPO_MANIFESTACAO, InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_CARDIOVASCULARES.value()))
                    .start().getList();

            investigacaoManifestacoesRespiratoriaList = LoadManager.getInstance(InvestigacaoManifestacoes.class)
                    .addProperties(new HQLProperties(InvestigacaoManifestacoes.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_TIPO_MANIFESTACAO, InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_RESPIRATORIA.value()))
                    .start().getList();

            investigacaoManifestacoesGastrintestinaisList = LoadManager.getInstance(InvestigacaoManifestacoes.class)
                    .addProperties(new HQLProperties(InvestigacaoManifestacoes.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_TIPO_MANIFESTACAO, InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_GASTRINTESTINAIS.value()))
                    .start().getList();

            investigacaoManifestacoesOutrasList = LoadManager.getInstance(InvestigacaoManifestacoes.class)
                    .addProperties(new HQLProperties(InvestigacaoManifestacoes.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoManifestacoes.PROP_TIPO_MANIFESTACAO, InvestigacaoManifestacoes.TipoManifestacao.MANIFESTACOES_CLINICAS_SISTEMICAS_OUTRAS.value()))
                    .start().getList();

            investigacaoEncerramentoFinalList = LoadManager.getInstance(InvestigacaoEncerramentoFinal.class)
                    .addProperties(new HQLProperties(InvestigacaoEncerramentoFinal.class).getProperties())
                    .addProperties(new HQLProperties(VacinaAplicacao.class, InvestigacaoEncerramentoFinal.PROP_VACINA_APLICACAO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoEncerramentoFinal.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoHemogramaList = LoadManager.getInstance(InvestigacaoAgravoHemograma.class)
                    .addProperties(new HQLProperties(InvestigacaoAgravoHemograma.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoAgravoHemograma.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoBioquimicaList = LoadManager.getInstance(InvestigacaoAgravoBioquimica.class)
                    .addProperties(new HQLProperties(InvestigacaoAgravoBioquimica.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoAgravoBioquimica.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoPuncaoLombarList = LoadManager.getInstance(InvestigacaoAgravoPuncaoLombar.class)
                    .addProperties(new HQLProperties(InvestigacaoAgravoPuncaoLombar.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoAgravoPuncaoLombar.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoUrinaList = LoadManager.getInstance(InvestigacaoAgravoUrina.class)
                    .addProperties(new HQLProperties(InvestigacaoAgravoUrina.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoAgravoUrina.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoDeteccaoViralList = LoadManager.getInstance(InvestigacaoAgravoDeteccaoViral.class)
                    .addProperties(new HQLProperties(InvestigacaoAgravoDeteccaoViral.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoAgravoDeteccaoViral.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoImunologiaList = LoadManager.getInstance(InvestigacaoAgravoImunologia.class)
                    .addProperties(new HQLProperties(InvestigacaoAgravoImunologia.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoAgravoImunologia.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoAdicionalList = LoadManager.getInstance(InvestigacaoAgravoAdicional.class)
                    .addProperties(new HQLProperties(InvestigacaoAgravoAdicional.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoAgravoAdicional.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

            investigacaoOutrosList = LoadManager.getInstance(InvestigacaoAgravoOutros.class)
                    .addProperties(new HQLProperties(InvestigacaoAgravoOutros.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(InvestigacaoAgravoOutros.PROP_INVESTIGACAO_AGRAVO, investigacaoAgravo))
                    .start().getList();

        }

    }

    public VacinaAplicacao carregarVacinaAplicacao(Long codigoVacinaAplicacao) {
        if (codigoVacinaAplicacao == null) return null;
        return LoadManager.getInstance(VacinaAplicacao.class)
                .addProperty(VacinaAplicacao.PROP_CODIGO)
                .addProperty(VacinaAplicacao.PROP_DATA_APLICACAO)
                .addProperty(VacinaAplicacao.PROP_DESCRICAO_VACINA)
                .addProperty(VacinaAplicacao.PROP_DOSE)
                .addProperty(VacinaAplicacao.PROP_LOTE)
                .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_PRODUTO_VACINA, ProdutoVacina.PROP_PRODUTO, Produto.PROP_FABRICANTE, FabricanteMedicamento.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_ESTRATEGIA, Calendario.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_PROFISSIONAL_APLICACAO, Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_PROFISSIONAL_APLICACAO, Profissional.PROP_NOME))
                .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                .addSorter(new QueryCustom.QueryCustomSorter(VacinaAplicacao.PROP_DATA_APLICACAO))
                .setId(codigoVacinaAplicacao).start().getVO();
    }
}
