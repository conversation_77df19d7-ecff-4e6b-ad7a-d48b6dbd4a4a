package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacaoestrutura.autocomplete.AutoCompleteConsultaLocalizacaoEstrutura;
import br.com.celk.view.basico.programasaude.autocomplete.AutoCompleteConsultaProgramaSaude;
import br.com.celk.view.basico.unidade.autocomplete.AutoCompleteConsultaUnidade;
import br.com.celk.view.materiais.classificacaocontabil.autocomplete.AutoCompleteConsultaClassificacaoContabil;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProdutoMulti;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoProdutoDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaSetor;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 63
 */
@Private
public class RelatorioRelacaoProdutoPage extends RelatorioPage<RelatorioRelacaoProdutoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProdutoMulti autoCompleteConsultaProdutoMulti;
    private AutoCompleteConsultaUnidade autoCompleteConsultaUnidade;
    private AutoCompleteConsultaLocalizacaoEstrutura autoCompleteConsultaLocalizacao;
    private AutoCompleteConsultaDeposito autoCompleteConsultaDeposito;
    private AutoCompleteConsultaProgramaSaude autoCompleteConsultaProgramaSaude;
    private AutoCompleteConsultaClassificacaoContabil autoCompleteClassificacaoContabil;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<String> dropDownExibirLotes;
    private DropDown<String> dropDownValidaValidade;
    private DropDown dropDownTipoPreco;

    private GrupoProduto grupoProdutoSubGrupo;
    private DateChooser dataValidade;
    private DropDown<String> visualizarApenasSemLocalizacao;
    private String valida;
//    private String classificaoContabil;

    public static final Long ESTOQUE_FISICO = 1L;
    public static final Long ESTOQUE_DISPONIVEL = 2L;
    private DropDown<String> dropDownListarPreco;

    @Override
    public void init(Form form) {
        dataValidade = new DateChooser("dataValidade");
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("lstEmpresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(autoCompleteConsultaProdutoMulti = new AutoCompleteConsultaProdutoMulti("lstProduto"));
        autoCompleteConsultaProdutoMulti.setIncluirInativo(true);
        form.add(autoCompleteConsultaUnidade = new AutoCompleteConsultaUnidade("lstUnidade"));
        form.add(autoCompleteConsultaDeposito = new AutoCompleteConsultaDeposito("lstDeposito"));
        form.add(autoCompleteConsultaProgramaSaude = new AutoCompleteConsultaProgramaSaude("lstProgramaSaude"));
        form.add(autoCompleteClassificacaoContabil = new AutoCompleteConsultaClassificacaoContabil("classificacaoContabil"));

//        form.add(autoCompleteConsultaLocalizacao = new AutoCompleteConsultaLocalizacao("lstLocalizacao"));
        form.add(autoCompleteConsultaLocalizacao = new AutoCompleteConsultaLocalizacaoEstrutura("localizacaoEstrutura").setExibirApenasVisivelSim(true));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(getDropDownValidarEstoque());
        form.add(getDropDownTipoEstoque());
        form.add(DropDownUtil.getNaoSimDropDown("pontoReposicao"));
        form.add(getDropDownOrdenacao());
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownControleMinimo());
        form.add(getDropDownListarPreco());
        form.add(getDropDownSituacaoProdutos());
        form.add(getDropDownReceita());
        form.add(DropDownUtil.getNaoSimDropDown("totalizarEstoque"));
        form.add(getDropDownControlado());
        form.add(getDropDownExibirLotes());
        form.add(getDropDownValidaValidade());
        form.add(dataValidade);
        form.add(DropDownUtil.getNaoSimDropDown("rename"));
        form.add(DropDownUtil.getNaoSimDropDown("agruparEmpresa"));
        form.add(visualizarApenasSemLocalizacao = DropDownUtil.getNaoSimDropDown("visualizarApenasSemLocalizacao"));
        form.add(getDropDownTipoPreco());
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));

        this.visualizarApenasSemLocalizacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarLocalizacao(target);
            }
        });

        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaUnidade.setMultiplaSelecao(true);
        autoCompleteConsultaUnidade.setOperadorValor(true);
        autoCompleteConsultaDeposito.setMultiplaSelecao(true);
        autoCompleteConsultaDeposito.setOperadorValor(true);
        autoCompleteConsultaProgramaSaude.setMultiplaSelecao(true);
        autoCompleteConsultaProgramaSaude.setOperadorValor(true);
//        autoCompleteConsultaLocalizacao.setMultiplaSelecao(true);
//        autoCompleteConsultaLocalizacao.setOperadorValor(true);

        dataValidade.setEnabled(false);
        dropDownValidaValidade.setEnabled(false);
        dropDownTipoPreco.setEnabled(false);

    }

    private DropDown getDropDownListarPreco() {
        if (dropDownListarPreco == null) {
            dropDownListarPreco = DropDownUtil.getNaoSimDropDown("listarPreco");
            dropDownListarPreco.addAjaxUpdateValue();
            dropDownListarPreco.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (RepositoryComponentDefault.SIM.equals(dropDownListarPreco.getComponentValue())) {
                        dropDownTipoPreco.setEnabled(true);
                    } else {
                        dropDownTipoPreco.setEnabled(false);
                        dropDownTipoPreco.setComponentValue(RepositoryComponentDefault.TipoPreco.PRECO_MEDIO);
                    }
                    target.add(dropDownTipoPreco);
                }
            });
        }
        return dropDownListarPreco;
    }

    private void habilitarLocalizacao(AjaxRequestTarget target) {
        String opcao = visualizarApenasSemLocalizacao.getComponentValue();
        if (opcao != null && !opcao.equals(RepositoryComponentDefault.NAO)) {
            autoCompleteConsultaLocalizacao.setEnabled(false);
        } else {
            autoCompleteConsultaLocalizacao.setEnabled(true);
        }
        autoCompleteConsultaLocalizacao.limpar(target);
        target.add(autoCompleteConsultaLocalizacao);
    }

    private DropDown getDropDownTipoPreco() {
        if (dropDownTipoPreco == null) {
            dropDownTipoPreco = DropDownUtil.getEnumDropDown("tipoPreco", RepositoryComponentDefault.TipoPreco.values());
            dropDownTipoPreco.add(new Tooltip().setText("msgTooltipTipoPreco"));
        }
        return dropDownTipoPreco;
    }

    public DropDown getDropDownValidaValidade() {
        if (dropDownValidaValidade == null) {
            this.dropDownValidaValidade = DropDownUtil.getNaoSimDropDown("valida", new PropertyModel(this, "valida"));
            dropDownValidaValidade.addAjaxUpdateValue();
            this.dropDownValidaValidade.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    String opcao = dropDownValidaValidade.getComponentValue();
                    if (opcao != null && !opcao.equals(RepositoryComponentDefault.NAO)) {
                        dataValidade.setEnabled(true);
                    } else {
                        dataValidade.setEnabled(false);
                        dataValidade.limpar(target);
                    }
                    target.add(dataValidade);
                }
            });
        }
        return dropDownValidaValidade;
    }

    public DropDown getDropDownExibirLotes() {
        if (dropDownExibirLotes == null) {
            this.dropDownExibirLotes = DropDownUtil.getNaoSimDropDown("exibirLotes");
            dropDownExibirLotes.addAjaxUpdateValue();
            this.dropDownExibirLotes.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    String opcao = dropDownExibirLotes.getComponentValue();
                    if (opcao != null && !opcao.equals(RepositoryComponentDefault.NAO)) {
                        dropDownValidaValidade.setEnabled(true);
                    } else {
                        dropDownValidaValidade.setEnabled(false);
                        dropDownValidaValidade.setComponentValue(RepositoryComponentDefault.NAO);
                        dataValidade.setEnabled(false);
                        dataValidade.limpar(target);
                    }
                    target.add(dataValidade);
                    target.add(dropDownValidaValidade);
                }
            });
        }
        return dropDownExibirLotes;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoProdutoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoProdutoDTOParam param) throws ReportException {

        List<GrupoProduto> lstGrupoProduto = new ArrayList<GrupoProduto>();
        if (grupoProdutoSubGrupo != null) {
            lstGrupoProduto.add(grupoProdutoSubGrupo);
        }
        param.setGrupoProdutoSubGrupo(null);
        if (!lstGrupoProduto.isEmpty()) {
            param.setGrupoProdutoSubGrupo(lstGrupoProduto);
        }

        Empresa empresa = ApplicationSession.get().getSession().getEmpresa();
        OperadorValor<List<Empresa>> empresaOperadorValor = (OperadorValor<List<Empresa>>) autoCompleteConsultaEmpresa.getComponentValue();
        if (empresaOperadorValor != null
                && CollectionUtils.isNotNullEmpty(empresaOperadorValor.getValue())
                && empresaOperadorValor.getValue().size() == 1) {
            empresa = empresaOperadorValor.getValue().get(0);
        }

        List<EmpresaSetor> empresaSetorList = LoadManager.getInstance(EmpresaSetor.class)
                .addProperty(VOUtils.montarPath(EmpresaSetor.PROP_SETOR, Empresa.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_EMPRESA, empresa))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_SITUACAO, EmpresaSetor.Situacao.ATIVO.value()))
                .start().getList();

        if(CollectionUtils.isNotNullEmpty(empresaSetorList)){
            List<Empresa> setores = Lambda.extract(empresaSetorList, Lambda.on(EmpresaSetor.class).getSetor());
            setores.add(empresa);

            OperadorValor<List<Empresa>> empresas = new OperadorValor<>();
            empresas.setValue(setores);
            empresas.setOperador(OperadorValor.Operadores.IN);

            param.setLstEmpresa(empresas);
        }

        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioRelacaoProduto(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("saldoEstoque");
    }

    public DropDown getDropDownControlado() {
        DropDown dropDown = new DropDown("controlado");
        dropDown.addChoice(null, BundleManager.getString("ambos"));
        dropDown.addChoice(SubGrupo.CONTROLADO_SIM, BundleManager.getString("sim"));
        dropDown.addChoice(SubGrupo.CONTROLADO_NAO, BundleManager.getString("nao"));

        return dropDown;
    }

    public DropDown getDropDownReceita() {
        DropDown<TipoReceita> dropDown = new DropDown("receita");
        dropDown.addChoice(null, BundleManager.getString("todas"));
        List<TipoReceita> tipoReceitas = LoadManager.getInstance(TipoReceita.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoReceita.PROP_TIPO_RECEITA, BuilderQueryCustom.QueryParameter.DIFERENTE, TipoReceita.RECEITA_MAGISTRAL))
                .start().getList();
        for (TipoReceita tipoReceita : tipoReceitas) {
            dropDown.addChoice(tipoReceita, tipoReceita.getDescricao());
        }
        return dropDown;
    }

    public DropDown getDropDownSituacaoProdutos() {
        DropDown dropDown = new DropDown("flagSituacaoProduto");
        dropDown.addChoice(null, BundleManager.getString("ambos"));
        dropDown.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("ativo"));
        dropDown.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("inativo"));

        return dropDown;
    }

    public DropDown getDropDownControleMinimo() {
        DropDown dropDown = new DropDown("controleMinimo");
        dropDown.addChoice(RepositoryComponentDefault.AMBOS, BundleManager.getString("ambos"));
        dropDown.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("comMinimo"));
        dropDown.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("semMinimo"));

        return dropDown;
    }

    public DropDown getDropDownOrdenacao() {
        DropDown dropDown = new DropDown("ordenacao");
        dropDown.addChoice(Produto.PROP_CODIGO, BundleManager.getString("codigo"));
        dropDown.addChoice(Produto.PROP_DESCRICAO, BundleManager.getString("descricao"));

        return dropDown;
    }

    public DropDown getDropDownTipoEstoque() {
        DropDown dropDown = new DropDown("tipoEstoque");
        dropDown.addChoice(ESTOQUE_FISICO, BundleManager.getString("fisico"));
        dropDown.addChoice(ESTOQUE_DISPONIVEL, BundleManager.getString("disponivel"));

        return dropDown;
    }

    public DropDown getDropDownValidarEstoque() {
        DropDown dropDown = new DropDown("estoqueFisico");
        dropDown.addChoice((long) ReportProperties.ESTOQUE_TODOS, BundleManager.getString("nao"));
        dropDown.addChoice((long) ReportProperties.ESTOQUE_SIM, BundleManager.getString("comEstoque"));
        dropDown.addChoice((long) ReportProperties.ESTOQUE_NAO, BundleManager.getString("semEstoque"));
        dropDown.addChoice((long) ReportProperties.ESTOQUE_NEGATIVO, BundleManager.getString("negativo"));

        return dropDown;
    }

    public DropDown getDropDownFormaApresentacao() {
        DropDown dropDown = new DropDown("formaApresentacao");
        dropDown.addChoice((long) ReportProperties.AGRUPAR_GRUPO, BundleManager.getString("grupo"));
        dropDown.addChoice((long) ReportProperties.AGRUPAR_PRODUTO, BundleManager.getString("produto"));
        dropDown.addChoice((long) ReportProperties.AGRUPAR_LOCALIZACAO, BundleManager.getString("localizacao"));

        return dropDown;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo", new PropertyModel(this, "grupoProdutoSubGrupo"));
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
            param.setGrupoProdutoSubGrupo(grupos);
        }
        return this.dropDownGrupoProduto;
    }
}
