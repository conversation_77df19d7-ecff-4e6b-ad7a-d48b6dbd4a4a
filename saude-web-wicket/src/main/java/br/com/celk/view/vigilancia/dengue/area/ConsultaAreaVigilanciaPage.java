package br.com.celk.view.vigilancia.dengue.area;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueAreaVigilancia;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import static ch.lambdaj.Lambda.on;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;

/**
 *
 * <AUTHOR>
 * Programa - 636
 */
public class ConsultaAreaVigilanciaPage extends ConsultaPage<DengueAreaVigilancia, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long situacao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField<String>("descricao"));
        form.add(DropDownUtil.getIEnumDropDown("situacao", DengueAreaVigilancia.Situacao.values()));

        if (situacao == null) {
            situacao = DengueAreaVigilancia.Situacao.ATIVO.value();
        }
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DengueAreaVigilancia proxy = on(DengueAreaVigilancia.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("codigo"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("area"), proxy.getDescricao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacao(), proxy.getDescricaoSituacao()));
        return columns;
    }

    private CustomColumn<DengueAreaVigilancia> getCustomColumn() {
        return new CustomColumn<DengueAreaVigilancia>() {

            @Override
            public Component getComponent(String componentId, final DengueAreaVigilancia rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAreaVigilanciaPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        rowObject.setSituacao(DengueAreaVigilancia.Situacao.INATIVO.value());
                        BOFactoryWicket.save(rowObject);
                        getPageableTable().populate(target);
                    }
                    
                    

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAreaVigilanciaPage(rowObject, true));
                    }

                    @Override
                    public boolean isExcluirVisible() {
                        return false;
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return DengueAreaVigilancia.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(DengueAreaVigilancia.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(DengueAreaVigilancia.PROP_DATA_CADASTRO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(DengueAreaVigilancia.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(DengueAreaVigilancia.PROP_SITUACAO, situacao));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAreaVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaAreaVigilancia");
    }
}
