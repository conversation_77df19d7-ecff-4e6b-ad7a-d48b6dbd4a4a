package br.com.celk.view.materiais.catmat;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.SalaUnidade;
import br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 972
 */
@Private
public class ConsultaMedicamentoCatmatPage extends ConsultaPage<MedicamentoCatmat, List<BuilderQueryCustom.QueryParameter>> {

    private String catmat;
    private String descricao;
    private Long tipoCatmat;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        form.add(new InputField<String>("catmat"));
        form.add(DropDownUtil.getIEnumDropDown("tipoCatmat", MedicamentoCatmat.TipoProduto.values(), true, bundle("todos"), false, false, true));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        MedicamentoCatmat proxy = on(MedicamentoCatmat.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("catmat"), proxy.getCatmat()));
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createSortableColumn(bundle("tipo"), proxy.getTipoCatmat(), proxy.getDescricaoTipoProduto()));
        return columns;
    }

    private CustomColumn<MedicamentoCatmat> getCustomColumn() {
        return new CustomColumn<MedicamentoCatmat>() {

            @Override
            public Component getComponent(String componentId, final MedicamentoCatmat rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroMedicamentoCatmatPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        Produto produto = LoadManager.getInstance(Produto.class)
                                .addProperty(Produto.PROP_CODIGO)
                                .addProperty(Produto.PROP_REFERENCIA)
                                .addProperty(Produto.PROP_DESCRICAO)
                                .addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_MEDICAMENTO_CATMAT, rowObject))
                                .setMaxResults(1)
                                .start().getVO();

                        if(produto != null){
                            throw new ValidacaoException(bundle("msgNaoPossivelExcluirRegistroVinculadoMedicamentoMaterialX", produto.getDescricaoFormatado()));
                        }

                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroMedicamentoCatmatPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return MedicamentoCatmat.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(MedicamentoCatmat.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(MedicamentoCatmat.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(MedicamentoCatmat.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(MedicamentoCatmat.PROP_CATMAT, catmat));
        parameters.add(new QueryCustom.QueryCustomParameter(MedicamentoCatmat.PROP_TIPO_CATMAT, tipoCatmat));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroMedicamentoCatmatPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaCatmat");
    }
}