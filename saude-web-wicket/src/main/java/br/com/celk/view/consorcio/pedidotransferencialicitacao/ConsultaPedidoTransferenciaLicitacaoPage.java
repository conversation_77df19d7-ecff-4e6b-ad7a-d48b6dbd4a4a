package br.com.celk.view.consorcio.pedidotransferencialicitacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.pedidotransferencialicitacao.columnpanel.PedidoTransferenciaLicitacaoColumnPanel;
import br.com.celk.view.consorcio.pedidotransferencialicitacao.customize.CustomizeConsultaPedidoTransferenciaLicitacao;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.FundoConsorcio;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 * Programa - 215
 */
@Private
public class ConsultaPedidoTransferenciaLicitacaoPage extends ConsultaPage<PedidoTransferenciaLicitacao, List<BuilderQueryCustom.QueryParameter>> {
    private FundoConsorcio fundoConsorcio;
    private Empresa consorciado;
    private DatePeriod periodo;
    private String tipoData;
    private Long situacao;
    private Long flagOrdemCompra;
    private Long aprovacao;
    private Long pedidoLicitacao;
    private Label fundo;
    private boolean existeFundo;
    private String utilizarControleFinanceiroPedidoTransferenciaConsorcio;
    private WebMarkupContainer containerControleFinanceiro;
    private DropDown<FundoConsorcio> fundoConsorcioDropdown;
    private WebMarkupContainer containerfundo;
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new PnlDatePeriod("periodo"));
        form.add(getCbxTipoData());
        form.add(DropDownUtil.getIEnumDropDown("situacao", PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.values(), true));
        form.add(new InputField<Long>("pedidoLicitacao"));

        containerfundo = new WebMarkupContainer("containerFundo");
        containerfundo.add(getDropDownFundoConsorcio());
        form.add(containerfundo.setVisible(existeFundo));

        containerControleFinanceiro = new WebMarkupContainer("containerControleFinanceiro");
        containerControleFinanceiro.setOutputMarkupId(true);
        containerControleFinanceiro.add(DropDownUtil.getIEnumDropDown("aprovacao", PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.values(), true));
        form.add(containerControleFinanceiro);

        if(RepositoryComponentDefault.NAO.equals(getUtilizarControleFinanceiroPedidoTransferenciaConsorcio())){
            containerControleFinanceiro.setVisible(false);
        }

        setExibeExpandir(true);
    }
    
    private DropDown getCbxTipoData(){
        DropDown cbxTipoData = new DropDown("tipoData");
        
        cbxTipoData.addChoice(PedidoTransferenciaLicitacao.PROP_DATA_CADASTRO, BundleManager.getString("cadastro"));
        cbxTipoData.addChoice(PedidoTransferenciaLicitacao.PROP_DATA_CANCELAMENTO, BundleManager.getString("cancelamento"));
        
        return cbxTipoData;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        verificarExistenciaFundo();
        ColumnFactory columnFactory = new ColumnFactory(PedidoTransferenciaLicitacao.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("almoxarifado"), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_EMPRESA_ALMOXARIFADO, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("consorciado"), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("cadastro"), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_DATA_CADASTRO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_STATUS), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_DESCRICAO_STATUS)));
        if(RepositoryComponentDefault.SIM.equals(getUtilizarControleFinanceiroPedidoTransferenciaConsorcio())) {
            columns.add(columnFactory.createSortableColumn(BundleManager.getString("aprovacao"), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_SITUACAO_CONTROLE_FINANCEIRO), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_DESCRICAO_SITUACAO_CONTROLE_FINANCEIRO)));
        }
        if (existeFundo) {
            columns.add(columnFactory.createSortableColumn(BundleManager.getString("fundo"), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_FUNDO_CONSORCIO, fundoConsorcio.PROP_DESCRICAO)));
        }
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("oc"), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_FLAG_ORDEM_COMPRA), VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_DESCRICAO_FLAG_OC)));

        return columns;
    }

    private DropDown getDropDownFundoConsorcio() {
        if (fundoConsorcioDropdown == null) {
            List<FundoConsorcio> fundoConsorcios = new ArrayList<>();
                fundoConsorcioDropdown = new DropDown<FundoConsorcio>("fundoConsorcio");
            if (existeFundo) {
                fundoConsorcios = LoadManager.getInstance(FundoConsorcio.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(FundoConsorcio.PROP_SITUACAO, FundoConsorcio.Situacao.ATIVO.value()))
                        .addSorter(new QueryCustom.QueryCustomSorter(FundoConsorcio.PROP_DESCRICAO, QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_LAST))
                        .start().getList();
            }
            fundoConsorcioDropdown.addChoice(null, "");
            if (existeFundo && CollectionUtils.isNotNullEmpty(fundoConsorcios)) {
                for (FundoConsorcio fundoConsorcio : fundoConsorcios) {
                    fundoConsorcioDropdown.addChoice(fundoConsorcio, fundoConsorcio.getDescricao());
                }
                fundoConsorcioDropdown.addAjaxUpdateValue();
            }
        }

        return fundoConsorcioDropdown;
    }


    private void verificarExistenciaFundo() {
        existeFundo = CollectionUtils.isNotNullEmpty(LoadManager.getInstance(FundoConsorcio.class).addProperty(FundoConsorcio.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(FundoConsorcio.PROP_SITUACAO, FundoConsorcio.Situacao.ATIVO.value())).start().getList());
    }
    private CustomColumn getCustomColumn(){
        return new CustomColumn<PedidoTransferenciaLicitacao>() {

            @Override
            public Component getComponent(String componentId, PedidoTransferenciaLicitacao rowObject) {
                return new PedidoTransferenciaLicitacaoColumnPanel(componentId, rowObject, getUtilizarControleFinanceiroPedidoTransferenciaConsorcio()) {

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        getPageableTable().update(target);
                    }
                };
            }
        };
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaPedidoTransferenciaLicitacao()){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(PedidoTransferenciaLicitacao.PROP_CODIGO, false);
            }
            
        };
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO), consorciado));
        if(tipoData != null){
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(tipoData), periodo));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_STATUS), situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_CODIGO), pedidoLicitacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_SITUACAO_CONTROLE_FINANCEIRO), aprovacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_FUNDO_CONSORCIO), fundoConsorcio));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroPedidoTransferenciaLicitacaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaPedidosTransferencia");
    }

    public String getUtilizarControleFinanceiroPedidoTransferenciaConsorcio() {
        if (utilizarControleFinanceiroPedidoTransferenciaConsorcio == null) {
            try {
                utilizarControleFinanceiroPedidoTransferenciaConsorcio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("utilizarControleFinanceiroPedidoTransferenciaConsorcio");
            } catch (DAOException ex) {
                Logger.getLogger(CadastroPedidoTransferenciaLicitacaoPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return utilizarControleFinanceiroPedidoTransferenciaConsorcio;
    }

}
