package br.com.celk.view.vigilancia.cva.termos;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.cva.animal.autocomplete.AutoCompleteConsultaCvaAnimal;
import br.com.celk.view.vigilancia.cva.proprietarioresponsavel.autocomplete.AutoCompleteConsultaCvaProprietarioResponsavelAnimal;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaEspecieAnimal;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.EspecieAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.TermoAdocaoCva;
import br.com.ksisolucoes.vo.vigilancia.cva.animal.CvaAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.proprietarioresponsavel.CvaProprietarioResponsavel;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 643
 */
public class ConsultaTermoAdocaoPage extends ConsultaPage<TermoAdocaoCva, List<BuilderQueryCustom.QueryParameter>> {

    private CvaProprietarioResponsavel proprietarioResponsavel;
    private CvaAnimal animal;
    private EspecieAnimal especieAnimal;
    private DatePeriod periodo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaCvaProprietarioResponsavelAnimal("proprietarioResponsavel"));
        form.add(new AutoCompleteConsultaCvaAnimal("animal"));
        form.add(new AutoCompleteConsultaEspecieAnimal("especieAnimal"));
        form.add(new PnlDatePeriod("periodo"));
        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        TermoAdocaoCva proxy = on(TermoAdocaoCva.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("data"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("responsavel"), proxy.getProprietarioResponsavel().getProprietarioResponsavel()));
        columns.add(createColumn(bundle("documento"), proxy.getProprietarioResponsavel().getCpfOuRgFormatado()));
        columns.add(createSortableColumn(bundle("animal"), proxy.getAnimal().getNomeAnimal()));
        columns.add(createSortableColumn(bundle("porte"), proxy.getPorte(), proxy.getDescricaoPorte()));
        columns.add(createSortableColumn(bundle("especie"), proxy.getAnimal().getCvaRacaAnimal().getEspecieAnimal().getDescricao()));
        columns.add(createSortableColumn(bundle("sexo"), proxy.getAnimal().getSexo(), proxy.getAnimal().getSexoFormatado()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TermoAdocaoCva>() {
            @Override
            public void customizeColumn(final TermoAdocaoCva rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<TermoAdocaoCva>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoAdocaoCva modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTermoAdocaoPage(rowObject, false));
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<TermoAdocaoCva>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoAdocaoCva modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTermoAdocaoPage(rowObject, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<TermoAdocaoCva>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoAdocaoCva modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<TermoAdocaoCva>() {
                    @Override
                    public DataReport action(TermoAdocaoCva modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoAdocao(modelObject.getCodigo());
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return TermoAdocaoCva.class;
            }

            @Override
            public String[] getProperties() {
                TermoAdocaoCva proxy = on(TermoAdocaoCva.class);

                return VOUtils.mergeProperties(
                        new HQLProperties(TermoAdocaoCva.class).getProperties(),
                        new HQLProperties(CvaAnimal.class, path(proxy.getAnimal())).getProperties(),
                        new String[]{
                            path(proxy.getProprietarioResponsavel().getRg()),
                            path(proxy.getProprietarioResponsavel().getCpf()),
                            path(proxy.getProprietarioResponsavel().getProprietarioResponsavel()),
                            path(proxy.getAnimal().getCvaRacaAnimal().getCodigo()),
                            path(proxy.getAnimal().getCvaRacaAnimal().getDescricao()),
                            path(proxy.getAnimal().getCvaRacaAnimal().getEspecieAnimal().getCodigo()),
                            path(proxy.getAnimal().getCvaRacaAnimal().getEspecieAnimal().getDescricao())
                        }
                );
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TermoAdocaoCva.PROP_DATA_CADASTRO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();

        TermoAdocaoCva proxy = on(TermoAdocaoCva.class);
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getProprietarioResponsavel()), proprietarioResponsavel));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAnimal()), animal));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAnimal().getCvaRacaAnimal().getEspecieAnimal()), especieAnimal));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getDataCadastro()), periodo));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTermoAdocaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaTermoAdocao");
    }

}
