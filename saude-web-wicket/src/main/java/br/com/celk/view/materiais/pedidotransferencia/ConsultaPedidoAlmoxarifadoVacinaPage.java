package br.com.celk.view.materiais.pedidotransferencia;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.pedidotransferencia.customize.CustomizeConsultaPedidoTransferencia;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoTransferenciaFacade;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.OrigemProcessoPedido;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * Programa - 948
 */

public class ConsultaPedidoAlmoxarifadoVacinaPage extends BasePage {

    private Empresa almoxarifado;
    private DatePeriod periodo;
    private Long situacao = PedidoTransferencia.STATUS_ABERTO;

    private CustomizeConsultaPagerProvider<PedidoTransferencia> dataProvider;
    private PageableTable pageableTable;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaAlmoxarifado;
    private DropDown DropDownSituacao;

    public ConsultaPedidoAlmoxarifadoVacinaPage() {
        init();
    }

    public ConsultaPedidoAlmoxarifadoVacinaPage(PageParameters pageParameters) {
        super(pageParameters);
        init();
    }

    public ConsultaPedidoAlmoxarifadoVacinaPage(IModel<?> model) {
        super(model);
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(autoCompleteConsultaAlmoxarifado = new AutoCompleteConsultaEmpresa("almoxarifado"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(populateDropDownSituacao(new DropDown<Long>("situacao")));

        pageableTable = new PageableTable<PedidoTransferencia>("table", getColumns(), getDataProvider());

        form.add(new ProcurarButton<List<BuilderQueryCustom.QueryParameter>>("btnProcurar", pageableTable) {

            @Override
            public List<BuilderQueryCustom.QueryParameter> getParam() {
                return ConsultaPedidoAlmoxarifadoVacinaPage.this.getParam();
            }
        });

        pageableTable.setScrollX("1500px");
        form.add(pageableTable);

        add(form);

        add(new BookmarkablePageLink("linkNovo", CadastroPedidoAlmoxarifadoVacinaPage.class));

        autoCompleteConsultaAlmoxarifado.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO));
    }

    private List getColumns() {
        List columns = new ArrayList();
        ColumnFactory columnFactory = new ColumnFactory(PedidoTransferencia.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidade"), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA, Empresa.PROP_DESCRICAO), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("almoxarifado"), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO), VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("pedido"), VOUtils.montarPath(PedidoTransferencia.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("data_pedido"), VOUtils.montarPath(PedidoTransferencia.PROP_DATA_PEDIDO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(PedidoTransferencia.PROP_STATUS), VOUtils.montarPath(PedidoTransferencia.PROP_DESCRICAO_STATUS)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("data_embarque"), VOUtils.montarPath(PedidoTransferencia.PROP_DATA_EMBARQUE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("data_recebimento"), VOUtils.montarPath(PedidoTransferencia.PROP_DATA_RECEBIMENTO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("statusEnvio"), VOUtils.montarPath(PedidoTransferencia.PROP_STATUS_SEPARACAO), VOUtils.montarPath(PedidoTransferencia.PROP_DESCRICAO_STATUS_SEPARACAO)));

        return columns;
    }

    private CustomizeConsultaPagerProvider<PedidoTransferencia> getDataProvider() {
        if (this.dataProvider == null) {
            this.dataProvider = new CustomizeConsultaPagerProvider<PedidoTransferencia>(new CustomizeConsultaPedidoTransferencia()) {

                @Override
                public SortParam getDefaultSort() {
                    return new SortParam(PedidoTransferencia.PROP_CODIGO, false);
                }

            };
        }


        return this.dataProvider;
    }

    private void cancelarPedido(AjaxRequestTarget target, PedidoTransferencia pedidoTransferencia) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cancelarPedidoTransferencia(pedidoTransferencia.getCodigo(), pedidoTransferencia.getVersion(), OrigemProcessoPedido.LANCAMENTO_WEB);
        pageableTable.update(target);
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<PedidoTransferencia>() {
            @Override
            public void customizeColumn(PedidoTransferencia rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<PedidoTransferencia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferencia modelObject) throws ValidacaoException, DAOException {
                        validarStatusPedido(target, modelObject);
                        PedidoTransferenciaDTO pedidoTransferenciaDTO = new PedidoTransferenciaDTO();
                        pedidoTransferenciaDTO.setPedidoTransferencia(modelObject);
                        setResponsePage(new CadastroPedidoAlmoxarifadoVacinaPage(pedidoTransferenciaDTO));
                    }
                }).setTitleBundleKey("editar").setEnabled(PedidoTransferencia.STATUS_SEPARACAO_AGUARDANDO_ENVIO.equals(rowObject.getStatusSeparacao()) && rowObject.getStatus().equals(PedidoTransferencia.STATUS_ABERTO) && !possuiPedidoPai(rowObject));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<PedidoTransferencia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferencia modelObject) throws ValidacaoException, DAOException {
                        validarStatusPedido(target, modelObject);
                        cancelarPedido(target, modelObject);
                    }
                }).setTitleBundleKey("cancelar").setIcon(Icon.DOC_DELETE).setEnabled(!PedidoTransferencia.STATUS_SEPARACAO_SIM.equals(rowObject.getStatusSeparacao()) && rowObject.getStatus().equals(PedidoTransferencia.STATUS_ABERTO) && !possuiPedidoFilho(rowObject));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<PedidoTransferencia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferencia modelObject) {
                        setResponsePage(new DetalhesPedidoAlmoxarifadoVacinaPage(modelObject));
                    }
                }).setTitleBundleKey("consultar");

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<PedidoTransferencia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferencia modelObject) throws ValidacaoException, DAOException {
                        modelObject.setStatusSeparacao(PedidoTransferencia.STATUS_SEPARACAO_NAO);
                        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).salvarPedidoTransferencia(modelObject, PedidoTransferencia.STATUS_ABERTO, false);
                        pageableTable.update(target);
                    }
                }).setQuestionDialogBundleKey("confirmaEnvioPedidoAlmoxarifado")
                        .setTitleBundleKey("enviar").setIcon(Icon.TRACK).setEnabled(PedidoTransferencia.STATUS_SEPARACAO_AGUARDANDO_ENVIO.equals(rowObject.getStatusSeparacao()) && rowObject.getStatus().equals(PedidoTransferencia.STATUS_ABERTO) && !possuiPedidoFilho(rowObject));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<PedidoTransferencia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferencia modelObject) throws ValidacaoException, DAOException {
                        modelObject.setStatusSeparacao(PedidoTransferencia.STATUS_SEPARACAO_AGUARDANDO_ENVIO);
                        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).salvarPedidoTransferencia(modelObject, PedidoTransferencia.STATUS_ABERTO, false);
                        pageableTable.update(target);
                    }
                }).setQuestionDialogBundleKey("confirmaReaberturaPedidoParaEdicao")
                        .setTitleBundleKey("reabrir").setIcon(Icon.REFRESH).setEnabled(PedidoTransferencia.STATUS_SEPARACAO_NAO.equals(rowObject.getStatusSeparacao()) && rowObject.getStatus().equals(PedidoTransferencia.STATUS_ABERTO) && !possuiPedidoFilho(rowObject));

            }

        };
    }

    private boolean possuiPedidoFilho(PedidoTransferencia rowObject) {
        return LoadManager.getInstance(PedidoTransferencia.class)
                .addProperties(new HQLProperties(PedidoTransferencia.class).getProperties())
                .addProperties(new HQLProperties(PedidoTransferencia.class, VOUtils.montarPath(PedidoTransferencia.PROP_PEDIDO_TRANSFERENCIA_PAI)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferencia.PROP_PEDIDO_TRANSFERENCIA_PAI, rowObject))
                .exists();
    }

    private boolean possuiPedidoPai(PedidoTransferencia rowObject) {
        PedidoTransferencia pedido = LoadManager.getInstance(PedidoTransferencia.class)
                .addProperties(new HQLProperties(PedidoTransferencia.class).getProperties())
                .addProperties(new HQLProperties(PedidoTransferencia.class, VOUtils.montarPath(PedidoTransferencia.PROP_PEDIDO_TRANSFERENCIA_PAI)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferencia.PROP_CODIGO, rowObject.getCodigo()))
                .start().getVO();
        return pedido.getPedidoTransferenciaPai() != null && pedido.getPedidoTransferenciaPai().getCodigo() != null;
    }

    private DropDown populateDropDownSituacao(DropDown<Long> dropDown) {
        dropDown.addChoice(PedidoTransferencia.STATUS_ABERTO, BundleManager.getString("aberto"));
        dropDown.addChoice(PedidoTransferencia.STATUS_SEPARANDO, BundleManager.getString("separando"));
        dropDown.addChoice(PedidoTransferencia.STATUS_PROCESSADO, BundleManager.getString("processado"));
        dropDown.addChoice(PedidoTransferencia.STATUS_RECEBIDO, BundleManager.getString("recebido"));
        dropDown.addChoice(PedidoTransferencia.STATUS_CANCELADO, BundleManager.getString("cancelado"));
        dropDown.addChoice(PedidoTransferencia.STATUS_NAO_APROVADO, BundleManager.getString("nao_aprovado"));
        dropDown.addChoice(null, BundleManager.getString("todos"));
        return dropDown;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("pedido_de_vacina_almoxarifado");
    }

    private List<BuilderQueryCustom.QueryParameter> getParam() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA), ApplicationSession.get().getSessaoAplicacao().getEmpresa()));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_EMPRESA_ORIGEM), almoxarifado));
        if (periodo != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_DATA_PEDIDO), Data.adjustRangeHour(periodo)));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_STATUS), situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_FLAG_VACINA), RepositoryComponentDefault.SIM_LONG));

        return parameters;
    }

    private void validarStatusPedido(AjaxRequestTarget target, PedidoTransferencia object) throws ValidacaoException, DAOException {
        PedidoTransferencia pedidoTransferencia = LoadManager.getInstance(PedidoTransferencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferencia.PROP_CODIGO, object.getCodigo()))
                .start().getVO();
        if (!pedidoTransferencia.getStatus().equals(PedidoTransferencia.STATUS_ABERTO)) {
            pageableTable.update(target);
            throw new ValidacaoException(BundleManager.getString("edicao_nao_permitida_situacao_pedido_foi_modificada"));
        }

        if (PedidoTransferencia.STATUS_SEPARACAO_SIM.equals(pedidoTransferencia.getStatusSeparacao())) {
            throw new ValidacaoException(BundleManager.getString("pedidoProcessoSeparacaoSemPermissao"));
        }
    }
}
