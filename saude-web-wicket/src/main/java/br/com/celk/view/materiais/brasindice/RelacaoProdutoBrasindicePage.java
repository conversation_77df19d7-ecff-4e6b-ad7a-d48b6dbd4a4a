package br.com.celk.view.materiais.brasindice;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.report.materiais.brasindice.interfaces.dto.RelacaoProdutoBrasindiceDTOParam;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.BasicoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 536
 */
@Private
public class RelacaoProdutoBrasindicePage extends RelatorioPage<RelacaoProdutoBrasindiceDTOParam> {
    
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    
    @Override
    public void init(Form form) {
        RelacaoProdutoBrasindiceDTOParam proxy = on(RelacaoProdutoBrasindiceDTOParam.class);
        
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoProduto()), RelacaoProdutoBrasindiceDTOParam.TipoProduto.values()));
        form.add(getDropDownGrupo(path(proxy.getGrupoProduto())));
        form.add(getDropDownSubGrupo(path(proxy.getSubGrupo())));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getVisualizarPreco())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoPreco()), RelacaoProdutoBrasindiceDTOParam.TipoPreco.values()));
    }
    
    private DropDown<GrupoProduto> getDropDownGrupo(String id) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>(id);

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();

                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }
    
    private DropDown<SubGrupo> getDropDownSubGrupo(String id) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>(id);

            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    @Override
    public Class getDTOParamClass() {
        return RelacaoProdutoBrasindiceDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelacaoProdutoBrasindiceDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(BasicoReportFacade.class).relatorioRelacaoProdutoBrasindice(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoProdutosBrasindice");
    }
}