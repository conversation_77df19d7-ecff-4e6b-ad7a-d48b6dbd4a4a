package br.com.celk.view.materiais.horus;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.horus.dto.GeracaoXmlHorusDTO;
import br.com.celk.system.asyncprocess.interfaces.IAsyncProcessNotification;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.horus.dlg.DlgSincronizarHorus;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.materiais.horus.EmpresaHorus;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import ch.lambdaj.Lambda;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 *
 * <AUTHOR>
 * Programa - 538
 */
@Private
public class SincronizacaoHorusPage extends BasePage implements IAsyncProcessNotification {

    private String periodo;
    private Long status;
    private ProcurarButton btnProcurar;
    private PageableTable tblHorusProcesso;
    private DlgSincronizarHorus dlgSincronizarHorus;

    public SincronizacaoHorusPage() {
        init();
    }

    public void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(new InputField("periodo", new PropertyModel(this, "periodo")));
        form.add(DropDownUtil.getIEnumDropDown("status", SincronizacaoHorusProcesso.Status.values(), true));

        form.add(tblHorusProcesso = new PageableTable("tblHorus", getColumns(), getPagerProviderInstance()));
        tblHorusProcesso.getDataProvider().setParameters(getParameters());
        tblHorusProcesso.populate();

        form.add(btnProcurar = new ProcurarButton("btnProcurar", tblHorusProcesso) {

            @Override
            public Object getParam() {
                return getParameters();
            }
        });

        form.add(new AbstractAjaxButton("btnGerarSincronizacaoHorus") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {

                Date dataDescontinucaoHorus = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("dataInicioIntegração");

                if(dataDescontinucaoHorus == null || Data.getDataAtual().before(dataDescontinucaoHorus))
                    viewDlgSincronizarHorus(target);
                else
                    throw new ValidacaoException("Horus descontinuado, utilize BNAFAR");;
            }
        });

        form.add(new AbstractAjaxButton("btnConfiguracoes") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConfiguracaoSincronizacaoHorusPage());
            }
        });

        add(form);
    }

    public List<ISortableColumn<SincronizacaoHorusProcesso>> getColumns() {
        List<ISortableColumn<SincronizacaoHorusProcesso>> columns = new ArrayList<ISortableColumn<SincronizacaoHorusProcesso>>();

        SincronizacaoHorusProcesso on = Lambda.on(SincronizacaoHorusProcesso.class);

        columns.add(getCustomColumn());
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("competencia"), path(on.getDataGeracao()), path(on.getCompetencia())).setPattern("MM/yyyy"));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("dataGeracao"), path(on.getDataGeracao()), path(on.getDataGeracao())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(createColumn(bundle("situacao"), on.getDescricaoStatus()));
        columns.add(createColumn(bundle("tipo"), on.getDescricaoTipoSincronizacao()));
        columns.add(createColumn(bundle("protocolo"), on.getProtocolosEnvioFormatado()));
        columns.add(createColumn(bundle("dataProtocolo"), on.getDataProtocoloRecebimentoFormatado()));
        columns.add(getCustomColumnStatus());

        return columns;
    }

    public CustomColumn getCustomColumn() {
        return new CustomColumn<SincronizacaoHorusProcesso>() {

            @Override
            public Component getComponent(String componentId, SincronizacaoHorusProcesso rowObject) {
                return new SincronizacaoHorusProcessoColumnPanel(componentId, rowObject) {

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        tblHorusProcesso.update(target);
                        target.add(tblHorusProcesso);
                    }
                };
            }
        };
    }

    private List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (periodo != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(Data.getDataParaPrimeiroDiaMes(Data.parserMounthYear(periodo)));

            Date competencia = c.getTime();

            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SincronizacaoHorusProcesso.PROP_COMPETENCIA), competencia));
        }

        if (status == null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SincronizacaoHorusProcesso.PROP_STATUS), QueryCustom.QueryCustomParameter.DIFERENTE, SincronizacaoHorusProcesso.Status.CANCELADO.value()));
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SincronizacaoHorusProcesso.PROP_STATUS), status));
        }

        return parameters;
    }

    public CustomColumn getCustomColumnStatus() {
        return new CustomColumn<SincronizacaoHorusProcesso>() {

            @Override
            public Component getComponent(String componentId, SincronizacaoHorusProcesso rowObject) {
                return new StatusSincronizacaoHorusColumnPanel(componentId, rowObject);
            }
        };
    }

    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return SincronizacaoHorusProcesso.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(SincronizacaoHorusProcesso.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(SincronizacaoHorusProcesso.PROP_CODIGO),});
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(SincronizacaoHorusProcesso.PROP_DATA_GERACAO), false);
            }
        };
    }

    public void viewDlgSincronizarHorus(AjaxRequestTarget target) {
        addModal(target, dlgSincronizarHorus = new DlgSincronizarHorus(newModalId()) {

            @Override
            public void onSincronizarHorus(AjaxRequestTarget target, String competencia, Long tipoSincronizacao) throws ValidacaoException, DAOException {
                    sincronizarHorus(target, competencia, tipoSincronizacao);
            }

        });

        dlgSincronizarHorus.show(target);
        tblHorusProcesso.update(target);
    }

    public void sincronizarHorus(AjaxRequestTarget target, String competencia, Long tipoSincronizacao) throws DAOException, ValidacaoException {
        GeracaoXmlHorusDTO dto = new GeracaoXmlHorusDTO();
        if (SincronizacaoHorusProcesso.TipoSincronizacao.DISPENSASAO.value().equals(tipoSincronizacao)) {
            dto.setTipoXml(RepositoryComponentDefault.TP_XML_DISPENSACAO_PACIENTE);
        } else if (SincronizacaoHorusProcesso.TipoSincronizacao.ENTRADA.value().equals(tipoSincronizacao)) {
            dto.setTipoXml(RepositoryComponentDefault.TP_XML_ENTRADA);
        } else if (SincronizacaoHorusProcesso.TipoSincronizacao.SAIDA.value().equals(tipoSincronizacao)) {
            dto.setTipoXml(RepositoryComponentDefault.TP_XML_SAIDA);
        } else if (SincronizacaoHorusProcesso.TipoSincronizacao.POSICAO_ESTOQUE.value().equals(tipoSincronizacao)) {
            dto.setTipoXml(RepositoryComponentDefault.TP_XML_POSICAO_ESTOQUE);
        }
        DatePeriod dataCompetencia = Data.adjustRangeMonth(Data.parserMounthYear(competencia));
        dto.setPeriodo(dataCompetencia);
        dto.setLstEmpresas(carregaEmpresasHorus());
        dto.setTipoSincronizacao(tipoSincronizacao);
        
        BOFactoryWicket.getBO(MaterialBasicoFacade.class).sincronizarHorus(dto);
        tblHorusProcesso.update(target);
    }

    private List<Empresa> carregaEmpresasHorus() throws ValidacaoException {
        List<EmpresaHorus> lstEmpresaHorus = LoadManager.getInstance(EmpresaHorus.class)
                .start().getList();
        List<Empresa> lstEmpresa = new ArrayList<Empresa>();
        if (CollectionUtils.isNotNullEmpty(lstEmpresaHorus)) {
            for (EmpresaHorus empresaHorus : lstEmpresaHorus) {
                lstEmpresa.add(empresaHorus.getEmpresa());
            }
        } else {
            throw new ValidacaoException(bundle("msgNenhumaEmpresaConfigurada"));
        }
        return lstEmpresa;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("integracao_Horus");
    }

    @Override
    public void notifyProcess(AjaxRequestTarget target, AsyncProcess event) {
        tblHorusProcesso.update(target);
    }
}
