package br.com.celk.view.consorcio.consorcioguiaprocedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioGuiaProcedimentoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaGuiasDTOParam;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import br.com.ksisolucoes.vo.consorcio.Conta;
import br.com.ksisolucoes.vo.consorcio.SubConta;
import org.apache.commons.collections.CollectionUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;


/**
 *
 * <AUTHOR>
 * Programa - 272
 */
@Private

public class ConfirmacaoGeralGuiasPage extends BasePage {

    private MultiSelectionTableOld<ConsorcioGuiaProcedimentoDTO> tblGuiasAberto;
    private Table<ConsorcioGuiaProcedimentoDTO> tblGuiasSelecionadas;
    private InputField txtCountGuias;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaConsorciado;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaPrestador;
    private InputField txtNumGuias;
    private RequiredPnlChoicePeriod pnlPeriodo;
    private List<ConsorcioGuiaProcedimentoDTO> guiasAbertas = new ArrayList<ConsorcioGuiaProcedimentoDTO>();
    private List<ConsorcioGuiaProcedimentoDTO> guiasSelecionadas = new ArrayList<ConsorcioGuiaProcedimentoDTO>();

    private Empresa consorciado;
    private Empresa prestador;
    private DatePeriod periodo;
    private Long nGuia;
    
    public ConfirmacaoGeralGuiasPage() {
        super();
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        form.add(autoCompleteConsultaConsorciado = new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(autoCompleteConsultaPrestador = new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(txtNumGuias = new InputField("nGuia"));
        form.add(pnlPeriodo = new RequiredPnlChoicePeriod("periodo"));

        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                pesquisar(target);
            }
        });
        
        form.add(tblGuiasAberto = new MultiSelectionTableOld("tblGuiasAberto", getColumnsGuiasAberto(), getCollectionProviderGuiasAberto()));
        
        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        
        form.add(tblGuiasSelecionadas = new Table("tblGuiasSelecionadas", getColumnsGuiasSelecionadas(), getCollectionProviderGuiasSelecionadas()));
        form.add(txtCountGuias = new DisabledInputField("countGuias", new LoadableDetachableModel() {
            @Override
            protected Object load() {
                return guiasSelecionadas.size();
            }
        }));
        form.add(new AbstractAjaxButton("btnConfirmarUtilizacao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmarUtilizacao(target);
            }
        });

        tblGuiasAberto.populate();
        tblGuiasAberto.setScrollY("200px");
        tblGuiasAberto.setScrollCollapse(false);
        tblGuiasSelecionadas.populate();
        tblGuiasSelecionadas.setScrollY("200px");
        tblGuiasSelecionadas.setScrollCollapse(false);

        add(form);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaConsorciado;
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("confirmacaoGuiasAberto");
    }
    
    //<editor-fold defaultstate="collapsed" desc="TBL Guias Em Aberto">
    private List<IColumn> getColumnsGuiasAberto(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(ConsorcioGuiaProcedimentoDTO.class);
        
        columns.add(columnFactory.createColumn(BundleManager.getString("nGuia"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CODIGO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("valor"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_VALOR_TOTAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("consorciado"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("prestador"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("paciente"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("data"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO)));
        
        return columns;
    }
    
    private ICollectionProvider getCollectionProviderGuiasAberto(){
        return new CollectionProvider() {
            
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return guiasAbertas;
            }
        };
    }
    //</editor-fold>
    
    //<editor-fold defaultstate="collapsed" desc="TBL Guias Selecionadas">
    private List<IColumn> getColumnsGuiasSelecionadas(){
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(ConsorcioGuiaProcedimentoDTO.class);
        
        columns.add(getCustomColumnGuiasSelecionadas());
        columns.add(columnFactory.createColumn(BundleManager.getString("nGuia"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CODIGO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("valor"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_VALOR_TOTAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("consorciado"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("prestador"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("paciente"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("data"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumnGuiasSelecionadas(){
        return new CustomColumn<ConsorcioGuiaProcedimentoDTO>() {
            @Override
            public Component getComponent(String componentId, final ConsorcioGuiaProcedimentoDTO rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerGuiaSelecionada(target, rowObject);
                    }
                };
            }
        };
    }
    
    private ICollectionProvider getCollectionProviderGuiasSelecionadas(){
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return guiasSelecionadas;
            }
        };
    }
    //</editor-fold>
    
    private void pesquisar(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        QueryConsultaGuiasDTOParam param = new QueryConsultaGuiasDTOParam();
        param.setPrestador(prestador);
        param.setConsorciado(consorciado);
        param.setNumeroGuia(nGuia);
        param.setPeriodo(periodo);
        guiasAbertas = BOFactoryWicket.getBO(ConsorcioFacade.class).consultarGuiasAbertas(param);
        guiasAbertas = (List<ConsorcioGuiaProcedimentoDTO>) CollectionUtils.subtract(guiasAbertas, guiasSelecionadas);
        tblGuiasAberto.update(target);
    }
    
    private void adicionar(AjaxRequestTarget target) {
        List<ConsorcioGuiaProcedimentoDTO> selectedObjects = tblGuiasAberto.getSelectedObjects();
        guiasSelecionadas.addAll(selectedObjects);
        guiasAbertas.removeAll(selectedObjects);
        tblGuiasAberto.updateAndClearSelection(target);
        tblGuiasSelecionadas.update(target);
        target.add(txtCountGuias);
    }
    
    private void removerGuiaSelecionada(AjaxRequestTarget target, ConsorcioGuiaProcedimentoDTO rowObject) {
        guiasSelecionadas.remove(rowObject);
        guiasAbertas.add(rowObject);
        tblGuiasAberto.update(target);
        tblGuiasSelecionadas.update(target);
        target.add(txtCountGuias);
    }
    
    private void confirmarUtilizacao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(ConsorcioFacade.class).confirmarUtilizacaoGuias(guiasSelecionadas);
        
        getSession().getFeedbackMessages().info(this, BundleManager.getString("guiasConfirmadasSucesso"));
        limparTela(target);
    }

    private void limparTela(AjaxRequestTarget target) {
        guiasSelecionadas.clear();
        target.add(tblGuiasSelecionadas);
        txtCountGuias.limpar(target);
    }
}
