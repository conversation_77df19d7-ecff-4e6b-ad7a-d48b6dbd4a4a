package br.com.celk.view.consorcio.pedidotransferencialicitacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.pedidotransferencialicitacao.columnpanel.AprovacaoFinanceiraPedidoTransferenciaColumnPanel;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.AprovacaoFinanceiraPedidoTransferenciaDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.AprovacaoFinanceiraPedidoTransferenciaDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 880
 */
@Private
public class AprovacaoFinanceiraPedidoTransferenciaPage extends BasePage {

    private Form form;

    private PageableTable pageableTable;
    private IPagerProvider pagerProvider;
    private AprovacaoFinanceiraPedidoTransferenciaDTOParam param = new AprovacaoFinanceiraPedidoTransferenciaDTOParam();

    public AprovacaoFinanceiraPedidoTransferenciaPage() {
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel<AprovacaoFinanceiraPedidoTransferenciaDTOParam>(param));

        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new PnlDatePeriod("periodo"));
        form.add(new LongField("codigo"));

        form.add(pageableTable = new PageableTable("table", getColumns(), getPagerProvider()));

        ProcurarButton procurarButton;
        form.add(procurarButton = new ProcurarButton<AprovacaoFinanceiraPedidoTransferenciaDTOParam>("btnProcurar", pageableTable) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                procurar(target);
            }

            @Override
            public AprovacaoFinanceiraPedidoTransferenciaDTOParam getParam() {
                return param;
            }
        });

        procurarButton.procurar();
        add(form);
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(AprovacaoFinanceiraPedidoTransferenciaDTO.class);
        AprovacaoFinanceiraPedidoTransferenciaDTO proxy = on(AprovacaoFinanceiraPedidoTransferenciaDTO.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(bundle("numeroPedido"), path(proxy.getPedidoTransferenciaLicitacao().getCodigo())));
        columns.add(columnFactory.createSortableColumn(bundle("consorciado"), path(proxy.getPedidoTransferenciaLicitacao().getEmpresaConsorciado().getDescricao())));
        columns.add(columnFactory.createSortableColumn(bundle("tipoConta"), path(proxy.getPedidoTransferenciaLicitacao().getSubContaMedicamento().getTipoConta().getDescricao())));
        columns.add(columnFactory.createColumn(bundle("saldoDisponivel"), path(proxy.getSaldoDisponivel())));
        columns.add(columnFactory.createSortableColumn(bundle("cadastro"), path(proxy.getPedidoTransferenciaLicitacao().getDataCadastro())));

        return columns;
    }

    private CustomColumn getCustomColumn(){
        return new CustomColumn<AprovacaoFinanceiraPedidoTransferenciaDTO>() {

            @Override
            public Component getComponent(String componentId, AprovacaoFinanceiraPedidoTransferenciaDTO rowObject) {
                return new AprovacaoFinanceiraPedidoTransferenciaColumnPanel(componentId, rowObject.getPedidoTransferenciaLicitacao()) {

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        pageableTable.update(target);
                    }
                };
            }
        };
    }

    public IPagerProvider getPagerProvider() {
        if (this.pagerProvider == null) {
            this.pagerProvider = new QueryPagerProvider<AprovacaoFinanceiraPedidoTransferenciaDTO, AprovacaoFinanceiraPedidoTransferenciaDTOParam>() {
                @Override
                public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(ConsorcioFacade.class).aprovacaoFinanceiraPedidoTransferenciaQueryPager(dataPaging);
                }

                @Override
                public void customizeParam(AprovacaoFinanceiraPedidoTransferenciaDTOParam param) {
                    SingleSortState<String> sortState = (SingleSortState) getPagerProvider().getSortState();

                    if (sortState.getSort() != null) {
                        param.setPropSort(sortState.getSort().getProperty());
                        param.setAscending(sortState.getSort().isAscending());
                    }
                }
            };
        }

        return this.pagerProvider;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("aprovacaoFinanceiraPedidosTransferencia");
    }

}