package br.com.celk.view.vigilancia.documentopersonalizado;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IHtmlReportAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.report.DocumentoVigilanciaHtmlReport;
import br.com.celk.report.HtmlReport;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.DocumentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TemplateDocumentoVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 887
 */
@Private
public class ConsultaDocumentoVigilanciaPage extends ConsultaPage<DocumentoVigilancia, List<BuilderQueryCustom.QueryParameter>> {

    private Date dataCadastro;
    private Long codigo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField("dataCadastro"));
        form.add(new InputField("codigo"));

        getLinkNovo().setVisible(false);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DocumentoVigilancia proxy = on(DocumentoVigilancia.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("nrDocumento"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("modeloDocumento"), proxy.getTemplateDocumentoVigilancia().getNome()));
        columns.add(createSortableColumn(bundle("usuarioCadastro"), proxy.getUsuario().getDescricaoFormatado()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<DocumentoVigilancia>() {
            @Override
            public void customizeColumn(DocumentoVigilancia rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<DocumentoVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, DocumentoVigilancia modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).cancelarDocumentoVigilancia(modelObject);
                        getPageableTable().update(target);
                    }
                });
                addAction(ActionType.IMPRIMIR, rowObject, new IHtmlReportAction<DocumentoVigilancia>() {
                    @Override
                    public HtmlReport action(AjaxRequestTarget target, DocumentoVigilancia documentoVigilancia) throws DAOException, ValidacaoException {
                        return new DocumentoVigilanciaHtmlReport(documentoVigilancia.getDescricao());
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return DocumentoVigilancia.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(DocumentoVigilancia.class).getProperties(),
                        new HQLProperties(TemplateDocumentoVigilancia.class, DocumentoVigilancia.PROP_TEMPLATE_DOCUMENTO_VIGILANCIA).getProperties()
                );
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(DocumentoVigilancia.PROP_DATA_CADASTRO, false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();
        parameters.add(new QueryCustom.QueryCustomParameter(DocumentoVigilancia.PROP_DATA_CADASTRO, dataCadastro));
        parameters.add(new QueryCustom.QueryCustomParameter(DocumentoVigilancia.PROP_CODIGO, codigo));
        parameters.add(new QueryCustom.QueryCustomParameter(DocumentoVigilancia.PROP_SITUACAO, DocumentoVigilancia.Situacao.EMITIDO.value()));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroDocumentoPersonalizadoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaDocumentoVigilancia");
    }
}
