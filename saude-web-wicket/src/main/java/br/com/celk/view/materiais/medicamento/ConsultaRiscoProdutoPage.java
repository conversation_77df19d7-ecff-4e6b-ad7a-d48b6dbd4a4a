package br.com.celk.view.materiais.medicamento;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.questionarioclassificacao.CadastroFatorRiscoSanitarioPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.TipoInfusao;
import br.com.ksisolucoes.vo.materiais.RiscoProduto;
import br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.DiagnosticoEnfermagemSae;
import br.com.ksisolucoes.vo.vigilancia.FatorRiscoSanitario;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;


/**
 * Programa - 1177
 */
public class ConsultaRiscoProdutoPage  extends ConsultaPage<RiscoProduto, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long status;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        form.add(getDropDownStatus());
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(RiscoProduto.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(RiscoProduto.PROP_DESCRICAO)));
        return columns;

    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<RiscoProduto>() {
            @Override
            public void customizeColumn(final RiscoProduto rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<RiscoProduto>() {
                    @Override
                    public void action(AjaxRequestTarget target, RiscoProduto modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRiscoProdutoPage(modelObject,false));
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<RiscoProduto>() {
                    @Override
                    public void action(AjaxRequestTarget target, RiscoProduto modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRiscoProdutoPage(modelObject,true));
                    }
                });
                addAction(ActionType.REATIVAR, rowObject, new IModelAction<RiscoProduto>() {
                    @Override
                    public void action(AjaxRequestTarget target, RiscoProduto modelObject) throws ValidacaoException, DAOException {
                        modelObject.setStatus(RepositoryComponentDefault.SIM_LONG);
                        BOFactoryWicket.save(modelObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(rowObject.getStatus().equals(RepositoryComponentDefault.NAO_LONG));
                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<RiscoProduto>() {
                    @Override
                    public void action(AjaxRequestTarget target, RiscoProduto modelObject) throws ValidacaoException, DAOException {
                        modelObject.setStatus(RepositoryComponentDefault.NAO_LONG);
                        BOFactoryWicket.save(modelObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(rowObject.getStatus().equals(RepositoryComponentDefault.SIM_LONG));
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter(){
            @Override
            public Class getClassConsulta() {
                return RiscoProduto.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(RiscoProduto.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RiscoProduto.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RiscoProduto.PROP_STATUS), status));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRiscoProdutoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consulta_risco_produto");
    }

    private DropDown getDropDownStatus() {
        DropDown dropDown = new DropDown("status");
        dropDown.addChoice(null, BundleManager.getString("ambos"));
        for (RiscoProduto.Status status : RiscoProduto.Status.values()) {
            dropDown.addChoice(status.value(), status.descricao());
        }
        return dropDown;
    }
}
