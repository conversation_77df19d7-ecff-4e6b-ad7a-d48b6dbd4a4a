<wicket:panel>
    <form wicket:id="formPainelRecuperarSenha" autocomplete="off" data-secure-form="true">
        <div class="span-10 last">
            <fieldset class="login-fieldset">
                <div wicket:id="messagePanel" class="span-10 last warning"
                     style="display: block; overflow: auto; height: auto; width: 265px">
                    <span wicket:id="message">message</span>
                </div>
                <div class="field"><input wicket:id="loginRecuperar" placeholder="Usuário" type="text" size="28"/></div>
                <div class="field"><input wicket:id="cpfRecuperar" placeholder="CPF" type="text" size="28"/></div>
                <div class="field"><input wicket:id="btnRecuperar" type="submit" class="checkmark first"
                                          wicket:message="value:recuperar"/></div>
            </fieldset>
        </div>
    </form>

    <script type="text/javascript">
        // Inicializa proteções de segurança específicas para este formulário
        document.addEventListener('DOMContentLoaded', function() {
            var form = document.querySelector('form[data-secure-form="true"]');
            if (form) {
                // Limpa campos automaticamente após 30 segundos de inatividade
                var inactivityTimer;
                var sensitiveInputs = form.querySelectorAll('input[data-sensitive="true"]');

                function resetInactivityTimer() {
                    clearTimeout(inactivityTimer);
                    inactivityTimer = setTimeout(function() {
                        sensitiveInputs.forEach(function(input) {
                            input.value = '';
                        });
                    }, 30000); // 30 segundos
                }

                // Monitora atividade nos campos
                sensitiveInputs.forEach(function(input) {
                    input.addEventListener('input', resetInactivityTimer);
                    input.addEventListener('focus', resetInactivityTimer);
                });

                // Limpa campos quando a janela perde o foco
                window.addEventListener('blur', function() {
                    setTimeout(function() {
                        sensitiveInputs.forEach(function(input) {
                            input.value = '';http://localhost:8080/?0-1.IBehaviorListener.0-form-modalEsqueciMinhaSenha-content-formPainelRecuperarSenha-btnRecuperar
                        });
                    }, 5000); // 5 segundos após perder foco
                });

                // Previne inspeção de elementos nos campos sensíveis
                sensitiveInputs.forEach(function(input) {
                    input.addEventListener('contextmenu', function(e) {
                        e.preventDefault();
                        return false;
                    });
                });
            }
        });
    </script>
</wicket:panel>
