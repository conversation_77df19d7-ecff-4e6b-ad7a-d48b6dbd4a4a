package br.com.celk.view.vigilancia.dengue.pontoestrategico;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.dengue.autocomplete.AutoCompleteConsultaDengueTipoPontoEstrategico;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade;
import br.com.ksisolucoes.vo.vigilancia.dengue.DenguePontoEstrategico;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoPontoEstrategico;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 640
 */
public class ConsultaDenguePontoEstrategicoPage extends ConsultaPage<DenguePontoEstrategico, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private DenguePontoEstrategico denguePontoEstrategico;
    private Long situacao = (Long) DenguePontoEstrategico.Situacao.ATIVO.value();
    private DengueTipoPontoEstrategico dengueTipoPontoEstrategico;
    private DropDown<Long> dropDownSituacao;

    public ConsultaDenguePontoEstrategicoPage() {
    }

    public ConsultaDenguePontoEstrategicoPage(DenguePontoEstrategico denguePontoEstrategico) {
        this.denguePontoEstrategico = denguePontoEstrategico;
        situacao = null;
        setProcurarAoAbrir(true);
        getPageableTable().populate();
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField("descricao"));
        form.add(new AutoCompleteConsultaDengueTipoPontoEstrategico("dengueTipoPontoEstrategico"));
        form.add(getDropDownSituacao("situacao"));

        add(form);
    }
    
    public DropDown<Long> getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>(id);
            dropDownSituacao.addChoice(null, BundleManager.getString("ambos"));
            dropDownSituacao.addChoice((Long) DenguePontoEstrategico.Situacao.ATIVO.value(), bundle("ativo"));
            dropDownSituacao.addChoice((Long) DenguePontoEstrategico.Situacao.INATIVO.value(), bundle("inativo"));
        }
        return dropDownSituacao;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DenguePontoEstrategico proxy = on(DenguePontoEstrategico.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("dataRegistro"), proxy.getDataRegistro()));
        columns.add(createSortableColumn(BundleManager.getString("pontoEstrategico"), proxy.getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("tipoPontoEstrategico"), proxy.getDengueTipoPontoEstrategico().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("localidade"), proxy.getDengueLocalidade().getLocalidade()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getSituacao(), proxy.getSituacaoFormatado()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<DenguePontoEstrategico>() {
            @Override
            public void customizeColumn(final DenguePontoEstrategico rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<DenguePontoEstrategico>() {
                    @Override
                    public void action(AjaxRequestTarget target, DenguePontoEstrategico modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroDenguePontoEstrategicoPage(rowObject, false, true));
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DenguePontoEstrategico>() {
                    @Override
                    public void action(AjaxRequestTarget target, DenguePontoEstrategico modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroDenguePontoEstrategicoPage(modelObject, true, true));
                    }
                }).setTitleBundleKey("detalhes");
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return DenguePontoEstrategico.class;
            }
            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(DenguePontoEstrategico.class).getProperties(),
                        new HQLProperties(DengueLocalidade.class, DenguePontoEstrategico.PROP_DENGUE_LOCALIDADE).getProperties(),
                        new HQLProperties(DengueTipoPontoEstrategico.class, DenguePontoEstrategico.PROP_DENGUE_TIPO_PONTO_ESTRATEGICO).getProperties(),
                        new HQLProperties(VigilanciaEndereco.class, DenguePontoEstrategico.PROP_VIGILANCIA_ENDERECO).getProperties());
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(DenguePontoEstrategico.PROP_DATA_CADASTRO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DenguePontoEstrategico.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DenguePontoEstrategico.PROP_SITUACAO), situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DenguePontoEstrategico.PROP_DENGUE_TIPO_PONTO_ESTRATEGICO), dengueTipoPontoEstrategico));
        if (denguePontoEstrategico != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DenguePontoEstrategico.PROP_CODIGO), denguePontoEstrategico.getCodigo()));
        }
        denguePontoEstrategico = null;
        situacao = (Long) DenguePontoEstrategico.Situacao.ATIVO.value();
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroDenguePontoEstrategicoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDenguePontoEstrategico");
    }
}
