package br.com.celk.view.vigilancia.faturamento.lancamentoAtividades;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 771
 */
@Private
public class ConsultaLancamentoAtividadesVigilanciaPage extends ConsultaPage<LancamentoAtividadesVigilancia, List<BuilderQueryCustom.QueryParameter>> {

    private String tipoAtividade;
    private Profissional profissional;
    private Date dataAtividade;
    private Long numeroProtocolo;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNaoExcluir;
    private Estabelecimento estabelecimento;
    private VigilanciaPessoa vigilanciaPessoa;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional"));
        form.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento("estabelecimento"));
        form.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa("vigilanciaPessoa"));
        form.add(new DateChooser("dataAtividade"));
        form.add(new InputField<String>("tipoAtividade"));
        form.add(new InputField("numeroProtocolo"));
        // Com permissão
        Profissional profissionalLogado = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if (isActionPermitted(Permissions.PROFISSIONAL, ConsultaLancamentoAtividadesVigilanciaPage.class)) {
            if (profissionalLogado != null) {
                profissional = profissionalLogado;
            }
            autoCompleteConsultaProfissional.setEnabled(true);
        } else {
            // Tem profissional
            if (profissionalLogado != null) {
                profissional = profissionalLogado;
                autoCompleteConsultaProfissional.setEnabled(false);
            } else {
                profissional = new Profissional();
                autoCompleteConsultaProfissional.setEnabled(true);
                autoCompleteConsultaProfissional.setComponentValue(null);
            }
        }
    }


    private Usuario getUsuarioLogado() {
        return ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
    }
    private Profissional getProfissional() {
        return SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        LancamentoAtividadesVigilancia proxy = on(LancamentoAtividadesVigilancia.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("dataAtividade"), proxy.getDataAtividade()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("tipoLancamento"), proxy.getTipoAtividade()));
        columns.add(createSortableColumn(bundle("protocolo"), proxy.getRequerimentoVigilancia().getProtocoloFormatado()));
        columns.add(createSortableColumn(bundle("estabelecimentoPessoa"), proxy.getNomePessoa()));
        columns.add(createSortableColumn(bundle("flagTipoContribuinte"), proxy.getFlagTipoFormatado()));
        return columns;
    }

    private CustomColumn<LancamentoAtividadesVigilancia> getCustomColumn() {
        return new CustomColumn<LancamentoAtividadesVigilancia>() {
            @Override
            public Component getComponent(String componentId, LancamentoAtividadesVigilancia rowObject) {
                return new CrudActionsColumnPanel<LancamentoAtividadesVigilancia>(componentId, rowObject) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        if(CollectionUtils.isNotNullEmpty(getAtividadesVigilanciaVinculadas(getObject()))){
                            initDlgConfirmacaoSimNao(target, getObject(), false);
                        }else{
                            setResponsePage(new CadastroLancamentoAtividadesVigilanciaPage(getObject()));
                        }
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                       if(CollectionUtils.isNotNullEmpty(getAtividadesVigilanciaVinculadas(getObject()))){
                           initDlgConfirmacaoSimNao(target, getObject(), true);
                       }else{
                           deletarLancamentoAtividade(target, getObject());
                       }
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLancamentoAtividadesVigilanciaPage(getObject(), true));
                    }
                };
            }
        };
    }

    private void deletarLancamentoAtividade(AjaxRequestTarget target, LancamentoAtividadesVigilancia lancamentoAtividade) throws ValidacaoException, DAOException {
        LancamentoAtividadesVigilanciaDTO lancamentoAtividadesVigilanciaDTO = new LancamentoAtividadesVigilanciaDTO();
        lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilancia(lancamentoAtividade);
        BOFactoryWicket.getBO(VigilanciaFacade.class).deletarLancamentoAtividadesVigilancia(lancamentoAtividadesVigilanciaDTO);
        getPageableTable().populate(target);
    }

    private List<LancamentoAtividadesVigilancia> getAtividadesVigilanciaVinculadas(LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia){
        if(lancamentoAtividadesVigilancia.getContaPaciente() != null) {
            return LoadManager.getInstance(LancamentoAtividadesVigilancia.class)
                    .addProperties(new HQLProperties(LancamentoAtividadesVigilancia.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE ,lancamentoAtividadesVigilancia.getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_CONTA_PACIENTE,lancamentoAtividadesVigilancia.getContaPaciente()))
                    .start().getList();
        }
        return null;
    }

    private void initDlgConfirmacaoSimNao(AjaxRequestTarget target, LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia, final boolean deletar) {
        addModal(target, dlgConfirmacaoSimNaoExcluir = new DlgConfirmacaoSimNao<LancamentoAtividadesVigilancia>(newModalId(),
                bundle("msgDesejaXLancamentoAtividadeEstaPossuiContaComOutrasAtividadesVinculadasQueSeraoExcluidas", deletar ? "Deletar" : "Editar")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                if (deletar) {
                    deletarLancamentoAtividade(target, getObject());
                } else {
                    setResponsePage(new CadastroLancamentoAtividadesVigilanciaPage(getObject()));
                }
            }
        });
        dlgConfirmacaoSimNaoExcluir.setObject(lancamentoAtividadesVigilancia);
        dlgConfirmacaoSimNaoExcluir.show(target);
    }


    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return LancamentoAtividadesVigilancia.class;
            }

            @Override
            public String[] getProperties() {

                LancamentoAtividadesVigilancia proxy = on(LancamentoAtividadesVigilancia.class);

                return VOUtils.mergeProperties(new HQLProperties(LancamentoAtividadesVigilancia.class).getProperties(),
                        new String[] {
                                path(proxy.getContaPaciente().getCodigo()),
                                path(proxy.getRequerimentoVigilancia().getCodigo()),
                                path(proxy.getRequerimentoVigilancia().getProtocolo()),
                        });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(LancamentoAtividadesVigilancia.PROP_DATA_CADASTRO), false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_PROFISSIONAL, profissional));
        parameters.add(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_DATA_ATIVIDADE, dataAtividade));
        parameters.add(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_TIPO_ATIVIDADE, BuilderQueryCustom.QueryParameter.ILIKE, tipoAtividade));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LancamentoAtividadesVigilancia.PROP_REQUERIMENTO_VIGILANCIA,  RequerimentoVigilancia.PROP_PROTOCOLO), numeroProtocolo));
        parameters.add(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_ESTABELECIMENTO, estabelecimento));
        parameters.add(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_VIGILANCIA_PESSOA, vigilanciaPessoa));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroLancamentoAtividadesVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("lancamentoAtividadesVigilancia");
    }
}