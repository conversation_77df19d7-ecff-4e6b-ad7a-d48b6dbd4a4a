package br.com.celk.view.materiais.compras.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioResumoAutorizacaoFornecimentoDTOParam;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 870
 */
public class RelatorioResumoAutorizacaoFornecimentoPage extends RelatorioPage<RelatorioResumoAutorizacaoFornecimentoDTOParam> {

    private RequiredPnlChoicePeriod requiredPnlChoicePeriod;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaPessoa("fornecedor"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(requiredPnlChoicePeriod = new RequiredPnlChoicePeriod("periodo"));
        requiredPnlChoicePeriod.setDefaultOutro();
        form.add(new InputField("nrPregao"));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioResumoAutorizacaoFornecimentoDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoOrdenacao", RelatorioResumoAutorizacaoFornecimentoDTOParam.TipoOrdenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("situacao", RelatorioResumoAutorizacaoFornecimentoDTOParam.Situacao.values()));

    }

    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoAutorizacaoFornecimento");
    }

    @Override
    public Class<RelatorioResumoAutorizacaoFornecimentoDTOParam> getDTOParamClass() {
        return RelatorioResumoAutorizacaoFornecimentoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoAutorizacaoFornecimentoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(MaterialBasicoFacade.class).relatorioImpressaoResumoAutorizacaoFornecimento(param);
    }
}