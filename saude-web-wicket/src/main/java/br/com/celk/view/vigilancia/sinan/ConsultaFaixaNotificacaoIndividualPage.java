package br.com.celk.view.vigilancia.sinan;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.FaixaNotificacaoIndividual;
import br.com.ksisolucoes.vo.vigilancia.base.BaseFaixaNotificacaoIndividual;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> S. Schmoeller
 * Programa - 934
 */
public class ConsultaFaixaNotificacaoIndividualPage extends ConsultaPage<FaixaNotificacaoIndividual, List<BuilderQueryCustom.QueryParameter>> {

    private Long numeracaoInicial;
    private Long numeracaoFinal;
    private DatePeriod periodo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel<>(this));
        form.add(new InputField<Long>("numeracaoInicial"));
        form.add(new InputField<Long>("numeracaoFinal"));
        form.add(new PnlDatePeriod("periodo"));

    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        FaixaNotificacaoIndividual proxy = on(FaixaNotificacaoIndividual.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("numeracaoInicial"), proxy.getNumeracaoInicial()));
        columns.add(createColumn(bundle("numeracaoFinal"), proxy.getNumeracaoFinal()));
        columns.add(createColumn(bundle("numeracaoAtual"), proxy.getNumeracaoAtual()));
        columns.add(new DateColumn(bundle("dataValidade"), path(proxy.getDataVigencia())).setPattern("dd/MM/yyyy"));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<FaixaNotificacaoIndividual>() {
            @Override
            public void customizeColumn(FaixaNotificacaoIndividual rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<FaixaNotificacaoIndividual>() {
                    @Override
                    public void action(AjaxRequestTarget target, FaixaNotificacaoIndividual modelObject) {
                        setResponsePage(new CadastroFaixaNotificacaoIndividualPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<FaixaNotificacaoIndividual>() {
                    @Override
                    public void action(AjaxRequestTarget target, FaixaNotificacaoIndividual modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().update(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider<CustomizeConsultaAdapter>(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return FaixaNotificacaoIndividual.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(FaixaNotificacaoIndividual.class).getProperties());
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam<>(BaseFaixaNotificacaoIndividual.PROP_DATA_VIGENCIA, true);
            }
        };
    }


    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<>();

        if (periodo != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseFaixaNotificacaoIndividual.PROP_DATA_VIGENCIA), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, periodo.getDataInicial()));
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseFaixaNotificacaoIndividual.PROP_DATA_VIGENCIA), BuilderQueryCustom.QueryParameter.MENOR_IGUAL, periodo.getDataFinal()));

        }


        if (numeracaoInicial != null && numeracaoFinal == null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseFaixaNotificacaoIndividual.PROP_NUMERACAO_INICIAL), BuilderQueryCustom.QueryParameter.IGUAL, numeracaoInicial));

        } else if (numeracaoInicial == null && numeracaoFinal != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseFaixaNotificacaoIndividual.PROP_NUMERACAO_FINAL), BuilderQueryCustom.QueryParameter.IGUAL, numeracaoFinal));
        }
        if (numeracaoInicial != null && numeracaoFinal != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseFaixaNotificacaoIndividual.PROP_NUMERACAO_INICIAL), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, numeracaoInicial));
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseFaixaNotificacaoIndividual.PROP_NUMERACAO_FINAL), BuilderQueryCustom.QueryParameter.MENOR_IGUAL, numeracaoFinal));
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroFaixaNotificacaoIndividualPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaFaixaNotificacaoIndividual");
    }
}
