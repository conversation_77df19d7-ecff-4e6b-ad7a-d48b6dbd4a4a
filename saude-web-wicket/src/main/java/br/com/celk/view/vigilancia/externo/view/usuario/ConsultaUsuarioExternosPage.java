package br.com.celk.view.vigilancia.externo.view.usuario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.controle.usuario.customize.CustomizeConsultaUsuario;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Programa - 695
 */
@Private
public class ConsultaUsuarioExternosPage extends ConsultaPage<Usuario, List<QueryParameter>> {

    private InputField txtNome;

    public ConsultaUsuarioExternosPage() {
    }

    public ConsultaUsuarioExternosPage(IModel<?> model) {
        super(model);
    }

    public ConsultaUsuarioExternosPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.add(txtNome = new InputField("txtNome", new Model()));
        getLinkNovo().setVisible(false);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(Usuario.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(Usuario.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("login"), VOUtils.montarPath(Usuario.PROP_LOGIN)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome"), VOUtils.montarPath(Usuario.PROP_NOME)));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<Usuario>() {
            @Override
            public void customizeColumn(final Usuario rowObject) {
                addAction(ActionType.DESATIVAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        rowObject.setStatus(Usuario.STATUS_INATIVO);
                        BOFactoryWicket.save(rowObject);
                        getPageableTable().update(target);
                    }
                }).setEnabled(Usuario.STATUS_ATIVO.equals(rowObject.getStatus()));
//                addAction(ActionType.EDITAR, new IAction() {
//                    @Override
//                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                        setResponsePage(new EditarUsuarioPage(rowObject, EditarUsuarioPage.Acao.EDITAR, getPageParameters()));
//                    }
//                });
//                addAction(ActionType.REMOVER, new IAction() {
//                    @Override
//                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
//                        getPageableTable().populate(target);
//                    }
//                });
//                addAction(ActionType.CLONAR, new IAction() {
//                    @Override
//                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                        setResponsePage(new EditarUsuarioPage(rowObject, EditarUsuarioPage.Acao.CLONAR, getPageParameters()));
//                    }
//                });
                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroUsuarioExternoPage(rowObject, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaUsuario()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Usuario.PROP_NOME, true);
            }
        };
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_NOME), QueryParameter.ILIKE, txtNome.getComponentValue()));
        if (!ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().isNivelAdminOrMaster()) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_NIVEL), QueryParameter.DIFERENTE, Usuario.NIVEL_ADMINISTRADOR));
        }
        if (!ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().isNivelMaster()) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_NIVEL), QueryParameter.DIFERENTE, Usuario.NIVEL_MASTER));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_TIPO_USUARIO), Usuario.TipoUsuario.USUARIO_VIGILANCIA.value()));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroUsuarioExternoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaUsuarios");
    }
}
