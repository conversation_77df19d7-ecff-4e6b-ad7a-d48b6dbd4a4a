package br.com.celk.view.materiais.dispensacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.dispensacao.columnpanel.ConsultaDispensacaoColumnPanel;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.ListaDispensacaoMedicamentoParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.apache.wicket.Component;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 * Programa - 151
 */
@Private
public class ConsultaDispensacaoPage extends ConsultaPage<DispensacaoMedicamento, ListaDispensacaoMedicamentoParam> {

    private Empresa empresaOrigem;
    private List<Empresa> estabelecimento;
    private String nomePaciente;
    private Produto produto;
    private DatePeriod periodo;
    private Long numeroDispensacao;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEstabelecimentoExecutante;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaEstabelecimentoExecutante = new AutoCompleteConsultaEmpresa("estabelecimento"));
        autoCompleteConsultaEstabelecimentoExecutante.setValidaUsuarioEmpresa(true);
        autoCompleteConsultaEstabelecimentoExecutante.setMultiplaSelecao(true);
        
        form.add(new AutoCompleteConsultaEmpresa("empresaOrigem"));
        form.add(new InputField("nomePaciente"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new PnlDatePeriod("periodo"));
        form.add(new InputField("numeroDispensacao"));
        getLinkNovo().setVisible(false);
        setExibeExpandir(true);

        Usuario usuario = SessaoAplicacaoImp.getInstance().getUsuario();
        if (!usuario.isNivelAdminOrMaster() && estabelecimento == null){
            try {
                if (usuario.getEmpresasUsuario() == null) {
                    usuario.setEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                }
                List<Long> list = ApplicationSession.get().getSessaoAplicacao().getUsuario().getEmpresasUsuario();
                
                if(CollectionUtils.isNotNullEmpty(list)){
                    estabelecimento = LoadManager.getInstance(Empresa.class)
                            .addProperties(new HQLProperties(Empresa.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, list))
                            .start().getList();
                }
            } catch (SGKException ex) {
                Logger.getLogger(ConsultaDispensacaoPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(DispensacaoMedicamento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estabelecimento"), VOUtils.montarPath(DispensacaoMedicamento.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dispensacao"), VOUtils.montarPath(DispensacaoMedicamento.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_NOME), VOUtils.montarPath(DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoReceita"), VOUtils.montarPath(DispensacaoMedicamento.PROP_TIPO_RECEITA, TipoReceita.PROP_DESCRICAO)));
        columns.add(new DateColumn(bundle("dataDispensacao"), VOUtils.montarPath(DispensacaoMedicamento.PROP_DATA_DISPENSACAO)).setPattern("dd/MM/yyyy - HH:mm"));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidadeOrigem"), VOUtils.montarPath(DispensacaoMedicamento.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO)));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<DispensacaoMedicamento>() {

            @Override
            public Component getComponent(String componentId, DispensacaoMedicamento rowObject) {
                return new ConsultaDispensacaoColumnPanel(componentId, rowObject);
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<DispensacaoMedicamento, ListaDispensacaoMedicamentoParam>() {

            @Override
            public DataPagingResult<DispensacaoMedicamento> executeQueryPager(DataPaging<ListaDispensacaoMedicamentoParam> dataPaging) throws DAOException, ValidacaoException {
                try {
                    dataPaging.getParam().setPropSort(((SingleSortState<String>) getPagerProvider().getSortState()).getSort().getProperty());
                    dataPaging.getParam().setAscending(((SingleSortState) getPagerProvider().getSortState()).getSort().isAscending());
                    return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).getListaDispensacaoMedicamento(dataPaging);
                } catch (SGKException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return null;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(DispensacaoMedicamento.PROP_CODIGO, false);
            }
        };
    }

    @Override
    public ListaDispensacaoMedicamentoParam getParameters() {
        ListaDispensacaoMedicamentoParam param = new ListaDispensacaoMedicamentoParam();

        param.setTruncDate(false);
        param.setEmpresa(empresaOrigem);
        param.setEstabelecimentoList(estabelecimento);
        param.setNomeUsuario(nomePaciente);
        param.setNumeroDispensacao(numeroDispensacao);
        param.setProduto(produto);
        param.setDatePeriod(periodo);

        return param;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaDispensacaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDispensacaoMedicamentos");
    }

}
