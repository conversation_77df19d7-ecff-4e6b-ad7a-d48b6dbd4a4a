package br.com.celk.view.materiais.pedidolimite;


import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.pedidolimite.customize.CustomizeLimitePedidoBranet;
import br.com.celk.view.materiais.pedidolimite.dialog.DlgEditarQuantidadePedidoLimite;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;


import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoLimiteBranetFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.UsuarioGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.AbstractLink;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 1029
 */
public class ConsultaLimitePedidoBranetPage extends BasePage {


    private static final String CONTROLADOR_BRANET = "Controlador Pedido Limite";
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private PageableTable pageableTable;
    private AbstractLink link;
    private DlgEditarQuantidadePedidoLimite dlgEditarQuantidadePedidoLimite;
    private WebMarkupContainer panelMsg;

    private Empresa empresa;
    private Produto produto;
    private GrupoProduto grupoProduto;
    private SubGrupo subGrupo;
    private boolean permiteCadastrar;


    public ConsultaLimitePedidoBranetPage(PageParameters parameters){
        super(parameters);
        init();
    }
    public ConsultaLimitePedidoBranetPage(IModel<?> model){
        super(model);
        init();
    }
    public ConsultaLimitePedidoBranetPage(){
        init();
    }

    public void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        panelMsg = new WebMarkupContainer("panelMsg");
        panelMsg.setOutputMarkupId(true);
        panelMsg.setOutputMarkupPlaceholderTag(true);
        panelMsg.setVisible(false);

        form.add(panelMsg);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        autoCompleteConsultaEmpresa.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE,
                Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO,
                Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO,
                Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL,
                Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO,
                Empresa.TIPO_ESTABELECIMENTO_FARMACIA));
        autoCompleteConsultaEmpresa.setComponentValue(SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa());

        form.add(dropDownSubGrupo = DropDownUtil.getDropDownSubGrupo(dropDownSubGrupo));
        form.add(dropDownGrupoProduto = DropDownUtil.getDropDownGrupo(dropDownGrupoProduto, dropDownSubGrupo, autoCompleteConsultaProduto));

        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(false);

        pageableTable = new PageableTable("table", getColumns(), getPagerProviderInstance());

        form.add(new ProcurarButton<List<BuilderQueryCustom.QueryParameter>>("btnProcurar", pageableTable) {
            @Override
            public List<BuilderQueryCustom.QueryParameter> getParam() {
                if (autoCompleteConsultaEmpresa.getComponentValue() == null) {
                    try {
                        throw new ValidacaoException(BundleManager.getString("selecioneUnidade"));
                    } catch (ValidacaoException e) {
                        e.printStackTrace();
                    }
                } else {
                    return ConsultaLimitePedidoBranetPage.this.getParameters();
                }
                return null;
            }
        });


        form.add(new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                limparItem(target);
            }
        });
        //pageableTable.setScrollX("1500px");
        form.add(pageableTable);

        dropDownSubGrupo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                autoCompleteConsultaProduto.limpar(target);
                autoCompleteConsultaProduto.clearInput();
                autoCompleteConsultaProduto.setSubGrupoFixo(null);
                SubGrupo subGrupo = dropDownSubGrupo.getComponentValue();
                autoCompleteConsultaProduto.setSubGrupoFixo(subGrupo);
                autoCompleteConsultaProduto.setEnabled(true);
            }
        });

        link = new BookmarkablePageLink("linkNovo",CadastroLimitePedidoBranetPage.class);
        link.setEnabled(liberarCadastroDeLimite());

        add(form);

        add(link);
        addModal(dlgEditarQuantidadePedidoLimite = new DlgEditarQuantidadePedidoLimite(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, PedidoLimiteBranet limite) throws ValidacaoException, DAOException {
                try {
                    BOFactoryWicket.getBO(PedidoLimiteBranetFacade.class).update(limite);
                } catch (Exception e) {
                    throw new ValidacaoException(bundle("valor_superior_limite_produto", limite.getProduto().getDescricao()));
                }
                limparPnlMsg(target);
                Label lblMsg = new Label("msgAtualizar","Limite atualizado com sucesso!");
                panelMsg.add(lblMsg);
                panelMsg.setVisible(true);
                pageableTable.update(target);
            }
        });

    }
    private void limparPnlMsg(AjaxRequestTarget target) {
        panelMsg.removeAll();
        panelMsg.setVisible(false);
        target.add(panelMsg);
    }

    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();

        PedidoLimiteBranet proxy = on(PedidoLimiteBranet.class);

        ColumnFactory columnFactory = new ColumnFactory(PedidoLimiteBranet.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("produto"),VOUtils.montarPath(PedidoLimiteBranet.PROP_PRODUTO,Produto.PROP_DESCRICAO),VOUtils.montarPath(PedidoLimiteBranet.PROP_PRODUTO,Produto.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn("Saldo Disponível",VOUtils.montarPath(PedidoLimiteBranet.PROP_SALDO_PARA_SOLICITACAO)));
        columns.add(columnFactory.createColumn("Qtd. Máxima Mensal",VOUtils.montarPath(PedidoLimiteBranet.PROP_QTD_MAXIMA_MENSAL)));
        columns.add(columnFactory.createColumn("Qtd. Máxima Anual",VOUtils.montarPath(PedidoLimiteBranet.PROP_QTD_MAXIMA_ANUAL)));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<PedidoLimiteBranet>() {
            @Override
            public void customizeColumn(final PedidoLimiteBranet rowObject) {
                addAction(ActionType.EDITAR,rowObject, new IModelAction<PedidoLimiteBranet>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoLimiteBranet limite) throws ValidacaoException, DAOException {
                            dlgEditarQuantidadePedidoLimite.setObject(target,limite);
                            dlgEditarQuantidadePedidoLimite.show(target);
                            limparPnlMsg(target);
                            pageableTable.update(target);
                    }
                }).setVisible(liberarCadastroDeLimite());
            }
        };
    }

    public IPagerProvider getPagerProviderInstance() {

        return new CustomizeConsultaPagerProvider(new CustomizeLimitePedidoBranet()){
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(PedidoLimiteBranet.PROP_PRODUTO, true); }
        };
    }


    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoLimiteBranet.PROP_EMPRESA, Empresa.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, empresa.getCodigo()));
        if(produto != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoLimiteBranet.PROP_PRODUTO, Produto.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, produto.getCodigo()));
        }
        if(grupoProduto != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoLimiteBranet.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, grupoProduto));
        }
        if(subGrupo != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoLimiteBranet.PROP_PRODUTO, Produto.PROP_SUB_GRUPO), BuilderQueryCustom.QueryParameter.IGUAL, subGrupo));
        }
        return parameters;
    }

    private void limparItem(AjaxRequestTarget target) {
        limparGrupoSubgrupo(target);
    }

    private void limparGrupoSubgrupo(AjaxRequestTarget target) {
        dropDownGrupoProduto.limpar(target);
        dropDownSubGrupo.limpar(target);
        dropDownSubGrupo.removeAllChoices();

        target.add(dropDownGrupoProduto);
        target.add(dropDownSubGrupo);
    }



    public boolean verificarPermissaoDeCadastro(){
        return isActionPermitted(Permissions.CADASTRAR);
    }

    public boolean liberarCadastroDeLimite(){
        String limiteBranetHabilitado = null;
        try {
            limiteBranetHabilitado = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("limitePedidosBranet");
        } catch (DAOException e) {
            e.printStackTrace();
        }
        if(verificarPermissaoDeCadastro() && limiteBranetHabilitado.equals("S") && verificarUsuarioControlador()){
            return true;
        }else{
            return false;
        }
    }

    protected boolean verificarUsuarioControlador(){
        boolean controlador = false;
        Usuario usuario = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
        if (usuario.isNivelAdminOrMaster()) {
            controlador = true;
        }else {
            List<UsuarioGrupo> usuarioGrupos = LoadManager.getInstance(UsuarioGrupo.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(path(on(UsuarioGrupo.class).getUsuario().getCodigo()), ApplicationSession.get().getSessaoAplicacao().getUsuario().getCodigo()))
                    .start().getList();
            for (UsuarioGrupo usuarioGrupo : usuarioGrupos) {
                if (usuarioGrupo.getGrupo().getNome().equals(CONTROLADOR_BRANET))
                    controlador = true;
            }
        }
            return controlador;
        }


    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaPedidoLimite");
    }
}
