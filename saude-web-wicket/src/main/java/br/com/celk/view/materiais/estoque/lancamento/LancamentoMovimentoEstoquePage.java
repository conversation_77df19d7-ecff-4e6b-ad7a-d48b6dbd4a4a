package br.com.celk.view.materiais.estoque.lancamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.ajaxcalllistener.ConditionListenerLoading;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.doublefield.RequiredDoubleField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.lote.entrada.PnlEntradaLote;
import br.com.celk.component.lote.saida.PnlSaidaLote;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.fabricante.autocomplete.AutoCompleteConsultaFabricante;
import br.com.celk.view.basico.fabricante.dialog.DlgCadastroFabricante;
import br.com.celk.view.basico.localizacaoestrutura.autocomplete.AutoCompleteConsultaLocalizacaoEstrutura;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.tipodocumento.autocomplete.AutoCompleteConsultaTipoDocumento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.MovimentoEstoquePageDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.ImpressaoReciboLancamentoEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import ch.lambdaj.Lambda;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 * Programa - 30
 */
@Private
public class LancamentoMovimentoEstoquePage extends BasePage implements PermissionContainer {

    private Form formControles;
    private CompoundPropertyModel<MovimentoEstoquePageDTO> modelMovimento;
    private DlgConfirmacao dialogConfirmacaoVoltar;
    private Table tableMovimentos;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaDestino;
    private AutoCompleteConsultaDeposito autoCompleteConsultaDeposito;
    private AutoCompleteConsultaCentroCusto autoCompleteConsultaCentroCusto;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaTipoDocumento autoCompleteConsultaTipoDocumento;
    private AutoCompleteConsultaLocalizacaoEstrutura autoCompleteConsultaLocalizacaoEstrutura;
    private AutoCompleteConsultaFabricante autoCompleteConsultaFabricante;
    private PnlSaidaLote pnlSaidaLote;
    private PnlEntradaLote pnlEntradaLote;
    private DoubleField txtQuantidade;
    private WebMarkupContainer containerMovimento;
    private WebMarkupContainer containerInterno;
    private WebMarkupContainer containerLocalizacao;
    private WebMarkupContainer containerFabricante;
    private WebMarkupContainer containerCodigoBarras;
    private WebMarkupContainer containerValorProduto;
    private DlgConfirmacao dlgConfirmarAdicionar;
    private InputArea txtObsevacao;
    private InputField txtUnidade;
    private InputField txtAutoIntimacao;
    private InputField txtItemDocumento;
    private InputField txtNumeroDocumento;
    private LongField txtCodigoBarras;
    private String sugerirPrimeiroLoteVencerSaida;
    private DlgImpressaoObject<ImpressaoReciboLancamentoEstoqueDTOParam> dlgImpressaoRecibo;
    private DlgCadastroFabricante dlgCadastroFabricante;
    private AbstractAjaxLink btnCadFabricante;

    private TipoDocumento tipoDocumento;

    private List<MovimentoEstoquePageDTO> movimentos = new ArrayList<MovimentoEstoquePageDTO>();
    private Long nrCodigoBarrasProduto;
    private CodigoBarrasProduto codigoBarrasProduto;
    private String validarSituacaoCodigoBarrasProduto;
    private DoubleField txtValorProduto;
    private String utilizaLocalizacaoEstoque;


    public LancamentoMovimentoEstoquePage() {
        init();
    }

    private void init() {
        Form formPrincipal = new Form("form");

        containerMovimento = new WebMarkupContainer("containerMovimento", modelMovimento = new CompoundPropertyModel<MovimentoEstoquePageDTO>(new MovimentoEstoquePageDTO()));

        String utilizarLeitoraCodigoBarrasProduto = null;
        try {
            utilizarLeitoraCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizarLeitoraCodigoBarrasProduto");
            validarSituacaoCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("validarSituacaoCodigoBarrasProduto");
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        containerMovimento.setOutputMarkupId(true);

        containerMovimento.add(autoCompleteConsultaTipoDocumento = new AutoCompleteConsultaTipoDocumento(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_TIPO_DOCUMENTO), new PropertyModel<TipoDocumento>(this, "tipoDocumento"), true).setApenasInterno(true));
        autoCompleteConsultaTipoDocumento.setLabel(new Model<String>(bundle("tipoDocumento")));

        containerInterno = new WebMarkupContainer("containerInterno");

        containerInterno.setOutputMarkupId(true);
        containerInterno.setEnabled(false);

        containerInterno.add(containerCodigoBarras = new WebMarkupContainer("containerCodigoBarras"));

        containerCodigoBarras.add(txtCodigoBarras = new LongField("codigoBarrasProduto", new PropertyModel(this, "nrCodigoBarrasProduto")));
        txtCodigoBarras.add(new AjaxFormComponentUpdatingBehavior("onChange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                try {
                    adicionarViaCodigoBarras(art);
                    art.appendJavaScript(JScript.removeAutoCompleteDrop());
                } catch (ValidacaoException ex) {
                    limparItem(art);
                    resetarFocoCodigoBarras(art);
                    Loggable.log.error(ex.getMessage(), ex);
                    warn(art, ex.getMessage());
                } catch (DAOException ex) {
                    limparItem(art);
                    resetarFocoCodigoBarras(art);
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                String condition = "!= 0";
                attributes.getAjaxCallListeners().add(new ConditionListenerLoading(txtCodigoBarras.getMarkupId(), condition));
            }
        });
        containerCodigoBarras.setVisible(RepositoryComponentDefault.SIM.equals(utilizarLeitoraCodigoBarrasProduto));

        containerInterno.add(autoCompleteConsultaEmpresaDestino = new AutoCompleteConsultaEmpresa(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_EMPRESA_DESTINO)));
        autoCompleteConsultaEmpresaDestino.setLabel(new Model<String>(bundle("unidadeDestino")));

        containerInterno.add(autoCompleteConsultaDeposito = new AutoCompleteConsultaDeposito(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_DEPOSITO)));
        autoCompleteConsultaDeposito.setLabel(new Model<String>(bundle("estoque")));

        containerInterno.add(autoCompleteConsultaCentroCusto = new AutoCompleteConsultaCentroCusto(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_CENTRO_CUSTO)));
        autoCompleteConsultaCentroCusto.setLabel(new Model<String>(bundle("centroCusto")));

        containerInterno.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_PRODUTO), true));
        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                carregarUnidade(target, object);
            }
        });
        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                txtUnidade.setComponentValue(null);
                target.add(txtUnidade);
            }
        });
        autoCompleteConsultaProduto.setEmpresas(Arrays.asList(ApplicationSession.get().getSessaoAplicacao().getEmpresa()));
        autoCompleteConsultaProduto.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO);
        autoCompleteConsultaProduto.setIncluirInativos(false);
        autoCompleteConsultaProduto.setLabel(new Model<String>(bundle("produto")));

        containerInterno.add(txtUnidade = new InputField(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        txtUnidade.setEnabled(false);
        containerValorProduto = new WebMarkupContainer("containerValorProduto");
        containerValorProduto.setOutputMarkupPlaceholderTag(true);
        containerValorProduto.getAjaxRegionMarkupId();
        containerValorProduto.add(txtValorProduto = new DoubleField(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_VALOR_PRODUTO)).setMDec(4));
        txtValorProduto.setVMax(99999999D);
        containerInterno.add(containerValorProduto);
        containerValorProduto.setVisible(false);

        containerInterno.add(txtQuantidade = new RequiredDoubleField(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_QUANTIDADE)).setMDec(0).setNegativeValue(false));
        txtQuantidade.setLabel(new Model<String>(bundle("quantidade")));

        containerInterno.add(txtNumeroDocumento = new InputField(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_NUMERO_DOCUMENTO)));
        txtNumeroDocumento.setLabel(new Model<String>(bundle("numeroDocumento")));

        containerInterno.add(txtAutoIntimacao = new InputField(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_AUTO_INTIMACAO)));
        txtAutoIntimacao.setLabel(new Model<String>(bundle("autoIntimacao")));

        containerInterno.add(txtItemDocumento = new InputField(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_ITEM_DOCUMENTO)));

        containerInterno.add(txtObsevacao = new InputArea(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_OBSERVACAO)));
        txtObsevacao.setLabel(new Model<String>(bundle("observacao")));

        containerInterno.add(pnlEntradaLote = new PnlEntradaLote(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_GRUPO_ESTOQUE)));
        pnlEntradaLote.setTxtQuantidade(txtQuantidade);
        pnlEntradaLote.setAutoCompleteConsultaProduto(autoCompleteConsultaProduto);
        pnlEntradaLote.registerEvents();

        containerLocalizacao = new WebMarkupContainer("containerLocalizacao");

        containerLocalizacao.setOutputMarkupId(true);
        containerInterno.add(containerLocalizacao);

        containerLocalizacao.add(autoCompleteConsultaLocalizacaoEstrutura = new AutoCompleteConsultaLocalizacaoEstrutura(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_LOCALIZACAO_ESTRUTURA), true));
        autoCompleteConsultaLocalizacaoEstrutura.setExibirApenasVisivelSim(true);
        autoCompleteConsultaLocalizacaoEstrutura.setEnabled(false);
        autoCompleteConsultaLocalizacaoEstrutura.setLabel(new Model<String>(bundle("localizacaoEstrutura")));

        pnlEntradaLote.setAutoCompleteConsultaLocalizacaoEstrutura(autoCompleteConsultaLocalizacaoEstrutura);

        containerFabricante = new WebMarkupContainer("containerFabricante");
        containerFabricante.setVisible(false);
        containerFabricante.setOutputMarkupId(true);
        containerInterno.add(containerFabricante);

        containerFabricante.add(autoCompleteConsultaFabricante = new AutoCompleteConsultaFabricante(MovimentoEstoque.PROP_FABRICANTE));
        autoCompleteConsultaFabricante.setEnabled(false);

        containerFabricante.add(btnCadFabricante = new AbstractAjaxLink("btnCadFabricante") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgCadastroFabricante(target);
            }
        });
        btnCadFabricante.setEnabled(false);

        pnlEntradaLote.setAutoCompleteConsultaFabricante(autoCompleteConsultaFabricante);
        pnlEntradaLote.setBtnCadFabricante(btnCadFabricante);

        pnlSaidaLote = new PnlSaidaLote(VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_GRUPO_ESTOQUE));
        pnlSaidaLote.setAutoCompleteConsultaProduto(autoCompleteConsultaProduto);
        pnlSaidaLote.setAutoCompleteConsultaDepositoOrigem(autoCompleteConsultaDeposito);
        pnlSaidaLote.setTxtQuantidade(txtQuantidade);
        pnlSaidaLote.setPermiteSugerirLoteAutomaticamente(true);
        pnlSaidaLote.setAutoCompleteConsultaLocalizacaoEstrutura(autoCompleteConsultaLocalizacaoEstrutura);
        pnlSaidaLote.setAutoCompleteConsultaFabricante(autoCompleteConsultaFabricante);
        pnlSaidaLote.setValidarLotesVencidos(false);

        containerMovimento.add(containerInterno);

        containerMovimento.add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarAdicionar(target);
            }

        });

        formPrincipal.add(containerMovimento);

        formPrincipal.add(tableMovimentos = new Table("tableMovimentos", getColumns(), getCollectionProvider()));

        tableMovimentos.populate();

        add(formPrincipal);

        formControles = new Form("formControles");

        formControles.add(new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        formControles.add(new AbstractAjaxButton("btnLimpar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
            }

        });

        add(formControles);

        autoCompleteConsultaTipoDocumento.add(new ConsultaListener<TipoDocumento>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoDocumento object) {
                eventoConsultaTipoDocumento(target, object);
            }

        });

        addModal(dlgConfirmarAdicionar = new DlgConfirmacao(newModalId(), BundleManager.getString("jaExisteProdutoLoteAdicionadoDesejaAtualizar")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                adicionarItem(target, modelMovimento.getObject().getMovimentoEstoque());
            }
        });

        if (RepositoryComponentDefault.NAO.equals(getUtilizaLocalizacaoEstoque())) {
            containerLocalizacao.setVisible(false);
        }
    }


    private void adicionarViaCodigoBarras(AjaxRequestTarget target) throws ValidacaoException, DAOException {

        if (nrCodigoBarrasProduto != null) {
            if (modelMovimento.getObject().getMovimentoEstoque().getDeposito() == null) {
                throw new ValidacaoException(bundle("informeDeposito"));
            }

            if (CollectionUtils.isNotNullEmpty(movimentos)) {
                for (MovimentoEstoquePageDTO movimento : movimentos) {
                    List<CodigoBarrasProduto> listCBP = movimento.getCodigoBarrasProdutoList();
                    if (listCBP != null && CollectionUtils.isNotNullEmpty(listCBP)) {
                        for (CodigoBarrasProduto cbp : listCBP) {
                            if (nrCodigoBarrasProduto.equals(cbp.getCodigo())) {
                                throw new ValidacaoException(bundle("codigoBarrasJaInseridoNesteLancamento"));
                            }
                        }
                    }
                }
            }

            codigoBarrasProduto = LoadManager.getInstance(CodigoBarrasProduto.class)
                    .addProperties(new HQLProperties(CodigoBarrasProduto.class).getProperties())
                    .addProperties(new HQLProperties(Produto.class, CodigoBarrasProduto.PROP_PRODUTO).getProperties())
                    .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_REFERENCIA, nrCodigoBarrasProduto.toString()))
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, CodigoBarrasProduto.Status.TRANSFERIDO.value()))
                    .setMaxResults(1).start().getVO();

            if (codigoBarrasProduto == null) {
                throw new ValidacaoException(bundle("codigoBarrasInvalido"));
            } else {
                if (RepositoryComponentDefault.SIM.equals(validarSituacaoCodigoBarrasProduto)) {
                    if (codigoBarrasProduto.isEtiquetaForaEstoque()) {
                        throw new ValidacaoException(bundle("codigoBarrasJaDispensado"));
                    }
                }
            }

            autoCompleteConsultaProduto.limpar(target);

            EstoqueEmpresaPK estoqueEmpresaPK = new EstoqueEmpresaPK();
            estoqueEmpresaPK.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
            estoqueEmpresaPK.setProduto(codigoBarrasProduto.getProduto());

            GrupoEstoquePK grupoEstoquePK = new GrupoEstoquePK();
            grupoEstoquePK.setGrupo(codigoBarrasProduto.getGrupo());
            grupoEstoquePK.setCodigoDeposito(((Deposito) autoCompleteConsultaDeposito.getComponentValue()).getCodigo());
            grupoEstoquePK.setEstoqueEmpresa(new EstoqueEmpresa(estoqueEmpresaPK));
            grupoEstoquePK.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());

            GrupoEstoque grupoEstoque = LoadManager.getInstance(GrupoEstoque.class)
                    .addProperties(new HQLProperties(GrupoEstoque.class).getProperties())
                    .addProperties(new HQLProperties(EstoqueEmpresa.class, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA)).getProperties())
                    .setId(grupoEstoquePK)
                    .start().getVO();

            if (grupoEstoque == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_encontrado_estoque_etiqueta_produto"));
            }

            MovimentoGrupoEstoqueItemDTO dto = new MovimentoGrupoEstoqueItemDTO();
            dto.setDataValidade(grupoEstoque.getDataValidade());
            dto.setDeposito(grupoEstoque.getRoDeposito());
            dto.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
            dto.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());
            dto.setFabricante(grupoEstoque.getFabricante());
            dto.setEstoqueEncomendado(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueEncomendado());
            dto.setEstoqueFisico(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueFisico());
            dto.setEstoqueReservado(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueReservado());
            dto.setGrupoEstoque(grupoEstoque.getId().getGrupo());
            dto.setProduto(codigoBarrasProduto.getProduto());

            Double quantidade = codigoBarrasProduto.getQuantidadeProduto();

            pnlSaidaLote.limpar(target);
            autoCompleteConsultaLocalizacaoEstrutura.limpar(target);

            pnlSaidaLote.setLoteSelecionado(dto);
            pnlSaidaLote.setModelObject(dto.getGrupoEstoque());

            if (pnlSaidaLote.getLoteSelecionado() != null) {
                if (quantidade > grupoEstoque.getEstoqueFisico()) {
                    throw new ValidacaoException(BundleManager.getString("quantidadeMaiorDisponivelLoteSelecionado"));
                }
            }

            txtQuantidade.setEnabled(false);
            autoCompleteConsultaProduto.setEnabled(false);
            autoCompleteConsultaDeposito.setEnabled(false);

            modelMovimento.getObject().getMovimentoEstoque().setLocalizacaoEstrutura(dto.getLocalizacaoEstrutura());
            modelMovimento.getObject().getMovimentoEstoque().setFabricante(dto.getFabricante());
            modelMovimento.getObject().getMovimentoEstoque().setDeposito(dto.getDeposito());
            modelMovimento.getObject().getMovimentoEstoque().setDataValidadeGrupoEstoque(dto.getDataValidade());
            modelMovimento.getObject().getMovimentoEstoque().setGrupoEstoque(dto.getGrupoEstoque());
            modelMovimento.getObject().getMovimentoEstoque().setQuantidade(quantidade);
            modelMovimento.getObject().getMovimentoEstoque().setProduto(dto.getProduto());

            target.add(txtQuantidade);
            target.add(containerInterno);
            target.add(containerMovimento);
            target.add(autoCompleteConsultaProduto);
            target.add(autoCompleteConsultaDeposito);
            target.add(autoCompleteConsultaLocalizacaoEstrutura);

            target.focusComponent(txtCodigoBarras);

            if (CollectionUtils.isNotNullEmpty(movimentos) && this.codigoBarrasProduto != null) {
                for (MovimentoEstoquePageDTO dtoMovimento : movimentos) {
                    if (isProdutoListado(dtoMovimento)) {
                        validarAdicionar(target);
                        break;
                    }
                }
            }
        } else {
            limparItem(target);
        }
    }

    private void resetarFocoCodigoBarras(AjaxRequestTarget target) {
        txtCodigoBarras.limpar(target);
        target.focusComponent(txtCodigoBarras);
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private void initDlgCadastroFabricante(AjaxRequestTarget target) {
        if (dlgCadastroFabricante == null) {
            addModal(target, dlgCadastroFabricante = new DlgCadastroFabricante(newModalId()) {
                @Override
                public void onSalvar(AjaxRequestTarget target, Fabricante fabricante) throws ValidacaoException, DAOException {
                    autoCompleteConsultaFabricante.limpar(target);
                    autoCompleteConsultaFabricante.setComponentValue(fabricante);
                    target.add(autoCompleteConsultaFabricante);
                }
            });
        }
        dlgCadastroFabricante.showDlg(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(MovimentoEstoquePageDTO.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("produto"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("lote"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_GRUPO_ESTOQUE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_QUANTIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("tipoDocumento"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_TIPO_DOCUMENTO, TipoDocumento.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("numeroDocumento"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_NUMERO_DOCUMENTO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("item"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_ITEM_DOCUMENTO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("unidadeDestino"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_EMPRESA_DESTINO, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("justificativa"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_JUSTIFICATIVA_LOTE_POSTERIOR)));
        columns.add(columnFactory.createColumn(BundleManager.getString("deposito"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_DEPOSITO)));

        if (RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())) {
            columns.add(columnFactory.createColumn(BundleManager.getString("localizacaoEstrutura"), VOUtils.montarPath(MovimentoEstoquePageDTO.PROP_MOVIMENTO_ESTOQUE, MovimentoEstoque.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_MASCARA)));
        }

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<MovimentoEstoquePageDTO>() {

            @Override
            public Component getComponent(String componentId, final MovimentoEstoquePageDTO rowObject) {
                return new RemoverActionColumnPanel(componentId) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        remover(target, rowObject.getMovimentoEstoque());
                    }
                };
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return movimentos;
            }
        };
    }

    private void validarAdicionar(AjaxRequestTarget target) throws ValidacaoException {
        MovimentoEstoque movimentoEstoque = modelMovimento.getObject().getMovimentoEstoque();

        movimentoEstoque.setTipoDocumento(tipoDocumento);

        if (movimentoEstoque.getGrupoEstoque() == null && pnlSaidaLote != null && pnlSaidaLote.getLoteSelecionado() != null) {
            movimentoEstoque.setGrupoEstoque(pnlSaidaLote.getLoteSelecionado().getGrupoEstoque());
        }

        if (pnlSaidaLote != null && pnlSaidaLote.getJustificativaLotePosterior() != null) {
            movimentoEstoque.setJustificativaLotePosterior(pnlSaidaLote.getJustificativaLotePosterior());
        }

        if (movimentoEstoque.getCentroCusto() == null
                && RepositoryComponentDefault.SIM.equals(tipoDocumento.getExigeCentroCusto())) {
            throw new ValidacaoException(BundleManager.getString("centroCustoObrigatorioTipoDocumento"));
        }

        if (movimentoEstoque.getEmpresaDestino() == null
                && RepositoryComponentDefault.SIM.equals(tipoDocumento.getExigeUnidadeDestino())) {
            throw new ValidacaoException(BundleManager.getString("unidadeDestinoObrigatorioTipoDocumento"));
        }

        if (movimentoEstoque.getAutoIntimacao() == null
                && RepositoryComponentDefault.SIM_LONG.equals(tipoDocumento.getFlagExigeAutoIntimacao())) {
            throw new ValidacaoException(BundleManager.getString("msgExigeAutoIntimacao"));
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(tipoDocumento.getFlagImprimeRecibo()) && CollectionUtils.isNotNullEmpty(movimentos)) {
            for (MovimentoEstoquePageDTO m : movimentos) {
                if ((m.getMovimentoEstoque().getEmpresaDestino() != null && movimentoEstoque.getEmpresaDestino() == null)
                        || (m.getMovimentoEstoque().getEmpresaDestino() == null && movimentoEstoque.getEmpresaDestino() != null)
                        || (m.getMovimentoEstoque().getEmpresaDestino() != null && movimentoEstoque.getEmpresaDestino() != null && !m.getMovimentoEstoque().getEmpresaDestino().getCodigo().equals(movimentoEstoque.getEmpresaDestino().getCodigo()))) {
                    throw new ValidacaoException(BundleManager.getString("msgUnidadeDestinoItensDeveSerMesma"));
                }
            }
        }

        if (tipoDocumento != null && !TipoDocumento.IS_ENTRADA.equals(tipoDocumento.getFlagTipoMovimento())) {
            MovimentoGrupoEstoqueItemDTO loteSelecionado = pnlSaidaLote.getLoteSelecionado();
            if (loteSelecionado != null) {
                if (movimentoEstoque.getQuantidade() > loteSelecionado.getEstoqueDisponivel()) {
                    throw new ValidacaoException(BundleManager.getString("quantidadeMaiorDisponivelLoteSelecionado"));
                }
            }
        }

        if (tipoDocumento != null && TipoDocumento.IS_ENTRADA.equals(tipoDocumento.getFlagTipoMovimento())) {
            if (autoCompleteConsultaFabricante.isEnabled() && autoCompleteConsultaFabricante.getComponentValue() == null) {
                throw new ValidacaoException(BundleManager.getString("informeFabricante"));
            }
        }

        if (RepositoryComponentDefault.SIM.equals(movimentoEstoque.getProduto().getSubGrupo().getFlagControlaGrupoEstoque())
                && TipoDocumento.IS_ENTRADA.equals(tipoDocumento.getFlagTipoMovimento())) {
            if (pnlEntradaLote.getDataValidade() == null) {
                throw new ValidacaoException(BundleManager.getString("informeDataValidade"));
            }

            movimentoEstoque.setDataValidadeGrupoEstoque(pnlEntradaLote.getDataValidade());
        }

        if (Coalesce.asDouble(movimentoEstoque.getQuantidade()) == 0D) {
            throw new ValidacaoException(BundleManager.getString("quantidadeDeveSerMaiorQueZero"));
        }

        MovimentoEstoquePageDTO dto = procurarMovimento(movimentoEstoque);
        if (dto != null && dto.getMovimentoEstoque() != null && this.codigoBarrasProduto == null) {
            dlgConfirmarAdicionar.show(target);
        } else {
            adicionarItem(target, null);
        }

    }

    private void adicionarItem(AjaxRequestTarget target, MovimentoEstoque movimentoEstoque) throws ValidacaoException {
        MovimentoEstoque movimentoEstoquePagina = modelMovimento.getObject().getMovimentoEstoque();

        boolean isEditItem = false;
        Fabricante fabricante = null;
        if (autoCompleteConsultaFabricante.getComponentValue() != null) {
            fabricante = (Fabricante) autoCompleteConsultaFabricante.getComponentValue();
        }
        if (this.codigoBarrasProduto != null) {
            if (CollectionUtils.isNotNullEmpty(movimentos)) {
                EstoqueEmpresaPK estoqueEmpresaPK = new EstoqueEmpresaPK();
                estoqueEmpresaPK.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
                estoqueEmpresaPK.setProduto(movimentoEstoquePagina.getProduto());

                GrupoEstoquePK grupoEstoquePK = new GrupoEstoquePK();
                grupoEstoquePK.setGrupo(movimentoEstoquePagina.getGrupoEstoque());
                grupoEstoquePK.setCodigoDeposito(movimentoEstoquePagina.getDeposito().getCodigo());
                grupoEstoquePK.setEstoqueEmpresa(new EstoqueEmpresa(estoqueEmpresaPK));
                grupoEstoquePK.setLocalizacaoEstrutura(movimentoEstoquePagina.getLocalizacaoEstrutura());

                GrupoEstoque grupoEstoque = LoadManager.getInstance(GrupoEstoque.class)
                        .addProperties(new HQLProperties(GrupoEstoque.class).getProperties())
                        .addProperties(new HQLProperties(EstoqueEmpresa.class, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA)).getProperties())
                        .setId(grupoEstoquePK)
                        .start().getVO();

                for (int i = 0; i < movimentos.size(); i++) {
                    MovimentoEstoquePageDTO dtoMovimento = movimentos.get(i);

                    if (isProdutoListado(dtoMovimento)) {
                        dtoMovimento.getMovimentoEstoque().setQuantidade(new Dinheiro(dtoMovimento.getMovimentoEstoque().getQuantidade()).somar(this.codigoBarrasProduto.getQuantidadeProduto()).doubleValue());
                        Double quantidade = dtoMovimento.getMovimentoEstoque().getQuantidade();
                        if (grupoEstoque != null) {
                            if (quantidade > grupoEstoque.getEstoqueFisico()) {
                                limparItem(target);
                                resetarFocoCodigoBarras(target);

                                throw new ValidacaoException(BundleManager.getString("quantidadeMaiorDisponivelLoteSelecionado"));
                            }
                        }

                        dtoMovimento.getCodigoBarrasProdutoList().add(this.codigoBarrasProduto);

                        movimentos.set(i, dtoMovimento);
                        isEditItem = true;

                        break;
                    }
                }
            }

            modelMovimento.getObject().getCodigoBarrasProdutoList().add(this.codigoBarrasProduto);
        }

        if (!isEditItem) {
            if (movimentoEstoque == null) {
                if (fabricante != null) {
                    modelMovimento.getObject().getMovimentoEstoque().setFabricante(fabricante);
                }
                movimentos.add(modelMovimento.getObject());
            } else {
                try {
                    MovimentoEstoquePageDTO dto = procurarMovimento(movimentoEstoque);
                    if (dto != null) {
                        dto.getCodigoBarrasProdutoList().clear();
                        BeanUtils.copyProperties(dto.getMovimentoEstoque(), movimentoEstoque);
                    }
                } catch (IllegalAccessException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (InvocationTargetException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        }

        tableMovimentos.update(target);
        modelMovimento.setObject(new MovimentoEstoquePageDTO());
        modelMovimento.getObject().setMovimentoEstoque(new MovimentoEstoque());
        modelMovimento.getObject().getMovimentoEstoque().setDataPortaria(Data.getDataAtual());
        modelMovimento.getObject().getMovimentoEstoque().setDeposito(movimentoEstoquePagina.getDeposito());

        autoCompleteConsultaTipoDocumento.setEnabled(false);
        target.add(autoCompleteConsultaTipoDocumento);
        limparItem(target);
        target.focusComponent(autoCompleteConsultaCentroCusto);
        updateNotificationPanel(target, false);
        JScript.setDirtyForm(target, formControles);
        txtQuantidade.setEnabled(true);
        autoCompleteConsultaProduto.setEnabled(true);
        autoCompleteConsultaDeposito.setEnabled(true);
    }

    private boolean isProdutoListado(MovimentoEstoquePageDTO dtoMovimento) {
        boolean produtoIgual = dtoMovimento.getMovimentoEstoque().getProduto() != null && dtoMovimento.getMovimentoEstoque().getProduto().getCodigo().equals(modelMovimento.getObject().getMovimentoEstoque().getProduto().getCodigo());
        boolean grupoIgual = dtoMovimento.getMovimentoEstoque().getGrupoEstoque() != null && dtoMovimento.getMovimentoEstoque().getGrupoEstoque().equals(modelMovimento.getObject().getMovimentoEstoque().getGrupoEstoque());
        boolean depositoIgual = dtoMovimento.getMovimentoEstoque().getDeposito() != null && dtoMovimento.getMovimentoEstoque().getDeposito().getCodigo().equals(modelMovimento.getObject().getMovimentoEstoque().getDeposito().getCodigo());
        boolean estruturaIgual = dtoMovimento.getMovimentoEstoque().getLocalizacaoEstrutura() != null && dtoMovimento.getMovimentoEstoque().getLocalizacaoEstrutura().getCodigo().equals(modelMovimento.getObject().getMovimentoEstoque().getLocalizacaoEstrutura().getCodigo());

        return produtoIgual && grupoIgual && estruturaIgual && depositoIgual;
    }

    private void remover(AjaxRequestTarget target, MovimentoEstoque movimentoEstoque) {
        for (int i = 0; i < movimentos.size(); i++) {
            if (movimentos.get(i).getMovimentoEstoque() == movimentoEstoque) {
                movimentos.remove(i);
                break;
            }
        }
        tableMovimentos.update(target);
    }

    private MovimentoEstoquePageDTO procurarMovimento(MovimentoEstoque movimentoEstoque) {
        for (MovimentoEstoquePageDTO _movimentoEstoqueDTO : movimentos) {
            if (_movimentoEstoqueDTO.getMovimentoEstoque().getProduto().equals(movimentoEstoque.getProduto())
                    && Coalesce.asString(_movimentoEstoqueDTO.getMovimentoEstoque().getGrupoEstoque()).equals(Coalesce.asString(movimentoEstoque.getGrupoEstoque()))
                    && (_movimentoEstoqueDTO.getMovimentoEstoque().getLocalizacaoEstrutura() == null || movimentoEstoque.getLocalizacaoEstrutura() == null
                    || _movimentoEstoqueDTO.getMovimentoEstoque().getLocalizacaoEstrutura().getCodigo().equals(movimentoEstoque.getLocalizacaoEstrutura().getCodigo()))) {
                return _movimentoEstoqueDTO;
            }
        }
        return null;
    }

    private void limparItem(AjaxRequestTarget target) {
        autoCompleteConsultaDeposito.setEnabled(true);
        target.add(autoCompleteConsultaDeposito);

        autoCompleteConsultaCentroCusto.limpar(target);

        autoCompleteConsultaProduto.setEnabled(true);
        autoCompleteConsultaProduto.limpar(target);

        autoCompleteConsultaFabricante.setEnabled(false);
        btnCadFabricante.setEnabled(false);
        autoCompleteConsultaFabricante.limpar(target);
        target.add(autoCompleteConsultaFabricante);
        target.add(btnCadFabricante);
        if (TipoDocumento.IS_SAIDA.equals(tipoDocumento.getFlagTipoMovimento())) {
            pnlSaidaLote.limpar(target);
        }
        if (TipoDocumento.IS_ENTRADA.equals(tipoDocumento.getFlagTipoMovimento())) {
            pnlEntradaLote.limpar(target);
        }
        txtQuantidade.setEnabled(true);
        txtQuantidade.limpar(target);
        txtNumeroDocumento.limpar(target);
        txtItemDocumento.limpar(target);
        txtObsevacao.limpar(target);
        txtCodigoBarras.limpar(target);
        codigoBarrasProduto = null;
        autoCompleteConsultaLocalizacaoEstrutura.limpar(target);
    }

    private void limparMovimento(AjaxRequestTarget target) {
        autoCompleteConsultaEmpresaDestino.limpar(target);
        autoCompleteConsultaDeposito.limpar(target);
        autoCompleteConsultaCentroCusto.limpar(target);
        autoCompleteConsultaProduto.limpar(target);
        if (tipoDocumento != null) {
            if (TipoDocumento.IS_SAIDA.equals(tipoDocumento.getFlagTipoMovimento())) {
                pnlSaidaLote.limpar(target);
            }
            if (TipoDocumento.IS_ENTRADA.equals(tipoDocumento.getFlagTipoMovimento())) {
                pnlEntradaLote.limpar(target);
            }
        }
        txtQuantidade.limpar(target);
        txtNumeroDocumento.limpar(target);
        txtItemDocumento.limpar(target);
        txtObsevacao.limpar(target);
    }

    private void limpar(AjaxRequestTarget target) {
        setResponsePage(LancamentoMovimentoEstoquePage.class);
    }

    public void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (movimentos.isEmpty()) {
            throw new ValidacaoException(BundleManager.getString("informePeloMenosUmMovimento"));
        }
        LancamentoMovimentoEstoque lancamentoMovimentoEstoque = new LancamentoMovimentoEstoque();
        lancamentoMovimentoEstoque.setMovimentoEstoqueSet(new HashSet<MovimentoEstoque>(Lambda.extract(movimentos, Lambda.on(MovimentoEstoquePageDTO.class).getMovimentoEstoque())));
        LancamentoMovimentoEstoque lancamentoMovimentoEstoqueSave = BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).saveLancamentoMovimentoEstoque(lancamentoMovimentoEstoque);

        TipoDocumento tipoDocumento = (TipoDocumento) autoCompleteConsultaTipoDocumento.getComponentValue();

        if (CollectionUtils.isNotNullEmpty(movimentos)) {
            for (MovimentoEstoquePageDTO movimento : movimentos) {
                List<CodigoBarrasProduto> listCBP = movimento.getCodigoBarrasProdutoList();
                if (listCBP != null && !CollectionUtils.isEmpty(listCBP)) {
                    for (CodigoBarrasProduto codigoBarrasProduto : listCBP) {
                        codigoBarrasProduto.setStatus(CodigoBarrasProduto.Status.UTILIZADO.value());
                        BOFactory.save(codigoBarrasProduto);
                    }
                }
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(tipoDocumento.getFlagImprimeRecibo())) {
            ImpressaoReciboLancamentoEstoqueDTOParam param = new ImpressaoReciboLancamentoEstoqueDTOParam();
            param.setMovimentoEstoqueList(new ArrayList<MovimentoEstoque>(lancamentoMovimentoEstoqueSave.getMovimentoEstoqueSet()));

            initImpressaoReciboLocalizacao(target, param);
        } else {
            atualizarPagina();
        }
    }

    private void atualizarPagina() {
        try {
            Page page = (Page) getResponsePage().newInstance();
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        } catch (InstantiationException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public Class getResponsePage() {
        return LancamentoMovimentoEstoquePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("lancamentoEstoque");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaTipoDocumento;
    }

    private void eventoConsultaTipoDocumento(AjaxRequestTarget target, TipoDocumento object) {
        tipoDocumento = object;
        if (object != null) {
            if (TipoDocumento.IS_ENTRADA.equals(object.getFlagTipoMovimento())) {
                if (pnlSaidaLote.getParent() != null) {
                    pnlSaidaLote.replaceWith(pnlEntradaLote);
                    target.add(pnlEntradaLote);
                    pnlSaidaLote.unregisterEvents();
                    pnlEntradaLote.registerEvents();
                }
                habilitarLocalizacaoEstrutura(target);
                containerCodigoBarras.setVisible(false);
                containerFabricante.setVisible(true);
                target.add(containerFabricante);
                containerValorProduto.setVisible(false);
                target.add(containerValorProduto);
            } else {
                if (pnlEntradaLote.getParent() != null) {
                    pnlEntradaLote.replaceWith(pnlSaidaLote);
                    target.add(pnlSaidaLote);
                    pnlEntradaLote.unregisterEvents();
                    pnlSaidaLote.registerEvents();
                }
                habilitarLocalizacaoEstrutura(target);
                containerCodigoBarras.setVisible(true);
                containerFabricante.setVisible(false);
                target.add(containerFabricante);
                if (RepositoryComponentDefault.SIM.equals(object.getFlagInformaValorProduto())) {
                    containerValorProduto.setVisible(true);
                } else {
                    containerValorProduto.setVisible(false);
                }
                target.add(containerValorProduto);
            }
            autoCompleteConsultaEmpresaDestino.setRequired(target, RepositoryComponentDefault.SIM.equals(object.getExigeUnidadeDestino()));
            autoCompleteConsultaCentroCusto.setRequired(target, RepositoryComponentDefault.SIM.equals(object.getExigeCentroCusto()));
        } else {
            target.focusComponent(autoCompleteConsultaTipoDocumento);
        }
        limparMovimento(target);
        carregarEstoquePadrao(target);
        target.appendJavaScript(JScript.initMasks());
        containerInterno.setEnabled(object != null);
        target.add(containerInterno);
    }

    public void carregarEstoquePadrao(AjaxRequestTarget target) {
        EmpresaMaterial empresaMaterial = new EmpresaMaterial();
        empresaMaterial = LoadManager.getInstance((EmpresaMaterial.class))
                .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_DESCRICAO))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa()))
                .start().getVO();
        autoCompleteConsultaDeposito.setComponentValue(empresaMaterial.getDeposito());
        target.add(autoCompleteConsultaDeposito);
    }

    private void habilitarLocalizacaoEstrutura(AjaxRequestTarget target) {
        if (target != null) {
            autoCompleteConsultaLocalizacaoEstrutura.limpar(target);
        }

        if (RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())) {
            autoCompleteConsultaLocalizacaoEstrutura.setEnabled(true);
        } else {
            autoCompleteConsultaLocalizacaoEstrutura.setEnabled(false);
        }

        if (target != null) {
            target.add(autoCompleteConsultaLocalizacaoEstrutura);
        }
    }

    private void initImpressaoReciboLocalizacao(AjaxRequestTarget target, ImpressaoReciboLancamentoEstoqueDTOParam param) {
        if (dlgImpressaoRecibo == null) {
            addModal(target, dlgImpressaoRecibo = new DlgImpressaoObject<ImpressaoReciboLancamentoEstoqueDTOParam>(newModalId(), bundle("msgDesejaImprimirCompranteEntrega")) {
                @Override
                public DataReport getDataReport(ImpressaoReciboLancamentoEstoqueDTOParam param) throws ReportException {
                    return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioImpressaoReciboLancamentoEstoque(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, ImpressaoReciboLancamentoEstoqueDTOParam param) throws ValidacaoException, DAOException {
                    atualizarPagina();
                }
            });
        }
        dlgImpressaoRecibo.show(target, param);
    }

    public String getSugerirPrimeiroLoteVencerSaida() {
        if (sugerirPrimeiroLoteVencerSaida == null) {
            try {
                sugerirPrimeiroLoteVencerSaida = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("sugerirPrimeiroLoteVencerSaida");
            } catch (DAOException ex) {
                Logger.getLogger(LancamentoMovimentoEstoquePage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return sugerirPrimeiroLoteVencerSaida;
    }

    private void carregarUnidade(AjaxRequestTarget target, Produto object) {
        List<Produto> produtos = LoadManager.getInstance(Produto.class).addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_CODIGO, object.getCodigo()))
                .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(Produto.PROP_UNIDADE)).getProperties()).start().getList();
        txtUnidade.setComponentValue(produtos.get(0).getUnidade().getDescricaoComSigla());
        target.add(txtUnidade);
    }

    public String getUtilizaLocalizacaoEstoque() {
        if (utilizaLocalizacaoEstoque == null) {
            try {
                utilizaLocalizacaoEstoque = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
            } catch (DAOException ex) {
                Logger.getLogger(LancamentoMovimentoEstoquePage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return utilizaLocalizacaoEstoque;
    }
}
