package br.com.celk.view.materiais.permissaogrupoestoque;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.vo.entradas.estoque.permissao.PermissaoGrupoEstoque;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Grupo;
import br.com.ksisolucoes.vo.entradas.estoque.permissao.PermissaoGrupoEstoqueItem;
import java.util.ArrayList;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;

/**
 *
 * <AUTHOR>
 * Programa - 625
 */
public class ConsultaPermissaoGrupoEstoquePage extends ConsultaPage<PermissaoGrupoEstoque, List<BuilderQueryCustom.QueryParameter>> {

    private String funcao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("funcao"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        PermissaoGrupoEstoque proxy = on(PermissaoGrupoEstoque.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("funcao"), proxy.getFuncao().getNome()));
        columns.add(createSortableColumn(bundle("tipo"), proxy.getTipoPermissao(), proxy.getDescricaoTipoPermissao()));
        return columns;
    }

    private CustomColumn<PermissaoGrupoEstoque> getCustomColumn() {
        return new CustomColumn<PermissaoGrupoEstoque>() {

            @Override
            public Component getComponent(String componentId, final PermissaoGrupoEstoque rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroPermissaoGrupoEstoquePage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        VOUtils.persistirListaVosModificados(PermissaoGrupoEstoqueItem.class, new ArrayList<PermissaoGrupoEstoqueItem>(), new QueryCustom.QueryCustomParameter(PermissaoGrupoEstoqueItem.PROP_PERMISSAO_GRUPO_ESTOQUE, rowObject));
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override   
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroPermissaoGrupoEstoquePage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return PermissaoGrupoEstoque.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(PermissaoGrupoEstoque.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(PermissaoGrupoEstoque.PROP_FUNCAO, Grupo.PROP_CODIGO),
                            VOUtils.montarPath(PermissaoGrupoEstoque.PROP_FUNCAO, Grupo.PROP_NOME),});

            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(PermissaoGrupoEstoque.PROP_DATA_CADASTRO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PermissaoGrupoEstoque.PROP_FUNCAO, Grupo.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, funcao));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroPermissaoGrupoEstoqueStep1Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaPermissaoMovimentacao");
    }
}
