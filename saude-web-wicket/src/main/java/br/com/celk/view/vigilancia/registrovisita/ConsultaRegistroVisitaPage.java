package br.com.celk.view.vigilancia.registrovisita;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.motivovisita.AutoCompleteConsultaMotivoVista;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaRegistroVisitaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RegistroVisitaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RegistroVisita;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 482
 */
@Private
public class ConsultaRegistroVisitaPage extends ConsultaPage<RegistroVisitaDTO, QueryConsultaRegistroVisitaDTOParam> {

    private QueryConsultaRegistroVisitaDTOParam param;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(param = new QueryConsultaRegistroVisitaDTOParam()));
        QueryConsultaRegistroVisitaDTOParam proxy = on(QueryConsultaRegistroVisitaDTOParam.class);

        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new AutoCompleteConsultaMotivoVista(path(proxy.getMotivoVisita())));
        form.add(new PnlDatePeriod(path(proxy.getPeriodo())));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        RegistroVisitaDTO proxy = on(RegistroVisitaDTO.class);
        RegistroVisita onRegistroVisita = on(RegistroVisita.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("estabelecimento"), onRegistroVisita.getDescEstabelecimentoFormatado(), proxy.getRegistroVisita().getDescEstabelecimentoFormatado()));
        columns.add(createSortableColumn(bundle("motivoVisita"), onRegistroVisita.getMotivoVisita().getDescricao(), proxy.getRegistroVisita().getMotivoVisita().getDescricao()));
        columns.add(new DateTimeColumn(bundle("dataVisita"), path(proxy.getRegistroVisita().getDataVisita()), path(proxy.getRegistroVisita().getDataVisita())));
        return columns;
    }

    private CustomColumn<RegistroVisitaDTO> getCustomColumn() {
        return new CustomColumn<RegistroVisitaDTO>() {

            @Override
            public Component getComponent(String componentId, final RegistroVisitaDTO rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroVisitaPage(rowObject));
                    }

                    @Override
                    public boolean isEditarEnabled() {
                        return !rowObject.isExisteContaPaciente();
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).deletarRegistroVisita(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public boolean isExcluirEnabled() {
                        return !rowObject.isExisteContaPaciente();
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroVisitaPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<RegistroVisitaDTO, QueryConsultaRegistroVisitaDTOParam>() {

            @Override
            public DataPagingResult<RegistroVisitaDTO> executeQueryPager(DataPaging<QueryConsultaRegistroVisitaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setAscending(((SingleSortState) getPagerProvider().getSortState()).getSort().isAscending());
                dataPaging.getParam().setPropSort(((SingleSortState<String>) getPagerProvider().getSortState()).getSort().getProperty());
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarRegistroVisita(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(RegistroVisitaDTO.PROP_REGISTRO_VISITA, RegistroVisita.PROP_DATA_VISITA), false);
            }
        };
    }

    @Override
    public QueryConsultaRegistroVisitaDTOParam getParameters() {
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRegistroVisitaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaRegistroVisitas");
    }

}
