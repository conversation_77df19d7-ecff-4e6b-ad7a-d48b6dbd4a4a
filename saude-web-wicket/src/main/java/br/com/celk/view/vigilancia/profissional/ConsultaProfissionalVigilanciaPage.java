package br.com.celk.view.vigilancia.profissional;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IFileAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaProfissionalVigilanciaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.io.File;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 688
 */
@Private
public class ConsultaProfissionalVigilanciaPage extends ConsultaPage<VigilanciaProfissional, ConsultaProfissionalVigilanciaDTOParam> {

    private CompoundPropertyModel<ConsultaProfissionalVigilanciaDTOParam> model;

    public ConsultaProfissionalVigilanciaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(model = new CompoundPropertyModel(new ConsultaProfissionalVigilanciaDTOParam()));
        ConsultaProfissionalVigilanciaDTOParam proxy = on(ConsultaProfissionalVigilanciaDTOParam.class);
        
        form.add(new InputField<String>(path(proxy.getNomeProfissional())));
        form.add(new InputField<String>(path(proxy.getNomeDiretor())));
        form.add(new InputField<Long>(path(proxy.getNumeroRegistro())));
        form.add(new AutoCompleteConsultaVigilanciaEndereco(path(proxy.getVigilanciaEndereco())));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(VigilanciaProfissional.class);
        VigilanciaProfissional proxy = on(VigilanciaProfissional.class);

        columns.add(getActionColumn());
        columns.add(columnFactory.createSortableColumn(bundle("numeroRegistro"), path(proxy.getNumeroRegistro())));
        columns.add(columnFactory.createSortableColumn(bundle("nomeProfissional"), path(proxy.getNomeProfissional())));
        columns.add(columnFactory.createSortableColumn(bundle("nomeDiretor"), path(proxy.getNomeDiretor())));
        columns.add(columnFactory.createColumn(bundle("endereco"), path(proxy.getProfissionalEndereco().getEnderecoFormatado())));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<VigilanciaProfissional>() {
            @Override
            public void customizeColumn(final VigilanciaProfissional rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<VigilanciaProfissional>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaProfissional modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroProfissionalVigilanciaPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<VigilanciaProfissional>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaProfissional modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).excluirProfissionalVigilancia(modelObject);
                        getPageableTable().populate(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<VigilanciaProfissional>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaProfissional modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroProfissionalVigilanciaPage(modelObject, true));
                    }
                });
                addAction(ActionType.HISTORICO, rowObject, new IFileAction<VigilanciaProfissional>() {
                    @Override
                    public File action(VigilanciaProfissional vigilanciaProfissional) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).historico(vigilanciaProfissional);
                    }
                });
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<VigilanciaProfissional>() {
                    @Override
                    public DataReport action(VigilanciaProfissional modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoProfissionalParaReceita(modelObject.getCodigo());
                    }
                });
            }
        };
    }
    
    @Override
    public IPagerProvider<VigilanciaProfissional, ConsultaProfissionalVigilanciaDTOParam> getPagerProviderInstance() {
        return new QueryPagerProvider<VigilanciaProfissional, ConsultaProfissionalVigilanciaDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaProfissionalVigilanciaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarVigilanciaProfissional(dataPaging);
            }
            
            @Override
            public void customizeParam(ConsultaProfissionalVigilanciaDTOParam param) {
                ConsultaProfissionalVigilanciaPage.this.model.getObject().setSortProp(getSort().getProperty());
                ConsultaProfissionalVigilanciaPage.this.model.getObject().setAscending(getSort().isAscending());
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam("vigilanciaProfissional.nomeProfissional", true);
            }
        };
    }
    
    @Override
    public ConsultaProfissionalVigilanciaDTOParam getParameters() {
        return model.getObject();
    }

    @Override
    public Class getCadastroPage() {
        return CadastroProfissionalVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaProfissionaisReceita");
    }
}
