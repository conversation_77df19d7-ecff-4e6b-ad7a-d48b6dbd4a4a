package br.com.celk.view.materiais.devolucao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.CodigoBarrasProduto;
import br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 *
 * <AUTHOR>
 * Programa - 259
 */
@Private

public class IniciarDevolucaoMedicamentoPage extends BasePage{

    private Form<DevolucaoMedicamento> form;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private Long codigoBarras;
    private InputField txtCodigoBarras;
    
    public IniciarDevolucaoMedicamentoPage() {
        init();
    }
    
    private void init(){
        DevolucaoMedicamento devolucaoProxy = on(DevolucaoMedicamento.class);
        
        form = new Form<DevolucaoMedicamento>("form", new CompoundPropertyModel<DevolucaoMedicamento>(new DevolucaoMedicamento()));
        
        form.add(txtCodigoBarras = new InputField("codigoBarras", new PropertyModel(this, "codigoBarras")));
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(devolucaoProxy.getUsuarioCadsus()),true));
        
        form.add(new AbstractAjaxButton("btnAvancar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                avancar();
            }
        });
        
        txtCodigoBarras.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                autoCompleteConsultaUsuarioCadsus.setEnabled(true);
                autoCompleteConsultaUsuarioCadsus.limpar(target);
                if(codigoBarras != null){
                    CodigoBarrasProduto cbp = LoadManager.getInstance(CodigoBarrasProduto.class)
                        .addProperty(CodigoBarrasProduto.PROP_CODIGO)
                        .addProperty(VOUtils.montarPath(CodigoBarrasProduto.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(CodigoBarrasProduto.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_NOME))
                        .addProperty(VOUtils.montarPath(CodigoBarrasProduto.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_APELIDO))
                        .addProperty(VOUtils.montarPath(CodigoBarrasProduto.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                        .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_REFERENCIA, codigoBarras.toString()))
                        .start().getVO();
                    
                    if(cbp != null && cbp.getDispensacaoMedicamentoItem() != null){
                        UsuarioCadsus uc = cbp.getDispensacaoMedicamentoItem().getUsuarioCadsusDestino();
                        
                        form.getModelObject().setUsuarioCadsus(uc);
                        autoCompleteConsultaUsuarioCadsus.setComponentValue(uc);
                        autoCompleteConsultaUsuarioCadsus.setEnabled(false);
                        target.add(autoCompleteConsultaUsuarioCadsus);
                        try {
                            avancar();
                        } catch (DAOException ex) {
                            Logger.getLogger(IniciarDevolucaoMedicamentoPage.class.getName()).log(Level.SEVERE, null, ex);
                        } catch (ValidacaoException ex) {
                            warn(target, ex.getMessage());
                        }
                    } else {
                        try {
                            throw new ValidacaoException(bundle("naoExistePrescricaoCodigoBarras"));
                        } catch (ValidacaoException ex) {
                            warn(target, ex.getMessage());
                        }
                    }                 
                }
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
            }
        });
        
        add(form);
        
        form.getModelObject().setEmpresa(br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa());
    }
    
    @Override
    public String getTituloPrograma() {
        return bundle("devolucaoMedicamento");
    }
    
    private void avancar() throws DAOException, ValidacaoException {
        if (codigoBarras == null && autoCompleteConsultaUsuarioCadsus.getComponentValue() == null) {
            throw new ValidacaoException(bundle("informeCodigoDeBarrasOuPaciente"));
        }
        
        setResponsePage(new
                DevolucaoMedicamentoPage(form.getModelObject()));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtCodigoBarras;
    }
    
}
