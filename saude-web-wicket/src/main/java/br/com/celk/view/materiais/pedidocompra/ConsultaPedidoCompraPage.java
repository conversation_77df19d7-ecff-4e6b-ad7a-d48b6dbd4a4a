package br.com.celk.view.materiais.pedidocompra;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgMotivoAreaMaxLength;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoTransferenciaFacade;
import br.com.ksisolucoes.bo.materiais.pedidocompra.dto.CadastroPedidoCompraDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.pedidocompra.interfaces.dto.RelatorioImpressaoPedidoCompraDTOParam;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompra;
import br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem;
import br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraOcorrencia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 * <AUTHOR>
 * Programa - 922
 */
@Private
public class ConsultaPedidoCompraPage extends ConsultaPage<PedidoCompra, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa empresaLogada;
    private Long pedidoCompra;
    private Long situacao;
    private DatePeriod periodo;
    private DropDown<Long> dropDownSituacao;
    private DlgMotivoAreaMaxLength dlgJustificativa;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(getDropDownSituacao());
        form.add(new PnlDatePeriod("periodo"));
        form.add(new InputField<Long>("pedidoCompra", new PropertyModel(this, "pedidoCompra")));

        empresaLogada = SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa();
        setExibeExpandir(true);
    }

    private DropDown<Long> getDropDownSituacao() {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>("situacao");

            dropDownSituacao.addChoice(null, bundle("todas"));
            dropDownSituacao.addChoice(PedidoCompra.Status.PENDENTE.value(), bundle("pendente"));
            dropDownSituacao.addChoice(PedidoCompra.Status.RECEBIDA.value(), bundle("recebida"));
            dropDownSituacao.addChoice(PedidoCompra.Status.CANCELADA.value(), bundle("cancelada"));
        }
        return dropDownSituacao;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        PedidoCompra proxy = on(PedidoCompra.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("pedidoCompra"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("data"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<PedidoCompra>() {
            @Override
            public void customizeColumn(PedidoCompra rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<PedidoCompra>() {

                    @Override
                    public void action(AjaxRequestTarget target, PedidoCompra modelObject) {
                        setResponsePage(new CadastroPedidoCompraPage(modelObject));
                    }

                }).setEnabled(PedidoCompra.Status.PENDENTE.value().equals(rowObject.getStatus()));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<PedidoCompra>() {

                    @Override
                    public void action(AjaxRequestTarget target, PedidoCompra modelObject) throws ValidacaoException, DAOException {
                        cancelarPedidoCompra(target, modelObject);
                    }

                }).setQuestionDialogBundleKey("desejaRealmenteCancelarPedidoCompra")
                        .setIcon(Icon.DOC_DELETE)
                        .setEnabled(PedidoCompra.Status.PENDENTE.value().equals(rowObject.getStatus()));

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<PedidoCompra>() {
                    @Override
                    public DataReport action(PedidoCompra PedidoCompra) throws ReportException {
                        RelatorioImpressaoPedidoCompraDTOParam param = new RelatorioImpressaoPedidoCompraDTOParam();
                        param.setPedidoCompra(PedidoCompra);
                        return BOFactoryWicket.getBO(MaterialBasicoFacade.class).relatorioImpressaoPedidoCompra(param);
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<PedidoCompra>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoCompra modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesPedidoCompraPage(modelObject));
                    }
                });
                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<PedidoCompra>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoCompra modelObject) throws ValidacaoException, DAOException {
                        finalizarPedidoCompra(target, modelObject);
                    }

                }).setQuestionDialogBundleKey("msgDesejaFinalizarPedidoCompra")
                        .setEnabled(PedidoCompra.Status.PENDENTE.value().equals(rowObject.getStatus()));
                addAction(ActionType.REVERTER, rowObject, new IModelAction<PedidoCompra>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoCompra modelObject) throws ValidacaoException, DAOException {
                        abreDlgJustificativa(target, modelObject);
                    }
                }).setEnabled(PedidoCompra.Status.ENVIADO.value().equals(rowObject.getStatus()));
            }
        };
    }

    private void abreDlgJustificativa(AjaxRequestTarget target, final PedidoCompra pedidoCompra) {
        if (dlgJustificativa == null) {
            addModal(target, dlgJustificativa = new DlgMotivoAreaMaxLength(newModalId(), BundleManager.getString("justificativa"), 512L) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, Object object) throws ValidacaoException, DAOException {
                    PedidoCompra pedidoCompraReverter = (PedidoCompra) object;

                    CadastroPedidoCompraDTO cadastroPedidoCompraDTO = new CadastroPedidoCompraDTO();
                    pedidoCompraReverter.setStatus(PedidoCompra.Status.PENDENTE.value());
                    cadastroPedidoCompraDTO.setPedidoCompra(pedidoCompraReverter);

                    PedidoCompraItem proxy = on(PedidoCompraItem.class);

                    if (pedidoCompraReverter != null) {
                        List<PedidoCompraItem> itensCarregados = LoadManager.getInstance(PedidoCompraItem.class)
                                .addProperties(new HQLProperties(PedidoCompraItem.class).getProperties())
                                .addProperty(path(proxy.getProduto().getCodigo()))
                                .addProperty(path(proxy.getProduto().getReferencia()))
                                .addProperty(path(proxy.getProduto().getDescricao()))
                                .addProperty(path(proxy.getProduto().getUnidade().getCodigo()))
                                .addProperty(path(proxy.getProduto().getUnidade().getUnidade()))
                                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getPedidoCompra()), pedidoCompraReverter))
                                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.DIFERENTE, PedidoCompraItem.Status.CANCELADA.value()))
                                .start().getList();

                        cadastroPedidoCompraDTO.setPedidoCompraItemList(itensCarregados);
                    }
                    cadastroPedidoCompraDTO.setStatus(PedidoCompra.Status.PENDENTE.value());
                    cadastroPedidoCompraDTO.setGerarOcorrencia(true);

                    PedidoCompra pedidoCompraSave = BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cadastrarPedidoCompra(cadastroPedidoCompraDTO);

                    PedidoCompraOcorrencia pedidoCompraOcorrencia = new PedidoCompraOcorrencia();
                    pedidoCompraOcorrencia.setTipoOcorrencia(PedidoCompraOcorrencia.TipoOcorrencia.REABERTURA.value());
                    pedidoCompraOcorrencia.setUsuario(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
                    pedidoCompraOcorrencia.setDataCadastro(DataUtil.getDataAtual());
                    pedidoCompraOcorrencia.setDescricao(BundleManager.getString("msgReaberturaPedidoX", pedidoCompra.getCodigo()));
                    pedidoCompraOcorrencia.setJustificativa(motivo);
                    pedidoCompraOcorrencia.setPedidoCompra(pedidoCompraReverter);

                    BOFactoryWicket.save(pedidoCompraOcorrencia);

                    setResponsePage(new CadastroPedidoCompraPage(pedidoCompraSave));
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        dlgJustificativa.setObject(pedidoCompra);
        dlgJustificativa.show(target);
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return PedidoCompra.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(PedidoCompra.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(PedidoCompra.PROP_CODIGO), false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoCompra.PROP_EMPRESA), empresaLogada));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoCompra.PROP_CODIGO), pedidoCompra));

        if (situacao == null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoCompra.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE, PedidoCompra.Status.CANCELADA.value()));
        } else if (PedidoCompra.Status.PENDENTE.value().equals(situacao)) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoCompra.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN,
                    Arrays.asList(PedidoCompra.Status.PENDENTE.value(), PedidoCompra.Status.PARCIAL.value())));
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoCompra.PROP_STATUS), BuilderQueryCustom.QueryParameter.IGUAL, situacao));
        }

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoCompra.PROP_DATA_CADASTRO), periodo));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroPedidoCompraPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaPedidoCompra");
    }

    private void cancelarPedidoCompra(AjaxRequestTarget target, PedidoCompra pedidoCompra) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cancelarPedidoCompra(pedidoCompra);
        getPageableTable().update(target);
    }

    private void finalizarPedidoCompra(AjaxRequestTarget target, PedidoCompra pedidoCompra) throws DAOException, ValidacaoException {
        pedidoCompra.setStatus(PedidoCompra.Status.ENVIADO.value());
        pedidoCompra = BOFactoryWicket.save(pedidoCompra);

        PedidoCompraOcorrencia pedidoCompraOcorrencia = new PedidoCompraOcorrencia();
        pedidoCompraOcorrencia.setTipoOcorrencia(PedidoCompraOcorrencia.TipoOcorrencia.ENVIADO.value());
        pedidoCompraOcorrencia.setUsuario(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
        pedidoCompraOcorrencia.setDataCadastro(DataUtil.getDataAtual());
        pedidoCompraOcorrencia.setDescricao(BundleManager.getString("msgEnviadoPedidoX", pedidoCompra.getCodigo()));
        pedidoCompraOcorrencia.setPedidoCompra(pedidoCompra);
        BOFactoryWicket.save(pedidoCompraOcorrencia);

        getPageableTable().update(target);
    }

}
