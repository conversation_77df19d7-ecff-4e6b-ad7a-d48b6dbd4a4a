package br.com.celk.view.vigilancia.agravos.resumo;

import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.cid.autocomplete.AutoCompleteConsultaCidMulti;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.prontuario.basico.classificacaocid.autocomplete.AutoCompleteConsultaClassificacaoCid;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioResumoAgravosDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 547
 */
public class RelatorioResumoAgravosPage extends RelatorioPage<RelatorioResumoAgravosDTOParam> {

    @Override
    public void init(Form<RelatorioResumoAgravosDTOParam> form) {
        RelatorioResumoAgravosDTOParam proxy = on(RelatorioResumoAgravosDTOParam.class);

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidade())));
        form.add(new AutoCompleteConsultaCidMulti(path(proxy.getListCid())));
        form.add(new AutoCompleteConsultaClassificacaoCid(path(proxy.getClassificacaoCids())));
        form.add(new InputField(path(proxy.getPaciente())));
        form.add(new InputField(path(proxy.getBairro())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getStatus()), RelatorioResumoAgravosDTOParam.Status.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoOrdenacao()), RelatorioResumoAgravosDTOParam.TipoOrdenacao.values()));
        form.add(new PnlChoicePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioResumoAgravosDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getGestante()), true));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoResumo()), RelatorioResumoAgravosDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));
    }

    @Override
    public Class<RelatorioResumoAgravosDTOParam> getDTOParamClass() {
        return RelatorioResumoAgravosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoAgravosDTOParam param) throws ReportException {
        return BOFactory.getBO(VigilanciaReportFacade.class).relatorioResumoAgravos(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("resumoAgravos");
    }

}
