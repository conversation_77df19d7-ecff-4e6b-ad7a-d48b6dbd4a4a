package br.com.celk.view.vigilancia.veiculo.usuariosetorvigilancia;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.SetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.UsuarioSetorVigilancia;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 250
 */
@Private
public class CadastroUsuarioSetorVigilanciaPage extends BasePage {

    private AutoCompleteConsultaUsuario autoCompleteConsultaUsuario;
    private Table<UsuarioSetorVigilancia> table;
    private List<IColumn> columns;
    private CollectionProvider collectionProvider;
    private WebMarkupContainer container;
    private AutoCompleteConsultaSetorVigilancia autoCompleteConsultaSetorVigilancia;
    
    private List<UsuarioSetorVigilancia> lstUsuSetor = new ArrayList<UsuarioSetorVigilancia>();
    private UsuarioSetorVigilancia usuarioSetorVigilancia;
    private SetorVigilancia setorVigilancia;
    private AbstractAjaxButton btnSalvar;
    private CompoundPropertyModel<UsuarioSetorVigilancia> model;
    private AbstractAjaxButton btnAdicionar;
    
    public CadastroUsuarioSetorVigilanciaPage() {
        init();
    }
    
    public void init() {
        Form form = new Form("form",new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaSetorVigilancia = new AutoCompleteConsultaSetorVigilancia("setorVigilancia"));
        
        container = new WebMarkupContainer("container", model = new CompoundPropertyModel(usuarioSetorVigilancia == null ? new UsuarioSetorVigilancia() : usuarioSetorVigilancia));
        
        container.add(autoCompleteConsultaUsuario = new AutoCompleteConsultaUsuario(UsuarioSetorVigilancia.PROP_USUARIO));
        container.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
        
        autoCompleteConsultaSetorVigilancia.add(new ConsultaListener<SetorVigilancia>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, SetorVigilancia object) {
                if (object != null) {
                    carregaRegistros(object);
                    setEnable(target,true);
                    target.focusComponent(autoCompleteConsultaUsuario);
                } else {
                    setEnable(target, false);
                    lstUsuSetor.clear();
                }
                table.update(target);
            }
        });
        container.setOutputMarkupId(true);
        container.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        
        form.add(btnSalvar = new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));
        
        btnSalvar.setEnabled(false);
        container.setEnabled(false);
        form.add(container);
        add(form);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (setorVigilancia == null) {
            target.focusComponent(autoCompleteConsultaSetorVigilancia);
            throw new ValidacaoException(BundleManager.getString("informeSetorVigilancia"));
        }
        
        if (model.getObject().getUsuario() == null) {
            target.focusComponent(autoCompleteConsultaUsuario);
            throw new ValidacaoException(BundleManager.getString("informeUsuario"));
        }
        
        if (!jaExiste()) {
            this.lstUsuSetor.add(model.getObject());
            this.table.update(target);
        } else {
            target.focusComponent(autoCompleteConsultaUsuario);
            throw new ValidacaoException(BundleManager.getString("usuarioJaInformado"));
        }
        limpar(target);
    }
    
    private boolean jaExiste() {
        for (UsuarioSetorVigilancia _usuarioSetorVigilancia : lstUsuSetor) {
            if (_usuarioSetorVigilancia.getUsuario().getCodigo().equals(this.model.getObject().getUsuario().getCodigo())) {
                return true;
            }
        }
        return false;
    }

    private CollectionProvider getCollectionProvider() {
        if (this.collectionProvider == null) {
            this.collectionProvider = new CollectionProvider() {
                @Override
                public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                    return lstUsuSetor;
                }
            };
        }
        return this.collectionProvider;
    }
    
    private List<IColumn> getColumns() {
        if (this.columns == null) {
            this.columns = new ArrayList<IColumn>();

            ColumnFactory columnFactory = new ColumnFactory(UsuarioSetorVigilancia.class);
            
            this.columns.add(getCustomColumn());
            this.columns.add(columnFactory.createSortableColumn(BundleManager.getString("usuario"), VOUtils.montarPath(UsuarioSetorVigilancia.PROP_USUARIO, Usuario.PROP_NOME)));
        }
        return this.columns;
    }
    
    private CustomColumn<UsuarioSetorVigilancia> getCustomColumn() {
        return new CustomColumn<UsuarioSetorVigilancia>() {

            @Override
            public Component getComponent(String componentId, final UsuarioSetorVigilancia rowObject) {
                return new RemoverActionColumnPanel(componentId) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException {
                        remover(target, rowObject);
                    }
                };
            }
        };
    }
    
    private void remover(AjaxRequestTarget target, UsuarioSetorVigilancia _usuarioSetorVigilancia) {
        for (int i = 0; i < lstUsuSetor.size(); i++) {
            if (lstUsuSetor.get(i).getUsuario().getCodigo().equals(_usuarioSetorVigilancia.getUsuario().getCodigo())) {
                lstUsuSetor.remove(i);
                break;
            }
        }
        table.update(target);
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaSetorVigilancia.getTxtDescricao().getTextField();
    }

    private void limpar(AjaxRequestTarget target) {
        model.setObject(new UsuarioSetorVigilancia());
        autoCompleteConsultaUsuario.limpar(target);
        target.focusComponent(autoCompleteConsultaUsuario);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("usuariosSetor");
    }
    
    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (setorVigilancia == null) {
            target.focusComponent(autoCompleteConsultaSetorVigilancia);
            throw new ValidacaoException(BundleManager.getString("informeSetorVigilancia"));
        }
        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarUsuariosSetorVigilancia(lstUsuSetor,setorVigilancia);
        limparTudo(target);
    }
    
    private void carregaRegistros(SetorVigilancia object) {
            this.lstUsuSetor = LoadManager.getInstance(UsuarioSetorVigilancia.class)
                    .addProperties(new HQLProperties(UsuarioSetorVigilancia.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioSetorVigilancia.PROP_SETOR_VIGILANCIA, object))
                    .start().getList();
    }
    
    private void limparTudo(AjaxRequestTarget target){
        limpar(target);
        this.lstUsuSetor.clear();
        table.update(target);
        autoCompleteConsultaSetorVigilancia.limpar(target);
        setEnable(target, false);
        target.focusComponent(autoCompleteConsultaSetorVigilancia.getTxtDescricao().getTextField());
    }
    
    public void setEnable(AjaxRequestTarget target, boolean enable) {
        container.setEnabled(enable);
        btnSalvar.setEnabled(enable);
        target.add(autoCompleteConsultaUsuario);
        target.add(btnAdicionar);
        target.add(btnSalvar);
    }
}
