package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.googlemaps.GoogleMapsConf;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.vigilancia.dto.LocalizacaoRegistroAgravoDTOParam;
import br.com.ksisolucoes.bo.enderecocoordenadas.LatitudeLongitudeEndereco;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.wicketstuff.gmap.GMap;
import org.wicketstuff.gmap.api.GLatLng;
import org.wicketstuff.gmap.api.GMarker;
import org.wicketstuff.gmap.api.GMarkerOptions;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 891
 */
@Private
public class ConsultaGeorreferenciamentoRegistroAgravoPage extends BasePage {

    private LocalizacaoRegistroAgravoDTOParam param;
    private AbstractAjaxButton btnFiltrar;
    private final GMap map;
    private Form<LocalizacaoRegistroAgravoDTOParam> form;

    LatitudeLongitudeEndereco latitudeLongitudeEndereco = new LatitudeLongitudeEndereco(SessaoAplicacaoImp.getInstance().getEmpresa().getCidade());

    public ConsultaGeorreferenciamentoRegistroAgravoPage() {
        this.map = new GMap("map", GoogleMapsConf.MAP_API_KEY, "https");
        init();
    }

    private void init() {
        LocalizacaoRegistroAgravoDTOParam proxy = on(LocalizacaoRegistroAgravoDTOParam.class);
        getForm().add(new AutoCompleteConsultaCid(path(proxy.getCid()), true));
        getForm().add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        getForm().add(getDropDownStatus(path(proxy.getStatus())));

        getForm().add(btnFiltrar = new AbstractAjaxButton("btnFiltrar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                filtrar(target);
            }
        });

        getForm().add(map);
        map.setOutputMarkupId(true);
        map.setStreetViewControlEnabled(true);
        map.setScaleControlEnabled(true);
        map.setScrollWheelZoomEnabled(true);
        map.setDraggingEnabled(true);
        map.setPanControlEnabled(true);
        map.setDoubleClickZoomEnabled(false);
        map.setMapTypeControlEnabled(true);

        GLatLng center;

        if (latitudeLongitudeEndereco.getLatitude() != 0.0 && latitudeLongitudeEndereco.getLongitude() != 0.0) {
            center = new GLatLng(latitudeLongitudeEndereco.getLatitude(), latitudeLongitudeEndereco.getLongitude());
        } else {
            center = new GLatLng(-28.7282759, -49.3674942); // loc. de Criciúma
        }
        map.setCenter(center);
        map.setZoom(10);


        add(getForm());
    }

    private Form getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<LocalizacaoRegistroAgravoDTOParam>(param = new LocalizacaoRegistroAgravoDTOParam()));
        }
        return form;
    }

    public DropDown getDropDownStatus(String id) {
        DropDown dropDownStatus = new DropDown(id);

        dropDownStatus.addChoice(null, bundle("todos"));
        dropDownStatus.addChoice(RegistroAgravo.Status.PENDENTE.value(), RegistroAgravo.Status.PENDENTE.descricao());
        dropDownStatus.addChoice(RegistroAgravo.Status.MONITORAMENTO.value(), RegistroAgravo.Status.MONITORAMENTO.descricao());
        dropDownStatus.addChoice(RegistroAgravo.Status.CONCLUIDO.value(), RegistroAgravo.Status.CONCLUIDO.descricao());

        dropDownStatus.setOutputMarkupId(true);
        dropDownStatus.addAjaxUpdateValue();

        return dropDownStatus;
    }

    public void filtrar(AjaxRequestTarget target) throws ValidacaoException {
        map.removeAllOverlays();
        List<RegistroAgravo> registroAgravoList = null;
        try {
            param.setTipoEndereco(LocalizacaoRegistroAgravoDTOParam.TipoEndereco.ENDERECO_AGRAVO.value());
            registroAgravoList = BOFactoryWicket.getBO(VigilanciaFacade.class).consultarLocalizacaoRegistroAgravo(param);
        } catch (ValidacaoException | DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        if (CollectionUtils.isNotNullEmpty(registroAgravoList)) {
            if (registroAgravoList.size() >= 3000) {
                throw new ValidacaoException("Sua pesquisa retornou mais de 3000 registros, redefina os critérios de pesquisa.");
            }

            filtrarRegistroAgravo(registroAgravoList);

        } else {
            info(target, "Sua pesquisa não retornou nenhum registro, redefina os critérios de pesquisa ou execute o processo de atualização dos dados - Agendador de Processos (419)");
        }
        target.add(map);
    }

    public void criarPonto(GLatLng latlng, String infoPonto) {
        GMarkerOptions options = new GMarkerOptions(map, latlng, infoPonto);
        options.bouncy(true);
        options.draggable(true);
        options.autoPan(true);
        options.clickable(true);
        map.addOverlay(new GMarker(options));
    }

    public StringBuilder criarInfoPonto(StringBuilder sb, RegistroAgravo ra) {
        sb.append("Paciente: ");
        sb.append(ra.getUsuarioCadsus().getNomeSocial());
        sb.append("\n");
        sb.append("CID: ");
        sb.append(ra.getCid().getDescricaoFormatado());
        sb.append("\n");
        sb.append("Data Registro: ");
        sb.append(Data.formatar(ra.getDataRegistro()));
        sb.append("\n");
        sb.append("Situação: ");
        sb.append(ra.getDescricaoStatus());
        sb.append("\n\n");

        return sb;
    }

    public void filtrarRegistroAgravo(List<RegistroAgravo> registroAgravoList) {
        Group<RegistroAgravo> groupByLatitude = Lambda.group(registroAgravoList, by(on(RegistroAgravo.class).getLatitude()));
        for (Group<RegistroAgravo> grupoRegistroAgravo : groupByLatitude.subgroups()) {
            StringBuilder sb = new StringBuilder();
            GLatLng latlng = null;
            for (RegistroAgravo ra : grupoRegistroAgravo.findAll()) {
                latlng = new GLatLng(ra.getLatitude(), ra.getLongitude());

                criarInfoPonto(sb, ra);
            }
            criarPonto(latlng, sb.toString());
        }
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaGeorreferenciamentoRegistroAgravo");
    }
}
