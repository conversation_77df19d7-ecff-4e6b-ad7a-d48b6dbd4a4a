package br.com.celk.view.vigilancia.questionariopopulacaocaesgatos;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioImpressaoPopulacaoCaesGatosDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaPopulacaoCaesGatosDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatos;
import br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatosTipoAnimal;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 1175
 */
public class ConsultaPopulacaoCaesGatosPage extends ConsultaPage<PopulacaoCaesGatos, List<BuilderQueryCustom.QueryParameter>> {

    private QueryConsultaPopulacaoCaesGatosDTOParam param;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteVigilanciaEndereco;
    private Profissional profissional;
    private String rua;
    private VigilanciaEndereco endereco;
    private Long codigoFamilia;
    private DropDown<String> dropDownSexo;
    private DropDown<Long> dropDownTipoAnimal;
    private String sexo;
    private Long tipoAnimal;
    private IPagerProvider<RelatorioImpressaoPopulacaoCaesGatosDTO, QueryConsultaPopulacaoCaesGatosDTOParam> pagerProvider;

    public ConsultaPopulacaoCaesGatosPage() {
        super();
    }

    public ConsultaPopulacaoCaesGatosPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional"));

        form.add(new InputField<String>("codigoFamilia", new PropertyModel<>(this, "codigoFamilia")));
        form.add(autoCompleteVigilanciaEndereco = new AutoCompleteConsultaVigilanciaEndereco("endereco"));

        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        PopulacaoCaesGatos proxy = on(PopulacaoCaesGatos.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("profissional"), proxy.getCodigoProfissional().getNome()));
        columns.add(createSortableColumn(bundle("endereco"), proxy.getCodigoEndereco().getEnderecoFormatado()));
        columns.add(createSortableColumn(bundle("numeroFamilia"), proxy.getCodigoFamiliaFormatado()));
        columns.add(createSortableColumn(bundle("microarea"), proxy.getCodigoMicroareaFormatado()));
        columns.add(createSortableColumn(bundle("observacao"), proxy.getObservacao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<PopulacaoCaesGatos>() {
            @Override
            public void customizeColumn(final PopulacaoCaesGatos rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<PopulacaoCaesGatos>() {
                    @Override
                    public void action(AjaxRequestTarget target, PopulacaoCaesGatos modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroQuestionarioPopulacaoCaesGatosPage(rowObject));
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<PopulacaoCaesGatos>() {
                    @Override
                    public void action(AjaxRequestTarget target, PopulacaoCaesGatos modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroQuestionarioPopulacaoCaesGatosPage(rowObject, false));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<PopulacaoCaesGatos>() {
                    @Override
                    public void action(AjaxRequestTarget target, PopulacaoCaesGatos modelObject) throws ValidacaoException, DAOException {
                        List<PopulacaoCaesGatosTipoAnimal> pcgt = LoadManager.getInstance(PopulacaoCaesGatosTipoAnimal.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(PopulacaoCaesGatosTipoAnimal.PROP_CODIGO_POPULACAO_CAES_GATOS, rowObject.getCodigo()))
                                .start()
                                .getList();

                        if (CollectionUtils.isNotNullEmpty(pcgt)) {
                            for (PopulacaoCaesGatosTipoAnimal populacaoCaesGatosTipoAnimal : pcgt) {
                                BOFactory.delete(populacaoCaesGatosTipoAnimal);
                            }
                        }
                        BOFactoryWicket.delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<PopulacaoCaesGatos>() {
                    @Override
                    public DataReport action(PopulacaoCaesGatos modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoQuestionarioPopulacaoCaesGatos(modelObject);
                    }
                });
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter(){
            @Override
            public Class getClassConsulta() {
                return PopulacaoCaesGatos.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(PopulacaoCaesGatos.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO_ENDERECO, VigilanciaEndereco.PROP_CODIGO),
                                VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO_ENDERECO, VigilanciaEndereco.PROP_LOGRADOURO),
                                VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO_ENDERECO, VigilanciaEndereco.PROP_BAIRRO),
                                VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO_ENDERECO, VigilanciaEndereco.PROP_CEP),
                                VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO_PROFISSIONAL, Profissional.PROP_CODIGO),
                                VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO_PROFISSIONAL, Profissional.PROP_NOME)
                                ,});
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO), false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {

        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (profissional != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO_PROFISSIONAL), BuilderQueryCustom.QueryParameter.IGUAL, profissional));
        }
        if (endereco != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO_ENDERECO), BuilderQueryCustom.QueryParameter.IGUAL, endereco));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PopulacaoCaesGatos.PROP_CODIGO_FAMILIA), BuilderQueryCustom.QueryParameter.IGUAL, codigoFamilia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PopulacaoCaesGatosTipoAnimal.PROP_TIPO_ANIMAL), BuilderQueryCustom.QueryParameter.IGUAL, tipoAnimal));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PopulacaoCaesGatosTipoAnimal.PROP_SEXO_ANIMAL), BuilderQueryCustom.QueryParameter.IGUAL, sexo));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroQuestionarioPopulacaoCaesGatosPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaQuestionarioCaesGatos");
    }
}