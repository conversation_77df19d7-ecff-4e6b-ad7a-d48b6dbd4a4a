package br.com.celk.view.vigilancia.atividadesecundaria;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.atividadesecundaria.AtividadeSecundaria;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 671
 */
public class ConsultaAtividadeSecundariaPage extends ConsultaPage<AtividadeSecundaria, List<BuilderQueryCustom.QueryParameter>> {
    
    private String descricao;

    public ConsultaAtividadeSecundariaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));

        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        AtividadeSecundaria proxy = on(AtividadeSecundaria.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("descricao"), proxy.getDescricao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AtividadeSecundaria>() {
            @Override
            public void customizeColumn(final AtividadeSecundaria rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<AtividadeSecundaria>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtividadeSecundaria modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAtividadeSecundariaPage(rowObject, false, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtividadeSecundaria>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtividadeSecundaria modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return AtividadeSecundaria.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(AtividadeSecundaria.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeSecundaria.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAtividadeSecundariaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaAtividadeSecundaria");
    }
}
