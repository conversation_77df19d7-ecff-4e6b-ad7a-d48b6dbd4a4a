package br.com.celk.view.vigilancia.dengue.registrodiarioantivetorial;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.dengue.autocomplete.AutoCompleteConsultaDengueLocalidade;
import br.com.celk.view.vigilancia.dengue.autocomplete.AutoCompleteConsultaDengueAtividade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueAtividade;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueCiclo;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade;
import br.com.ksisolucoes.vo.vigilancia.dengue.RegistroDiarioAntivetorial;
import br.com.ksisolucoes.vo.vigilancia.dengue.RegistroDiarioAntivetorialPesquisa;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 649
 */
public class ConsultaRegistroDiarioAntivetorialPage extends ConsultaPage<RegistroDiarioAntivetorial, List<BuilderQueryCustom.QueryParameter>> {

    private DengueLocalidade localidade;
    private DengueAtividade atividade;
    private Profissional profissional;
    private DengueCiclo ciclo;
    private DatePeriod periodoAtividade;
    private Long resultadoPendente;
    private DlgMotivoObject<RegistroDiarioAntivetorial> dlgMotivo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaDengueLocalidade("localidade"));
        form.add(new AutoCompleteConsultaDengueAtividade("atividade"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(getDropDownCiclo());
        form.add(new PnlDatePeriod("periodoAtividade"));
        form.add(DropDownUtil.getSimNaoLongDropDown("resultadoPendente", true, false));

        add(form);
        setExibeExpandir(true);
    }

    private DropDown getDropDownCiclo() {
        List<DengueCiclo> ciclos = LoadManager.getInstance(DengueCiclo.class)
                .addProperties(new HQLProperties(DengueCiclo.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(DengueCiclo.PROP_DATA_SEMANA_INICIAL))
                .start().getList();

        DropDown<DengueCiclo> dropDown = new DropDown("ciclo");
        dropDown.addChoice(null, "");
        for (DengueCiclo ciclo : ciclos) {
            dropDown.addChoice(ciclo, ciclo.getDescricaoPeriodoCiclo());
        }
        return dropDown;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        RegistroDiarioAntivetorial proxy = on(RegistroDiarioAntivetorial.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("ciclo"), proxy.getCiclo().getDataSemanaInicial(), proxy.getCiclo().getDescricaoPeriodoCiclo()));
        columns.add(createSortableColumn(bundle("dataAtividade"), proxy.getDataAtividade()));
        columns.add(createSortableColumn(bundle("atividade"), proxy.getAtividade().getDescricao()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("localidade"), proxy.getLocalidade().getLocalidade()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<RegistroDiarioAntivetorial>() {
            @Override
            public void customizeColumn(final RegistroDiarioAntivetorial rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<RegistroDiarioAntivetorial>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroDiarioAntivetorial modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroDiarioAntivetorialStep1Page(rowObject));
                    }
                }).setEnabled(RegistroDiarioAntivetorial.Status.NORMAL.value().equals(rowObject.getStatus()));

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<RegistroDiarioAntivetorial>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroDiarioAntivetorial modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesRegistroDiarioAntivetorialPage(rowObject, false));
                    }
                }).setIcon(Icon.LABORATORY)
                        .setTitleBundleKey("resultadoLaboratorio")
                        .setEnabled(RegistroDiarioAntivetorial.Status.NORMAL.value().equals(rowObject.getStatus()));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<RegistroDiarioAntivetorial>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroDiarioAntivetorial modelObject) throws ValidacaoException, DAOException {
                        viewDlgMotivo(target, rowObject);
                    }
                }).setEnabled(RegistroDiarioAntivetorial.Status.NORMAL.value().equals(rowObject.getStatus()));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<RegistroDiarioAntivetorial>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroDiarioAntivetorial modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesRegistroDiarioAntivetorialPage(rowObject, true));
                    }
                }).setTitleBundleKey("detalhes");
            }
        };
    }

    private void viewDlgMotivo(AjaxRequestTarget target, RegistroDiarioAntivetorial registro) {
        if (dlgMotivo == null) {
            addModal(target, dlgMotivo = new DlgMotivoObject<RegistroDiarioAntivetorial>(newModalId(), bundle("motivoCancelamento")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, RegistroDiarioAntivetorial object) throws ValidacaoException, DAOException {
                    object.setMotivoCancelamento(motivo);
                    object.setStatus(RegistroDiarioAntivetorial.Status.CANCELADO.value());
                    BOFactoryWicket.save(object);
                    getPageableTable().update(target);
                }

                @Override
                public Long getMaxLengthMotivo() {
                    return 100l;
                }
            });
        }

        dlgMotivo.setObject(registro);
        dlgMotivo.show(target);
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return RegistroDiarioAntivetorial.class;
            }

            @Override
            public String[] getProperties() {
                RegistroDiarioAntivetorial proxy = on(RegistroDiarioAntivetorial.class);
                return VOUtils.mergeProperties(
                        new HQLProperties(RegistroDiarioAntivetorial.class).getProperties(),
                        new String[]{
                            path(proxy.getCiclo().getDataSemanaInicial()),
                            path(proxy.getCiclo().getDataSemanaFinal()),
                            path(proxy.getAtividade().getDescricao()),
                            path(proxy.getAtividade().getFlagInformaPontoEstrategico()),
                            path(proxy.getAtividade().getFlagLevantamentoIndice()),
                            path(proxy.getProfissional().getNome()),
                            path(proxy.getLocalidade().getLocalidade())
                        }
                );
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(RegistroDiarioAntivetorial.PROP_DATA_CADASTRO, true);
            }

            @Override
            public List getInterceptors() {
                return Arrays.asList(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        if (resultadoPendente != null) {
                            StringBuilder where = new StringBuilder();
                            where.append("select 1 ");
                            where.append("from RegistroDiarioAntivetorialPesquisa rdap ");
                            where.append("where rdap.registroDiarioAntivetorial.codigo = ").append(alias).append(".codigo and rdap.status = ");
                            if (RepositoryComponentDefault.SIM_LONG.equals(resultadoPendente)) {
                                where.append(RegistroDiarioAntivetorialPesquisa.Status.PENDENTE.value());
                            } else {
                                where.append(RegistroDiarioAntivetorialPesquisa.Status.CONCLUIDO.value());
                            }
                            where.insert(0, "exists(").append(")");
                            hql.addToWhereWhithAnd(where.toString());
                        }
                    }
                });
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();

        RegistroDiarioAntivetorial proxy = on(RegistroDiarioAntivetorial.class);
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getLocalidade()), localidade));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAtividade()), atividade));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getProfissional()), profissional));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getCiclo()), ciclo));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getDataAtividade()), periodoAtividade));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRegistroDiarioAntivetorialStep1Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaRegistroDiarioAntivetorial");
    }

}
