package br.com.celk.view.materiais.pedidotransferencia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaTipoVacina;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.pedido.interfaces.dto.RelacaoPedidoTransferenciaDTOParam;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItem;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 117
 */
@Private
public class RelacaoPedidoTransferenciaPage extends RelatorioPage<RelacaoPedidoTransferenciaDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaOrigem;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaDestino;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaDeposito autoCompleteConsultaDeposito;
    private AutoCompleteConsultaCentroCusto autoCompleteConsultaCentroCusto;
    private AutoCompleteConsultaTipoVacina autoCompleteConsultaTipoVacina;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresaOrigem = new AutoCompleteConsultaEmpresa("unidadeOrigem"));
        form.add(autoCompleteConsultaEmpresaDestino = new AutoCompleteConsultaEmpresa("unidadeDestino"));
        form.add(autoCompleteConsultaTipoVacina = new AutoCompleteConsultaTipoVacina("vacina"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(autoCompleteConsultaDeposito = new AutoCompleteConsultaDeposito("deposito"));
        form.add(autoCompleteConsultaCentroCusto = new AutoCompleteConsultaCentroCusto("centroCusto"));
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownTipoRelatorio());
        form.add(getDropDownSituacao());
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getDropDownTipoPeriodo());

        autoCompleteConsultaEmpresaDestino.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresaOrigem.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaCentroCusto.setMultiplaSelecao(true);
        autoCompleteConsultaDeposito.setMultiplaSelecao(true);

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresaDestino.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        
        form.add(DropDownUtil.getTipoRelatorioDropDown("tipoArquivo"));
    }

    @Override
    public Class getDTOParamClass() {
        return RelacaoPedidoTransferenciaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelacaoPedidoTransferenciaDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioRelacaoPedidoTransferencia(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoPedidoTransferencia");
    }

    public DropDown getDropDownFormaApresentacao() {
        DropDown dropDown = new DropDown("formaApresentacao");
        dropDown.addChoice(PedidoTransferencia.PROP_EMPRESA_ORIGEM, BundleManager.getString("unidadeOrigem"));
        dropDown.addChoice(PedidoTransferencia.PROP_EMPRESA_DESTINO, BundleManager.getString("unidadeDestino"));
        dropDown.addChoice(PedidoTransferenciaItem.PROP_CENTRO_CUSTO, BundleManager.getString("centroCusto"));

        return dropDown;
    }

    public DropDown getDropDownTipoRelatorio() {
        DropDown dropDown = new DropDown("tipoRelatorio");
        dropDown.addChoice(ReportProperties.RESUMIDO, BundleManager.getString("resumido"));
        dropDown.addChoice(ReportProperties.DETALHADO, BundleManager.getString("detalhado"));

        return dropDown;
    }

    public DropDown getDropDownSituacao() {
        DropDown dropDown = new DropDown("situacao");
        dropDown.addChoice(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.ABERTO.value(), BundleManager.getString("aberto"));
        dropDown.addChoice(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.PROCESSADO.value(), BundleManager.getString("processado"));
        dropDown.addChoice(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.RECEBIDO.value(), BundleManager.getString("recebido"));
        dropDown.addChoice(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.CANCELADO.value(), BundleManager.getString("cancelado"));
        dropDown.addChoice(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.SEM_ESTOQUE.value(), BundleManager.getString("semEstoque"));
        dropDown.addChoice(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.NAO_APROVADO.value(), BundleManager.getString("naoAprovado"));
        dropDown.addChoice(null, BundleManager.getString("todos"));

        return dropDown;
    }

    public DropDown getDropDownTipoPeriodo() {
        DropDown dropDown = new DropDown("tipoPeriodo");
        dropDown.addChoice(PedidoTransferencia.PROP_DATA_PEDIDO, BundleManager.getString("pedido"));
        dropDown.addChoice(PedidoTransferencia.PROP_DATA_RECEBIMENTO, BundleManager.getString("recebimento"));

        return dropDown;
    }
}
