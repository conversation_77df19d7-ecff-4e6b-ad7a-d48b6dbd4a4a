package br.com.celk.view.vigilancia.rotinas.autoIntimacao;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.celk.view.vigilancia.requerimentos.CadastroRelatorioInspecaoPage;
import br.com.celk.view.vigilancia.requerimentos.ConsultaRelatorioInspecaoPage;
import br.com.celk.view.vigilancia.requerimentovigilancia.PendenciasFiscalPage;
import br.com.celk.view.vigilancia.requerimentovigilancia.dialog.DlgCancelamentoFinalizacaoRequerimentoVigilancia;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.dialog.DlgAutoIntimacaoReceber;
import br.com.celk.view.vigilancia.rotinas.autoinfracao.dlg.DlgConcluirIntimacao;
import br.com.celk.view.vigilancia.rotinas.automulta.CadastroAutoMultaPage;
import br.com.celk.view.vigilancia.rotinas.automulta.ConsultaAutoMultaPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.util.string.StringValue;

import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 617
 */
public class ConsultaAutoIntimacaoPage extends ConsultaPage<ConsultaAutoIntimacaoDTO, QueryConsultaAutoIntimacaoDTOParam> {

    private PageParameters parameters;

    private QueryConsultaAutoIntimacaoDTOParam param;
    private Class returnClass;

    private AbstractAjaxButton btnVoltar;
    private AbstractAjaxButton btnNovo;

    private DlgConcluirIntimacao dlgConcluirIntimacao;
    private DlgAutoIntimacaoReceber dlgAutoIntimacaoReceber;
    private DlgCancelamentoFinalizacaoRequerimentoVigilancia dlgFinalizacaoRequerimentoVigilancia;
    private DropDown cbxSituacao;
    private boolean autoSubsistenteMulta;
    private DropDown<Long> dropDownExibirOutrosFiscais;

    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaEndereco;

    public ConsultaAutoIntimacaoPage() {
        super();
    }

    public ConsultaAutoIntimacaoPage(PageParameters parameters) {
        super(parameters);
//        setParameters(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(param = new QueryConsultaAutoIntimacaoDTOParam()));
        QueryConsultaAutoIntimacaoDTOParam proxy = on(QueryConsultaAutoIntimacaoDTOParam.class);

        form.add(new InputField(path(proxy.getNumeroAutoIntimacao())));
        form.add(new InputField(path(proxy.getAutuado())));
        form.add(autoCompleteConsultaEndereco = new AutoCompleteConsultaVigilanciaEndereco(path(proxy.getVigilanciaEndereco())));
        form.add(new PnlDatePeriod(path(proxy.getPeriodo())));
        form.add(cbxSituacao = DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), AutoIntimacao.Status.values(), true));
        cbxSituacao.addAjaxUpdateValue();
        cbxSituacao.setOutputMarkupId(true);
        form.add(DropDownUtil.getIEnumDropDown("tipo", AutoIntimacao.Tipo.values(), true));

        form.add(dropDownExibirOutrosFiscais = DropDownUtil.getNaoSimLongDropDown("exibirOutrosFiscais"));
        dropDownExibirOutrosFiscais.addAjaxUpdateValue();
        dropDownExibirOutrosFiscais.setOutputMarkupPlaceholderTag(true);


        setExibeExpandir(true);
        getLinkNovo().setVisible(false);

        getControls().add(btnVoltar = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar();
            }
        });

        btnVoltar.setVisible(false);
        btnVoltar.add(new AttributeModifier("type", "button"));
        btnVoltar.add(new AttributeModifier("class", "arrow-left"));
        btnVoltar.add(new AttributeModifier("value", bundle("voltar")));

        getControls().add(btnNovo = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                CadastroAutoIntimacaoPage cadastroAutoIntimacaoPage = new CadastroAutoIntimacaoPage(getPageParameters());
                setResponsePage(cadastroAutoIntimacaoPage);
            }
        });

        btnNovo.add(new AttributeModifier("class", "doc-new"));
        btnNovo.add(new AttributeModifier("value", bundle("novo")));
        btnNovo.add(new AttributeModifier("style", "margin-left: 5px;"));

        setParameters(this.getPageParameters());

        param.setExibirOutrosFiscais(RepositoryComponentDefault.NAO_LONG);
    }

    private void voltar() {
        setResponsePage(returnClass);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ConsultaAutoIntimacaoDTO proxy = on(ConsultaAutoIntimacaoDTO.class);
        columns.add(getActionColumn());
        columns.add(createColumn(bundle("numeroIntimacaoAbv"), proxy.getAutoIntimacao().getNumeroFormatado()));
        columns.add(createSortableColumn(bundle("numeroRoteiroInspecaoAbv"), proxy.getAutoIntimacao().getRegistroInspecao().getCodigo()));
        columns.add(createSortableColumn(bundle("dataIntimacao"), proxy.getAutoIntimacao().getDataIntimacao()));
        columns.add(createSortableColumn(bundle("autuado"), proxy.getAutoIntimacao().getAutuado()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getAutoIntimacao().getSituacao(), proxy.getAutoIntimacao().getStatusDescricao()));
        columns.add(createSortableColumn(bundle("tipo"), proxy.getAutoIntimacao().getTipo(), proxy.getAutoIntimacao().getTipoDescricao()));
        columns.add(createColumn(bundle("prazoFinal"), proxy.getPrazoFinalExigencias()));
        columns.add(createColumn(bundle("qtdExigencias"), proxy.getQuantidadeExigencias()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ConsultaAutoIntimacaoDTO>() {

            @Override
            public void customizeColumn(final ConsultaAutoIntimacaoDTO rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ConsultaAutoIntimacaoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaAutoIntimacaoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAutoIntimacaoPage(modelObject.getAutoIntimacao(), modelObject.getAutoIntimacao().getAutoInfracao(), getPageParameters()));
                    }
                }).setEnabled(enableAlterarIntimacao(rowObject));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<ConsultaAutoIntimacaoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaAutoIntimacaoDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).removerAutoIntimacao(modelObject.getAutoIntimacao());
                        getPageableTable().populate();
                        getPageableTable().update(target);
                    }
                }).setEnabled(enableAlterarIntimacao(rowObject));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ConsultaAutoIntimacaoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaAutoIntimacaoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAutoIntimacaoPage(modelObject.getAutoIntimacao(), true, getPageParameters()));
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<ConsultaAutoIntimacaoDTO>() {
                    @Override
                    public DataReport action(ConsultaAutoIntimacaoDTO modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoIntimacao(modelObject.getAutoIntimacao().getCodigo(), modelObject.getAutoIntimacao().getNumeroFormatado());
                    }
                });

                ModelActionLinkPanel acaoReceber = addAction(ActionType.ANEXO, rowObject, new IModelAction<ConsultaAutoIntimacaoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaAutoIntimacaoDTO modelObject) throws ValidacaoException, DAOException {
                        addModal(target, dlgAutoIntimacaoReceber = new DlgAutoIntimacaoReceber(newModalId(), modelObject.getAutoIntimacao()) {
                            @Override
                            public void onRecebimentoAuto(AjaxRequestTarget target, AutoIntimacao object) throws DAOException, ValidacaoException {
                                salvarDataCumprimentoPrazo(target, object);
                            }
                        });
                        dlgAutoIntimacaoReceber.show(target);
                    }
                });
                acaoReceber.setTitleBundleKey("receberAuto");
                acaoReceber.setEnabled(enableReceberIntimacao(rowObject));
                acaoReceber.setIcon(Icon.CALENDAR_EDIT);

                ModelActionLinkPanel acaoConcluir = addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<ConsultaAutoIntimacaoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaAutoIntimacaoDTO modelObject) throws ValidacaoException, DAOException {
                        showDialogConcluirAuto(target, modelObject.getAutoIntimacao());
                    }
                });

                acaoConcluir.setQuestionDialogBundleKey(null);
                acaoConcluir.setEnabled(enableAlterarIntimacao(rowObject));
                acaoConcluir.setTitleBundleKey("finalizarArquivar");

                ModelActionLinkPanel acaoRelatorioInspecao = addAction(ActionType.LAUDAR, rowObject, new IModelAction<ConsultaAutoIntimacaoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaAutoIntimacaoDTO modelObject) throws ValidacaoException, DAOException {
                        CadastroRelatorioInspecaoPage relatorioClass = new CadastroRelatorioInspecaoPage();
                        relatorioClass.setClassReturn(ConsultaRelatorioInspecaoPage.class);
                        relatorioClass.setAutoIntimacao(modelObject.getAutoIntimacao());
                        relatorioClass.buscarAutoIntimacao(target, modelObject.getAutoIntimacao().getCodigo());
                        setResponsePage(relatorioClass);
                    }
                });
                acaoRelatorioInspecao.setTitleBundleKey("tituloNumRelatorioInspecao");
                acaoRelatorioInspecao.setEnabled(!existsRelatorioInspecao(rowObject.getAutoIntimacao()) && isProfissionalAuto(rowObject));
                acaoRelatorioInspecao.setVisible(new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaRelatorioInspecaoPage.class.getName()));

                ModelActionLinkPanel acaoAutoMulta = addAction(ActionType.MONEY, rowObject, new IModelAction<ConsultaAutoIntimacaoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaAutoIntimacaoDTO modelObject) throws ValidacaoException, DAOException {
                        CadastroAutoMultaPage autoMultaPage = new CadastroAutoMultaPage() {
                            @Override
                            public Class getResponsePage() {
                                return ConsultaAutoIntimacaoPage.class;
                            }
                        };

                        autoMultaPage.instanceFromIntimacao(target, modelObject.getAutoIntimacao());
                        setResponsePage(autoMultaPage);
                    }
                });
                acaoAutoMulta.setTitleBundleKey("autoDeMulta");
                acaoAutoMulta.setEnabled(!existsAutoMulta(rowObject.getAutoIntimacao()) && isProfissionalAuto(rowObject));
                acaoAutoMulta.setVisible(isActionPermitted(Permissions.AUTO_MULTA) && new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaAutoMultaPage.class.getName()) && !isAutoSubsistenteMulta(rowObject));
            }
        };
    }

    private boolean enableAlterarIntimacao(ConsultaAutoIntimacaoDTO rowObject) {
        boolean pendente = !AutoIntimacao.Status.CONCLUIDO.value().equals(rowObject.getAutoIntimacao().getSituacao());
        if(pendente) {
            Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
            if(profissional != null && profissional.getCodigo() != null) {
                return AutosHelper.isFiscalIntimacao(rowObject.getAutoIntimacao(), profissional);
            }
        }
        return pendente;
    }

    private boolean enableReceberIntimacao(ConsultaAutoIntimacaoDTO rowObject) {
        boolean aguardandoRecebimento = AutoIntimacao.Status.AGUARDANDO_RECEBIMENTO.value().equals(rowObject.getAutoIntimacao().getSituacao());
        if(aguardandoRecebimento) {
            Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
            if(profissional != null && profissional.getCodigo() != null) {
                return AutosHelper.isFiscalIntimacao(rowObject.getAutoIntimacao(), profissional);
            }
        }
        return aguardandoRecebimento;
    }

    private boolean isProfissionalAuto(ConsultaAutoIntimacaoDTO rowObject) {
        Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if(profissional != null && profissional.getCodigo() != null) {
            return AutosHelper.isFiscalIntimacao(rowObject.getAutoIntimacao(), profissional);
        } else {
            return SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster();
        }
    }

    private void salvarDataCumprimentoPrazo(AjaxRequestTarget target, AutoIntimacao object) throws DAOException, ValidacaoException {
        SalvarAutoIntimacaoDTO salvarAutoIntimacaoDTO = AutosHelper.montarDadosRecebimentoIntimacao(object);
        object = (AutoIntimacao) BOFactoryWicket.getBO(VigilanciaFacade.class).salvarAutoIntimacao(salvarAutoIntimacaoDTO);
        getSession().getFeedbackMessages().info(this, bundle("msgDataRecebimentoSalvaComSucesso"));
        getPageableTable().update(target);
    }



    private boolean existsRelatorioInspecao(AutoIntimacao rowObject) {
        return LoadManager.getInstance(RelatorioInspecao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(RelatorioInspecao.PROP_AUTO_INTIMACAO, rowObject))
                .exists();
    }

    private boolean existsAutoMulta(AutoIntimacao rowObject) {
        return LoadManager.getInstance(AutoMulta.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AutoMulta.PROP_AUTO_INTIMACAO, rowObject))
                .exists();
    }

    private void showDialogConcluirAuto(AjaxRequestTarget target, AutoIntimacao autoIntimacao) {
        if (autoIntimacao.getRegistroInspecao() != null && autoIntimacao.getRegistroInspecao().getRequerimentoVigilancia() != null) {
            if (dlgFinalizacaoRequerimentoVigilancia == null) {
                addModal(target, dlgFinalizacaoRequerimentoVigilancia = new DlgCancelamentoFinalizacaoRequerimentoVigilancia(newModalId(), BundleManager.getString("concluirIntimacao")) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaCancelamentoFinalizacaoDTO finalizarRequerimentoDTO) throws ValidacaoException, DAOException {
                        AutoIntimacao autoIntimacao = (AutoIntimacao) getObject();

                        FinalizarAutoIntimacaoDTO dto = new FinalizarAutoIntimacaoDTO();
                        dto.setAutoIntimacao(autoIntimacao);

                        if (finalizarRequerimentoDTO.getSituacao() == null) {
                            finalizarRequerimentoDTO.setSituacao(RequerimentoVigilancia.Situacao.FINALIZADO);
                        }

                        dto.setFinalizacaoRequerimentoDTO(finalizarRequerimentoDTO);
                        dto.getAutoIntimacao().setDataConclusao(finalizarRequerimentoDTO.getDataFinalizacao());
                        BOFactoryWicket.getBO(VigilanciaFacade.class).finalizarAutoIntimacao(dto);

                        getPageableTable().populate();
                        getPageableTable().update(target);
                    }
                });
            }

            RequerimentoVigilancia rv = LoadManager.getInstance(RequerimentoVigilancia.class)
                    .setId(autoIntimacao.getRegistroInspecao().getRequerimentoVigilancia())
                    .start().getVO();

            dlgFinalizacaoRequerimentoVigilancia.setObject(autoIntimacao);
            dlgFinalizacaoRequerimentoVigilancia.show(target, rv, true);
        } else {
            if (dlgConcluirIntimacao == null) {
                addModal(target, dlgConcluirIntimacao = new DlgConcluirIntimacao(newModalId()) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target, AutoIntimacao autoIntimacao, Date dataConclusao) throws DAOException, ValidacaoException {
                        FinalizarAutoIntimacaoDTO dto = new FinalizarAutoIntimacaoDTO();
                        autoIntimacao.setDataConclusao(dataConclusao);
                        dto.setAutoIntimacao(autoIntimacao);
                        BOFactoryWicket.getBO(VigilanciaFacade.class).finalizarAutoIntimacao(dto);

                        getPageableTable().populate();
                        getPageableTable().update(target);
                    }
                });
            }
            dlgConcluirIntimacao.show(target, autoIntimacao);
        }
    }

    private void setParameters(PageParameters pageParameters) {
        this.parameters = pageParameters;

        StringValue situacaoParam = parameters.get("situacao");
        if (!situacaoParam.isEmpty()) {
            param.setSituacao(situacaoParam.toLong());
        }

        StringValue autosVencendoParam = parameters.get("autosVencendo");
        if (!autosVencendoParam.isEmpty()) {
            param.setAutosVencendo(autosVencendoParam.toBoolean());
        }


        StringValue returnClassParam = parameters.get("returnClass");
        if (!returnClassParam.isEmpty()) {
            try {
                returnClass = Class.forName(returnClassParam.toString());
                if (returnClass != null) {
                    btnVoltar.setVisible(true);
                }
                if (returnClass != null && returnClass.getName().equals(PendenciasFiscalPage.class.getName())) {
                    param.setChamadoPorPendenciasFiscais(true);
                    cbxSituacao.setEnabled(false);
                }
            } catch (ClassNotFoundException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
    }

    public boolean isAutoSubsistenteMulta(ConsultaAutoIntimacaoDTO consultaAutoIntimacaoDTO) {
        if(AutoIntimacao.Tipo.SUBSISTENTE.value().equals(consultaAutoIntimacaoDTO.getAutoIntimacao().getTipo())){
            if(consultaAutoIntimacaoDTO.getAutoIntimacao().getAutoMulta() != null && consultaAutoIntimacaoDTO.getAutoIntimacao().getAutoMulta().getCodigo() != null) {
                return true;
            }
        }
        return false;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<ConsultaAutoIntimacaoDTO, QueryConsultaAutoIntimacaoDTOParam>() {
            @Override
            public DataPagingResult<ConsultaAutoIntimacaoDTO> executeQueryPager(DataPaging<QueryConsultaAutoIntimacaoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setAscending(((SingleSortState) getPagerProvider().getSortState()).getSort().isAscending());
                dataPaging.getParam().setPropSort(((SingleSortState<String>) getPagerProvider().getSortState()).getSort().getProperty());
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarAutoIntimacao(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ConsultaAutoIntimacaoDTO.PROP_AUTO_INTIMACAO, AutoIntimacao.PROP_DATA_CADASTRO), false);
            }
        };
    }

    @Override
    public QueryConsultaAutoIntimacaoDTOParam getParameters() {
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAutoIntimacaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaAutoIntimacao");
    }


}
