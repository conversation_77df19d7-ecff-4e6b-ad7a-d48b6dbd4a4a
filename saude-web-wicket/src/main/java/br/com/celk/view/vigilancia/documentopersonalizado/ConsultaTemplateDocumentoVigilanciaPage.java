package br.com.celk.view.vigilancia.documentopersonalizado;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.documentopersonalizado.customize.CustomizeConsultaTemplateDocumentoVigilancia;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.TemplateDocumentoVigilancia;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 * Programa - 248
 */
@Private
public class ConsultaTemplateDocumentoVigilanciaPage extends ConsultaPage<TemplateDocumentoVigilancia, List<BuilderQueryCustom.QueryParameter>> {

    private String nome;

    public ConsultaTemplateDocumentoVigilanciaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("nome"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(TemplateDocumentoVigilancia.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome"), VOUtils.montarPath(TemplateDocumentoVigilancia.PROP_NOME)));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<TemplateDocumentoVigilancia>() {

            @Override
            public void customizeColumn(TemplateDocumentoVigilancia rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<TemplateDocumentoVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, TemplateDocumentoVigilancia modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTemplateDocumentoVigilanciaPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<TemplateDocumentoVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, TemplateDocumentoVigilancia modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(modelObject);
                        getPageableTable().populate(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaTemplateDocumentoVigilancia()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TemplateDocumentoVigilancia.PROP_NOME, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TemplateDocumentoVigilancia.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nome));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTemplateDocumentoVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaModeloDocumentoVigilancia");
    }
}
