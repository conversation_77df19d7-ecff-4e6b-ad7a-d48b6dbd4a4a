package br.com.celk.view.materiais.kitpedidopaciente;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.materiais.interfaces.dto.CadastroKitPedidoPacienteDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.materiais.KitPedidoPaciente;
import br.com.ksisolucoes.vo.materiais.KitPedidoPacienteItem;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 860
 */
@Private
public class CadastroKitPedidoPacientePage extends BasePage {

    private Form<CadastroKitPedidoPacienteDTO> form;
    private CompoundPropertyModel<KitPedidoPacienteItem> kitPedidoPacienteItemModel;
    private WebMarkupContainer containerItensUtilizados;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private LongField txtQuantidade;
    private Table tblKitPedidoPacienteItem;
    private KitPedidoPaciente kitPedidoPaciente;
    private InputField<String> txtDescricao;
    private KitPedidoPacienteItem itemEdicao;

    public CadastroKitPedidoPacientePage() {
        init(true);
    }

    public CadastroKitPedidoPacientePage(KitPedidoPaciente kitPedidoPaciente) {
        this.kitPedidoPaciente = kitPedidoPaciente;
        init(true);
    }

    public CadastroKitPedidoPacientePage(KitPedidoPaciente kitPedidoPaciente, boolean viewOnly) {
        this.kitPedidoPaciente = kitPedidoPaciente;
        init(false);
    }

    private void init(boolean enabled) {
        CadastroKitPedidoPacienteDTO proxy = on(CadastroKitPedidoPacienteDTO.class);

        getForm().add(txtDescricao = (InputField<String>) new RequiredInputField<String>(path(proxy.getKitPedidoPaciente().getDescricao()))
                .setLabel(new Model<String>(bundle("descricao"))).setEnabled(enabled));

        KitPedidoPacienteItem proxyKit = on(KitPedidoPacienteItem.class);

        containerItensUtilizados = new WebMarkupContainer("containerItensUtilizados", kitPedidoPacienteItemModel = new CompoundPropertyModel<KitPedidoPacienteItem>(new KitPedidoPacienteItem()));
        containerItensUtilizados.setOutputMarkupId(true);

        getForm().add(containerItensUtilizados);

        containerItensUtilizados.add(autoCompleteConsultaProduto = (AutoCompleteConsultaProduto) new AutoCompleteConsultaProduto(path(proxyKit.getProduto())).setEnabled(enabled));
        autoCompleteConsultaProduto.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO);
        autoCompleteConsultaProduto.setIncluirInativos(false);
        containerItensUtilizados.add(txtQuantidade = (LongField) new LongField(path(proxyKit.getQuantidade())).setEnabled(enabled));
        txtQuantidade.setVMax(99999999L);
        txtQuantidade.setVMin(0L);

        containerItensUtilizados.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        }).setEnabled(enabled);

        containerItensUtilizados.add(tblKitPedidoPacienteItem = new Table("tblKitPedidoPacienteItem", getColumns(), getCollectionProvider()));
        tblKitPedidoPacienteItem.populate();
        tblKitPedidoPacienteItem.setScrollY("1800");
        tblKitPedidoPacienteItem.setEnabled(enabled);

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }).setVisible(enabled));

        add(getForm());
        carregarItens();
    }


    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        KitPedidoPacienteItem kitPedidoPacienteItem = kitPedidoPacienteItemModel.getObject();

        if (kitPedidoPacienteItem.getProduto() == null) {
            throw new ValidacaoException(bundle("informeProduto"));
        }

        int idx = 0;
        for (int i = 0; i < getForm().getModel().getObject().getKitPedidoPacienteItems().size(); i++) {
            KitPedidoPacienteItem kppi = getForm().getModel().getObject().getKitPedidoPacienteItems().get(i);
            if (kppi.getProduto().equals(kitPedidoPacienteItem.getProduto())
                    && itemEdicao != kppi) {
                throw new ValidacaoException(bundle("produtoJaAdicionado"));
            }

            if (itemEdicao != null && itemEdicao == kppi) {
                idx = i;
            }
        }

        if (itemEdicao != null && CollectionUtils.isNotNullEmpty(getForm().getModel().getObject().getKitPedidoPacienteItems())) {
            getForm().getModel().getObject().getKitPedidoPacienteItems().remove(idx);
            getForm().getModel().getObject().getKitPedidoPacienteItems().add(idx, kitPedidoPacienteItem);
        } else {
            getForm().getModel().getObject().getKitPedidoPacienteItems().add(0, kitPedidoPacienteItem);
        }


        limpar(target);
        tblKitPedidoPacienteItem.update(target);
        target.focusComponent(autoCompleteConsultaProduto.getTxtDescricao().getTextField());
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        KitPedidoPacienteItem proxy = on(KitPedidoPacienteItem.class);

        columns.add(getActionColumnItem());
        columns.add(createColumn(bundle("produto"), proxy.getProduto().getDescricao()));
        columns.add(createColumn(bundle("unidade"), proxy.getProduto().getUnidade().getDescricao()));
        columns.add(createColumn(bundle("quantidade"), proxy.getQuantidade()));

        return columns;
    }

    private IColumn getActionColumnItem() {
        return new MultipleActionCustomColumn<KitPedidoPacienteItem>() {
            @Override
            public void customizeColumn(KitPedidoPacienteItem rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<KitPedidoPacienteItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, KitPedidoPacienteItem modelObject) throws ValidacaoException, DAOException {
                        limparItem(target);
                        itemEdicao = modelObject;
                        kitPedidoPacienteItemModel.setObject((KitPedidoPacienteItem) SerializationUtils.clone(modelObject));
                        target.add(containerItensUtilizados);
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<KitPedidoPacienteItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, KitPedidoPacienteItem modelObject) throws ValidacaoException, DAOException {
                        remover(target, modelObject);
                    }
                });
            }
        };
    }

    private void limparItem(AjaxRequestTarget target) {
        kitPedidoPacienteItemModel.setObject(new KitPedidoPacienteItem());
        target.add(containerItensUtilizados);
        itemEdicao = null;
        autoCompleteConsultaProduto.limpar(target);
        txtQuantidade.limpar(target);
    }

    private void remover(AjaxRequestTarget target, KitPedidoPacienteItem kitPedidoPacienteItem) {
        limpar(target);
        for (int i = 0; i < getForm().getModel().getObject().getKitPedidoPacienteItems().size(); i++) {
            if (getForm().getModel().getObject().getKitPedidoPacienteItems().get(i) == kitPedidoPacienteItem) {
                getForm().getModel().getObject().getKitPedidoPacienteItems().remove(i);
                break;
            }
        }
        tblKitPedidoPacienteItem.update(target);
    }

    private void limpar(AjaxRequestTarget target) {
        kitPedidoPacienteItemModel.setObject(new KitPedidoPacienteItem());
        autoCompleteConsultaProduto.limpar(target);
        txtQuantidade.limpar(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModel().getObject().getKitPedidoPacienteItems();
            }
        };
    }

    private Form<CadastroKitPedidoPacienteDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new CadastroKitPedidoPacienteDTO()));
            form.getModel().getObject().setKitPedidoPaciente(kitPedidoPaciente);
        }
        return form;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    private void salvar() throws DAOException, ValidacaoException {
        if(!CollectionUtils.isNotNullEmpty(getForm().getModel().getObject().getKitPedidoPacienteItems())) {
            throw new ValidacaoException(bundle("informeProduto"));
        }
        BOFactoryWicket.getBO(MaterialBasicoFacade.class).salvarKitPedidoPaciente(getForm().getModel().getObject());
        ConsultaKitPedidoPacientePage page = new ConsultaKitPedidoPacientePage();
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        setResponsePage(page);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroKitPedidoPaciente");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (CollectionUtils.isNotNullEmpty(getForm().getModelObject().getKitPedidoPacienteItems())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerItensUtilizados)));
        }

    }

    private void carregarItens() {
        if (kitPedidoPaciente != null) {
            KitPedidoPacienteItem proxy = on(KitPedidoPacienteItem.class);

            List<KitPedidoPacienteItem> list = LoadManager.getInstance(KitPedidoPacienteItem.class)
                    .addProperties(new HQLProperties(KitPedidoPacienteItem.class).getProperties())
                    .addProperties(new HQLProperties(KitPedidoPaciente.class, path(proxy.getKitPedidoPaciente())).getProperties())
                    .addProperties(new HQLProperties(Produto.class, path(proxy.getProduto())).getProperties())
                    .addParameter(new QueryCustomParameter(path(proxy.getKitPedidoPaciente()), kitPedidoPaciente))
                    .addSorter(new QueryCustomSorter(path(proxy.getProduto().getDescricao()), BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(list)) {
                getForm().getModel().getObject().setKitPedidoPacienteItems(list);
            }
        }
    }


}
