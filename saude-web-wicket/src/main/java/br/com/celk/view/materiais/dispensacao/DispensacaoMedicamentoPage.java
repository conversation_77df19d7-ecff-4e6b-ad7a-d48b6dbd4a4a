package br.com.celk.view.materiais.dispensacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.ajaxcalllistener.ConditionListenerLoading;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.resources.Resources;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.view.atendimento.consultaprontuario.ConsultaProntuarioPage;
import br.com.celk.view.atendimento.consultaprontuario.ProntuarioPage;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarCidDispensacao;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarCidsReceita;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.empresa.dialog.DlgCadastrarEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.basico.profissionalsemvinculo.autocomplete.AutoCompleteConsultaProfissionalSemVinculo;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.cadsus.usuariocadsus.dialog.DlgCadastroPacienteBasico;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.materiais.dispensacao.item.DlgAdicionarItem;
import br.com.celk.view.materiais.dispensacao.item.DlgAdicionarItemSemEstoque;
import br.com.celk.view.materiais.dispensacao.saldo.DlgHistoricoDispensacoes;
import br.com.celk.view.materiais.dispensacao.saldo.DlgReceitasPaciente;
import br.com.celk.view.materiais.dispensacao.tabbedpanel.DispensacaoMedicamentosCheckboxColumnPanel;
import br.com.celk.view.materiais.pedidotransferenciaitem.DlgDetalhesItemAlmoxarifado;
import br.com.celk.view.materiais.pedidotransferenciaitem.DlgDetalhesItemAlmoxarifadoKit;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProdutoEstoque;
import br.com.celk.view.materiais.profissionalsemvinculo.dialog.DlgCadastrarProfissionalSemVinculo;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryProfissionalTipoAtendimentoDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.facade.DispensacaoMedicamentoFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryEstoqueEmpresaSemLoteVencidoDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryEstoqueEmpresaSemLoteVencidoDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoTransferenciaFacade;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ReceituarioHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioImpressaoDispensacaoMedicamentoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioDTO;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.*;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.materiais.KitPedidoPaciente;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.ajax.markup.html.form.AjaxCheckBox;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;
import org.odlabs.wiquery.core.javascript.helper.DateHelper;

import java.lang.reflect.InvocationTargetException;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 44
 */
@Private
public class DispensacaoMedicamentoPage extends BasePage {

    private static final long NOVENTA_DIAS = 90L;
    private static final long SEIS_MESES = 180L;
    private  static final long MAIOR_IDADE = 18L;
    private final List<DispensacaoMedicamentoItem> items = new ArrayList();
    private InputField txtCodigoBarras;
    private CompoundPropertyModel<DispensacaoMedicamento> modelDispensacao;
    private CompoundPropertyModel<DispensacaoMedicamentoItem> modelDispensacaoItem;
    private Form<DispensacaoMedicamento> form;
    private AutoCompleteConsultaUsuarioCadsus pnlConsultaUsuarioCadsus;
    private DoubleField txtPeso;
    private InputField<Double> txtAltura;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private DropDown cbxTipoReceita;
    private DropDown cbxReceitaContinua;
    private InputField txtReceita;
    private DateChooser dchDataReceita;
    private AutoCompleteConsultaEmpresa pnlConsultaEmpresaOrigem;
    private DlgCadastrarEmpresa dlgCadastrarEmpresa;
    private DlgCadastrarProfissionalSemVinculo dlgCadastrarProfissionalSemVinculo;
    private AutoCompleteConsultaProfissional pnlConsultaProfissional;
    private WebMarkupContainer divProfissionalFaturamento;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissionalFaturamento;
    private InputField txtResponsavel;
    private WebMarkupContainer identificacaoItem;
    private WebMarkupContainer dadosReceitaItem;
    private WebMarkupContainer containerItem;
    private AutoCompleteConsultaProdutoEstoque pnlConsultaProduto;
    private AbstractAjaxButton btnAdicionarItem;
    private AbstractAjaxButton btnSalvar;
    private AbstractAjaxButton btnSalvarContinuar;
    private Table<DispensacaoMedicamentoItem> tblItems;
    private DlgAdicionarItem dlgAdicionarItem;
    private DlgAdicionarItemSemEstoque dlgAdicionarItemSemEstoque;
    private DlgHistoricoDispensacoes dlgHistoricoDispensacoes;
    private DlgRegistrarAtendimento dlgRegistrarAtendimento;
    private DlgHistoricoDispensacoes dlgHistoricoDispensacoesProduto;
    private DlgAvisoDispensacoesHistorico dlgAvisoDispensacoesHistorico;
    private DlgReceitasPaciente dlgReceitasPaciente;
    private AbstractAjaxButton btnHistoricoDispensacoes;
    private AbstractAjaxButton btnReceitaPaciente;
    private AbstractAjaxButton btnProntuarioPaciente;
    private AbstractAjaxButton btnRegistrarAtendimento;
    private String evolucaoAtendimento;
    private DispensacaoMedicamento dispensacaoMedicamento;
    private DispensacaoMedicamentoItem dispensacaoMedicamentoItem;
    private String codigoBarras;
    private boolean salvarContinuar;
    private boolean receitaContinua = false;
    private IParameterModuleContainer parametroModuloMateriais;
    private WebMarkupContainer containerDadosProfissional;
    private WebMarkupContainer containerCodBarraProduto;
    private AutoCompleteConsultaProfissionalSemVinculo autoCompleteConsultaProfissionalSemVinculo;
    private LongField txtCodigoBarrasProduto;
    private String codigoBarrasProduto;
    private DlgImpressaoObject dlgImprimirComprovante;
    private UsuarioCadsusDado usuarioCadsusDado;
    private InputArea<String> txtObservacaoDispensacao;
    private AbstractAjaxLink btnEditarObservacao;
    private AbstractAjaxLink btnSalvarObservacao;
    private DropDown<Long> dropDownFaturarDispensa;
    private RequiredDateChooser dcDataDispensacao;
    private HoraMinutoField txtHora;
    private Date horaDispensacao;
    private Date dataMinimaDispensacao;
    private Integer parametroDiasDataPrescricao;
    private DlgCadastroPacienteBasico dlgCadPaciente;
    private AbstractAjaxLink btnCadPaciente;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEstabelecimentoExecutante;
    private DlgDetalhesItemAlmoxarifado dlgDetalhesItemAlmoxarifado;
    private DlgDetalhesItemAlmoxarifadoKit dlgDetalhesItemAlmoxarifadoKit;
    private String dataReceita;
    private UsuarioCadsus usuarioCadsus;
    private Long codigoUsuario;
    private InputField txtCodigoUsuario;

    private WebMarkupContainer containerKit;
    private MultiSelectionTableOld<KitPedidoPaciente> tblKitPedido;
    private MultiSelectionTableOld<UsuarioCadsusKit> tblKitPedidoPaciente;
    private List<KitPedidoPaciente> kitPedidoList = new ArrayList();
    private List<UsuarioCadsusKit> kitPedidoPacienteList = new ArrayList();
    private List<DispensacaoMedicamentoItem> selectedItens = new ArrayList<>();
    private boolean verificaUsoCampoCodigo;
    private Boolean profissionalNaoCadastrado;
    private AjaxCheckBox cbxProfissionalNaoCadastrado;
    private boolean parametroReferencia;
    private Image imagem;
    private String utilizarLeitoraCodigoBarrasProduto;
    private String validarSituacaoCodigoBarrasProduto;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private DlgInformarCidDispensacao dlgInformarCidDispensacao;
    private Label labelCodigoUsuario;
    private String textoLabelCodigoUsuario;
    private boolean permiteRegistrarAtendimento;
    private TipoAtendimento tipoAtendimento;
    private String codigoBarrasVersion;
    private Double peso;
    private Double altura;

    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaResponsavel;
    private boolean obrigaResponsavelMenorIdade;
    private UsuarioCadsus responsavel;

    private AbstractAjaxLink linkAdicionarCid;
    private DlgInformarCidsReceita dlgInformarCidsReceita;
    private List<DispensacaoMedicamentoCid> cidListMedicamento = new ArrayList<>();
    private Produto produto;

    public DispensacaoMedicamentoPage() {
        init(null);
    }

    public DispensacaoMedicamentoPage(DispensacaoMedicamento dispensacaoMedicamento) {
        this.dispensacaoMedicamento = dispensacaoMedicamento;
        this.salvarContinuar = true;
        init(null);
    }

    public DispensacaoMedicamentoPage(String codigoBarras, AjaxRequestTarget target) {
        this.codigoBarras = codigoBarras;
        init(target);
    }

    private void init(AjaxRequestTarget target) {
        carregaKitPaciente();
        try {
            permiteRegistrarAtendimento = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("RegistrarAtendimento"));
            tipoAtendimento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("TipoAtendimentoDispensacao");
            verificaUsoCampoCodigo = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("buscaCodigoPacienteDispensacaoMedicamentos"));
            parametroReferencia = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("referencia"));
            this.parametroModuloMateriais = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS);
            this.utilizarLeitoraCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizarLeitoraCodigoBarrasProduto");
            this.validarSituacaoCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("validarSituacaoCodigoBarrasProduto");
            this.obrigaResponsavelMenorIdade = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ObrigaResponsavelMenorIdade"));
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        if (horaDispensacao == null) {
            if (dispensacaoMedicamento == null || (dispensacaoMedicamento != null && dispensacaoMedicamento.getDataDispensacao() == null)) {
                horaDispensacao = DataUtil.getDataAtual();
            } else if (dispensacaoMedicamento != null) {
                horaDispensacao = dispensacaoMedicamento.getDataDispensacao();
            }
        }
        if (parametroReferencia) {
            textoLabelCodigoUsuario = BundleManager.getString("referencia");
        } else {
            textoLabelCodigoUsuario = BundleManager.getString("codigo");
        }
        if (dispensacaoMedicamento == null) {
            dispensacaoMedicamento = new DispensacaoMedicamento();
            dispensacaoMedicamento.setEmpresa(ApplicationSession.get().getSession().getEmpresa());
        }
        if (dispensacaoMedicamento.getDataDispensacao() == null) {
            dispensacaoMedicamento.setDataDispensacao(DataUtil.getDataAtual());
        }
        form = new Form("form", modelDispensacao = new CompoundPropertyModel(dispensacaoMedicamento));
        form.setOutputMarkupId(true);

        identificacaoItem = new WebMarkupContainer("identificacao");
        identificacaoItem.setOutputMarkupId(true);

        identificacaoItem.add(txtCodigoBarras = new InputField("codigoBarras", new PropertyModel(this, "codigoBarras")));
        identificacaoItem.add(dcDataDispensacao = new RequiredDateChooser("dataDispensacao"));
        dcDataDispensacao.addAjaxUpdateValue();
        dcDataDispensacao.setOutputMarkupId(true);
        identificacaoItem.add(txtHora = new HoraMinutoField("horaDispensacao", new Model<Date>(horaDispensacao)));
        txtHora.addAjaxUpdateValue();
        txtHora.setOutputMarkupId(true);
        identificacaoItem.add(dropDownFaturarDispensa = DropDownUtil.getNaoSimLongDropDown(VOUtils.montarPath(DispensacaoMedicamento.PROP_FATURAR_DISPENSA)));
        dropDownFaturarDispensa.addAjaxUpdateValue();
        dropDownFaturarDispensa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                autoCompleteConsultaProfissionalFaturamento.limpar(target);

                onUpdateDropDownFaturarDispensa(target);
            }
        });

        identificacaoItem.add(autoCompleteConsultaEstabelecimentoExecutante = new AutoCompleteConsultaEmpresa(VOUtils.montarPath(DispensacaoMedicamento.PROP_EMPRESA), true));
        autoCompleteConsultaEstabelecimentoExecutante.setValidaUsuarioEmpresa(true);
        autoCompleteConsultaEstabelecimentoExecutante.setLabel(new Model(bundle("estabelecimento")));

        autoCompleteConsultaEstabelecimentoExecutante.getTxtDescricao().getTextField().add(new AjaxFormComponentUpdatingBehavior("onblur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        autoCompleteConsultaEstabelecimentoExecutante.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                autoCompleteConsultaProfissionalFaturamento.limpar(target);
                autoCompleteConsultaProfissionalFaturamento.setCodigoEmpresa(object.getCodigo());
                autoCompleteConsultaProfissionalFaturamento.setEnabled(true);
                target.add(autoCompleteConsultaProfissionalFaturamento);

                pnlConsultaProduto.limpar(target);
                pnlConsultaProduto.setEnabled(true);
                pnlConsultaProduto.setMostraInativos(RepositoryComponentDefault.SIM_LONG);
                pnlConsultaProduto.setEmpresas(Collections.singletonList(object));
                target.add(pnlConsultaProduto);

                dropDownFaturarDispensa.limpar(target);
                dropDownFaturarDispensa.setEnabled(true);
                target.add(dropDownFaturarDispensa);

                cbxTipoReceita.limpar(target);
                cbxTipoReceita.setEnabled(true);
                target.add(cbxTipoReceita);

                pnlConsultaUsuarioCadsus.limpar(target);
                pnlConsultaUsuarioCadsus.setEnabled(true);
                if (verificaUsoCampoCodigo) {
                    txtCodigoUsuario.setEnabled(true);
                }
                target.add(txtCodigoUsuario);
                target.add(pnlConsultaUsuarioCadsus);

                txtCodigoBarras.limpar(target);
                txtCodigoBarras.setEnabled(true);
                target.add(txtCodigoBarras);

                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        form.add(containerKit = new WebMarkupContainer("containerKit"));
        containerKit.setOutputMarkupId(true);

        containerKit.add(new AbstractAjaxButton("btnAdicionarKit") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarKit(target);
            }
        }.setDefaultFormProcessing(false));

        containerKit.add(tblKitPedido = new MultiSelectionTableOld("tblKitPedido", getColumnsKitPedido(), getCollectionProviderKitPedido()));
        eventoConsultaUsuario(null, dispensacaoMedicamento.getUsuarioCadsusDestino());
        tblKitPedido.populate();
        tblKitPedido.setScrollY("208px");

        containerKit.add(new AbstractAjaxButton("btnAdicionarKitPaciente") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarKitPaciente(target);
            }

            @Override
            protected void onError(AjaxRequestTarget target, Form<?> form) {
                super.onError(target, form);
                target.appendJavaScript(JScript.showFieldset(containerKit));
            }
        });

        containerKit.add(tblKitPedidoPaciente = new MultiSelectionTableOld("tblKitPedidoPaciente", getColumnsKitPedidoPaciente(), getCollectionProviderKitPedidoPaciente()));
        tblKitPedidoPaciente.populate();
        tblKitPedidoPaciente.setScrollY("208px");

        autoCompleteConsultaEstabelecimentoExecutante.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                autoCompleteConsultaProfissionalFaturamento.setEnabled(false);
                target.add(autoCompleteConsultaProfissionalFaturamento);

                pnlConsultaProduto.setEnabled(false);
                target.add(pnlConsultaProduto);

                dropDownFaturarDispensa.limpar(target);
                dropDownFaturarDispensa.setEnabled(false);
                target.add(dropDownFaturarDispensa);

                onUpdateDropDownFaturarDispensa(target);

                cbxTipoReceita.limpar(target);
                cbxTipoReceita.setEnabled(false);
                target.add(cbxTipoReceita);

                pnlConsultaUsuarioCadsus.limpar(target);
                pnlConsultaUsuarioCadsus.setEnabled(false);
                if (verificaUsoCampoCodigo) {
                    txtCodigoUsuario.setEnabled(false);
                }
                target.add(txtCodigoUsuario);
                target.add(pnlConsultaUsuarioCadsus);

                removerUsuarioCadsusAutoComplete(target);

                txtCodigoBarras.limpar(target);
                txtCodigoBarras.setEnabled(false);
                target.add(txtCodigoBarras);

                target.focusComponent(pnlConsultaUsuarioCadsus);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        identificacaoItem.add(divProfissionalFaturamento = new WebMarkupContainer("divProfissionalFaturamento"));
        divProfissionalFaturamento.setOutputMarkupId(true);
        divProfissionalFaturamento.setVisible(false); // Inicia invisível até que o campo "Fatura Dispensa" seja "Sim".
        divProfissionalFaturamento.add(autoCompleteConsultaProfissionalFaturamento = new AutoCompleteConsultaProfissional(VOUtils.montarPath(DispensacaoMedicamento.PROP_PROFISSIONAL_FATURAMENTO_DISPENSACAO), true));
        autoCompleteConsultaProfissionalFaturamento.setLabel(new Model(bundle("profissionalOrientador")));

        try {
            if (getEstabelecimentoExecutante() != null) {
                autoCompleteConsultaProfissionalFaturamento.setCodigoEmpresa(getEstabelecimentoExecutante().getCodigo()).setPeriodoEmpresa(true).setProcedimento(getProcedimentoFaturadoDispensa());
            }
        } catch (Exception ex) {
            warn(ex.getMessage());
        }

        dcDataDispensacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                if (dcDataDispensacao.getData().getConvertedInput() != null) {
                    if (dcDataDispensacao.getData().getConvertedInput().before(dataMinimaDispensacao)) {
                        dcDataDispensacao.limpar(art);
                        warn(art, bundle("dataDispensacaoNaoPodeInferiorXDias", parametroDiasDataPrescricao));
                        art.focusComponent(dcDataDispensacao.getData());
                        art.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'maxDate', " + DateHelper.getJSDate(DataUtil.getDataAtual()) + " );");
                        art.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'minDate', " + DateHelper.getJSDate(dataMinimaDispensacao) + " );");
                    } else if (dcDataDispensacao.getData().getConvertedInput().after(DataUtil.getDataAtual())) {
                        dcDataDispensacao.limpar(art);
                        warn(art, bundle("dataDispensacaoNaoPodeSuperiorDataAtual"));
                        art.focusComponent(dcDataDispensacao.getData());
                        art.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'maxDate', " + DateHelper.getJSDate(DataUtil.getDataAtual()) + " );");
                        art.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'minDate', " + DateHelper.getJSDate(dataMinimaDispensacao) + " );");
                    }
                }
            }
        });
        modelDispensacao.getObject().setDataDispensacao(DataUtil.getDataAtual());
        identificacaoItem.add(txtCodigoUsuario = new InputField<Long>("codigoUsuario", new PropertyModel(this, "codigoUsuario")));
        txtCodigoUsuario.setVisible(verificaUsoCampoCodigo);
        identificacaoItem.add(labelCodigoUsuario = new Label("labelCodigoUsuario", textoLabelCodigoUsuario));
        labelCodigoUsuario.setVisible(verificaUsoCampoCodigo);
        if (verificaUsoCampoCodigo) {
            txtCodigoUsuario.addAjaxUpdateValue();
            txtCodigoUsuario.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                    carregarUsuario(ajaxRequestTarget);
                }
            });
        }
        identificacaoItem.add(pnlConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(VOUtils.montarPath(DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO), true) {
            @Override
            public Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
            }
        });
        pnlConsultaUsuarioCadsus.setOutputMarkupPlaceholderTag(true);
        pnlConsultaUsuarioCadsus.addAjaxUpdateValue();
        pnlConsultaUsuarioCadsus.addBusyIndicator();

        identificacaoItem.add(autoCompleteConsultaResponsavel = new AutoCompleteConsultaUsuarioCadsus(VOUtils.montarPath(DispensacaoMedicamento.PROP_USUARIO_CADSUS_RESPONSAVEL)) {
            @Override
            public Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
            }
        });
        autoCompleteConsultaResponsavel.setLabel(new Model("Resposavel"));
        autoCompleteConsultaResponsavel.setOutputMarkupPlaceholderTag(true);
        autoCompleteConsultaResponsavel.addAjaxUpdateValue();
        autoCompleteConsultaResponsavel.addBusyIndicator();
        autoCompleteConsultaResponsavel.setVisible(obrigaResponsavelMenorIdade);
        autoCompleteConsultaResponsavel.setEnabled(false);

        identificacaoItem.add(txtPeso = new DoubleField("peso", new PropertyModel(this, "peso")).setMDec(2).setVMax(300D));
        identificacaoItem.add(txtAltura = new InputField<Double>("altura", new PropertyModel(this, "altura")));


        identificacaoItem.add(linkAdicionarCid = new AbstractAjaxLink("linkAdicionarCid") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgInformarCidsReceita.show(target,cidListMedicamento,produto);
            }
        });
        linkAdicionarCid.setEnabled(false);

        identificacaoItem.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(VOUtils.montarPath(DispensacaoMedicamento.PROP_CID)));

        autoCompleteConsultaCid.add(new ConsultaListener<Cid>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cid object) {
                if (produto != null && TipoReceita.RECEITA_BRANCA_C3.equals(produto.getTipoReceita().getTipoReceita())) {
                    linkAdicionarCid.setEnabled(true);

                    DispensacaoMedicamentoCid medicamentoCid =  new DispensacaoMedicamentoCid();
                    medicamentoCid.setCid(object);
                    cidListMedicamento.add(medicamentoCid);

                    target.add(linkAdicionarCid);
                }
            }
        });

        autoCompleteConsultaCid.add(new RemoveListener<Cid>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cid object) {
                if (produto != null && TipoReceita.RECEITA_BRANCA_C3.equals(produto.getTipoReceita().getTipoReceita())) {
                    linkAdicionarCid.setEnabled(false);
                    cidListMedicamento.clear();
                    target.add(linkAdicionarCid);
                }
            }
        });



        identificacaoItem.add(btnCadPaciente = new AbstractAjaxLink("btnCadPaciente") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgCadPaciente.show(target);
            }

            @Override
            public boolean isEnabled() {
                String permiteCadastroPaciente = null;

                try {
                    permiteCadastroPaciente = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("PermiteCadastroPaciente");
                } catch (DAOException ex) {
                    Logger.getLogger(DispensacaoMedicamentoPage.class.getName()).log(Level.SEVERE, null, ex);
                }

                return RepositoryComponentDefault.SIM.equals(permiteCadastroPaciente);
            }
        });

        identificacaoItem.add(imagem = new NonCachingImage("imgAvatar", Resources.Images.AVATAR_SEM_FOTO.resourceReference().getResource()));
        imagem.setOutputMarkupId(true);

        identificacaoItem.add(txtObservacaoDispensacao = new InputArea<>("observacaoDispensacao", new PropertyModel<String>(this, "usuarioCadsusDado.observacaoDispensacao")));
        txtObservacaoDispensacao.addAjaxUpdateValue();
        txtObservacaoDispensacao.setEnabled(false);

        identificacaoItem.add(btnEditarObservacao = new AbstractAjaxLink("btnEditarObservacao") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                btnSalvarObservacao.setEnabled(true);
                target.add(btnSalvarObservacao);
                txtObservacaoDispensacao.setEnabled(true);
                target.add(txtObservacaoDispensacao);
                target.focusComponent(txtObservacaoDispensacao);
            }
        });
        btnEditarObservacao.add(new AttributeModifier("title", BundleManager.getString("editar")));
        btnEditarObservacao.setEnabled(false);
        btnEditarObservacao.setOutputMarkupId(true);

        identificacaoItem.add(btnSalvarObservacao = new AbstractAjaxLink("btnSalvarObservacao") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                salvarUsuarioCadsusDado();
                btnSalvarObservacao.setEnabled(false);
                txtObservacaoDispensacao.setEnabled(false);
                target.add(btnSalvarObservacao);
                target.add(txtObservacaoDispensacao);
            }
        });

        btnSalvarObservacao.add(new AttributeModifier("title", BundleManager.getString("salvar")));
        btnSalvarObservacao.setEnabled(false);

        identificacaoItem.add(btnHistoricoDispensacoes = new AbstractAjaxButton("btnHistoricoDispensacoes") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgHistoricoDispensacoes.show(target);
            }
        });
        btnHistoricoDispensacoes.setDefaultFormProcessing(false);
        identificacaoItem.add(btnReceitaPaciente = new AbstractAjaxButton("btnReceitaPaciente") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgReceitasPaciente.show(target);
            }
        });
        btnReceitaPaciente.setDefaultFormProcessing(false);
        identificacaoItem.add(btnProntuarioPaciente = new AbstractAjaxButton("btnProntuarioPaciente") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                abrirProntuarioPaciente(target, usuarioCadsus);
            }
        });
        btnProntuarioPaciente.setDefaultFormProcessing(false);
        btnProntuarioPaciente.setEnabled(false);
        identificacaoItem.add(txtResponsavel = new InputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_NOME_USUARIO_ORIGEM)));

        identificacaoItem.add(btnRegistrarAtendimento = new AbstractAjaxButton("btnRegistrarAtendimento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgRegistrarAtendimento.show(target);
            }
        });
        btnRegistrarAtendimento.setDefaultFormProcessing(false);

        if (permiteRegistrarAtendimento) {
            btnRegistrarAtendimento.setEnabled(false);
            if (tipoAtendimento != null) {
                Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().getUsuario();
                if (usuarioLogado.getProfissional() != null) {
                    QueryProfissionalTipoAtendimentoDTOParam param = new QueryProfissionalTipoAtendimentoDTOParam();
                    param.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
                    param.setTipoAtendimento(tipoAtendimento);

                    List<Profissional> profissionais = null;
                    try {
                        profissionais = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaProfissionalTipoAtendimento(param);
                    } catch (DAOException | ValidacaoException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                    if (profissionais != null && profissionais.contains(usuarioLogado.getProfissional())) {
                        btnRegistrarAtendimento.setEnabled(true);
                    }
                }
            }
        } else {
            btnRegistrarAtendimento.setVisible(false);
        }

        form.add(identificacaoItem);

        dadosReceitaItem = new WebMarkupContainer("dadosReceita");
        dadosReceitaItem.setOutputMarkupId(true);

        dadosReceitaItem.add(getDropDownReceitas());
        dadosReceitaItem.add(getDropDownReceitaContinua());
        dadosReceitaItem.add(txtReceita = new InputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_RECEITA)));
        dadosReceitaItem.add(dchDataReceita = new RequiredDateChooser(VOUtils.montarPath(DispensacaoMedicamento.PROP_DATA_RECEITA)));
        dadosReceitaItem.add(pnlConsultaEmpresaOrigem = new AutoCompleteConsultaEmpresa(VOUtils.montarPath(DispensacaoMedicamento.PROP_EMPRESA_ORIGEM), true).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_EXTERNO)));

        addModal(dlgCadastrarEmpresa = new DlgCadastrarEmpresa(newModalId()) {
            @Override
            public void onSalvar(AjaxRequestTarget target, Empresa empresa) throws ValidacaoException, DAOException {
                pnlConsultaEmpresaOrigem.limpar(target);
                pnlConsultaEmpresaOrigem.setComponentValue(target, empresa);
            }
        });

        dlgCadastrarEmpresa.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_EXTERNO));

        dadosReceitaItem.add(new AbstractAjaxLink("lnkCadEmpresa") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgCadastrarEmpresa.show(target);
            }
        });

        dadosReceitaItem.add(pnlConsultaProfissional = new AutoCompleteConsultaProfissional(VOUtils.montarPath(DispensacaoMedicamento.PROP_PROFISSIONAL)));

        dadosReceitaItem.add(cbxProfissionalNaoCadastrado = new AjaxCheckBox("profissionalNaoCadastrado", new PropertyModel<Boolean>(this, "profissionalNaoCadastrado")) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (profissionalNaoCadastrado) {
                    pnlConsultaProfissional.setEnabled(false);
                    containerDadosProfissional.setVisible(true);
                } else {
                    pnlConsultaProfissional.setEnabled(true);
                    containerDadosProfissional.setVisible(false);
                }

                pnlConsultaProfissional.limpar(target);
                ComponentUtils.limparContainer(containerDadosProfissional, target);
            }
        });

        dadosReceitaItem.add(containerDadosProfissional = new WebMarkupContainer("containerDadosProfissional"));
        containerDadosProfissional.setOutputMarkupPlaceholderTag(true);
        containerDadosProfissional.setVisible(false);
        containerDadosProfissional.add(autoCompleteConsultaProfissionalSemVinculo = new AutoCompleteConsultaProfissionalSemVinculo(DispensacaoMedicamento.PROP_PROFISSIONAL_SEM_VINCULO));

        addModal(dlgCadastrarProfissionalSemVinculo = new DlgCadastrarProfissionalSemVinculo(newModalId()) {
            @Override
            public void onSalvar(AjaxRequestTarget target, ProfissionalSemVinculo profissionalSemVinculo) throws ValidacaoException, DAOException {
                autoCompleteConsultaProfissionalSemVinculo.limpar(target);
                autoCompleteConsultaProfissionalSemVinculo.setComponentValue(target, profissionalSemVinculo);
            }
        });

        containerDadosProfissional.add(new AbstractAjaxLink("lnkCadProfissionalSemVinculo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgCadastrarProfissionalSemVinculo.show(target);
            }
        });

        pnlConsultaProfissional.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                ComponentUtils.limparContainer(containerDadosProfissional, target);
            }
        });

        form.add(dadosReceitaItem);

        containerItem = new WebMarkupContainer("containerItem", modelDispensacaoItem = new CompoundPropertyModel(dispensacaoMedicamentoItem = new DispensacaoMedicamentoItem()));
        containerItem.setOutputMarkupId(true);

        containerItem.add(pnlConsultaProduto = new AutoCompleteConsultaProdutoEstoque(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO)) {
            @Override
            public String[] getPropertiesLoad() {

                String[] propertiesDefault = super.getPropertiesLoad();
                return VOUtils.mergeProperties(propertiesDefault, new HQLProperties(Unidade.class, Produto.PROP_UNIDADE).getProperties(), new HQLProperties(TipoReceita.class, Produto.PROP_TIPO_RECEITA).getProperties());
            }
        }.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO).setEmpresas(Collections.singletonList(getEstabelecimentoExecutante())));
        pnlConsultaProduto.setMostraInativos(RepositoryComponentDefault.NAO_LONG);

        containerItem.add(containerCodBarraProduto = new WebMarkupContainer("containerCodBarraProduto"));
        containerCodBarraProduto.setOutputMarkupPlaceholderTag(true);
        containerCodBarraProduto.add(txtCodigoBarrasProduto = new LongField("codigoBarrasProduto", new PropertyModel(this, "codigoBarrasProduto")));
        txtCodigoBarrasProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                try {
                    carregarMedicamentoCodigoBarras(target);
                    target.appendJavaScript(JScript.removeAutoCompleteDrop());
                } catch (ValidacaoException | DAOException e) {
                    txtCodigoBarrasProduto.limpar(target);
                    target.focusComponent(txtCodigoBarrasProduto);
                    modalWarn(target, e);
                }
            }
        });
        containerCodBarraProduto.setVisible(RepositoryComponentDefault.SIM.equals(utilizarLeitoraCodigoBarrasProduto));

        containerItem.add(btnAdicionarItem = new AbstractAjaxButton("btnAdicionarItem") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (getEstabelecimentoExecutante() == null) {
                    throw new ValidacaoException(BundleManager.getString("informeEstabelecimento"));
                }

                if (validarAcaoBtnAdicionarItem(target, dispensacaoMedicamentoItem, false)
                        && validacaoDispensacaoHistorico(dispensacaoMedicamentoItem.getProduto(), target, true) == null) {
                    dispensacaoMedicamentoItem.setDispensacaoMedicamento(dispensacaoMedicamento);

                    if (consultarEstoqueSemLoteVencido(dispensacaoMedicamentoItem.getProduto())) {
                        dlgAdicionarItem.setObject(target, dispensacaoMedicamentoItem, false, getEstabelecimentoExecutante(), null);
                        dlgAdicionarItem.show(target);
                    } else {
                        initDlgAdicionarItemSemEstoque(target, dispensacaoMedicamentoItem);
                    }
                }
            }

            @Override
            protected void onError(AjaxRequestTarget target, Form<?> form) {
                super.onError(target, form);
                target.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'maxDate', " + DateHelper.getJSDate(DataUtil.getDataAtual()) + " );");
                target.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'minDate', " + DateHelper.getJSDate(dataMinimaDispensacao) + " );");
            }
        });

        containerItem.add(tblItems = new Table("tableItems", getColumnsItems(), getCollectionProviderItems()));
        tblItems.populate();

        form.add(containerItem);

        form.add(btnSalvar = new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvarContinuar = false;
                validaVinculoDomiciliar(target);
                salvar(target);
            }
        }) {
            @Override
            protected void onError(AjaxRequestTarget target, Form<?> form) {
                super.onError(target, form);
                target.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'maxDate', " + DateHelper.getJSDate(DataUtil.getDataAtual()) + " );");
                target.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'minDate', " + DateHelper.getJSDate(dataMinimaDispensacao) + " );");
            }
        });

        form.add(btnSalvarContinuar = new SubmitButton("btnSalvarContinuar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvarContinuar = true;
                validaVinculoDomiciliar(target);
                salvar(target);
            }
        }) {
            @Override
            protected void onError(AjaxRequestTarget target, Form<?> form) {
                super.onError(target, form);
                target.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'maxDate', " + DateHelper.getJSDate(DataUtil.getDataAtual()) + " );");
                target.appendJavaScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'minDate', " + DateHelper.getJSDate(dataMinimaDispensacao) + " );");
            }
        });
        AbstractAjaxButton btnLimpar;
        form.add(btnLimpar = new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                items.clear();
                setResponsePage(DispensacaoMedicamentoPage.class);
            }
        });
        btnLimpar.setDefaultFormProcessing(false);

        // btn para remover os itens selecionado com o checkbox
        AbstractAjaxButton btnRemoverSelecionados;
        form.add(btnRemoverSelecionados = new AbstractAjaxButton("btnRemoverSelecionados") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (CollectionUtils.isEmpty(selectedItens)) {
                    throw new ValidacaoException(bundle("msgProdutoObrigatorio"));
                }
                addModal(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(newModalId(), BundleManager.getString("msgDesejaRemoverProdutosSelecionado")) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        actionBtnRemoverSelecionados(target);
                    }
                });
                dlgConfirmacaoSimNao.show(target);
            }
        });
        btnRemoverSelecionados.setDefaultFormProcessing(false);

        add(form);

        addModal(dlgAdicionarItem = new DlgAdicionarItem(newModalId()) {
            @Override
            public void adicionar(AjaxRequestTarget target, DispensacaoMedicamentoItem itemOrigem, DispensacaoMedicamentoItem itemDestino) throws ValidacaoException, DAOException {
                adicionarItem(target, itemOrigem, itemDestino);
            }

            @Override
            public void fechar(AjaxRequestTarget target) {
                limparAdd(target);
                if (CollectionUtils.isEmpty(items)) {
                    btnAdicionarItem.setEnabled(true);
                    txtCodigoBarrasProduto.setEnabled(true);
                    pnlConsultaProduto.setEnabled(true);
                    target.add(btnAdicionarItem);
                    target.add(txtCodigoBarrasProduto);
                    target.add(pnlConsultaProduto);
                }
            }
        });

        addModal(dlgRegistrarAtendimento = new DlgRegistrarAtendimento(newModalId()) {
            @Override
            public void onSalvar(AjaxRequestTarget target, String evolucao) throws DAOException, ValidacaoException {
                evolucaoAtendimento = evolucao;
            }

            @Override
            public void onLimpar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                evolucaoAtendimento = null;
            }
        });

        addModal(dlgHistoricoDispensacoes = new DlgHistoricoDispensacoes(newModalId()));
        addModal(dlgHistoricoDispensacoesProduto = new DlgHistoricoDispensacoes(newModalId()));
        addModal(dlgAvisoDispensacoesHistorico = new DlgAvisoDispensacoesHistorico(newModalId(), dlgHistoricoDispensacoesProduto) {
            @Override
            public void fechar(AjaxRequestTarget target, Produto produto) {
                items.remove(dispensacaoMedicamentoItem);
                tblItems.update(target);
                tblItems.populate();
                pnlConsultaProduto.focus(target);
                pnlConsultaProduto.setComponentValue(target, produto);
            }
        });
        addModal(dlgReceitasPaciente = new DlgReceitasPaciente(newModalId()) {
            @Override
            public void confirmar(AjaxRequestTarget target, Receituario receituario) {
                if (dispensacaoMedicamento != null && dispensacaoMedicamento.getReceituario() != null) {
                    try {
                        throw new ValidacaoException(BundleManager.getString("ePermitidoCarregarSomenteReceituarioPorVez"));
                    } catch (ValidacaoException ex) {
                        Loggable.log.warn(ex.getMessage());
                        MessageUtil.warn(target, this, bundle("ePermitidoCarregarSomenteReceituarioPorVez"));
                    }
                } else {
                    codigoBarras = receituario.getCodigo().toString();
//                    codigoBarrasVersion = receituario.getVersionPrescricao().toString();
                    loadReceitaCodigoBarras(target);
                }
            }
        });
        addModal(dlgImprimirComprovante = new DlgImpressaoObject<RelatorioImpressaoDispensacaoMedicamentoDTOParam>(newModalId(), BundleManager.getString("msgItensDispensadosComSucessoDesejaImprimirComprovante")) {
            @Override
            public DataReport getDataReport(RelatorioImpressaoDispensacaoMedicamentoDTOParam object) throws ReportException {
                return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioImpressaoDispensacaoMedicamento(object);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RelatorioImpressaoDispensacaoMedicamentoDTOParam object) throws ValidacaoException, DAOException {
                super.onFechar(target, object);
                redirectPage();
            }
        });
        addModal(dlgCadPaciente = new DlgCadastroPacienteBasico(newModalId()) {
            @Override
            public void setUsuarioCadsus(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
                pnlConsultaUsuarioCadsus.limpar(target);
                pnlConsultaUsuarioCadsus.setComponentValue(target, usuarioCadsus);
                controlarAvatarUsuario(target, usuarioCadsus);
            }
        });
        addModal(dlgInformarCidsReceita = new DlgInformarCidsReceita(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, List<DispensacaoMedicamentoCid> cidList) throws ValidacaoException, DAOException {
                cidListMedicamento = cidList;
            }
        });

        txtCodigoBarras.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                loadReceitaCodigoBarras(target);
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                String condition = "!= ''";
                attributes.getAjaxCallListeners().add(new ConditionListenerLoading(txtCodigoBarras.getMarkupId(), condition));
            }
        });

        pnlConsultaUsuarioCadsus.add(new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                controlarAvatarUsuario(target, object);
                carregarCamposUsuario(target, object);
                target.appendJavaScript(JScript.initMasks());
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        pnlConsultaUsuarioCadsus.add(new RemoveListener<UsuarioCadsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                removerUsuarioCadsusAutoComplete(target);
            }
        });

        autoCompleteConsultaResponsavel.add(new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                responsavel =  object;
            }
        });

        autoCompleteConsultaResponsavel.add(new RemoveListener<UsuarioCadsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                responsavel = null;
                autoCompleteConsultaResponsavel.limpar(target);
            }
        });

        pnlConsultaProduto.add(new ConsultaListener<Produto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                validaCamposBnafar(target, object);
                eventoProduto(target, object);
                if (object.getTipoReceita() != null &&
                        TipoReceita.RECEITA_BRANCA_C3.equals(object.getTipoReceita().getTipoReceita())) {
                    autoCompleteConsultaCid.limpar(target);
                    autoCompleteConsultaCid.setProduto(object);
                    produto = object;
                    target.add(autoCompleteConsultaCid);
                }
            }
        });

        pnlConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                validaCamposBnafar(target, null);
                if (object.getTipoReceita() != null &&
                        TipoReceita.RECEITA_BRANCA_C3.equals(object.getTipoReceita().getTipoReceita())) {
                    autoCompleteConsultaCid.limpar(target);
                    autoCompleteConsultaCid.setProduto(null);
                    produto =  null;
                    target.add(autoCompleteConsultaCid);
                }
            }
        });

        if (!salvarContinuar) {
            btnHistoricoDispensacoes.setEnabled(false);
            btnReceitaPaciente.setEnabled(false);
        } else {
            verificarHistoricoDispensacoes(dispensacaoMedicamento.getUsuarioCadsusDestino());
            verificarReceituarioPendente(dispensacaoMedicamento.getUsuarioCadsusDestino());
            loadUsuarioCadusDado(null, dispensacaoMedicamento.getUsuarioCadsusDestino());
        }
        if (codigoBarras != null) {
            loadReceitaCodigoBarras(target);
        }
    }

    private void carregarCamposUsuario(AjaxRequestTarget target, UsuarioCadsus object) {
        try {

            btnProntuarioPaciente.setEnabled(true);
            usuarioCadsus = object;

            verificarDispensacaoPendenteUsuarioProvisorio(object);
            verificarHistoricoDispensacoes(target, object);
            verificarReceituarioPendente(target, object);
            loadUsuarioCadusDado(target, object);
            controlarAvatarUsuario(target, object);
            eventoConsultaUsuario(target, object);
            if (verificaUsoCampoCodigo) {
                if (parametroReferencia) {
                    txtCodigoUsuario.setComponentValue(object.getReferencia());
                } else {
                    txtCodigoUsuario.setComponentValue(object.getCodigo());
                }
            }
            if (obrigaResponsavelMenorIdade){
                autoCompleteConsultaResponsavel.setEnabled(true);
                if (MAIOR_IDADE > usuarioCadsus.getIdade()){
                    autoCompleteConsultaResponsavel.setRequired(true);
                }
                if (usuarioCadsus.getResponsavelFamiliar() != null) {
                    autoCompleteConsultaResponsavel.setComponentValue(target, usuarioCadsus.getResponsavelFamiliar());
                }
            }
            target.add(autoCompleteConsultaResponsavel);
            target.add(txtCodigoUsuario);
            target.add(btnProntuarioPaciente);
        } catch (ValidacaoException | DAOException ex) {
            warn(target, ex.getMessage());
            pnlConsultaUsuarioCadsus.limpar(target);
            kitPedidoPacienteList = new ArrayList();
            target.add(tblKitPedidoPaciente);
            tblKitPedidoPaciente.populate();
            tblKitPedido.clearSelection(target);
        }
    }

    private void carregarUsuario(AjaxRequestTarget target) {
        if (target != null) {
            pnlConsultaUsuarioCadsus.limpar(target);
        }
        if (codigoUsuario != null) {
            LoadManager load = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperty(UsuarioCadsus.PROP_CODIGO)
                    .addProperty(UsuarioCadsus.PROP_REFERENCIA)
                    .addProperty(UsuarioCadsus.PROP_NOME)
                    .addProperty(UsuarioCadsus.PROP_APELIDO)
                    .addProperty(UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL);
            if (parametroReferencia) {
                load.addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_REFERENCIA, StringUtil.getDigits(codigoUsuario.toString())));
            } else {
                load.addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, Coalesce.asLong(StringUtil.getDigits(codigoUsuario.toString()))));
            }
            List<UsuarioCadsus> usuarioCadsusList = load.start().getList();
            if (CollectionUtils.isNotNullEmpty(usuarioCadsusList)) {
                UsuarioCadsus object = usuarioCadsusList.get(0);
                pnlConsultaUsuarioCadsus.setComponentValue(object);
                carregarCamposUsuario(target, object);
            } else if (pnlConsultaUsuarioCadsus.getComponentValue() != null) {
                removerUsuarioCadsusAutoComplete(target);
                btnHistoricoDispensacoes.setEnabled(false);
                btnReceitaPaciente.setEnabled(false);
            } else {
                txtCodigoUsuario.setComponentValue(null);
                target.add(txtCodigoUsuario);
                btnHistoricoDispensacoes.setEnabled(false);
                btnReceitaPaciente.setEnabled(false);
//                btnCadPaciente.setVisible(true);
            }
        } else if (pnlConsultaUsuarioCadsus.getComponentValue() != null) {
            txtCodigoUsuario.setComponentValue(null);
            target.add(txtCodigoUsuario);
            btnHistoricoDispensacoes.setEnabled(false);
            btnReceitaPaciente.setEnabled(false);
        } else {
            txtCodigoUsuario.setComponentValue(null);
            target.add(txtCodigoUsuario);
            btnHistoricoDispensacoes.setEnabled(false);
            btnReceitaPaciente.setEnabled(false);
        }

        pnlConsultaUsuarioCadsus.addAjaxUpdateValue();
        pnlConsultaUsuarioCadsus.setOutputMarkupPlaceholderTag(true);
        if (target != null) {
            target.appendJavaScript(JScript.initMasks());
            target.appendJavaScript(JScript.removeAutoCompleteDrop());
            target.add(pnlConsultaUsuarioCadsus);
            target.add(btnHistoricoDispensacoes);
            target.add(btnReceitaPaciente);
        }
        btnCadPaciente.setOutputMarkupPlaceholderTag(true);
        target.add(btnCadPaciente);
    }

    private void controlarBtnProntuarioPaciente(AjaxRequestTarget target, boolean enable) {
        if (enable) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().getUsuario();
            if (usuarioLogado.isNivelAdminOrMaster()) {
                btnProntuarioPaciente.setEnabled(true);
            } else {
                boolean pagePermitted = new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().getUsuario(), ConsultaProntuarioPage.class.getName());
                if (isActionPermitted(usuarioLogado, Permissions.CONSULTAR) && pagePermitted) {
                    btnProntuarioPaciente.setEnabled(true);
                }
            }
        } else {
            btnProntuarioPaciente.setEnabled(false);
        }
        target.add(btnProntuarioPaciente);
    }

    private void abrirProntuarioPaciente(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
        Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().getUsuario();
        if (!usuarioLogado.isNivelAdminOrMaster() && usuarioLogado.getProfissional() == null) {
            warn(bundle("msgDeveSerConfiguradoProfissionalParaUsuario"));
        } else {
            setResponsePage(new ProntuarioPage(usuarioLogado, usuarioCadsus, codigoBarras, DispensacaoMedicamentoPage.class));
        }
    }

    private void actionBtnRemoverSelecionados(AjaxRequestTarget target) throws ValidacaoException {
        removerItemSelecionado();
        tblItems.update(target);

        if (CollectionUtils.isEmpty(items)) {
            autoCompleteConsultaEstabelecimentoExecutante.setEnabled(true);
            target.add(autoCompleteConsultaEstabelecimentoExecutante);
        }
    }

    private void validaCamposObrigatorios(AjaxRequestTarget target) throws ValidacaoException {
        String msg = "";
        if (dispensacaoMedicamento.getTipoReceita() == null) {
            if (!msg.equals("")) {
                msg = msg.concat("</br>");
            }
            msg = msg.concat(bundle("msgInformeTipoReceita"));
        }
        if (dispensacaoMedicamento.getUsuarioCadsusDestino() == null) {
            if (!msg.equals("")) {
                msg = msg.concat("</br>");
            }
            msg = msg.concat(bundle("msgInformePaciente"));
        }
        if (pnlConsultaEmpresaOrigem.getComponentValue() == null) {
            if (!msg.equals("")) {
                msg = msg.concat("</br>");
            }
            msg = msg.concat(bundle("msgInformeUnidadeOrigem"));
        }
        if (getEstabelecimentoExecutante() == null) {
            if (!msg.equals("")) {
                msg = msg.concat("</br>");
            }
            msg = msg.concat(bundle("msgInformeEstabeleciomento"));
        }
        if (!msg.equals("")) {
            throw new ValidacaoException(msg);
        }
    }

    private DropDown getDropDownReceitaContinua() {
        cbxReceitaContinua = DropDownUtil.getNaoSimDropDown(DispensacaoMedicamento.PROP_RECEITA_CONTINUA);

        cbxReceitaContinua.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean enable = txtReceita.isEnabled();
                txtReceita.setEnabled(!cbxReceitaContinua.getModelObject().equals(RepositoryComponentDefault.SIM)
                        && !dispensacaoMedicamento.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_BASICA) && !dispensacaoMedicamento.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_BRANCA));
                if (enable != txtReceita.isEnabled()) {
                    txtReceita.limpar(target);
                }
                target.add(txtReceita);
            }
        });

        return cbxReceitaContinua;
    }

    private DropDown getDropDownReceitas() {
        cbxTipoReceita = new RequiredDropDown(VOUtils.montarPath(DispensacaoMedicamento.PROP_TIPO_RECEITA));

        cbxTipoReceita.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                cbxReceitaContinua.limpar(target);
                TipoReceita tipoReceita = dispensacaoMedicamento.getTipoReceita();
                cbxReceitaContinua.setEnabled(tipoReceita != null && (TipoReceita.RECEITA_BASICA.equals(tipoReceita.getTipoReceita())
                        || TipoReceita.RECEITA_ANTIMICROBIANA.equals(tipoReceita.getTipoReceita())));

                boolean isHabilitaNumeroReceita = tipoReceita != null &&
                        Arrays.asList(TipoReceita.RECEITA_AMARELA, TipoReceita.RECEITA_AZUL,TipoReceita.RECEITA_BRANCA_C3).contains(tipoReceita.getTipoReceita());
                boolean isReceitaAzul = TipoReceita.RECEITA_AZUL.equals(tipoReceita.getTipoReceita());
                boolean isRecietaBrancaC3 = TipoReceita.RECEITA_BRANCA_C3.equals(tipoReceita.getTipoReceita());
                txtReceita.setEnabled(isHabilitaNumeroReceita);
                txtReceita.setRequired(isReceitaAzul || isRecietaBrancaC3);

                autoCompleteConsultaCid.setRequired(target,isRecietaBrancaC3);

                if (tipoReceita != null && !Arrays.asList(TipoReceita.RECEITA_AMARELA, TipoReceita.RECEITA_AZUL, TipoReceita.RECEITA_BRANCA_C3).contains(tipoReceita.getTipoReceita())) {
                    txtReceita.limpar(target);
                }
                target.add(cbxReceitaContinua);
                target.add(txtReceita);
            }
        });

        cbxTipoReceita.addChoice(null, "");

        List<TipoReceitaEmpresa> tipoReceitaEmpresas = LoadManager.getInstance(TipoReceitaEmpresa.class)
                .addProperties(new HQLProperties(TipoReceita.class, TipoReceitaEmpresa.PROP_TIPO_RECEITA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(TipoReceitaEmpresa.PROP_EMPRESA, getEstabelecimentoExecutante()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoReceitaEmpresa.PROP_TIPO_RECEITA, TipoReceita.PROP_TIPO_RECEITA), BuilderQueryCustom.QueryParameter.DIFERENTE, TipoReceita.RECEITA_MAGISTRAL))
                .start().getList();
        for (TipoReceitaEmpresa tipoReceitaEmpresa : tipoReceitaEmpresas) {
            this.cbxTipoReceita.addChoice(tipoReceitaEmpresa.getTipoReceita(), tipoReceitaEmpresa.getTipoReceita().getDescricao());
        }

        return cbxTipoReceita;
    }

    private List<IColumn> getColumnsItems() {
        List<IColumn> columns = new ArrayList();

        DispensacaoMedicamentoItem proxy = on(DispensacaoMedicamentoItem.class);


        columns.add(getCustomColumnCheckBox());
        columns.add(getCustomColumnItems());
        columns.add(createColumn(bundle("produto"), proxy.getProduto().getDescricaoFormatado()));
        columns.add(createColumn(bundle("un"), proxy.getProduto().getUnidade().getUnidade()));
        columns.add(createColumn(bundle("lotes"), proxy.getDescricaoLote()));
        columns.add(createColumn(bundle("prescrito"), proxy.getQuantidadePrescrita()));
        columns.add(createColumn(bundle("posologia"), proxy.getPosologiaFormatado()));
        columns.add(createColumn(bundle("tipoPrescricao"), proxy.getProduto().getFlagDispensacaoEspecialFormatado()));
        columns.add(createColumn(bundle("dispensado"), proxy.getQuantidadeDispensada()));
        columns.add(createColumn(bundle("dataProxDispensacao"), proxy.getDataProximaDispensacaoCalculada()));
        columns.add(createColumn(bundle("situacao"), proxy.getDescricaoStatus()));
        columns.add(createColumn(bundle("cid"), proxy.getCid().getCodigo()));

        return columns;
    }

    //Método criado para que possa ser permitido adicionar um Check box ao lado de cada elemente contido na tabela de item
    //Permitindo assim remover mais de um item selecionado por vez
    private CustomColumn<DispensacaoMedicamentoItem> getCustomColumnCheckBox() {
        return new CustomColumn<DispensacaoMedicamentoItem>() {
            @Override
            public Component getComponent(String componentId, DispensacaoMedicamentoItem rowObject) {
                boolean selected = Lambda.exists(selectedItens, Lambda.having(Lambda.on(DispensacaoMedicamentoItem.class).getProduto(), Matchers.equalTo(rowObject.getProduto())));
                return new DispensacaoMedicamentosCheckboxColumnPanel(componentId, rowObject, selected) {
                    @Override
                    public void onSelectionAction(AjaxRequestTarget target, boolean selected) {
                        DispensacaoMedicamentoItem dmi = getObject();
                        if (selected) {
                            selectedItens.add(dmi);
                        } else {
                            selectedItens = Lambda.select(selectedItens, Lambda.having(Lambda.on(DispensacaoMedicamentoItem.class).getProduto(), Matchers.not(Matchers.equalTo(dmi.getProduto()))));
                        }
                    }
                };
            }
        };
    }

    //Metodo criado para remover todos os elementi selecionado com o CheckBox
    private void removerItemSelecionado() throws ValidacaoException {

        if (selectedItens != null) {
            principal:
            for (int i = 0; i < selectedItens.size(); i++) {
                for (int j = 0; j < items.size(); j++) {
                    if (items.get(j).getProduto().equals(selectedItens.get(i).getProduto())) {
                        items.remove(j);
                        continue principal;
                    }
                }
            }
        }
        selectedItens.clear();
    }

    private IColumn getCustomColumnItems() {
        return new MultipleActionCustomColumn<DispensacaoMedicamentoItem>() {
            @Override
            public void customizeColumn(DispensacaoMedicamentoItem rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<DispensacaoMedicamentoItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItem modelObject) throws ValidacaoException, DAOException {
                        if (DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE.equals(modelObject.getStatus())) {
                            initDlgAdicionarItemSemEstoque(target, modelObject);
                        } else {
                            dlgAdicionarItem.setObject(target, modelObject, true, getEstabelecimentoExecutante(), null);
                            dlgAdicionarItem.show(target);
                        }
                    }
                }).setEnabled(isEnabledEditar(rowObject));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<DispensacaoMedicamentoItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItem modelObject) throws ValidacaoException, DAOException {
                        removerItem(modelObject);
                        tblItems.update(target);

                        if (CollectionUtils.isEmpty(items)) {
                            autoCompleteConsultaEstabelecimentoExecutante.setEnabled(true);
                            target.add(autoCompleteConsultaEstabelecimentoExecutante);
                        }
                    }
                });

                if (rowObject.getReceituarioItem() != null) {
                    List<DispensacaoMedicamentoItem> itens = buscarDispensacoesDoItemNoReceituario(
                            rowObject,
                            rowObject.getReceituarioItem().getReceituario());

                    Double totalDispensado = calcularTotalDispensado(itens);

                    final long resultado = subtrairQuantidadeDispensadaDaPrescrita(rowObject.getReceituarioItem(), totalDispensado);

                    if (resultado > 0) {
                        addAction(ActionType.WARN, new IAction() {
                            @Override
                            public void action(AjaxRequestTarget target) throws ValidacaoException {
                                throw new ValidacaoException(BundleManager.getString(("msgExistemMedicamentosPrescritosNaoRetirados")));
                            }
                        });
                    }
                }
            }
        };
    }

    private void atribuirZeroSeQuantidadeIgualNulo(ReceituarioItem item) {
        if (item.getQuantidadePrescrita() == null) {
            item.setQuantidadePrescrita(0L);
        }
        if (item.getQuantidadeDispensada() == null) {
            item.setQuantidadeDispensada(0L);
        }
    }

    private long subtrairQuantidadeDispensadaDaPrescrita(ReceituarioItem item, Double totalDispensado) {
        return item.getQuantidadePrescrita() - totalDispensado.longValue();
    }

    private boolean isEnabledEditar(DispensacaoMedicamentoItem item) {
        return RepositoryComponentDefault.NAO.equals(utilizarLeitoraCodigoBarrasProduto) || txtCodigoBarras.isEnabled() && (CollectionUtils.isEmpty(item.getMovimentoGrupoEstoqueItemDTOList()) || item.getMovimentoGrupoEstoqueItemDTOList().get(0).getGrupoEstoque() == null || !item.getMovimentoGrupoEstoqueItemDTOList().get(0).getGrupoEstoque().equals("0"));
    }

    private void removerItem(DispensacaoMedicamentoItem rowObject) {
        for (int i = 0; i < items.size(); i++) {
            if (items.get(i) == rowObject) {
                items.remove(i);
            }
        }
    }

    private ICollectionProvider getCollectionProviderItems() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return items;
            }
        };
    }

    private void validaDataDispensacao() throws ValidacaoException {
        if (txtHora.getComponentValue() == null) {
            throw new ValidacaoException(bundle("msgInformeHorario"));
        }
        if (modelDispensacao.getObject().getDataDispensacao().before(dataMinimaDispensacao)) {
            throw new ValidacaoException(bundle("dataDispensacaoNaoPodeInferiorXDias", parametroDiasDataPrescricao));
        }
        if (modelDispensacao.getObject().getDataDispensacao().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(bundle("dataDispensacaoNaoPodeSuperiorDataAtual"));
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("dispensacaoMedicamentosMateriais");
    }

    private void loadReceitaCodigoBarras(AjaxRequestTarget target) {
        boolean err = false;
        try {
            loadReceita(target);
        } catch (DAOException ex) {
            err = true;
            modalError(target, ex);
        } catch (ValidacaoException ex) {
            err = true;
            modalWarn(target, ex);
        }
        if (err) {
            txtCodigoBarras.limpar(target);
            btnHistoricoDispensacoes.setEnabled(false);
            btnReceitaPaciente.setEnabled(false);
            target.add(btnHistoricoDispensacoes);
            target.add(btnReceitaPaciente);
        }
        tblItems.update(target);
    }

    private void loadReceita(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        String codigoBarras_ = this.codigoBarras;
//        String codigoBarrasVersion_ = this.codigoBarrasVersion;

        if (StringUtils.trimToNull(this.codigoBarras) == null) {
            return;
        }

        if (dispensacaoMedicamento != null && dispensacaoMedicamento.getReceituario() != null) {
            throw new ValidacaoException(BundleManager.getString("ePermitidoCarregarSomenteReceituarioPorVez"));
        }

        if (dcDataDispensacao.getComponentValue() == null) {
            throw new ValidacaoException(BundleManager.getString("msg_informe_data_dispensacao_para_prosseguir"));
        }

        Date dataDisp = DataUtil.mergeDataHora(dcDataDispensacao.getComponentValue(), txtHora.getComponentValue());

        target.add(dadosReceitaItem);
        txtCodigoBarras.limpar(target);
        txtReceita.limpar(target);
        txtResponsavel.limpar(target);
        cbxTipoReceita.limpar(target);
        cbxReceitaContinua.limpar(target);
        dchDataReceita.limpar(target);
        pnlConsultaUsuarioCadsus.limpar(target);
        txtObservacaoDispensacao.limpar(target);
        pnlConsultaEmpresaOrigem.limpar(target);
        pnlConsultaProfissional.limpar(target);
        pnlConsultaProduto.limpar(target);
        target.prependJavaScript("$('#" + pnlConsultaUsuarioCadsus.getTxtDescricao().getTextField().getMarkupId() + "').tokenInput('hideDropDown')");
        modelDispensacaoItem.setObject(dispensacaoMedicamentoItem = new DispensacaoMedicamentoItem());

        this.codigoBarras = codigoBarras_;
//        this.codigoBarrasVersion = codigoBarrasVersion_;
        Empresa estabelecimento = modelDispensacao.getObject().getEmpresa();
        modelDispensacao.setObject(dispensacaoMedicamento = new DispensacaoMedicamento());
        dispensacaoMedicamento.setDataDispensacao(dataDisp);
        horaDispensacao = dataDisp;
        txtHora.setComponentValue(dataDisp);
        form.getModel().getObject().setEmpresa(estabelecimento);
        items.clear();
//        Receituario receituario = loadReceituario(codigoBarras, true);

        Receituario receituario = LoadManager.getInstance(Receituario.class)
                .addProperties(new HQLProperties(Receituario.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(Receituario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS)).getProperties())
                .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(Receituario.PROP_TIPO_RECEITA)).getProperties())
                .addProperty(VOUtils.montarPath(Receituario.PROP_PROFISSIONAL, Profissional.PROP_NUMERO_REGISTRO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_CODIGO), new Long(this.codigoBarras)))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.DIFERENTE, Receituario.Situacao.CANCELADO.value()))
                .start().getVO();

//        if (receituario == null) {
//            receituario = loadReceituario(codigoBarras.substring(0, codigoBarras.length() - 3), false);
//        }

        if (receituario == null) {
            enableFields(target, true);
            txtCodigoBarras.limpar(target);
            throw new ValidacaoException(Bundle.getStringApplication("msg_receita_invalida"));
        }

//        String codigoBarrasTela = txtCodigoBarras.getValue().replaceAll("^0+", "").trim();
//
//        if (!codigoBarrasTela.equals(receituario.getCodigoBarras())) {
//            throw new ValidacaoException(Bundle.getStringApplication("msg_codigo_barras_inativo", receituario.getCodigoBarras()));
//        }

        LoadManager loadManagerReceituarioItem = LoadManager.getInstance(ReceituarioItem.class)
                .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                .addProperties(new HQLProperties(Produto.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO)).getProperties())
                .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_TIPO_RECEITA)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO), receituario))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_STATUS), QueryCustom.QueryCustomParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()));

        List<ReceituarioItem> receituarioItems = new ArrayList();
        List<ReceituarioItem> receituarioItemsTemp = loadManagerReceituarioItem.start().getList();

        Long validadeReceita = 0L;
        if (TipoReceita.RECEITA_AMARELA.equals(receituario.getTipoReceita().getTipoReceita())) {
            validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaAmarela");
        } else if (TipoReceita.RECEITA_AZUL.equals(receituario.getTipoReceita().getTipoReceita())) {
            validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaAzul");
        } else if (TipoReceita.RECEITA_BRANCA.equals(receituario.getTipoReceita().getTipoReceita())) {
            validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaBranca");
        } else if (TipoReceita.RECEITA_BASICA.equals(receituario.getTipoReceita().getTipoReceita())) {
            validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaBasica");
        } else if (TipoReceita.RECEITA_ANTIMICROBIANA.equals(receituario.getTipoReceita().getTipoReceita())) {
            List<Long> codigoReceituarioItemList = Lambda.extract(receituarioItemsTemp, Lambda.on(ImpressaoReceituarioDTO.class).getCodigo());
            validadeReceita = ReceituarioHelper.diasValidadeReceitaAntimicrobiano(codigoReceituarioItemList);
        } else if (TipoReceita.RECEITA_BRANCA_C3.equals(receituario.getTipoReceita().getTipoReceita())) {
            validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaBrancaC3");
        }

        if (dataDisp == null) {
            dataDisp = DataUtil.getDataAtual();
        }

        Date dataValidade = Data.addDias(receituario.getDataCadastro(), validadeReceita != null ? validadeReceita.intValue() : 0);

        if (DataUtil.zerarHoraData(dataValidade).before(DataUtil.zerarHoraData(dataDisp))) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_receita_fora_validade"));
        }

        DispensacaoMedicamento loadDispensacaoMedicamento = LoadManager.getInstance(DispensacaoMedicamento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamento.PROP_RECEITUARIO, receituario))
                .addSorter(new QueryCustom.QueryCustomSorter(DispensacaoMedicamento.PROP_DATA_ULTIMA_DISPENSACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .setMaxResults(1)
                .start().getVO();

        usuarioCadsus = receituario.getUsuarioCadsus();

        validacaoLiberacaoReceitaPacienteForaMunicipio(usuarioCadsus);

        Long permiteDispensarSemDataLimite = null;

        try {
            permiteDispensarSemDataLimite = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).<Long>getParametro("permiteDispensarSemDataLimite");
        } catch (DAOException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        receitaContinua = false;
        for (ReceituarioItem receituarioItem : receituarioItemsTemp) {
            if (RepositoryComponentDefault.SIM.equals(receituarioItem.getFlagContinuo())) {
                receitaContinua = true;
            }
            if (RepositoryComponentDefault.SIM.equals(receituarioItem.getFlagContinuo()) || receituarioItem.getDispensacaoMedicamentoItem() == null) {
                receituarioItems.add(receituarioItem);
            } else if (RepositoryComponentDefault.SIM_LONG.equals(permiteDispensarSemDataLimite)) {
                receituarioItems.add(receituarioItem);
            }
        }

        if (receitaContinua) {
            cbxReceitaContinua.limpar(target);
            cbxReceitaContinua.setComponentValue(RepositoryComponentDefault.SIM);
            cbxReceitaContinua.setModelObject(RepositoryComponentDefault.SIM);
            cbxReceitaContinua.setEnabled(true);
            target.add(cbxReceitaContinua);
        }

        if (receituarioItems.isEmpty() && loadDispensacaoMedicamento == null && !RepositoryComponentDefault.SIM_LONG.equals(permiteDispensarSemDataLimite)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_receita_invalida"));
        } else if (receituarioItems.isEmpty() && loadDispensacaoMedicamento != null && !RepositoryComponentDefault.SIM_LONG.equals(permiteDispensarSemDataLimite)) {
            throw new ValidacaoException(BundleManager.getString("receitaPacienteDispesadaDia", usuarioCadsus.getNomeSocial(), loadDispensacaoMedicamento.getDataUltimaDispensacao()));
        } else {
            dispensacaoMedicamento = new DispensacaoMedicamento();
            dispensacaoMedicamento.setUsuarioCadsusDestino(usuarioCadsus);
            dispensacaoMedicamento.setTipoReceita(receituario.getTipoReceita());
            dispensacaoMedicamento.setReceitaContinua(receituario.getReceitaContinua());
            dispensacaoMedicamento.setReceita(Coalesce.asString(receituario.getNumeroReceita()));
            dispensacaoMedicamento.setDataReceita(receituario.getDataCadastro());
            dispensacaoMedicamento.setEmpresaOrigem(receituario.getEmpresa());
            dispensacaoMedicamento.setProfissional(receituario.getProfissional());
            dispensacaoMedicamento.setNumeroRegistro(receituario.getProfissional().getNumeroRegistro());
            dispensacaoMedicamento.setDataDispensacao(dataDisp);
            dispensacaoMedicamento.setEmpresa(estabelecimento);
            if (obrigaResponsavelMenorIdade && usuarioCadsus != null){
                dispensacaoMedicamento.setUsuarioCadsusResponsavel(usuarioCadsus.getResponsavelFamiliar());
            }

            verificarHistoricoDispensacoes(target, usuarioCadsus);
            verificarReceituarioPendente(target, usuarioCadsus);
            loadUsuarioCadusDado(target, usuarioCadsus);

            items.clear();
            Long i = 0L;

            List<DispensacaoMedicamentoItem> itensAdd = new ArrayList();
            Map<Produto, Date> itensNotAdd = new HashMap();
            Map<Produto, Date> itensVencidos = new HashMap();

            for (ReceituarioItem receituarioItem : receituarioItems) {
                Produto produto = receituarioItem.getProduto();


                if (produto == null) {
                    continue;
                }

                if (RepositoryComponentDefault.SIM_LONG.equals(receituarioItem.getProduto().getFlagEmiteLme()) && periodoEntreDias(receituario.getDataCadastro(), NOVENTA_DIAS)) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_data_solicitacao_lme"));
                }

                DispensacaoMedicamentoItem _dispensacaoMedicamentoItem = new DispensacaoMedicamentoItem();

                if (!consultarEstoqueSemLoteVencido(produto)) {
                    _dispensacaoMedicamentoItem.setStatus(DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE);
                }
                if (_dispensacaoMedicamentoItem.getStatus() == null) {
                    _dispensacaoMedicamentoItem.setStatus(DispensacaoMedicamentoItem.STATUS_NORMAL);
                }
                _dispensacaoMedicamentoItem.setDispensacaoMedicamento(dispensacaoMedicamento);
                _dispensacaoMedicamentoItem.setItem(i);
                _dispensacaoMedicamentoItem.setReceituarioItem(receituarioItem);
                _dispensacaoMedicamentoItem.setProduto(produto);
                _dispensacaoMedicamentoItem.setCid(receituarioItem.getCid());
                _dispensacaoMedicamentoItem.setQuantidadePrescrita(Coalesce.asLong(receituarioItem.getQuantidadePrescrita()).doubleValue());
                _dispensacaoMedicamentoItem.setPosologia(receituarioItem.getQuantidadePosologia() != null ? receituarioItem.getQuantidadePosologia() : 0L);

                if (receituarioItem.getFlagTratamentoContinuo().equals(RepositoryComponentDefault.NAO_LONG)) {
                    Double totalDispensado = calcularTotalDispensado(buscarDispensacoesDoItemNoReceituario(_dispensacaoMedicamentoItem, receituario));
                    _dispensacaoMedicamentoItem.setQuantidadeDispensada(totalDispensado);
                } else {
                    _dispensacaoMedicamentoItem.setQuantidadeDispensada(receituarioItem.getQuantidadeDispensada() != null ? receituarioItem.getQuantidadeDispensada().doubleValue() : 0D);
                }

                if (ReceituarioItem.FREQUENCIA_MES.equals(receituarioItem.getFrequencia())) {
                    _dispensacaoMedicamentoItem.setTipoUso(DispensacaoMedicamentoItem.TipoUso.MES.value());
                } else if (ReceituarioItem.FREQUENCIA_SEMANA.equals(receituarioItem.getFrequencia())) {
                    _dispensacaoMedicamentoItem.setTipoUso(DispensacaoMedicamentoItem.TipoUso.SEMANA.value());
                } else {
                    _dispensacaoMedicamentoItem.setTipoUso(DispensacaoMedicamentoItem.TipoUso.DIA.value());
                }

                validarAcaoBtnAdicionarItem(target, _dispensacaoMedicamentoItem, false);
                Date dataDispensacao = validacaoDispensacaoHistorico(_dispensacaoMedicamentoItem.getProduto(), target, false);

                if (receituarioItem.getDataValidadeItem() != null && DataUtil.getDataAtualSemHora().after(receituarioItem.getDataValidadeItem())) {
                    itensVencidos.put(_dispensacaoMedicamentoItem.getProduto(), receituarioItem.getDataValidadeItem());
                } else if (dataDispensacao != null) {
                    itensNotAdd.put(_dispensacaoMedicamentoItem.getProduto(), dataDispensacao);
                } else {
                    itensAdd.add(_dispensacaoMedicamentoItem);
                }
            }
            if (!items.isEmpty()) {
                throw new ValidacaoException(BundleManager.getString("itensJaAdicionadosLimpeParaContinuaroProcesso"));
            }
            items.addAll(itensAdd);
            dispensacaoMedicamento.setReceituario(receituario);
            modelDispensacao.setObject(dispensacaoMedicamento);
            enableFields(target, false);
            pnlConsultaProduto.setEnabled(!TipoReceita.RECEITA_AZUL.equals(receituario.getTipoReceita().getTipoReceita()) && !TipoReceita.RECEITA_AMARELA.equals(receituario.getTipoReceita().getTipoReceita()));

            target.focusComponent(txtResponsavel);
            if (!itensNotAdd.isEmpty() || !itensVencidos.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                if (!itensVencidos.isEmpty()) {
                    for (Map.Entry<Produto, Date> entry : itensVencidos.entrySet()) {
                        sb.append(BundleManager.getString("itemReceitaVencido", entry.getKey().getDescricaoFormatado(), Data.formatar(entry.getValue())));
                        sb.append("</br>");
                    }
                }
                if (!itensNotAdd.isEmpty()) {
                    List<String> codigoProdutos = new ArrayList();
                    for (Map.Entry<Produto, Date> entry : itensNotAdd.entrySet()) {
                        codigoProdutos.add(entry.getKey().getCodigo());
                        sb.append(BundleManager.getString("medicamentoJaDispensadoPacienteDataProximaDispensacao", entry.getKey().getDescricaoFormatado(), Data.formatar(entry.getValue())));
                        sb.append("</br>");
                    }
                    BOFactoryWicket.getBO(DispensacaoMedicamentoFacade.class).gerarDispensacaoEstatistica(dispensacaoMedicamento.getUsuarioCadsusDestino().getCodigo(), codigoProdutos);
                }
                modalWarn(target, new ValidacaoException(sb.toString()));
            }
        }

        if (parametroReferencia) {
            txtCodigoUsuario.setComponentValue(usuarioCadsus.getReferencia());
        } else {
            txtCodigoUsuario.setComponentValue(usuarioCadsus.getCodigo());
        }

        target.add(txtCodigoUsuario);
        controlarAvatarUsuario(target, usuarioCadsus);
        txtHora.setEnabled(false);
        dcDataDispensacao.setEnabled(false);
        txtCodigoBarras.setEnabled(false);
        controlarBtnProntuarioPaciente(target, true);
        target.add(txtHora);
        target.add(dcDataDispensacao);
    }


    private Receituario loadReceituario(String codigoBarras, boolean buscarCodigoDeBarras) {
        LoadManager lm = LoadManager.getInstance(Receituario.class)
                .addProperties(new HQLProperties(Receituario.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(Receituario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS)).getProperties())
                .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(Receituario.PROP_TIPO_RECEITA)).getProperties())
                .addProperty(VOUtils.montarPath(Receituario.PROP_PROFISSIONAL, Profissional.PROP_NUMERO_REGISTRO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.DIFERENTE, Receituario.Situacao.CANCELADO.value()));

        if (buscarCodigoDeBarras) {
            lm.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_CODIGO_BARRAS), codigoBarras));
        } else {
            String codAtualizado = codigoBarras.replaceAll("^0+", "").trim();
            lm.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_CODIGO), new Long(codAtualizado)));
        }
        return lm.start().getVO();
    }

    private boolean totalmenteDispensado(ReceituarioItem receituarioItem) {
        return receituarioItem.getQuantidadePrescrita().equals(receituarioItem.getQuantidadeDispensada());
    }

    private void verificarDispensacaoPendenteUsuarioProvisorio(UsuarioCadsus usuarioCadsus) throws ValidacaoException, DAOException {
        String parametroValidaDispensacaoPacienteProvisorio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("validaDispensacaoPacienteProvisorio");
        if (RepositoryComponentDefault.NAO.equals(parametroValidaDispensacaoPacienteProvisorio)) {
            return;
        }
        if (usuarioCadsus != null && UsuarioCadsus.SITUACAO_PROVISORIO.equals(usuarioCadsus.getSituacao())) {
            DispensacaoMedicamento item = LoadManager.getInstance(DispensacaoMedicamento.class)
                    .addProperty(DispensacaoMedicamento.PROP_CODIGO)
                    .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO, usuarioCadsus))
                    .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamento.PROP_DATA_DISPENSACAO, QueryCustom.QueryCustomParameter.MENOR, Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial()))
                    .setMaxResults(1)
                    .start().getVO();

            if (item != null) {
                throw new ValidacaoException(BundleManager.getString("msgNaoPossivelRealizarDispensacaoPacienteProvisorioFavorRegularizarInformacoesCadastrais"));
            }
        }
    }

    private void loadUsuarioCadusDado(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
        usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addProperties(new HQLProperties(UsuarioCadsusDado.class).getProperties())
                .setId(usuarioCadsus.getCodigo())
                .start().getVO();

        if (usuarioCadsusDado != null) {
            if (usuarioCadsusDado.getPeso() != null) {
                txtPeso.setComponentValue(usuarioCadsusDado.getPeso());
                if (target != null) target.add(txtPeso);
            }

            if (usuarioCadsusDado.getAltura() != null) {
                txtAltura.setComponentValue(usuarioCadsusDado.getAltura());
                if (target != null) target.add(txtAltura);
            }
        }

        btnEditarObservacao.setEnabled(true);
        if (target != null) {
            target.add(txtObservacaoDispensacao);
            target.add(btnEditarObservacao);
        }
    }

    private void verificarHistoricoDispensacoes(UsuarioCadsus usuarioCadsus) {
        btnHistoricoDispensacoes.setEnabled(false);
        if (usuarioCadsus != null) {
            DispensacaoMedicamentoItem item = LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                    .addProperty(DispensacaoMedicamentoItem.PROP_CODIGO)
                    .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, usuarioCadsus))
                    .setMaxResults(1)
                    .start().getVO();
            if (item != null) {
                try {
                    dlgHistoricoDispensacoes.setModelObject(usuarioCadsus, getEstabelecimentoExecutante());
                    btnHistoricoDispensacoes.setEnabled(true);
                } catch (ValidacaoException | DAOException e) {
                    Loggable.log.error(e);
                }
            } else {
                btnHistoricoDispensacoes.setEnabled(false);
            }
        }
    }

    private void verificarHistoricoDispensacoes(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
        autoCompleteConsultaCid.limpar(target);
        verificarHistoricoDispensacoes(usuarioCadsus);
        target.add(autoCompleteConsultaCid);
        target.add(btnHistoricoDispensacoes);
    }

    private void verificarReceituarioPendente(UsuarioCadsus usuarioCadsus) {
        try {
            btnReceitaPaciente.setEnabled(false);
            if (usuarioCadsus != null) {
                boolean empresaPossuiAntimicrobiano = LoadManager.getInstance(TipoReceitaEmpresa.class)
                        .addProperty(TipoReceitaEmpresa.PROP_CODIGO)
                        .addParameter(new QueryCustom.QueryCustomParameter(TipoReceitaEmpresa.PROP_EMPRESA, getEstabelecimentoExecutante()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoReceitaEmpresa.PROP_TIPO_RECEITA, TipoReceita.PROP_TIPO_RECEITA), BuilderQueryCustom.QueryParameter.IGUAL, TipoReceita.RECEITA_ANTIMICROBIANA))
                        .exists();

                Long permiteDispensarSemDataLimite = null;

                try {
                    permiteDispensarSemDataLimite = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).<Long>getParametro("permiteDispensarSemDataLimite");
                } catch (DAOException e) {
                    br.com.ksisolucoes.util.log.Loggable.log.error(e);
                }

                Collection<Receituario> receituarios = BOFactoryWicket.getBO(DispensacaoMedicamentoFacade.class).queryReceituarioPacienteSemDispensacao(usuarioCadsus.getCodigo(), empresaPossuiAntimicrobiano, permiteDispensarSemDataLimite);

                if (CollectionUtils.isNotNullEmpty(receituarios)) {
                    List<Receituario> receituariosList = new ArrayList<>(receituarios);
                    for (int i = receituarios.size() - 1; i >= 0; i--) {
                        if (receituariosList.get(i).getCid() != null) {
                            Cid cid = receituariosList.get(i).getCid();
                            if (cid != null && cid.getCodigo() != null) {
                                autoCompleteConsultaCid.setComponentValue(receituariosList.get(i).getCid());
                            }
                            break;
                        }
                    }
                }

                if (receituarios.size() > 0L) {
                    dlgReceitasPaciente.setReceituarios(receituarios);
                    btnReceitaPaciente.setEnabled(true);
                } else {
                    btnReceitaPaciente.setEnabled(false);
                }
            }
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void verificarReceituarioPendente(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
        verificarReceituarioPendente(usuarioCadsus);
        target.add(autoCompleteConsultaCid);
        target.add(btnReceitaPaciente);
    }

    private void enableFields(AjaxRequestTarget target, boolean enable) {
        pnlConsultaUsuarioCadsus.setEnabled(enable);
        if (verificaUsoCampoCodigo) {
            txtCodigoUsuario.setEnabled(enable);
        }
        if (codigoBarras != null && dispensacaoMedicamento.getTipoReceita() != null && TipoReceita.RECEITA_AZUL.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
            txtReceita.setRequired(true);
        } else if (dispensacaoMedicamento.getTipoReceita() != null && TipoReceita.RECEITA_BRANCA_C3.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
            txtReceita.setRequired(true);
        } else {
            txtReceita.setEnabled(enable);
            txtReceita.setRequired(false);
        }
        dchDataReceita.setEnabled(enable);
        pnlConsultaEmpresaOrigem.setEnabled(enable);
        pnlConsultaProfissional.setEnabled(enable);
        if (receitaContinua) {
            cbxReceitaContinua.setComponentValue(RepositoryComponentDefault.SIM);
            cbxReceitaContinua.setEnabled(true);
        } else {
            cbxReceitaContinua.setEnabled(enable);
        }
        cbxTipoReceita.setEnabled(enable);
        autoCompleteConsultaEstabelecimentoExecutante.setEnabled(enable);
        if (target != null) {
            target.add(autoCompleteConsultaEstabelecimentoExecutante);
            target.add(txtCodigoUsuario);
        }
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (!validarProcedimentoFaturadoDispensa()) {
            throw new ValidacaoException(bundle("msg_e_obrigatorio_a_definicao_do_parametro_x", "ProcedimentoFaturadoDispensa"));
        }
        if (dispensacaoMedicamento != null
                && dispensacaoMedicamento.getReceituario() != null
                && RepositoryComponentDefault.SIM_LONG.equals(dispensacaoMedicamento.getReceituario().getFlagDispensado())) {
            throw new ValidacaoException(bundle("msg_receita_ja_dispensada_dia_X", dispensacaoMedicamento.getDataDispensacao()));
        }
        dispensacaoMedicamento.setDataDispensacao(DataUtil.mergeDataHora(dispensacaoMedicamento.getDataDispensacao(), txtHora.getComponentValue()));
        modelDispensacao.getObject().setDataDispensacao(DataUtil.mergeDataHora(dispensacaoMedicamento.getDataDispensacao(), txtHora.getComponentValue()));
        validaDataDispensacao();
        if (saveDispensacao()) {
            if (existsItemPermissaoComprovante()) {
                RelatorioImpressaoDispensacaoMedicamentoDTOParam param = new RelatorioImpressaoDispensacaoMedicamentoDTOParam();
                param.setCodigosDispensasaoMedicamento(Collections.singletonList(dispensacaoMedicamento.getCodigo()));
                dlgImprimirComprovante.show(target, param);
            } else {
                redirectPage();
            }
        }
    }

    private void validaCamposBnafar(AjaxRequestTarget target, Produto produto) {
        if (produto != null) {
            if (produto.getTipoProdutoCatmat() != null) {
                if (RepositoryComponentDefault.SIM_LONG.equals(produto.getFlagExportaHorus()) && Produto.TipoProdutoCatmat.ESPECIALIZADO.value().equals(produto.getTipoProdutoCatmat())) {
                    txtPeso.setRequired(true);
                    txtAltura.setRequired(true);
                    autoCompleteConsultaCid.setRequired(true);
                    target.add(txtPeso);
                    target.add(txtAltura);
                    target.add(autoCompleteConsultaCid);
                }
            }
        } else {
            txtPeso.setRequired(false);
            txtAltura.setRequired(false);
            autoCompleteConsultaCid.setRequired(false);
            target.add(txtPeso);
            target.add(txtAltura);
            target.add(autoCompleteConsultaCid);
        }
    }



    private void validaVinculoDomiciliar(AjaxRequestTarget target) throws DAOException {
        String emiteAlertaSemDomicilio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("EmiteAlertaSemDomicilio");
        if (RepositoryComponentDefault.SIM.equals(emiteAlertaSemDomicilio)) {
            if (!verificaVinculoDomiciliar((UsuarioCadsus) pnlConsultaUsuarioCadsus.getComponentValue())){
                warn(Bundle.getStringApplication("msgInformaVinculoDomiciliar"));
            }
        }
    }

    private void redirectPage() {
        Page page;
        if (salvarContinuar) {
            page = new DispensacaoMedicamentoPage(createDispensacaoContinuar());
        } else {
            page = new DispensacaoMedicamentoPage();
        }

        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, getMsgSalvo(dispensacaoMedicamento));
    }

    private boolean existsItemPermissaoComprovante() {
        return LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, dispensacaoMedicamento))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_COMPROVANTE), RepositoryComponentDefault.SIM_LONG))
                .exists();
    }

    private boolean saveDispensacao() throws DAOException, ValidacaoException {
        verificarDispensacaoPendenteUsuarioProvisorio(dispensacaoMedicamento.getUsuarioCadsusDestino());

        if (cbxTipoReceita.getModelObject() != null) {
            TipoReceita tipoReceita = (TipoReceita) cbxTipoReceita.getModelObject();
            if (tipoReceita.getControlada().equals("S")) {
                if (pnlConsultaProfissional.getModelObject() != null) {
                    Profissional profissional = (Profissional) pnlConsultaProfissional.getModelObject();
                    if (profissional.getNumeroRegistro() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_profissional_sem_crm"));
                    }
                }
            }
        }

        if (items.isEmpty()) {
            throw new ValidacaoException(BundleManager.getString("informePeloMenosUmItem"));
        }

        for (DispensacaoMedicamentoItem _dispensacaoMedicamentoItem : items) {
            if (!DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE.equals(_dispensacaoMedicamentoItem.getStatus())) {
                if (_dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada() == 0D) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_todos_itens_dispensacao_quantidade_maior_zero"));
                }
                SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class)
                        .setId(_dispensacaoMedicamentoItem.getProduto().getSubGrupo().getId())
                        .start().getVO();
                if (subGrupo.getFlagControlaGrupoEstoque().equals(RepositoryComponentDefault.SIM)) {
                    if (_dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList() == null || _dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList().isEmpty()) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_todos_itens_com_controle_de_lote_tem_que_ser_definido"));
                    }
                }
            }
        }

        if (dispensacaoMedicamento.getReceituario() != null) {
            if (this.codigoBarras == null || this.codigoBarras.isEmpty()) {
                throw new ValidacaoException(BundleManager.getString("codigoBarrasDiferenteFavorRepitaOProcesso"));
            } else if (!dispensacaoMedicamento.getReceituario().getCodigo().equals(new Long(this.codigoBarras))) {
                throw new ValidacaoException(BundleManager.getString("codigoBarrasDiferenteFavorRepitaOProcesso"));
            }
        }

        dispensacaoMedicamento.setItensDispensacaoMedicamentoSet(new HashSet(items));

        QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
        param.setProcedimento(getProcedimentoFaturadoDispensa());
        param.setEmpresa(dispensacaoMedicamento.getEmpresa());
        param.setProfissional(dispensacaoMedicamento.getProfissionalFaturamentoDispensacao());
        TabelaCbo cbo = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaProfissionalCargaHoraria(param);

        dispensacaoMedicamento.setTabelaCboFaturamentoDispensacao(cbo);
        dispensacaoMedicamento.setProcedimentoFaturamentoDispensacao(getProcedimentoFaturadoDispensa());

        if (dispensacaoMedicamento.getNumeroRegistro() == null && dispensacaoMedicamento.getProfissional() != null) {
            final String numeroRegistro = dispensacaoMedicamento.getProfissional().getNumeroRegistro();
            dispensacaoMedicamento.setNumeroRegistro(numeroRegistro);
        }

        dispensacaoMedicamento.setEvolucaoAtendimento(evolucaoAtendimento);

        dispensacaoMedicamento = BOFactoryWicket.save(dispensacaoMedicamento);
        if (cidListMedicamento != null && !cidListMedicamento.isEmpty()) {
            for (DispensacaoMedicamentoCid dispensacaoMedicamentoCid : cidListMedicamento) {
                dispensacaoMedicamentoCid.setDispensacaoMedicamento(dispensacaoMedicamento);
                BOFactoryWicket.save(dispensacaoMedicamentoCid);
            }
        }

        salvarUsuarioCadsusDado();
        return true;
    }

    private boolean verificaVinculoDomiciliar(UsuarioCadsus usuarioCadsus){
        return LoadManager.getInstance(DominioEnderecoDomicilio.class)
                .addProperties(new HQLProperties(DominioEnderecoDomicilio.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DominioEnderecoDomicilio.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL,  usuarioCadsus.getCodigo()))
                .exists();
    }

    private void salvarUsuarioCadsusDado() throws DAOException, ValidacaoException {
        if (usuarioCadsusDado != null) {
            usuarioCadsusDado.setCodigo(dispensacaoMedicamento.getUsuarioCadsusDestino().getCodigo());
            usuarioCadsusDado.setAltura(altura);
            usuarioCadsusDado.setPeso(peso);
            BOFactoryWicket.save(usuarioCadsusDado);
        }

        if (usuarioCadsus != null && responsavel != null){
            usuarioCadsus.setResponsavelFamiliar(responsavel);
            BOFactoryWicket.save(usuarioCadsus);
        }
    }

    private DispensacaoMedicamento createDispensacaoContinuar() {
        DispensacaoMedicamento newDispensacao = new DispensacaoMedicamento();

        newDispensacao.setEmpresa(this.dispensacaoMedicamento.getEmpresa());
        newDispensacao.setUsuarioCadsusDestino(this.dispensacaoMedicamento.getUsuarioCadsusDestino());
        if (obrigaResponsavelMenorIdade && this.dispensacaoMedicamento.getUsuarioCadsusDestino() != null) newDispensacao.setUsuarioCadsusResponsavel(this.dispensacaoMedicamento.getUsuarioCadsusResponsavel());
        newDispensacao.setNomeUsuarioOrigem(this.dispensacaoMedicamento.getNomeUsuarioOrigem());
        newDispensacao.setEmpresaOrigem(this.dispensacaoMedicamento.getEmpresaOrigem());
        newDispensacao.setProfissional(this.dispensacaoMedicamento.getProfissional());
        newDispensacao.setNumeroRegistro(this.dispensacaoMedicamento.getNumeroRegistro());
        newDispensacao.setCid(this.dispensacaoMedicamento.getCid());

        return newDispensacao;
    }

    public String getMsgSalvo(DispensacaoMedicamento returnObject) {
        String msg = BundleManager.getString("registro_salvo_sucesso");

        String identificador = null;

        if (returnObject.getCodigo() != null) {
            identificador = returnObject.getCodigo().toString();
            msg += " " + BundleManager.getString("codigo") + ": " + identificador;
        }
        return msg;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        if (autoCompleteConsultaEstabelecimentoExecutante != null && autoCompleteConsultaEstabelecimentoExecutante.getComponentValue() == null) {
            return autoCompleteConsultaEstabelecimentoExecutante.getTxtDescricao().getTextField();
        }
        if (salvarContinuar) {
            return cbxTipoReceita;
        }
        return txtCodigoBarras;
    }

    private boolean validarAcaoBtnAdicionarItem(AjaxRequestTarget target, DispensacaoMedicamentoItem _dispensacaoMedicamentoItem, boolean isAdicaoEtiqueta) throws ValidacaoException, DAOException {
        validacaoLiberacaoReceitaPacienteForaMunicipio(dispensacaoMedicamento.getUsuarioCadsusDestino());
        validarReceita(target);

        if (_dispensacaoMedicamentoItem.getProduto() == null) {
            throw new ValidacaoException(BundleManager.getString("informeProduto"));
        }
        if (!isAdicaoEtiqueta) {
            for (DispensacaoMedicamentoItem item : items) {
                if (_dispensacaoMedicamentoItem.getProduto().equals(item.getProduto())) {
                    throw new ValidacaoException(BundleManager.getString("produtoJaAdicionado"));
                }
            }
        }

        if (ProdutoHelper.isMedicamento(_dispensacaoMedicamentoItem.getProduto())) {
            if (dispensacaoMedicamento.getDataReceita() != null && !DispensacaoMedicamentoHelper.isDataReceitaValida(dispensacaoMedicamento.getDataReceita())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_data_prescricao_invalida"));
            }

            if (RepositoryComponentDefault.SIM.equals(dispensacaoMedicamento.getReceitaContinua()) || (_dispensacaoMedicamentoItem.getReceituarioItem() != null && RepositoryComponentDefault.SIM.equals(_dispensacaoMedicamentoItem.getReceituarioItem().getFlagContinuo()))) {
                boolean condicao1 = !RepositoryComponentDefault.SIM.equals(_dispensacaoMedicamentoItem.getProduto().getUsoContinuo()) || !TipoReceita.RECEITA_BASICA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita());
                // Condição adicionada a partir do card GMT-867 que define que receitas do tipo antimicrobiana e que o produto esteja como sim deve permitir a inclusão como receita continua.
                // Está sendo usada a negativa dela para poder se adaptar à condição 1 já existente.
                boolean condicao2 = !(TipoReceita.RECEITA_ANTIMICROBIANA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita()) && RepositoryComponentDefault.SIM.equals(_dispensacaoMedicamentoItem.getProduto().getUsoContinuo()));
                if (condicao1 && condicao2) {
                    throw new ValidacaoException(BundleManager.getString("paraReceitaUsoContinuoProdutoUsoContinuoReceitaBasica"));
                }
            }

            _dispensacaoMedicamentoItem.setDataValidadeReceita(dispensacaoMedicamento.getDataReceita());

            Produto produto = _dispensacaoMedicamentoItem.getProduto();

            if (produto.getTipoReceita() == null) {
                throw new ValidacaoException(BundleManager.getString("produtoSelecionadoNaoConfiguradoTipoReceita"));
            }

            if (TipoReceita.RECEITA_BASICA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                if (!Objects.equals(TipoReceita.RECEITA_BASICA, produto.getTipoReceita().getTipoReceita())) {
                    throw new ValidacaoException(BundleManager.getString("msgTipoReceitaInvalidoParaProduto"));
                }
            } else if (TipoReceita.RECEITA_BRANCA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                if (!Arrays.asList(TipoReceita.RECEITA_BASICA, TipoReceita.RECEITA_BRANCA).contains(produto.getTipoReceita().getTipoReceita())) {
                    throw new ValidacaoException(BundleManager.getString("msgTipoReceitaInvalidoParaProduto"));
                }
            } else if (TipoReceita.RECEITA_AMARELA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                if (!Arrays.asList(TipoReceita.RECEITA_BASICA, TipoReceita.RECEITA_BRANCA, TipoReceita.RECEITA_AMARELA).contains(produto.getTipoReceita().getTipoReceita())) {
                    throw new ValidacaoException(BundleManager.getString("msgTipoReceitaInvalidoParaProduto"));
                }
            } else if (TipoReceita.RECEITA_AZUL.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                if (!Arrays.asList(TipoReceita.RECEITA_BASICA, TipoReceita.RECEITA_BRANCA, TipoReceita.RECEITA_AZUL).contains(produto.getTipoReceita().getTipoReceita())) {
                    throw new ValidacaoException(BundleManager.getString("msgTipoReceitaInvalidoParaProduto"));
                }
            }
        }

        return true;
    }

    private void validarAdicionarItem(AjaxRequestTarget target, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) throws ValidacaoException, DAOException {
        SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class)
                .addProperty(VOUtils.montarPath(SubGrupo.PROP_FLAG_MEDICAMENTO))
                .addProperty(VOUtils.montarPath(SubGrupo.PROP_FLAG_CONTROLA_ESTOQUE))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO), dispensacaoMedicamentoItem.getProduto().getSubGrupo().getId().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), dispensacaoMedicamentoItem.getProduto().getSubGrupo().getId().getCodigoGrupoProduto()))
                .start().getVO();

        if (dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada() == 0L) {
            throw new ValidacaoException(BundleManager.getString("quantidadeDispensarMaiorZero"));
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(subGrupo.getFlagControlaEstoque())) {
            EstoqueEmpresa estoqueEmpresa = LoadManager.getInstance(EstoqueEmpresa.class)
                    .setId(new EstoqueEmpresaPK(dispensacaoMedicamentoItem.getProduto(), getEstabelecimentoExecutante()))
                    .start().getVO();
            if (dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada()
                    > Math.abs(estoqueEmpresa.getEstoqueDisponivel())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_quantidade_maior_disponivel"));
            }
        }

        if (ProdutoHelper.isMedicamento(subGrupo)) {
            if (!DispensacaoMedicamentoItem.DISPENSACAO_SEM_CONTROLE.equals(dispensacaoMedicamentoItem.getProduto().getFlagDispensacaoEspecial())) {
                if (dispensacaoMedicamentoItem.getPosologia() == null || dispensacaoMedicamentoItem.getPosologia() <= 0D) {
                    throw new ValidacaoException(BundleManager.getString("msgPosologiaDeveSerMaiorQue0"));
                }
            }

            if (dispensacaoMedicamentoItem.getReceituarioItem() != null
                    && dispensacaoMedicamentoItem.getReceituarioItem().getUnidade() != null
                    && !dispensacaoMedicamentoItem.getProduto().getUnidade().equals(dispensacaoMedicamentoItem.getReceituarioItem().getUnidade())) {
                Double qtd = dispensacaoMedicamentoItem.getQuantidadePrescrita() * Coalesce.asLong(dispensacaoMedicamentoItem.getProduto().getQuantidadeUnidade());
                if (dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada() > (qtd)) {
                    throw new ValidacaoException(BundleManager.getString("msg_quantidade_dispensar_superior_quantidade_padrao", dispensacaoMedicamentoItem.getReceituarioItem().getUnidade().getDescricao(), qtd));
                }
            }

            if (dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada() > Coalesce.asDouble(dispensacaoMedicamentoItem.getQuantidadePrescrita())
                    && dispensacaoMedicamentoItem.getProduto() != null
                    && dispensacaoMedicamentoItem.getProduto().getUnidadeReceituario() == null
                    && Coalesce.asLong(dispensacaoMedicamentoItem.getProduto().getQuantidadeUnidade()) > 0L
                    && RepositoryComponentDefault.NAO_LONG.equals(dispensacaoMedicamentoItem.getProduto().getFlagFracionado())) {
                Dinheiro quantidadePrescrita = new Dinheiro(dispensacaoMedicamentoItem.getQuantidadePrescrita());
                Dinheiro quantidadeUnidade = new Dinheiro(dispensacaoMedicamentoItem.getProduto().getQuantidadeUnidade());
                Dinheiro numeroEmbalagem = quantidadePrescrita.dividir(quantidadeUnidade, new MathContext(0, RoundingMode.UP));
                double qtdMaxima = numeroEmbalagem.doubleValue() * dispensacaoMedicamentoItem.getProduto().getQuantidadeUnidade();
                if (dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada() > qtdMaxima) {
                    throw new ValidacaoException(bundle("msgQuantidadeDispensarSuperiorQuantidadePermitidaX", qtdMaxima));
                }
            } else if (dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada() > Coalesce.asDouble(dispensacaoMedicamentoItem.getQuantidadePrescrita())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_quantidade_maior_prescrita"));
            }

            if (!ReceituarioHelper.isAntibioticos(dispensacaoMedicamentoItem.getProduto())) {
                Long duracaoTratamento = Coalesce.asLong(dispensacaoMedicamentoItem.getProduto().getDuracaoTratamento());
                Long quantidadeDias = DispensacaoMedicamentoItemHelper.getQuantidadeDias(dispensacaoMedicamentoItem);
                Long tempoMaximoTratamento;

                if (dispensacaoMedicamentoItem.getProduto().getTipoReceita() != null) {
                    tempoMaximoTratamento = dispensacaoMedicamentoItem.getProduto().getTipoReceita().getDiasMaximoTratamento();
                } else {
                    tempoMaximoTratamento = dispensacaoMedicamento.getTipoReceita().getDiasMaximoTratamento();
                }

                if (duracaoTratamento > 0) {
                    if (quantidadeDias > duracaoTratamento) {
                        throw new ValidacaoException(Bundle.getStringApplication("rotulo_quantidade_nao_pode_ser_maior_duracao_tratamewnto", duracaoTratamento));
                    }
                } else if (tempoMaximoTratamento > 0) {
                    if (quantidadeDias > tempoMaximoTratamento) {
                        Double posologia = dispensacaoMedicamentoItem.getPosologia();
                        Double quantidadeMaxima = tempoMaximoTratamento * posologia;
                        Double quantidadeDispensada = dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada();
                        throw new ValidacaoException(Bundle.getStringApplication("rotulo_quantidade_nao_pode_ser_maior_periodo_receita", Valor.adicionarFormatacaoMonetaria(quantidadeDispensada), quantidadeMaxima));
                    }
                }
            }
        }
    }

    private void adicionarItem(AjaxRequestTarget target, DispensacaoMedicamentoItem itemOrigem, DispensacaoMedicamentoItem itemDestino) throws ValidacaoException, DAOException {
        if (itemOrigem != null
                && itemOrigem.getReceituarioItem() != null
                && itemOrigem.getReceituarioItem().getFlagTratamentoContinuo().equals(RepositoryComponentDefault.NAO_LONG)) {

            Long quantidadeUnidade = null;

            if (itemOrigem.getProduto() != null) {
                quantidadeUnidade = itemOrigem.getProduto().getQuantidadeUnidade();
            }

            if (quantidadeUnidade == null) {
                Double quantidadeParaDispensar = itemOrigem.getQuantidadeDispensada();
                Double totalDispensado = obterQuantidadeDispensada(itemOrigem);
                if (totalDispensado > 0D) {
                    verificarQuantidadeDispensadaMaiorQuePrescrita(itemOrigem, totalDispensado, quantidadeParaDispensar);
                }
            }
        }

        Produto produto = dispensacaoMedicamentoItem.getProduto();
        if (produto == null) {
            produto = itemDestino.getProduto();
        }

        boolean isMedicamento = ProdutoHelper.isMedicamento(produto);
        if (isMedicamento) {
            dchDataReceita.setEnabled(false);
            cbxReceitaContinua.setEnabled(false);
            cbxTipoReceita.setEnabled(false);
            pnlConsultaUsuarioCadsus.setEnabled(false);
            if (verificaUsoCampoCodigo) {
                txtCodigoUsuario.setEnabled(false);
            }
            target.add(txtCodigoUsuario);
//            target.add(txtReceita);
            target.add(dchDataReceita);
            target.add(cbxReceitaContinua);
            target.add(cbxTipoReceita);
            target.add(pnlConsultaUsuarioCadsus);
        }

        boolean add = true;
        validarAdicionarItem(target, itemOrigem);
        try {
            BeanUtils.copyProperties(itemDestino, itemOrigem);
        } catch (IllegalAccessException | InvocationTargetException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        if (isMedicamento) {
            itemDestino.setDataProximaDispensacao(DispensacaoMedicamentoItemHelper.getDataProximaDispensacao(itemDestino, dispensacaoMedicamento.getUsuarioCadsusDestino(), dispensacaoMedicamento.getDataDispensacao()));
            if (itemDestino.getMovimentoGrupoEstoqueItemDTOList() != null) {
                for (MovimentoGrupoEstoqueItemDTO item : itemDestino.getMovimentoGrupoEstoqueItemDTOList()) {
                    Date dataProximaDispensacaoLote = DispensacaoMedicamentoItemHelper.getDataProximaDispensacao(itemDestino, dispensacaoMedicamento.getUsuarioCadsusDestino(), dispensacaoMedicamento.getDataDispensacao(), item.getQuantidade(), false);
                    if (dataProximaDispensacaoLote != null && dataProximaDispensacaoLote.after(item.getDataValidade())) {
                        throw new ValidacaoException(BundleManager.getString("msgQuantidadeInformadoLoteXUltrapassaDataValidadePeriodoTratamentoDataY", item.getGrupoEstoque(), new SimpleDateFormat("dd/MM/yyyy").format(dataProximaDispensacaoLote)));
                    }
                }
            }

            Long dias = 0L;
            if (ReceituarioHelper.isAntibioticos(produto)) {
                if (produto.getTipoReceita().getDiasValidadeAntibiotico() == null || produto.getTipoReceita().getDiasMaximoValidadeAntibiotico() == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_dias_minimo_maximo_nao_configurado_tipo_receita"));
                }

                dias = DispensacaoMedicamentoItemHelper.getQuantidadeDiasAntibioticos(itemDestino, produto.getTipoReceita());

                if (produto.getTipoReceita() != null && dias > Coalesce.asLong(produto.getTipoReceita().getDiasMaximoValidadeAntibiotico()).intValue()) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_dias_excedeu_configurado_tipo_receita_X", produto.getTipoReceita().getDiasMaximoValidadeAntibiotico()));
                }
                Date dataValidade = Data.addDias(dispensacaoMedicamento.getDataReceita(), dias.intValue());
                if (DataUtil.zerarHoraData(dataValidade).before(DataUtil.getDataAtualSemHora())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_receita_fora_validade"));
                }
                itemDestino.setDataValidadeReceita(dataValidade);
            } else {
                Long validadeReceita = 0L;
                if (TipoReceita.RECEITA_BASICA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita()) && RepositoryComponentDefault.SIM.equals(dispensacaoMedicamento.getReceitaContinua())) {
                    validadeReceita = Coalesce.asLong(produto.getValidadeReceitaContinua(), parametroModuloMateriais.getParametro("validadeReceitaUsoContinuo"));
                } else if (TipoReceita.RECEITA_AMARELA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                    validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaAmarela");
                } else if (TipoReceita.RECEITA_AZUL.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                    validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaAzul");
                } else if (TipoReceita.RECEITA_BRANCA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                    validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaBranca");
                } else if (TipoReceita.RECEITA_BASICA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                    validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaBasica");
                } else if (TipoReceita.RECEITA_BRANCA_C3.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                    validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaBrancaC3");
                }

                Date dataValidade = Data.addDias(dispensacaoMedicamento.getDataReceita(), validadeReceita.intValue());
                if (DataUtil.zerarHoraData(dataValidade).before(DataUtil.getDataAtualSemHora())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_receita_fora_validade"));
                }
                itemDestino.setDataValidadeReceita(dispensacaoMedicamento.getDataReceita());
            }
        } else {
            itemDestino.setDataProximaDispensacao(DataUtil.getDataAtual());
            itemDestino.setDataValidadeReceita(DataUtil.getDataAtual());
        }

        for (DispensacaoMedicamentoItem _dispensacaoMedicamentoItem : items) {
            if (_dispensacaoMedicamentoItem.getProduto().equals(itemDestino.getProduto())) {
                add = false;
                Integer aux = 0;
                for (DispensacaoMedicamentoItem item : items) {
                    if (item.getProduto().equals(dispensacaoMedicamentoItem.getProduto())) {
                        itemDestino.setQuantidadeDispensar(item.getQuantidadeDispensar());
                        itemDestino.setQuantidadePrescrita(item.getQuantidadePrescrita());
                        if (CollectionUtils.isNotNullEmpty(item.getMovimentoGrupoEstoqueItemDTOList())) {
                            for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : item.getMovimentoGrupoEstoqueItemDTOList()) {
                                if (!item.getMovimentoGrupoEstoqueItemDTOList().contains(movimentoGrupoEstoqueItemDTO)) {
                                    itemDestino.getMovimentoGrupoEstoqueItemDTOList().addAll(item.getMovimentoGrupoEstoqueItemDTOList());
                                }
                            }
                        }
                        itemDestino.setDataProximaDispensacao(DispensacaoMedicamentoItemHelper.getDataProximaDispensacao(itemDestino, dispensacaoMedicamento.getUsuarioCadsusDestino(), dispensacaoMedicamento.getDataDispensacao()));
                        items.set(aux, itemDestino);
                    }
                    aux++;
                }
            }
        }
        if (add) {
            if (itemDestino.getStatus() == null) {
                itemDestino.setStatus(DispensacaoMedicamentoItem.STATUS_NORMAL);
            }
            items.add(itemDestino);
        }

        limparAdd(target);
    }

    protected Double obterQuantidadeDispensada(DispensacaoMedicamentoItem item) throws ValidacaoException {
        if (item != null && item.getReceituarioItem() != null) {
            Receituario receituario = item.getReceituarioItem().getReceituario();

            Double totalDispensado = calcularTotalDispensado(buscarDispensacoesDoItemNoReceituario(item, receituario));

            return totalDispensado;
        } else {
            return 0D;
        }
    }

    private void verificarQuantidadeDispensadaMaiorQuePrescrita(DispensacaoMedicamentoItem item,
                                                                Double totalDispensado,
                                                                Double quantidadeParaDispensar) throws ValidacaoException {
        LocalDate hoje = LocalDate.now();
        LocalDate dataProximaDispensacao = null;
        if (!(item.getDataProximaDispensacao() == null)) {
            dataProximaDispensacao = item.getDataProximaDispensacao().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        } else {
            List<DispensacaoMedicamentoItem> dispensacoes = new ArrayList<DispensacaoMedicamentoItem>();
            dispensacoes = buscarDispensacoesDoItemNoReceituario(item, item.getReceituarioItem().getReceituario());
            for (DispensacaoMedicamentoItem dmiRetorno : dispensacoes) {
                if (dmiRetorno.getProduto().equals(item.getProduto())) {
                    if (dmiRetorno.getDataProximaDispensacao() == null) {
                        dataProximaDispensacao = item.getReceituarioItem()
                                .getDispensacaoMedicamentoItem()
                                .getDataProximaDispensacao()
                                .toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                    } else {
                        dataProximaDispensacao = dmiRetorno
                                .getDataProximaDispensacao()
                                .toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                    }
                }
            }
        }
        long diasToleranciaParaDispensacao = parametroModuloMateriais.getParametro("diasToleranciaParaDispensacao");
        LocalDate dataToleranca = dataToleranca = dataProximaDispensacao.minusDays(diasToleranciaParaDispensacao);
        if (hoje.isBefore(dataToleranca) || hoje.equals(dataToleranca)) {
            if ((totalDispensado + quantidadeParaDispensar) > item.getQuantidadePrescrita()) {
                throw new ValidacaoException(BundleManager.getString("msgQuantidadeDispensadaMaiorQuantidadePrescrita"));
            }
        }
    }

    private Double calcularTotalDispensado(List<DispensacaoMedicamentoItem> itens) {
        Double totalDispensado = 0D;

        if (itens != null && itens.size() > 0) {
            for (DispensacaoMedicamentoItem item : itens) {
                totalDispensado += item.getQuantidadeDispensada();
            }
        }
        return totalDispensado;
    }

    private List<DispensacaoMedicamentoItem> buscarDispensacoesDoItemNoReceituario(DispensacaoMedicamentoItem item, Receituario receituario) {
        return LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                .addProperties(new HQLProperties(DispensacaoMedicamentoItem.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, usuarioCadsus.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_PRODUTO, item.getProduto().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(
                        VOUtils.montarPath(
                                DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO,
                                DispensacaoMedicamento.PROP_RECEITUARIO),
                        receituario.getCodigo())
                )
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .addSorter(new QueryCustom.QueryCustomSorter(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
    }

    private void limparAdd(AjaxRequestTarget target) {
        modelDispensacaoItem.setObject(this.dispensacaoMedicamentoItem = new DispensacaoMedicamentoItem());
        tblItems.update(target);
        pnlConsultaProduto.limpar(target);
        if (RepositoryComponentDefault.NAO.equals(utilizarLeitoraCodigoBarrasProduto)) {
            pnlConsultaProduto.focus(target);
        } else {
            target.focusComponent(txtCodigoBarrasProduto);
        }
        autoCompleteConsultaEstabelecimentoExecutante.setEnabled(false);
        target.add(autoCompleteConsultaEstabelecimentoExecutante);
    }

    private void validarReceita(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        Long permiteDispensarSemDataLimite = null;

        try {
            permiteDispensarSemDataLimite = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).<Long>getParametro("permiteDispensarSemDataLimite");
        } catch (DAOException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        if (RepositoryComponentDefault.NAO_LONG.equals(permiteDispensarSemDataLimite)) {

            String numeroReceita = (String) txtReceita.getModelObject();

            if (StringUtils.isNotBlank(numeroReceita)) {
                List<DispensacaoMedicamento> list = LoadManager.getInstance(DispensacaoMedicamento.class)
                        .setLazyMode(true)
                        .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamento.PROP_RECEITA, numeroReceita))
                        .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamento.PROP_TIPO_RECEITA, dispensacaoMedicamento.getTipoReceita()))
                        .start().getList();

                if (!list.isEmpty()) {
                    txtReceita.limpar(target);
                    target.focusComponent(txtReceita);
                    throw new ValidacaoException(BundleManager.getString("receitaJaUtilizada"));
                }
            }
        }
    }

    private void eventoProduto(AjaxRequestTarget target, Produto object) {
        if (object != null) {
            boolean isMedicamento = ProdutoHelper.isMedicamento(object);
            if (isMedicamento) {
                if (object.getTipoReceita() == null) {
                    modalWarn(target, new ValidacaoException(BundleManager.getString("medicamentoNaoPossuiTipoReceitaConfiguradoFavorAjustar")));
                    pnlConsultaProduto.limpar(target);
                }
            }

            cbxTipoReceita.setRequired(isMedicamento);
        }
    }

    private Date validacaoDispensacaoHistorico(Produto produto, AjaxRequestTarget target, boolean showDialog) throws ValidacaoException {
        Date maxDataProximaDispensacao = null;
        List<DispensacaoMedicamentoItem> listaDispensacao = LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                .addProperty(DispensacaoMedicamentoItem.PROP_CODIGO)
                .addProperty(DispensacaoMedicamentoItem.PROP_QUANTIDADE_DEVOLVIDA)
                .addProperty(DispensacaoMedicamentoItem.PROP_QUANTIDADE_DISPENSADA)
                .addProperty(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO)
                .addProperty(DispensacaoMedicamentoItem.PROP_DATA_ULTIMA_DISPENSACAO)
                .addProperty(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_DATA_DISPENSACAO))
                .addProperty(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_MEDICAMENTO))
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, dispensacaoMedicamento.getUsuarioCadsusDestino()))
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_PRODUTO, produto))
                .addParameter(new QueryCustom.QueryCustomParameter(
                        VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO),
                        BuilderQueryCustom.QueryParameter.MAIOR, Data.addDias(DataUtil.getDataAtual(), ((Long) parametroModuloMateriais.getParametro("diasToleranciaParaDispensacao")).intValue())))
                .addSorter(new QueryCustom.QueryCustomSorter(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();

        Date dataProximaDispensacaoCalculada = null;
        for (DispensacaoMedicamentoItem medicamentoItem : listaDispensacao) {
            if (Coalesce.asDouble(medicamentoItem.getQuantidadeDevolvida(), 0D) < medicamentoItem.getQuantidadeDispensada()) {
                maxDataProximaDispensacao = medicamentoItem.getDataProximaDispensacao();
                dataProximaDispensacaoCalculada = medicamentoItem.getDataProximaDispensacaoCalculada();
                break;
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(produto.getFlagEmiteLme())) {
            for (DispensacaoMedicamentoItem dispensacaoMedicamentoItem : listaDispensacao) {
                if (periodoEntreDias(dispensacaoMedicamentoItem.getDataUltimaDispensacao(), SEIS_MESES)) {
                    throw new ValidacaoException(BundleManager.getString("msgPortaria13"));
                }
            }
        }

        Date diasToleranciaParaDispensacao = Data.addDias(DataUtil.getDataAtual(), ((Long) parametroModuloMateriais.getParametro("diasToleranciaParaDispensacao")).intValue());
        if (maxDataProximaDispensacao != null && maxDataProximaDispensacao.after(diasToleranciaParaDispensacao)) {
            Integer liberacoes = LoadManager.getInstance(LiberacaoReceita.class)
                    .setLazyMode(true)
                    .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_PRODUTO, produto))
                    .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_USUARIO_CADSUS, dispensacaoMedicamento.getUsuarioCadsusDestino()))
                    .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_STATUS, LiberacaoReceita.STATUS_ABERTO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LiberacaoReceita.PROP_EMPRESA, Empresa.PROP_CODIGO), getEstabelecimentoExecutante().getCodigo()))
                    .start().getList().size();
            if (liberacoes == null || liberacoes <= 0) {
                if (showDialog) {
                    dlgAvisoDispensacoesHistorico.show(target, isActionPermitted(Permissions.CADASTRAR), getEstabelecimentoExecutante(), dataProximaDispensacaoCalculada, dispensacaoMedicamento.getUsuarioCadsusDestino(), produto);
                    pnlConsultaProduto.limpar(target);
                }
                return maxDataProximaDispensacao;
            }
        }
        return null;
    }

    private boolean periodoEntreDias(Date dataComparacao, long periodo) {
        Calendar today = Calendar.getInstance();
        Calendar dtCompare = Calendar.getInstance();
        dtCompare.setTime(DataUtil.zerarHora(dataComparacao));
        return ChronoUnit.DAYS.between(dtCompare.toInstant(), today.toInstant()) > periodo;
    }

    private void validacaoLiberacaoReceitaPacienteForaMunicipio(UsuarioCadsus usuarioCadsus) throws ValidacaoException {
        if (usuarioCadsus == null) {
            return;
        }

        UsuarioCadsusEndereco usuarioCadsusEndereco = LoadManager.getInstance(UsuarioCadsusEndereco.class)
                .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID, UsuarioCadsusEnderecoPK.PROP_ENDERECO)).getProperties())
                .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID, UsuarioCadsusEnderecoPK.PROP_ENDERECO, EnderecoUsuarioCadsus.PROP_CIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID, UsuarioCadsusEnderecoPK.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusEndereco.PROP_STATUS, UsuarioCadsusEndereco.STATUS_ABERTO))
                .start().getVO();
        if (usuarioCadsusEndereco != null && usuarioCadsusEndereco.getId() != null && usuarioCadsusEndereco.getId().getEndereco() != null
                && !getEstabelecimentoExecutante().getCidade().equals(usuarioCadsusEndereco.getId().getEndereco().getCidade())
                && RepositoryComponentDefault.NAO_LONG.equals(usuarioCadsusEndereco.getId().getEndereco().getCidade().getLiberarDispensacao())) {

            int count = LoadManager.getInstance(LiberacaoReceita.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_USUARIO_CADSUS, usuarioCadsus))
                    .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_TIPO_LIBERACAO, LiberacaoReceita.LIBERACAO_PACIENTE_EXTERNO))
                    .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_STATUS, LiberacaoReceita.STATUS_ABERTO))
                    .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_EMPRESA, getEstabelecimentoExecutante()))
                    .start().getList().size();

            if (count == 0) {
                throw new ValidacaoException(BundleManager.getString("msgSohPermitidoDispensacaoPacientesForaMunicipioComLiberacaoReceita"));
            }
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_INDICATOR_APPENDER));
        response.render(OnDomReadyHeaderItem.forScript(JScript.setIndicatorAppenderId(pnlConsultaUsuarioCadsus.getAjaxIndicatorMarkupId())));
        try {
            parametroDiasDataPrescricao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).<Long>getParametro("diasDataDispensacao").intValue();
            dataMinimaDispensacao = Data.adjustRangeHour(Data.removeDias(DataUtil.getDataAtual(), parametroDiasDataPrescricao)).getDataInicial();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        response.render(OnDomReadyHeaderItem.forScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'maxDate', " + DateHelper.getJSDate(DataUtil.getDataAtual()) + " );"));
        response.render(OnDomReadyHeaderItem.forScript("$( '#" + dcDataDispensacao.getData().getMarkupId() + "' ).datepicker( 'option', 'minDate', " + DateHelper.getJSDate(dataMinimaDispensacao) + " );"));
    }

    public void controlarAvatarUsuario(AjaxRequestTarget target, UsuarioCadsus u) {
        imagem.setImageResource(ImagemAvatarHelper.carregarAvatarResourceReference(u).getResource());
        target.add(imagem);
    }

    private void initDlgAdicionarItemSemEstoque(AjaxRequestTarget target, DispensacaoMedicamentoItem item) {
        if (dlgAdicionarItemSemEstoque == null) {
            addModal(target, dlgAdicionarItemSemEstoque = new DlgAdicionarItemSemEstoque(newModalId()) {

                @Override
                public void onOk(AjaxRequestTarget target, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) throws ValidacaoException, DAOException {
                    adicionarItemSemEstoque(target, dispensacaoMedicamentoItem);
                }

            });
        }
        dlgAdicionarItemSemEstoque.show(target, item);
    }

    private void adicionarItemSemEstoque(AjaxRequestTarget target, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) throws ValidacaoException, DAOException {
        boolean isMedicamento = ProdutoHelper.isMedicamento(dispensacaoMedicamentoItem.getProduto());
        if (isMedicamento) {
            txtReceita.setEnabled(false);
            dchDataReceita.setEnabled(false);
            cbxReceitaContinua.setEnabled(false);
            cbxTipoReceita.setEnabled(false);
            pnlConsultaUsuarioCadsus.setEnabled(false);
            if (verificaUsoCampoCodigo) {
                txtCodigoUsuario.setEnabled(false);
            }
            target.add(txtCodigoUsuario);
            target.add(txtReceita);
            target.add(dchDataReceita);
            target.add(cbxReceitaContinua);
            target.add(cbxTipoReceita);
            target.add(pnlConsultaUsuarioCadsus);
        }

        Long validadeReceita = 0L;
        if (TipoReceita.RECEITA_BRANCA_C3.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
            validadeReceita = parametroModuloMateriais.getParametro("validadeReceitaBrancaC3");
        }

        Date dataValidade = Data.addDias(dispensacaoMedicamento.getDataReceita(), validadeReceita.intValue());
        if (DataUtil.zerarHoraData(dataValidade).before(DataUtil.getDataAtualSemHora())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_receita_fora_validade"));
        }

        boolean add = true;
        for (DispensacaoMedicamentoItem _dispensacaoMedicamentoItem : items) {
            if (_dispensacaoMedicamentoItem == dispensacaoMedicamentoItem) {
                add = false;
                break;
            }
        }
        if (add) {
            items.add(dispensacaoMedicamentoItem);
        }
        modelDispensacaoItem.setObject(this.dispensacaoMedicamentoItem = new DispensacaoMedicamentoItem());
        tblItems.update(target);
        pnlConsultaProduto.limpar(target);
        pnlConsultaProduto.focus(target);
        autoCompleteConsultaEstabelecimentoExecutante.setEnabled(false);
        target.add(autoCompleteConsultaEstabelecimentoExecutante);
    }

    private boolean consultarEstoqueSemLoteVencido(Produto produto) throws DAOException, ValidacaoException {
        QueryEstoqueEmpresaSemLoteVencidoDTOParam param = new QueryEstoqueEmpresaSemLoteVencidoDTOParam();
        param.setEmpresa(getEstabelecimentoExecutante());
        param.setProduto(produto);
        List<QueryEstoqueEmpresaSemLoteVencidoDTO> list = BOFactoryWicket.getBO(EstoqueEmpresaFacade.class).queryEstoqueEmpresaSemLoteVencido(param);

        boolean possuiEstoqueSemLoteVencido = true;
        for (QueryEstoqueEmpresaSemLoteVencidoDTO itemEstoqueEmpresa : list) {
            if (Coalesce.asDouble(itemEstoqueEmpresa.getEstoque()) <= 0) {
                possuiEstoqueSemLoteVencido = false;
                break;
            }
        }
        return possuiEstoqueSemLoteVencido;
    }

    public Procedimento getProcedimentoFaturadoDispensa() {
        try {
            return BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ProcedimentoFaturadoDispensa");
        } catch (DAOException ex) {
            Logger.getLogger(DispensacaoMedicamentoPage.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    private boolean validarProcedimentoFaturadoDispensa() {
        boolean retorno = true;
        Procedimento procedimentoFaturadoDispensa = getProcedimentoFaturadoDispensa();
        if (RepositoryComponentDefault.SIM_LONG.equals(dropDownFaturarDispensa.getComponentValue()) && procedimentoFaturadoDispensa == null) {
            retorno = false;
        }
        return retorno;
    }

    private void updatePanel(AjaxRequestTarget target) {
        this.updateNotificationPanel(target);
    }

    private Empresa getEstabelecimentoExecutante() {
        return form.getModel().getObject().getEmpresa();
    }

    private void onUpdateDropDownFaturarDispensa(AjaxRequestTarget target) {
        if (validarProcedimentoFaturadoDispensa()) {
            boolean faturar = RepositoryComponentDefault.SIM_LONG.equals(dropDownFaturarDispensa.getComponentValue());
            if (getEstabelecimentoExecutante() != null) {
                autoCompleteConsultaProfissionalFaturamento.setCodigoEmpresa(getEstabelecimentoExecutante().getCodigo()).setPeriodoEmpresa(true).setProcedimento(getProcedimentoFaturadoDispensa());
            }
            divProfissionalFaturamento.setVisible(faturar);
            getSession().getFeedbackMessages().clear();
        } else {
            autoCompleteConsultaProfissionalFaturamento.setCodigoEmpresa(null);
            divProfissionalFaturamento.setVisible(false);
            dropDownFaturarDispensa.setComponentValue(RepositoryComponentDefault.NAO_LONG);
            getSession().getFeedbackMessages().warn(getPage(), bundle("msg_e_obrigatorio_a_definicao_do_parametro_x", "ProcedimentoFaturadoDispensa"));
        }
        target.add(autoCompleteConsultaProfissionalFaturamento);
        target.add(divProfissionalFaturamento);
        target.add(identificacaoItem);
        target.add(dropDownFaturarDispensa);
        updatePanel(target);
    }

    private void removerUsuarioCadsusAutoComplete(AjaxRequestTarget target) {
        if (verificaUsoCampoCodigo) {
            txtCodigoUsuario.setComponentValue(null);
        }
        txtPeso.limpar(target);
        txtAltura.limpar(target);
        target.add(txtCodigoUsuario);
        usuarioCadsusDado = null;
        txtObservacaoDispensacao.setEnabled(false);
        txtObservacaoDispensacao.limpar(target);
        btnEditarObservacao.setEnabled(false);
        btnSalvarObservacao.setEnabled(false);
        btnHistoricoDispensacoes.setEnabled(false);
        btnReceitaPaciente.setEnabled(false);
        btnProntuarioPaciente.setEnabled(false);
        autoCompleteConsultaResponsavel.setEnabled(false);
        autoCompleteConsultaResponsavel.limpar(target);
        imagem.setImageResource(Resources.Images.AVATAR_SEM_FOTO.resourceReference().getResource());
        target.add(txtPeso);
        target.add(txtAltura);
        target.add(imagem);
        target.add(btnEditarObservacao);
        target.add(btnSalvarObservacao);
        target.add(btnHistoricoDispensacoes);
        target.add(btnReceitaPaciente);
        target.add(btnProntuarioPaciente);
        target.add(autoCompleteConsultaResponsavel);
        kitPedidoPacienteList = new ArrayList();
        target.add(tblKitPedidoPaciente);
        tblKitPedidoPaciente.populate();
        tblKitPedido.clearSelection(target);
        pnlConsultaUsuarioCadsus.setOutputMarkupPlaceholderTag(true);
        pnlConsultaUsuarioCadsus.addAjaxUpdateValue();
        pnlConsultaUsuarioCadsus.addBusyIndicator();
        pnlConsultaUsuarioCadsus.limpar(target);
        target.appendJavaScript(JScript.initMasks());
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
        target.add(pnlConsultaUsuarioCadsus);
    }

    private void carregarMedicamentoCodigoBarras(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (this.codigoBarrasProduto != null) {
            validaCamposObrigatorios(target);
            String codigoBarrasProduto = StringUtil.getDigits(this.codigoBarrasProduto);
            CodigoBarrasProduto cbp = LoadManager.getInstance(CodigoBarrasProduto.class)
                    .addProperties(new HQLProperties(CodigoBarrasProduto.class).getProperties())
                    .addProperties(new HQLProperties(Produto.class, CodigoBarrasProduto.PROP_PRODUTO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_REFERENCIA, codigoBarrasProduto))
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, CodigoBarrasProduto.Status.TRANSFERIDO.value()))
                    .setMaxResults(1)
                    .start().getVO();

            if (cbp != null) {
                if (RepositoryComponentDefault.SIM.equals(validarSituacaoCodigoBarrasProduto)) {
                    if (cbp.isEtiquetaForaEstoque()) {
                        txtCodigoBarrasProduto.limpar(target);
                        target.focusComponent(txtCodigoBarrasProduto);
                        throw new ValidacaoException(bundle("codigoBarrasJaDispensado"));
                    }
                }
                if (CollectionUtils.isNotNullEmpty(items)) {
                    for (DispensacaoMedicamentoItem dto : items) {
                        if (CollectionUtils.isNotNullEmpty(dto.getMovimentoGrupoEstoqueItemDTOList())) {
                            for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : dto.getMovimentoGrupoEstoqueItemDTOList()) {
                                if (movimentoGrupoEstoqueItemDTO.getLstCodigoBarrasProduto().contains(cbp)) {
                                    txtCodigoBarrasProduto.limpar(target);
                                    target.focusComponent(txtCodigoBarrasProduto);
                                    throw new ValidacaoException(bundle("codigoBarrasJaInseridoNestaDispensacao"));
                                }
                            }
                        }
                    }
                }

                Produto produto = LoadManager.getInstance(Produto.class)
                        .addProperties(new HQLProperties(Produto.class).getProperties())
                        .addProperties(new HQLProperties(Unidade.class, Produto.PROP_UNIDADE).getProperties())
                        .addProperties(new HQLProperties(Unidade.class, Produto.PROP_UNIDADE_PRESCRICAO).getProperties())
                        .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(Produto.PROP_SUB_GRUPO)).getProperties())
                        .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(Produto.PROP_TIPO_RECEITA)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_CODIGO, cbp.getProduto()))
                        .start().getVO();
                adicionarProdutoEtiqueta(target, produto, cbp);
                txtCodigoBarrasProduto.limpar(target);
            } else {
                txtCodigoBarrasProduto.limpar(target);
                warn(target, bundle("codigoBarrasIndisponivel"));
                target.focusComponent(txtCodigoBarrasProduto);
            }
        }
    }

    private void adicionarProdutoEtiqueta(AjaxRequestTarget target, Produto produto, CodigoBarrasProduto codigoBarrasProduto) throws ValidacaoException, DAOException {
        DispensacaoMedicamentoItem dispensacaoMedicamentoItem = new DispensacaoMedicamentoItem();
        dispensacaoMedicamentoItem = this.dispensacaoMedicamentoItem;
        if (getEstabelecimentoExecutante() == null) {
            throw new ValidacaoException(BundleManager.getString("informeEstabelecimento"));
        }
        if (codigoBarrasProduto.getQuantidadeProduto() == null) {
            throw new ValidacaoException(BundleManager.getString("msgQuantidadeNaoInformadaNaEtiqueta"));
        }

        for (DispensacaoMedicamentoItem item : items) {
            if (item.getProduto().equals(produto)) {
                if (CollectionUtils.isNotNullEmpty(item.getMovimentoGrupoEstoqueItemDTOList())) {
                    for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : item.getMovimentoGrupoEstoqueItemDTOList()) {
                        if (CollectionUtils.isEmpty(movimentoGrupoEstoqueItemDTO.getLstCodigoBarrasProduto())) {
                            throw new ValidacaoException(BundleManager.getString("msgProdutoInseridoManualmenteNaoPermitidoInsercaoLeitor"));
                        }
                    }
                }
                dispensacaoMedicamentoItem = item;
            }
        }
        if (dispensacaoMedicamento.getDataReceita() == null) {
            dispensacaoMedicamento.setDataReceita(DataUtil.getDataAtual());
        }
        dispensacaoMedicamentoItem.setProduto(produto);
        if (validarAcaoBtnAdicionarItem(target, dispensacaoMedicamentoItem, true) && validacaoDispensacaoHistorico(dispensacaoMedicamentoItem.getProduto(), target, true) == null) {
            dispensacaoMedicamentoItem.setDispensacaoMedicamento(dispensacaoMedicamento);
            if (CollectionUtils.isNotNullEmpty(items)) {
                for (DispensacaoMedicamentoItem dto : items) {
                    if (CollectionUtils.isNotNullEmpty(dto.getMovimentoGrupoEstoqueItemDTOList())) {
                        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : dto.getMovimentoGrupoEstoqueItemDTOList()) {
                            if (movimentoGrupoEstoqueItemDTO.getLstCodigoBarrasProduto().contains(codigoBarrasProduto)) {
                                txtCodigoBarrasProduto.limpar(target);
                                target.focusComponent(txtCodigoBarrasProduto);
                                throw new ValidacaoException(bundle("codigoBarrasJaInseridoNestaDispensacao"));
                            }
                        }
                    }
                }
            }

            if (consultarEstoqueSemLoteVencido(dispensacaoMedicamentoItem.getProduto())) {
                if (!ProdutoHelper.isMedicamento(produto.getSubGrupo()) || itemJaAdicionado(dispensacaoMedicamentoItem)) {
                    adicionarProduto(target, produto, codigoBarrasProduto, dispensacaoMedicamentoItem.getCid());
                } else {
                    dlgAdicionarItem.setObject(target, dispensacaoMedicamentoItem, false, getEstabelecimentoExecutante(), codigoBarrasProduto);
                    dlgAdicionarItem.show(target);
                }
            } else {
                throw new ValidacaoException(BundleManager.getString("msgProdutoSemQuantidadeNoLote"));
            }
        }
    }

    private boolean itemJaAdicionado(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        if (CollectionUtils.isNotNullEmpty(items)) {
            for (DispensacaoMedicamentoItem item : items) {
                if (item.getProduto().equals(dispensacaoMedicamentoItem.getProduto())) {
                    return item.getPosologia() != null;
                }
            }
        }
        return false;
    }

    private void adicionarProduto(AjaxRequestTarget target, Produto produto, CodigoBarrasProduto codigoBarrasProduto, Cid cid) throws ValidacaoException, DAOException {
        DispensacaoMedicamentoItem dispensacaoMedicamentoItem = new DispensacaoMedicamentoItem();

        if (getEstabelecimentoExecutante() == null) {
            throw new ValidacaoException(BundleManager.getString("informeEstabelecimento"));
        }

        dispensacaoMedicamentoItem.setProduto(produto);
        dispensacaoMedicamentoItem.setDispensacaoMedicamento(dispensacaoMedicamento);

        if (!consultarEstoqueSemLoteVencido(produto)) {
            dispensacaoMedicamentoItem.setStatus(DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE);
            dispensacaoMedicamentoItem.setQuantidadeDispensada(0D);
        }

        if (dispensacaoMedicamentoItem.getStatus() == null) {
            dispensacaoMedicamentoItem.setStatus(DispensacaoMedicamentoItem.STATUS_NORMAL);
            dispensacaoMedicamentoItem.setQuantidadeDispensada(codigoBarrasProduto.getQuantidadeProduto());
        }

        dispensacaoMedicamentoItem.setQuantidadeDispensar(0D);
        dispensacaoMedicamentoItem.setQuantidadePrescrita(0D);

        if (CollectionUtils.isNotNullEmpty(items)) {
            for (DispensacaoMedicamentoItem dto : items) {
                if (CollectionUtils.isNotNullEmpty(dto.getMovimentoGrupoEstoqueItemDTOList())) {
                    for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : dto.getMovimentoGrupoEstoqueItemDTOList()) {
                        if (movimentoGrupoEstoqueItemDTO.getLstCodigoBarrasProduto().contains(codigoBarrasProduto)) {
                            txtCodigoBarrasProduto.limpar(target);
                            target.focusComponent(txtCodigoBarrasProduto);
                            throw new ValidacaoException(bundle("codigoBarrasJaInseridoNestaDispensacao"));
                        }
                    }
                }
            }
        }

        MovimentoGrupoEstoqueItemDTO dto = getLote(dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList(), codigoBarrasProduto);
        if (dto != null) {
            dto.setQuantidade(new Dinheiro(dto.getQuantidade()).somar(codigoBarrasProduto.getQuantidadeProduto()).doubleValue());
            dto.getLstCodigoBarrasProduto().add(codigoBarrasProduto);
        } else {
            EstoqueEmpresaPK estoqueEmpresaPK = new EstoqueEmpresaPK();
            estoqueEmpresaPK.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
            estoqueEmpresaPK.setProduto(produto);

            EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class)
                    .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_DESCRICAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, ApplicationSession.get().getSession().getEmpresa()))
                    .start().getVO();

            if (empresaMaterial == null) {
                throw new ValidacaoException("Não foi encontrado um depósito padrão definido para a empresa.");
            }

            GrupoEstoquePK grupoEstoquePK = new GrupoEstoquePK();
            grupoEstoquePK.setGrupo(codigoBarrasProduto.getGrupo());
            grupoEstoquePK.setCodigoDeposito(empresaMaterial.getDeposito().getCodigo());
            grupoEstoquePK.setEstoqueEmpresa(new EstoqueEmpresa(estoqueEmpresaPK));
            grupoEstoquePK.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());

            GrupoEstoque grupoEstoque = LoadManager.getInstance(GrupoEstoque.class)
                    .addProperties(new HQLProperties(GrupoEstoque.class).getProperties())
                    .addProperties(new HQLProperties(EstoqueEmpresa.class, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA)).getProperties())
                    .setId(grupoEstoquePK)
                    .start().getVO();

            if (grupoEstoque == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_encontrado_estoque_etiqueta_produto"));
            }

            dto = new MovimentoGrupoEstoqueItemDTO();
            dto.setDataValidade(grupoEstoque.getDataValidade());
            dto.setDeposito(grupoEstoque.getRoDeposito());
            dto.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
            dto.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());
            dto.setEstoqueFisico(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueFisico());
            dto.setEstoqueReservado(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueReservado());
            dto.setEstoqueEncomendado(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueEncomendado());
            dto.setGrupoEstoque(grupoEstoque.getId().getGrupo());
            dto.setProduto(codigoBarrasProduto.getProduto());
            dto.setQuantidade(codigoBarrasProduto.getQuantidadeProduto());
            dto.getLstCodigoBarrasProduto().add(codigoBarrasProduto);

            if (dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList() == null) {
                dispensacaoMedicamentoItem.setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
            }

            dispensacaoMedicamentoItem.getLstCodigoBarrasProduto().add(codigoBarrasProduto);
            dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList().add(dto);
        }

        double quantidadeTotal = dispensacaoMedicamentoItem.getQuantidadeDispensada() + codigoBarrasProduto.getQuantidadeProduto();
        if (cid == null && codigoBarrasProduto.getProduto().getLimiteDispensacao() != null && quantidadeTotal > codigoBarrasProduto.getProduto().getLimiteDispensacao()) {
            viewDlgInformarCid(target, produto, codigoBarrasProduto, dispensacaoMedicamentoItem);
        } else {
            if (cid != null) {
                dispensacaoMedicamentoItem.setCid(cid);
            }
            adicionarPorCodigoBarras(target, produto, codigoBarrasProduto, dispensacaoMedicamentoItem);
        }
    }

    private void adicionarPorCodigoBarras(AjaxRequestTarget target, Produto produto, CodigoBarrasProduto codigoBarrasProduto, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) throws DAOException, ValidacaoException {
        Date dataDispensacao = validacaoDispensacaoHistorico(dispensacaoMedicamentoItem.getProduto(), target, false);
        if (dataDispensacao != null) {
            throw new ValidacaoException(BundleManager.getString("medicamentoJaDispensadoPacienteDataProximaDispensacao", produto.getDescricao(), dataDispensacao));
        } else {
            if (CollectionUtils.isNotNullEmpty(items)) {
                List<Produto> produtosAdicionados = Lambda.extract(items, Lambda.on(DispensacaoMedicamentoItem.class).getProduto());
                if (produtosAdicionados.contains(produto)) {
                    Integer aux = 0;
                    for (DispensacaoMedicamentoItem item : items) {
                        if (item.getProduto().equals(dispensacaoMedicamentoItem.getProduto())) {
                            Date dataValidadeLote = null;
                            String grupoEstoque = "";
                            if (CollectionUtils.isNotNullEmpty(item.getMovimentoGrupoEstoqueItemDTOList()) || CollectionUtils.isNotNullEmpty(dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList())) {
                                List<MovimentoGrupoEstoqueItemDTO> list = new ArrayList();
                                if (CollectionUtils.isEmpty(item.getMovimentoGrupoEstoqueItemDTOList())) {
                                    item.setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                                }
                                if (CollectionUtils.isEmpty(dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList())) {
                                    dispensacaoMedicamentoItem.setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                                }
                                list.addAll(item.getMovimentoGrupoEstoqueItemDTOList());
                                list.addAll(dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList());

                                List<MovimentoGrupoEstoqueItemDTO> groupedList = new ArrayList();
                                Group<MovimentoGrupoEstoqueItemDTO> byGrupoEstoque = Lambda.group(list, Lambda.by(Lambda.on(MovimentoGrupoEstoqueItemDTO.class).getGrupoEstoque()));
                                for (Group<MovimentoGrupoEstoqueItemDTO> group : byGrupoEstoque.subgroups()) {
                                    MovimentoGrupoEstoqueItemDTO groupDTO = group.first();
                                    List<MovimentoGrupoEstoqueItemDTO> all = group.findAll();

                                    List<List<CodigoBarrasProduto>> codigoBarrasExtract = Lambda.extract(all, Lambda.on(MovimentoGrupoEstoqueItemDTO.class).getLstCodigoBarrasProduto());
                                    List<CodigoBarrasProduto> codigosBarraJoined = new ArrayList();
                                    for (List<CodigoBarrasProduto> codigoBarrasProdutos : codigoBarrasExtract) {
                                        codigosBarraJoined.addAll(codigoBarrasProdutos);
                                    }

                                    Double quantidadeTotal = Lambda.sum(all, Lambda.on(MovimentoGrupoEstoqueItemDTO.class).getQuantidade());
                                    groupDTO.setQuantidade(quantidadeTotal);
                                    groupDTO.setLstCodigoBarrasProduto(codigosBarraJoined);
                                    groupedList.add(groupDTO);
                                    dataValidadeLote = groupDTO.getDataValidade();
                                    grupoEstoque = groupDTO.getGrupoEstoque();
                                }
                                Double quantidadeTotalItem = Lambda.sum(groupedList, Lambda.on(MovimentoGrupoEstoqueItemDTO.class).getQuantidade());
                                if (Coalesce.asDouble(item.getQuantidadePrescrita()) > 0D && quantidadeTotalItem > item.getQuantidadePrescrita()) {
                                    if (CollectionUtils.isNotNullEmpty(items.get(aux).getMovimentoGrupoEstoqueItemDTOList()) && items.get(aux).getMovimentoGrupoEstoqueItemDTOList().get(0) != null && CollectionUtils.isNotNullEmpty(items.get(aux).getMovimentoGrupoEstoqueItemDTOList().get(0).getLstCodigoBarrasProduto())) {
                                        items.get(aux).getMovimentoGrupoEstoqueItemDTOList().get(0).getLstCodigoBarrasProduto().remove(codigoBarrasProduto);
                                    }
                                    throw new ValidacaoException(BundleManager.getString("msgQuantidadeDispensadaMaiorQuantidadePrescrita"));
                                }

                                item.setMovimentoGrupoEstoqueItemDTOList(groupedList);

                                item.setQuantidadeDispensada(quantidadeTotalItem);
                                item.setCid(dispensacaoMedicamentoItem.getCid());
                            }
                            Date dataProximaDispensacao = DispensacaoMedicamentoItemHelper.getDataProximaDispensacao(item, dispensacaoMedicamento.getUsuarioCadsusDestino(), dispensacaoMedicamento.getDataDispensacao());
                            item.setDataProximaDispensacao(dataProximaDispensacao);

                            if (dataValidadeLote != null && dataProximaDispensacao != null && dataProximaDispensacao.after(dataValidadeLote)) {
                                if (items.get(aux).getMovimentoGrupoEstoqueItemDTOList().get(0) != null && CollectionUtils.isNotNullEmpty(items.get(aux).getMovimentoGrupoEstoqueItemDTOList().get(0).getLstCodigoBarrasProduto())) {
                                    items.get(aux).getMovimentoGrupoEstoqueItemDTOList().get(0).getLstCodigoBarrasProduto().remove(codigoBarrasProduto);
                                }
                                throw new ValidacaoException(BundleManager.getString("msgQuantidadeInformadoLoteXUltrapassaDataValidadePeriodoTratamentoDataY", grupoEstoque, new SimpleDateFormat("dd/MM/yyyy").format(dataProximaDispensacao)));
                            }
                            items.set(aux, item);
                        }
                        aux++;
                    }
                } else {
                    items.add(dispensacaoMedicamentoItem);
                }
            } else {
                items.add(dispensacaoMedicamentoItem);
            }
        }
        target.add(tblItems);
        tblItems.populate();
        target.focusComponent(txtCodigoBarrasProduto);
    }

    private MovimentoGrupoEstoqueItemDTO getLote(List<MovimentoGrupoEstoqueItemDTO> lstLote, CodigoBarrasProduto cbp) {
        if (CollectionUtils.isNotNullEmpty(lstLote)) {
            for (MovimentoGrupoEstoqueItemDTO dto : lstLote) {
                if (dto.getGrupoEstoque().equals(cbp.getGrupo())) {
                    return dto;
                }
            }
        }
        return null;
    }

    private void adicionarKit(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        List<KitPedidoPaciente> selectedObjects = tblKitPedido.getSelectedObjects();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(selectedObjects)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_pelo_menos_um_registro"));
        } else {
            kitPedidoPacienteList.addAll(BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cadastrarUsuarioCadsusKit(selectedObjects, dispensacaoMedicamento.getUsuarioCadsusDestino()));
            tblKitPedido.clearSelection(target);
            tblKitPedidoPaciente.update(target);
            tblKitPedidoPaciente.populate();
        }
    }

    private void adicionarKitPaciente(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        adicionarKits(target);
        tblKitPedidoPaciente.clearSelection(target);
        tblItems.update(target);
    }

    private void adicionarKits(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        List<UsuarioCadsusKit> selectedObjects = tblKitPedidoPaciente.getSelectedObjects();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(selectedObjects)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_pelo_menos_um_registro"));
        } else {
            Map<Produto, Date> itensNotAddKit = new HashMap();
            Map<Produto, String> produtosNotAddKit = new HashMap();
            List<DispensacaoMedicamentoItem> itensAddKit = new ArrayList<>();
            for (UsuarioCadsusKit selectedObject : selectedObjects) {
                List<UsuarioCadsusKitItem> itensKit = LoadManager.getInstance(UsuarioCadsusKitItem.class)
                        .addProperties(new HQLProperties(UsuarioCadsusKitItem.class).getProperties())
                        .addProperties(new HQLProperties(Produto.class, VOUtils.montarPath(UsuarioCadsusKitItem.PROP_PRODUTO)).getProperties())
                        .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(UsuarioCadsusKitItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                        .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(UsuarioCadsusKitItem.PROP_PRODUTO, Produto.PROP_TIPO_RECEITA)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusKitItem.PROP_USUARIO_CADSUS_KIT), selectedObject))
                        .start().getList();
                if (br.com.celk.util.CollectionUtils.isNotNullEmpty(itensKit)) {
                    for (UsuarioCadsusKitItem usuarioCadsusKitItem : itensKit) {
                        DispensacaoMedicamentoItem dispensacaoMedicamentoItem = new DispensacaoMedicamentoItem();
                        Produto produto = usuarioCadsusKitItem.getProduto();

                        List<Produto> produtos = Lambda.extract(itensAddKit, Lambda.on(DispensacaoMedicamentoItem.class).getProduto());

                        if (produtos.contains(produto)) {
                            continue;
                        }

                        if (getEstabelecimentoExecutante() == null) {
                            throw new ValidacaoException(BundleManager.getString("informeEstabelecimento"));
                        }

                        dispensacaoMedicamentoItem.setProduto(produto);
                        String erroValidacao = validarAcaoBtnAdicionarItemKit(target, dispensacaoMedicamentoItem, selectedObject.getDescricao());
                        dispensacaoMedicamentoItem.setDispensacaoMedicamento(dispensacaoMedicamento);

                        if (!consultarEstoqueSemLoteVencido(produto)) {
                            dispensacaoMedicamentoItem.setStatus(DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE);
                        }

                        if (dispensacaoMedicamentoItem.getStatus() == null) {
                            dispensacaoMedicamentoItem.setStatus(DispensacaoMedicamentoItem.STATUS_NORMAL);
                        }

                        if (dispensacaoMedicamento.getUsuarioCadsusDestino() != null && usuarioCadsusKitItem.getUsuarioCadsusKit() != null) {
                            usuarioCadsusKitItem.getUsuarioCadsusKit().setUsuarioCadsus(dispensacaoMedicamento.getUsuarioCadsusDestino());
                            dispensacaoMedicamentoItem.setUsuarioCadsusKit(usuarioCadsusKitItem.getUsuarioCadsusKit());
                        }

                        Date dataDispensacao = validacaoDispensacaoHistorico(dispensacaoMedicamentoItem.getProduto(), target, false);

                        if (erroValidacao != null) {
                            produtosNotAddKit.put(dispensacaoMedicamentoItem.getProduto(), erroValidacao);
                        } else if (dataDispensacao != null) {
                            itensNotAddKit.put(dispensacaoMedicamentoItem.getProduto(), dataDispensacao);
                        } else {
                            itensAddKit.add(dispensacaoMedicamentoItem);
                        }
                    }
                } else {
                    throw new ValidacaoException(bundle("msg_kit_sem_itens", selectedObject.getDescricao()));
                }
            }
            List<Produto> produtoList = Lambda.extract(items, Lambda.on(DispensacaoMedicamentoItem.class).getProduto());
            for (DispensacaoMedicamentoItem medicamentoItem : itensAddKit) {
                if (!produtoList.contains(medicamentoItem.getProduto())) {
                    items.add(medicamentoItem);
                }
            }
            if (!itensNotAddKit.isEmpty() || !produtosNotAddKit.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                List<String> codigoProdutos = new ArrayList();
                if (!itensNotAddKit.isEmpty()) {
                    for (Map.Entry<Produto, Date> entry : itensNotAddKit.entrySet()) {
                        codigoProdutos.add(entry.getKey().getCodigo());
                        sb.append(BundleManager.getString("medicamentoJaDispensadoPacienteDataProximaDispensacao", entry.getKey().getDescricaoFormatado(), Data.formatar(entry.getValue())));
                        sb.append("</br>");
                    }
                    BOFactoryWicket.getBO(DispensacaoMedicamentoFacade.class).gerarDispensacaoEstatistica(dispensacaoMedicamento.getUsuarioCadsusDestino().getCodigo(), codigoProdutos);
                }
                if (!produtosNotAddKit.isEmpty()) {
                    for (Map.Entry<Produto, String> entry : produtosNotAddKit.entrySet()) {
                        codigoProdutos.add(entry.getKey().getCodigo());
                        sb.append(entry.getValue());
                        sb.append("</br>");
                    }
                }
                modalWarn(target, new ValidacaoException(sb.toString()));
            }
        }
    }

    private String validarAcaoBtnAdicionarItemKit(AjaxRequestTarget target, DispensacaoMedicamentoItem _dispensacaoMedicamentoItem, String nomeKit) throws ValidacaoException, DAOException {
        String msg = null;
        validacaoLiberacaoReceitaPacienteForaMunicipio(dispensacaoMedicamento.getUsuarioCadsusDestino());
        validarReceita(target);

        if (_dispensacaoMedicamentoItem.getProduto() == null) {
            msg = BundleManager.getString("informeProduto");
        }

        if (ProdutoHelper.isMedicamento(_dispensacaoMedicamentoItem.getProduto().getSubGrupo())) {
            if (!DispensacaoMedicamentoHelper.isDataReceitaValida(dispensacaoMedicamento.getDataReceita())) {
                msg = Bundle.getStringApplication("msg_data_prescricao_invalida");
            }

            if (RepositoryComponentDefault.SIM.equals(dispensacaoMedicamento.getReceitaContinua())
                    || (dispensacaoMedicamentoItem.getReceituarioItem() != null && RepositoryComponentDefault.SIM.equals(dispensacaoMedicamentoItem.getReceituarioItem().getFlagContinuo()))) {
                if (!RepositoryComponentDefault.SIM.equals(_dispensacaoMedicamentoItem.getProduto().getUsoContinuo()) || !TipoReceita.RECEITA_BASICA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                    msg = BundleManager.getString("msgReceitaUsoContinuoProdutoUsoContinuoReceitaBasicaProdutoXKitY", _dispensacaoMedicamentoItem.getProduto(), nomeKit);
                }
            }

            dispensacaoMedicamentoItem.setDataValidadeReceita(dispensacaoMedicamento.getDataReceita());

            Produto produto = _dispensacaoMedicamentoItem.getProduto();

            if (produto.getTipoReceita() == null) {
                msg = BundleManager.getString("produtoSelecionadoNaoConfiguradoTipoReceitaProdutoXKitX", produto.getDescricao(), nomeKit);
            }

            if (TipoReceita.RECEITA_BASICA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                if (!Objects.equals(TipoReceita.RECEITA_BASICA, produto.getTipoReceita().getTipoReceita())) {
                    msg = BundleManager.getString("msgTipoReceitaInvalidoParaProdutoXKitX", produto.getDescricao(), nomeKit);
                }
            } else if (TipoReceita.RECEITA_BRANCA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                if (!Arrays.asList(TipoReceita.RECEITA_BASICA, TipoReceita.RECEITA_BRANCA).contains(produto.getTipoReceita().getTipoReceita())) {
                    msg = BundleManager.getString("msgTipoReceitaInvalidoParaProdutoXKitX", produto.getDescricao(), nomeKit);
                }
            } else if (TipoReceita.RECEITA_AMARELA.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                if (!Arrays.asList(TipoReceita.RECEITA_BASICA, TipoReceita.RECEITA_BRANCA, TipoReceita.RECEITA_AMARELA).contains(produto.getTipoReceita().getTipoReceita())) {
                    msg = BundleManager.getString("msgTipoReceitaInvalidoParaProdutoXKitX", produto.getDescricao(), nomeKit);
                }
            } else if (TipoReceita.RECEITA_AZUL.equals(dispensacaoMedicamento.getTipoReceita().getTipoReceita())) {
                if (!Arrays.asList(TipoReceita.RECEITA_BASICA, TipoReceita.RECEITA_BRANCA, TipoReceita.RECEITA_AZUL).contains(produto.getTipoReceita().getTipoReceita())) {
                    msg = BundleManager.getString("msgTipoReceitaInvalidoParaProdutoXKitX", produto.getDescricao(), nomeKit);
                }
            }
        }
        return msg;
    }

    private List<IColumn> getColumnsKitPedido() {
        List<IColumn> columns = new ArrayList();

        KitPedidoPaciente proxy = on(KitPedidoPaciente.class);

        columns.add(getCustomColumnKitPedido());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));

        return columns;
    }

    private List<IColumn> getColumnsKitPedidoPaciente() {
        List<IColumn> columns = new ArrayList();

        UsuarioCadsusKit proxy = on(UsuarioCadsusKit.class);

        columns.add(getCustomColumnKitPedidoPaciente());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createColumn(bundle("paciente"), proxy.getUsuarioCadsus().getDescricaoSocialFormatado()));

        return columns;
    }

    private IColumn getCustomColumnKitPedido() {
        return new MultipleActionCustomColumn<KitPedidoPaciente>() {
            @Override
            public void customizeColumn(KitPedidoPaciente rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<KitPedidoPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, KitPedidoPaciente modelObject) throws ValidacaoException, DAOException {
                        initViewDlgKitPedido(target, modelObject);
                    }
                });
            }

        };
    }

    private IColumn getCustomColumnKitPedidoPaciente() {
        return new MultipleActionCustomColumn<UsuarioCadsusKit>() {
            @Override
            public void customizeColumn(UsuarioCadsusKit rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<UsuarioCadsusKit>() {
                    @Override
                    public void action(AjaxRequestTarget target, UsuarioCadsusKit modelObject) throws ValidacaoException, DAOException {
                        initViewDlgUsuarioKit(target, modelObject, true);
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<UsuarioCadsusKit>() {
                    @Override
                    public void action(AjaxRequestTarget target, UsuarioCadsusKit modelObject) throws ValidacaoException, DAOException {
                        initViewDlgUsuarioKit(target, modelObject, false);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<UsuarioCadsusKit>() {
                    @Override
                    public void action(AjaxRequestTarget target, UsuarioCadsusKit modelObject) throws ValidacaoException, DAOException {
                        removerUsuarioCadsusKit(target, modelObject);
                    }
                });
            }

        };
    }

    private void removerUsuarioCadsusKit(AjaxRequestTarget target, UsuarioCadsusKit modelObject) throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).deletarUsuarioCadsusKit(modelObject);
        kitPedidoPacienteList.remove(modelObject);
        tblKitPedidoPaciente.update(target);
        tblKitPedidoPaciente.populate();
    }

    private void initViewDlgKitPedido(AjaxRequestTarget target, KitPedidoPaciente kit) {
        if (dlgDetalhesItemAlmoxarifado == null) {
            addModal(target, dlgDetalhesItemAlmoxarifado = new DlgDetalhesItemAlmoxarifado(newModalId()));
        }
        dlgDetalhesItemAlmoxarifado.show(target, kit);
    }

    private void initViewDlgUsuarioKit(AjaxRequestTarget target, UsuarioCadsusKit kit, boolean edicao) {
        if (dlgDetalhesItemAlmoxarifadoKit == null) {
            addModal(target, dlgDetalhesItemAlmoxarifadoKit = new DlgDetalhesItemAlmoxarifadoKit(newModalId()) {
                @Override
                public void onSalvar(AjaxRequestTarget target, List<UsuarioCadsusKitItem> usuarioCadsusKitItemList) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cadastrarUsuarioCadsusKitItens(usuarioCadsusKitItemList);
                    DispensacaoMedicamentoPage.this.info(target, Bundle.getStringApplication("msg_registro_salvo"));
                }
            });
        }
        dlgDetalhesItemAlmoxarifadoKit.show(target, kit, edicao);
    }

    private CollectionProvider getCollectionProviderKitPedido() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return kitPedidoList;
            }
        };
    }


    private CollectionProvider getCollectionProviderKitPedidoPaciente() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return kitPedidoPacienteList;
            }
        };
    }

    private void carregaKitPaciente() {
        List<KitPedidoPaciente> itensKit = LoadManager.getInstance(KitPedidoPaciente.class)
                .addProperties(new HQLProperties(KitPedidoPaciente.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(KitPedidoPaciente.PROP_DESCRICAO, QueryCustom.QueryCustomSorter.CRESCENTE))
                .start().getList();
        kitPedidoList = new ArrayList<KitPedidoPaciente>();
        for (KitPedidoPaciente kitPedidoPaciente : itensKit) {
            kitPedidoList.add(kitPedidoPaciente);
        }
    }

    private void eventoConsultaUsuario(AjaxRequestTarget target, UsuarioCadsus object) {
        if (object != null) {
            List<UsuarioCadsusKit> itensKit = LoadManager.getInstance(UsuarioCadsusKit.class)
                    .addProperties(new HQLProperties(UsuarioCadsusKit.class).getProperties())
                    .addProperty(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                    .addProperty(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                    .addProperty(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS), object))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(UsuarioCadsusKit.PROP_DESCRICAO), QueryCustom.QueryCustomSorter.CRESCENTE))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(itensKit)) {
                kitPedidoPacienteList = new ArrayList();
                kitPedidoPacienteList.addAll(itensKit);
                if (target != null) {
                    target.add(tblKitPedidoPaciente);
                    tblKitPedidoPaciente.populate();
                }
            }
        }
    }

    private void viewDlgInformarCid(AjaxRequestTarget target, Produto produto, CodigoBarrasProduto codigoBarrasProduto, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        if (dlgInformarCidDispensacao == null) {
            addModal(target, dlgInformarCidDispensacao = new DlgInformarCidDispensacao(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Cid cid, Produto produto, CodigoBarrasProduto codigoBarrasProduto, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) throws ValidacaoException, DAOException {
                    dispensacaoMedicamentoItem.setCid(cid);
                    adicionarPorCodigoBarras(target, produto, codigoBarrasProduto, dispensacaoMedicamentoItem);
                }

                @Override
                public void changeDlg(AjaxRequestTarget target) {

                }
            });
        }
        dlgInformarCidDispensacao.show(target, bundle("msgInformarCidDispensacao"), produto, codigoBarrasProduto, dispensacaoMedicamentoItem);
    }

}