package br.com.celk.view.materiais.relatorios.judicial;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.dto.ReciboProdutoSolicitadoDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.geral.interfaces.facade.GeralReportFacade;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitado;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoItem;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 525
 */
@Private
public class ConsultaImpressaoReciboJudicialPage extends ConsultaPage<ProdutoSolicitadoMovimento, List<BuilderQueryCustom.QueryParameter>> {

    private String nomePaciente;
    private Produto produto;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField("nomePaciente"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);

        getLinkNovo().setVisible(false);

        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ProdutoSolicitadoMovimento proxy = on(ProdutoSolicitadoMovimento.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("dataDisp"), proxy.getDataMovimento()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getProdutoSolicitadoItem().getProdutoSolicitado().getUsuarioCadsus().getNome(), proxy.getProdutoSolicitadoItem().getProdutoSolicitado().getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("unidadeDisp"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("operador"), proxy.getUsuario().getNome()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ProdutoSolicitadoMovimento>() {
            @Override
            public void customizeColumn(ProdutoSolicitadoMovimento rowObject) {
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<ProdutoSolicitadoMovimento>() {
                    @Override
                    public DataReport action(ProdutoSolicitadoMovimento modelObject) throws ReportException {
                        ReciboProdutoSolicitadoDTOParam param = new ReciboProdutoSolicitadoDTOParam();
                        param.setCodigosMovimentos(Arrays.asList(modelObject.getNumeroBaixa()));
                        return BOFactory.getBO(GeralReportFacade.class).reciboEntregaMedicamentoSolicitado(param);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public void consultaCustomizeViewProperties(Map<String, String> properties) {
                properties.put(BundleManager.getString("paciente"), 
                        VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_PRODUTO_SOLICITADO_ITEM, ProdutoSolicitadoItem.PROP_PRODUTO_SOLICITADO, 
                                ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME));
            }

            @Override
            public Class getClassConsulta() {
                return ProdutoSolicitadoMovimento.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(
                        new String[] {
                                VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_PRODUTO_SOLICITADO_ITEM, ProdutoSolicitadoItem.PROP_PRODUTO_SOLICITADO, ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),
                                VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_PRODUTO_SOLICITADO_ITEM, ProdutoSolicitadoItem.PROP_PRODUTO_SOLICITADO, ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO),
                                VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_PRODUTO_SOLICITADO_ITEM, ProdutoSolicitadoItem.PROP_PRODUTO_SOLICITADO, ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL),
                                VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_EMPRESA, Empresa.PROP_CODIGO),
                                VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
                                VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_USUARIO, Usuario.PROP_CODIGO),
                                VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_USUARIO, Usuario.PROP_NOME),
                                ProdutoSolicitadoMovimento.PROP_NUMERO_BAIXA,
                                ProdutoSolicitadoMovimento.PROP_DATA_MOVIMENTO}
                );
            }

            @Override
            public boolean isDistinct() {
                return true;
            }
            

        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_PRODUTO_SOLICITADO_ITEM, ProdutoSolicitadoItem.PROP_PRODUTO_SOLICITADO, ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), true);
            }

            @Override
            public List<LoadInterceptor> getInterceptors() {
                LoadInterceptor interceptor = new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        HQLHelper hqlExists = hql.getNewInstanceSubQuery();
                        hqlExists.addToSelect("1");
                        hqlExists.addToFrom("ProdutoSolicitadoItem t1 left join t1.produtoSolicitado t2 left join t1.produto t3");
                        hqlExists.addToWhereWhithAnd("t2.codigo = " + alias + ".produtoSolicitadoItem.produtoSolicitado.codigo");
                        hqlExists.addToWhereWhithAnd("t3 = ", produto);
                        hql.addToWhereWhithAnd("exists (" + hqlExists.getQuery() + ")");
                    }
                };

                return Arrays.asList(interceptor);
            }
            
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        if (nomePaciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_PRODUTO_SOLICITADO_ITEM, ProdutoSolicitadoItem.PROP_PRODUTO_SOLICITADO,
                                            ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_PRODUTO_SOLICITADO_ITEM, ProdutoSolicitadoItem.PROP_PRODUTO_SOLICITADO,
                                            ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProdutoSolicitadoMovimento.PROP_PRODUTO_SOLICITADO_ITEM, ProdutoSolicitadoItem.PROP_PRODUTO_SOLICITADO,
                                            ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))))));
        }

        return parameters;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("reciboEntregaProdutosSolicitados");
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaImpressaoReciboJudicialPage.class;
    }
}
