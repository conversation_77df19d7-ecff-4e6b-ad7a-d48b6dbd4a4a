package br.com.celk.view.vigilancia.rotinas.automulta.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.report.vigilancia.automulta.dto.RelatorioAutoMultaDTOParam;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.grupoestabelecimento.autocomplete.AutoCompleteConsultaGrupoEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 952
 */
@Private
public class RelatorioAutoMultaPage extends RelatorioPage<RelatorioAutoMultaDTOParam> {

    private DropDown dropDownTipoAutuado;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private PnlAtividadeEstabelecimento pnlAtividadeEstabelecimento;
    private AutoCompleteConsultaGrupoEstabelecimento autoCompleteConsultaGrupoEstabelecimento;
    private DropDown dropDownFormaFormaApresentacao;
    private DropDown dropDownFormaFormaSituacao;

    @Override
    public void init(final Form<RelatorioAutoMultaDTOParam> form) {
        RelatorioAutoMultaDTOParam proxy = on(RelatorioAutoMultaDTOParam.class);

        form.add(dropDownTipoAutuado = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAutuado()), AutoMulta.TipoAutuado.values(), true, bundle("ambos")));
        form.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getVigilanciaPessoa())));
        form.add(autoCompleteConsultaGrupoEstabelecimento = new AutoCompleteConsultaGrupoEstabelecimento(path(proxy.getGrupoEstabelecimento())));
        form.add(pnlAtividadeEstabelecimento = new PnlAtividadeEstabelecimento(path(proxy.getAtividadeEstabelecimento())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getPrazoDefesa()), RelatorioAutoMultaDTOParam.PrazoDefesa.values(), bundle("ambos")));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoPeriodo()), RelatorioAutoMultaDTOParam.TipoPeriodo.values()));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(dropDownFormaFormaSituacao = DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), AutoMulta.Situacao.values(), true));
        form.add(dropDownFormaFormaApresentacao = DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioAutoMultaDTOParam.FormaApresentacao.values()));
        enableCampos(null, null);
        dropDownTipoAutuado.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCampos(target, (Long) dropDownTipoAutuado.getComponentValue());
            }
        });
        dropDownFormaFormaApresentacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCampoSituacao(target, form.getModelObject().getFormaApresentacao().toString());
            }
        });
    }

    public void enableCampos(AjaxRequestTarget target, Long tipoDenunciado) {
        if (tipoDenunciado != null) {

            Boolean controle;
            if (AutoMulta.TipoAutuado.PESSOA.value().equals(tipoDenunciado)) {
                controle = false;
            } else {
                controle = true;
            }

            pnlAtividadeEstabelecimento.setEnabled(controle);
            autoCompleteConsultaGrupoEstabelecimento.setEnabled(controle);
            autoCompleteConsultaEstabelecimento.setEnabled(controle);
            autoCompleteConsultaVigilanciaPessoa.setEnabled(!controle);
        } else {
            pnlAtividadeEstabelecimento.setEnabled(true);
            autoCompleteConsultaGrupoEstabelecimento.setEnabled(true);
            autoCompleteConsultaEstabelecimento.setEnabled(true);
            autoCompleteConsultaVigilanciaPessoa.setEnabled(true);
        }

        if (target != null) {
            pnlAtividadeEstabelecimento.limpar(target);
            autoCompleteConsultaEstabelecimento.limpar(target);
            autoCompleteConsultaGrupoEstabelecimento.limpar(target);
            autoCompleteConsultaVigilanciaPessoa.limpar(target);
            target.add(autoCompleteConsultaVigilanciaPessoa);
            target.add(pnlAtividadeEstabelecimento);
            target.add(autoCompleteConsultaEstabelecimento);
            target.add(autoCompleteConsultaGrupoEstabelecimento);
        }
    }

    public void enableCampoSituacao(AjaxRequestTarget target, String fa) {
        if (RelatorioAutoMultaDTOParam.FormaApresentacao.PRAZO.toString().equals(fa)) {
            dropDownFormaFormaSituacao.setComponentValue(null);
            dropDownFormaFormaSituacao.setEnabled(false);
        } else {
            dropDownFormaFormaSituacao.setEnabled(true);
        }

        target.add(dropDownFormaFormaSituacao);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioAutoMulta");
    }

    @Override
    public Class<RelatorioAutoMultaDTOParam> getDTOParamClass() {
        return RelatorioAutoMultaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioAutoMultaDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioAutoMulta(param);
    }

}
