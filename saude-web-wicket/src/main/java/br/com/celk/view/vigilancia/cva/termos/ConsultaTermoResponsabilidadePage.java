package br.com.celk.view.vigilancia.cva.termos;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.cva.animal.autocomplete.AutoCompleteConsultaCvaAnimal;
import br.com.celk.view.vigilancia.cva.proprietarioresponsavel.autocomplete.AutoCompleteConsultaCvaProprietarioResponsavelAnimal;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaEspecieAnimal;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.EspecieAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.TermoResponsabilidadeCva;
import br.com.ksisolucoes.vo.vigilancia.cva.animal.CvaAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.proprietarioresponsavel.CvaProprietarioResponsavel;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 634
 */
public class ConsultaTermoResponsabilidadePage extends ConsultaPage<TermoResponsabilidadeCva, List<BuilderQueryCustom.QueryParameter>> {

    private CvaProprietarioResponsavel proprietarioResponsavel;
    private CvaAnimal animal;
    private EspecieAnimal especieAnimal;
    private DatePeriod periodo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaCvaProprietarioResponsavelAnimal("proprietarioResponsavel"));
        form.add(new AutoCompleteConsultaCvaAnimal("animal"));
        form.add(new AutoCompleteConsultaEspecieAnimal("especieAnimal"));
        form.add(new PnlDatePeriod("periodo"));
        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        TermoResponsabilidadeCva proxy = on(TermoResponsabilidadeCva.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("data"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("responsavel"), proxy.getProprietarioResponsavel().getProprietarioResponsavel()));
        columns.add(createColumn(bundle("documento"), proxy.getProprietarioResponsavel().getCpfOuRgFormatado()));
        columns.add(createSortableColumn(bundle("animal"), proxy.getAnimal().getNomeAnimal()));
        columns.add(createSortableColumn(bundle("especie"), proxy.getAnimal().getCvaRacaAnimal().getEspecieAnimal().getDescricao()));
        columns.add(createSortableColumn(bundle("sexo"), proxy.getAnimal().getSexo(), proxy.getAnimal().getSexoFormatado()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TermoResponsabilidadeCva>() {
            @Override
            public void customizeColumn(final TermoResponsabilidadeCva rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<TermoResponsabilidadeCva>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoResponsabilidadeCva modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTermoResponsabilidadePage(rowObject, false));
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<TermoResponsabilidadeCva>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoResponsabilidadeCva modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTermoResponsabilidadePage(rowObject, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<TermoResponsabilidadeCva>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoResponsabilidadeCva modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<TermoResponsabilidadeCva>() {
                    @Override
                    public DataReport action(TermoResponsabilidadeCva modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoResponsabilidade(modelObject.getCodigo());
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return TermoResponsabilidadeCva.class;
            }

            @Override
            public String[] getProperties() {
                TermoResponsabilidadeCva proxy = on(TermoResponsabilidadeCva.class);

                return VOUtils.mergeProperties(
                        new HQLProperties(TermoResponsabilidadeCva.class).getProperties(),
                        new String[]{
                            path(proxy.getProprietarioResponsavel().getRg()),
                            path(proxy.getProprietarioResponsavel().getCpf()),
                            path(proxy.getProprietarioResponsavel().getProprietarioResponsavel()),
                            path(proxy.getAnimal().getNomeAnimal()),
                            path(proxy.getAnimal().getSexo()),
                            path(proxy.getAnimal().getCvaRacaAnimal().getCodigo()),
                            path(proxy.getAnimal().getCvaRacaAnimal().getDescricao()),
                            path(proxy.getAnimal().getCvaRacaAnimal().getEspecieAnimal().getCodigo()),
                            path(proxy.getAnimal().getCvaRacaAnimal().getEspecieAnimal().getDescricao()),
                            path(proxy.getAtividadeVeterinaria().getInformarMicrochip())
                        }
                );
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TermoResponsabilidadeCva.PROP_DATA_CADASTRO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();

        TermoResponsabilidadeCva proxy = on(TermoResponsabilidadeCva.class);
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getProprietarioResponsavel()), proprietarioResponsavel));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAnimal()), animal));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAnimal().getCvaRacaAnimal().getEspecieAnimal()), especieAnimal));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getDataCadastro()), periodo));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTermoResponsabilidadePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaTermoResponsabilidade");
    }

}
