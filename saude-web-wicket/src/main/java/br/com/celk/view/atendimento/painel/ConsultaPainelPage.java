package br.com.celk.view.atendimento.painel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.view.painel.atendimento.Painel1Page;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.painel.Painel;
import static ch.lambdaj.Lambda.*;
import javax.servlet.http.Cookie;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.http.WebResponse;

/**
 *
 * <AUTHOR>
 * Programa - 452
 */
@Private
public class ConsultaPainelPage extends ConsultaPage<Painel, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    
    @Override
    public void initForm(Form form) {
        form.add(new InputField("descricao", new PropertyModel(this, "descricao")));
        
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        Painel on = on(Painel.class);
        
        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("descricao"), on.getDescricao()));
        
        return columns;
    }
    
    private IColumn getActionColumn(){
        return new MultipleActionCustomColumn<Painel>() {

            @Override
            public void customizeColumn(Painel rowObject) {
                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<Painel>() {
                    @Override
                    public void action(AjaxRequestTarget target, Painel modelObject) throws ValidacaoException, DAOException {
                        registrarPainel(modelObject);
                        info(target, bundle("painelRegitradoSucesso"));
                    }
                }).setTitleBundleKey("registrar");
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<Painel>() {
                    @Override
                    public void action(AjaxRequestTarget target, Painel modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(Painel1Page.class);
                    }
                });
                addAction(ActionType.EDITAR, rowObject, new IModelAction<Painel>() {
                    @Override
                    public void action(AjaxRequestTarget target, Painel modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroPainelPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Painel>() {
                    @Override
                    public void action(AjaxRequestTarget target, Painel modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().update(target);
                    }
                });
            }
        };
    }

    private void registrarPainel(Painel painel) {
        WebResponse webResponse = (WebResponse)RequestCycle.get().getResponse();
        Cookie cookie = new Cookie("panelid", painel.getChave());
        cookie.setPath("/");
        cookie.setMaxAge(3650*24*60*60);
        webResponse.addCookie(cookie);        
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter(){

            @Override
            public Class getClassConsulta() {
                return Painel.class;
            }

            @Override
            public String[] getProperties() {
                return new HQLProperties(Painel.class).getProperties();
            }
        }){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Painel.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Painel.PROP_DESCRICAO), QueryParameter.ILIKE, descricao));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroPainelPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaPaineis");
    }

}
