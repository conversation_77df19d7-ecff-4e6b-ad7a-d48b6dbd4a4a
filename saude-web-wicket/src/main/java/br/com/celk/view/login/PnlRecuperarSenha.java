package br.com.celk.view.login;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.security.RateLimitingService;
import br.com.celk.security.SecurePasswordRecoveryService;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.commons.lang3.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.Url;
import org.apache.wicket.request.cycle.RequestCycle;

public class PnlRecuperarSenha extends Panel {

    private Form<String> form;
    private String message;
    private InputField<String> inputLoginRecuperar;
    private InputField<String> inputCpfRecuperar;
    private MultiLineLabel label;
    private WebMarkupContainer messagePanel;
    private String emailMascarado;

    public PnlRecuperarSenha(String id) {
        super(id);
        init();
    }

    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();
        this.message = "";
        inputLoginRecuperar.setModelObject("");
        inputCpfRecuperar.setModelObject("");
        messagePanel.setVisible(false);
    }

    private void init() {
        setOutputMarkupId(true);
        setOutputMarkupPlaceholderTag(true);

        form = new Form<String>("formPainelRecuperarSenha") {
            @Override
            protected void onSubmit() {
                // Previne submissão normal do form para evitar exposição na URL
                // Toda lógica é tratada via AJAX
            }
        };

        // Desabilita submissão padrão do formulário
        form.setDefaultButton(null);

        inputLoginRecuperar = new InputField<String>("loginRecuperar", new Model<>(""));
        inputCpfRecuperar = new InputField<String>("cpfRecuperar", new Model<>(""));

        form.add(inputCpfRecuperar);
        form.add(inputLoginRecuperar);
        form.add(new RecuperarSenhaButton("btnRecuperar"));

        messagePanel = new WebMarkupContainer("messagePanel");
        messagePanel.setOutputMarkupId(true);
        messagePanel.setOutputMarkupPlaceholderTag(true);

        label = new MultiLineLabel("message", new PropertyModel<String>(this, "message"));
        label.setOutputMarkupId(true);
        label.setOutputMarkupPlaceholderTag(true);
        messagePanel.add(label);
        form.add(messagePanel);

        add(form);

        // Adiciona JavaScript de segurança após o componente ser renderizado
//        add(new AbstractDefaultAjaxBehavior() {
//            @Override
//            protected void respond(AjaxRequestTarget target) {
                // Não faz nada - é apenas para adicionar o JavaScript
//            }

//        });
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(JavaScriptHeaderItem.forScript(getSecurityJavaScript(), "security-js"));
    }
    private void gerarNovaSenha() throws ValidacaoException, DAOException {
        String loginValue = StringUtils.trimToNull(inputLoginRecuperar.getValue());
        String cpfValue = StringUtils.trimToNull(inputCpfRecuperar.getValue());

        if (loginValue == null && cpfValue == null) {
            throw new ValidacaoException("Informe o login ou CPF do usuário.");
        }

        // Usa o valor preenchido (login ou CPF)
        String loginData = loginValue != null ? loginValue : cpfValue;

        // Cria token temporário para os dados sensíveis
        String clientIP = getClientIP();
        SecurePasswordRecoveryService secureService = SecurePasswordRecoveryService.getInstance();
        String token = secureService.createRecoveryToken(loginData, clientIP);

        if (token == null) {
            throw new ValidacaoException("Erro interno. Tente novamente.");
        }

        // Processa usando o token (dados não ficam expostos na requisição)
        String urlVigilancia = RequestCycle.get().getUrlRenderer().renderFullUrl(Url.parse(urlFor(SaudeNovaSenhaPage.class, null).toString()));
        this.emailMascarado = secureService.processPasswordRecovery(token, clientIP, urlVigilancia);
    }

    private void erroReiniciarSenha(String message, AjaxRequestTarget target) {
        this.message = message;
        messagePanel.setVisible(true);
        target.add(messagePanel);
        target.add(label);
    }

    private void mensagemSucesso(AjaxRequestTarget target) {
        this.message = BundleManager.getString("recuperarSenhaSucessoMsg", emailMascarado);
        messagePanel.setVisible(true);
        target.add(messagePanel);
        target.add(label);
    }

    class RecuperarSenhaButton extends AbstractAjaxButton {
        public RecuperarSenhaButton(String id) {
            super(id);
        }

        @Override
        public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
            String clientIP = getClientIP();
            RateLimitingService rateLimiting = RateLimitingService.getInstance();

            // Verifica rate limiting
            if (!rateLimiting.canAttemptPasswordRecovery(clientIP)) {
                long minutesRemaining = rateLimiting.getMinutesUntilNextAttempt(clientIP);
                erroReiniciarSenha("Muitas tentativas de recuperação de senha. Tente novamente em " + minutesRemaining + " minutos.", target);
                return;
            }

            try {
                // Registra a tentativa
                rateLimiting.recordPasswordRecoveryAttempt(clientIP);

                gerarNovaSenha();
                // Limpa os campos após submissão para evitar exposição de dados
                limparCamposFormulario(target);
                mensagemSucesso(target);
            } catch (DAOException ex) {
                Loggable.log.error("Erro ao processar recuperação de senha", ex);
                // Usa mensagem genérica para todos os tipos de erro
                erroReiniciarSenha("Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha.", target);
            } catch (ValidacaoException ex) {
                erroReiniciarSenha(ex.getMessage(), target);
            }
        }
    }

    /**
     * Limpa os campos do formulário após submissão para evitar
     * exposição de dados sensíveis na URL ou histórico do navegador
     */
    private void limparCamposFormulario(AjaxRequestTarget target) {
        inputLoginRecuperar.setModelObject("");
        inputCpfRecuperar.setModelObject("");
        target.add(inputLoginRecuperar);
        target.add(inputCpfRecuperar);
    }

    /**
     * Obtém o IP do cliente para controle de rate limiting
     */
    private String getClientIP() {
        try {
            return getRequest().getClientUrl().getHost();
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * Gera JavaScript de segurança para proteção dos campos
     */
    private String getSecurityJavaScript() {
        return "setTimeout(function() {" +
               "  var loginInput = document.querySelector('input[name*=\"loginRecuperar\"]');" +
               "  var cpfInput = document.querySelector('input[name*=\"cpfRecuperar\"]');" +
               "  var inputs = [loginInput, cpfInput].filter(function(input) { return input != null; });" +
               "  " +
               "  var inactivityTimer;" +
               "  function resetTimer() {" +
               "    clearTimeout(inactivityTimer);" +
               "    inactivityTimer = setTimeout(function() {" +
               "      inputs.forEach(function(input) { if(input) input.value = ''; });" +
               "    }, 30000);" +
               "  }" +
               "  " +
               "  inputs.forEach(function(input) {" +
               "    if(input) {" +
               "      input.addEventListener('input', resetTimer);" +
               "      input.addEventListener('focus', resetTimer);" +
               "      input.addEventListener('contextmenu', function(e) { e.preventDefault(); });" +
               "    }" +
               "  });" +
               "  " +
               "  window.addEventListener('blur', function() {" +
               "    setTimeout(function() {" +
               "      inputs.forEach(function(input) { if(input && input.value) input.value = ''; });" +
               "    }, 10000);" +
               "  });" +
               "}, 1000);";
    }
}
