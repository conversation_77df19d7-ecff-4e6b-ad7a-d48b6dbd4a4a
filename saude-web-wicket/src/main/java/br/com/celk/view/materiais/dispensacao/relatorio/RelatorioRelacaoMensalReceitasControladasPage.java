package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRelacaoMensalReceitasControladasDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 265
 */
@Private
public class RelatorioRelacaoMensalReceitasControladasPage extends RelatorioPage<RelatorioRelacaoMensalReceitasControladasDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DisabledInputField<String> txtCrf;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    private String mesAno;
    private InputField<String> txtMesAno;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown dropDownSubGrupo;
    private DropDown<TipoReceita> dropDownReceita;
    private InputField txtNumeroLista;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa", true));
        form.add(txtMesAno = new RequiredInputField("mesAno", new PropertyModel(this, "mesAno")));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissionalResponsavel", true));
        form.add(txtCrf = new DisabledInputField("crf"));
        form.add(getDropDownReceita());
        form.add(txtNumeroLista = new InputField<Long>("numeroLista"));
        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                if (object != null) {
                    Profissional pro = LoadManager.getInstance(Profissional.class)
                            .addProperties(new HQLProperties(Profissional.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_CODIGO, object.getCodigo()))
                            .start().getVO();
                    txtCrf.setComponentValue(pro.getNumeroRegistro());
                    target.add(txtCrf);
                } else {
                    txtCrf.limpar(target);
                }
            }
        });
        this.txtMesAno.setComponentValue(Data.formatarMesAno(DataUtil.getDataAtual()));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(getFormaApresentacaoDropDown());
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoMensalReceitasControladasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoMensalReceitasControladasDTOParam param) throws ReportException {
        param.setAnoMes(Data.parserMounthYear(mesAno));
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioRelacaoMensalReceitasControladas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoMensalReceitasControladas");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();

                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");

            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    private DropDown<TipoReceita> getDropDownReceita() {
        if (dropDownReceita == null) {
            dropDownReceita = new DropDown("tipoReceita");
            this.dropDownReceita.addChoice(null, "");

            List<TipoReceita> tipoReceitas = LoadManager.getInstance(TipoReceita.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(TipoReceita.PROP_TIPO_RECEITA, BuilderQueryCustom.QueryParameter.DIFERENTE, TipoReceita.RECEITA_MAGISTRAL))
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoReceita.PROP_DESCRICAO))
                    .start().getList();

            for (TipoReceita tipoReceita : tipoReceitas) {
                this.dropDownReceita.addChoice(tipoReceita, tipoReceita.getDescricao());
            }
        }

        return dropDownReceita;
    }

    public DropDown getFormaApresentacaoDropDown() {
        DropDown drop = new DropDown("formaApresentacao");

        for (RelatorioRelacaoMensalReceitasControladasDTOParam.FormaApresentacao fa : RelatorioRelacaoMensalReceitasControladasDTOParam.FormaApresentacao.values()) {
            drop.addChoice(fa.toString(), fa.toString());
        }

        return drop;
    }
}
