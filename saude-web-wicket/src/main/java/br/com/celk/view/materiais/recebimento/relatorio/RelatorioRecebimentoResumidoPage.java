package br.com.celk.view.materiais.recebimento.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.tipodocumento.autocomplete.AutoCompleteConsultaTipoDocumento;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.dto.RelatorioRecebimentoResumidoParam;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.facade.RegistroNotaFiscalReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 103
 */
@Private
public class RelatorioRecebimentoResumidoPage extends RelatorioPage<RelatorioRecebimentoResumidoParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaUsuario autoCompleteConsultaUsuario;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private AutoCompleteConsultaPessoa autoCompleteConsultaPessoa;
    private AutoCompleteConsultaTipoDocumento autoCompleteConsultaTipoDocumento;
    private DropDown<String> dropDownOrdenacao;
    private DropDown<String> dropDownQuantidade;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("lstEmpresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("lstProduto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(autoCompleteConsultaUsuario = new AutoCompleteConsultaUsuario("lstUsuario"));
        form.add(autoCompleteConsultaPessoa = new AutoCompleteConsultaPessoa("lstFornecedor"));
        form.add(autoCompleteConsultaTipoDocumento = new AutoCompleteConsultaTipoDocumento("lstTipoDocumento"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getDropDownTipoData());
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownTipoResumo());
        form.add(getDropDownQuantidade());
        form.add(getDropDownOrdenacao());
        form.add(getDropDownTipoOrdenacao());

        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setOperadorValor(true);
        autoCompleteConsultaUsuario.setMultiplaSelecao(true);
        autoCompleteConsultaUsuario.setOperadorValor(true);
        autoCompleteConsultaPessoa.setMultiplaSelecao(true);
        autoCompleteConsultaPessoa.setOperadorValor(true);
        autoCompleteConsultaTipoDocumento.setMultiplaSelecao(true);
        autoCompleteConsultaTipoDocumento.setOperadorValor(true);

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRecebimentoResumidoParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRecebimentoResumidoParam param) throws ReportException {
        return BOFactoryWicket.getBO(RegistroNotaFiscalReportFacade.class).getRelatorioRecebimentoResumido(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoDasEntradas");
    }

    public DropDown getDropDownTipoResumo() {
        DropDown dropDown = new DropDown<String>("tipoResumo");
        dropDown.addChoice(RegistroItemNotaFiscal.PROP_PRODUTO, BundleManager.getString("produto"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_FORNECEDOR, BundleManager.getString("fornecedor"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_EMISSAO, BundleManager.getString("dataEmissao"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_PORTARIA, BundleManager.getString("dataEntrada"));

        return dropDown;
    }

    public DropDown getDropDownOrdenacao() {
        if (dropDownOrdenacao == null) {
            dropDownOrdenacao = new DropDown<String>("ordenacao");
            ordenacaoSemQuantidade();
        }
        return dropDownOrdenacao;
    }

    public DropDown getDropDownTipoOrdenacao() {
        DropDown dropDown = new DropDown<Long>("tipoOrdenacao");
        dropDown.addChoice(ReportProperties.CRESCENTE, BundleManager.getString("crescente"));
        dropDown.addChoice(ReportProperties.DECRESCENTE, BundleManager.getString("decrescente"));

        return dropDown;
    }

    public DropDown getDropDownFormaApresentacao() {
        DropDown dropDown = new DropDown("formaApresentacao");
        dropDown.addChoice(RegistroItemNotaFiscal.PROP_PRODUTO, BundleManager.getString("produto"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_FORNECEDOR, BundleManager.getString("fornecedor"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_PORTARIA, BundleManager.getString("dataEntrada"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_EMISSAO, BundleManager.getString("dataEmissao"));
        dropDown.addChoice(Produto.PROP_SUB_GRUPO, BundleManager.getString("grupoProdutoSubGrupo"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_TIPO_DOCUMENTO, BundleManager.getString("tipoDocumento"));

        return dropDown;
    }

    public DropDown getDropDownTipoData() {
        DropDown dropDown = new DropDown("tipoData");
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_EMISSAO, BundleManager.getString("emissao"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_PORTARIA, BundleManager.getString("entrada"));

        return dropDown;
    }

    private DropDown<String> getDropDownQuantidade() {
        if (this.dropDownQuantidade == null) {
            this.dropDownQuantidade = new DropDown<String>("visualizaQtdade");
            dropDownQuantidade.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("nao"));
            dropDownQuantidade.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("sim"));

            dropDownQuantidade.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (RepositoryComponentDefault.SIM.equals(dropDownQuantidade.getComponentValue())) {
                        dropDownOrdenacao.removeAllChoices();
                        ordenacaoComQuantidade();
                        target.add(dropDownOrdenacao);
                    } else if (RepositoryComponentDefault.NAO.equals(dropDownQuantidade.getComponentValue())) {
                        dropDownOrdenacao.removeAllChoices();
                        ordenacaoSemQuantidade();
                        target.add(dropDownOrdenacao);
                    };
                }
            });

        }

        return this.dropDownQuantidade;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }

    private void ordenacaoSemQuantidade() {
        dropDownOrdenacao.addChoice(RegistroItemNotaFiscal.PROP_PRODUTO, BundleManager.getString("produto"));
        dropDownOrdenacao.addChoice(RegistroNotaFiscal.PROP_VALOR_TOTAL, BundleManager.getString("valorTotal"));
    }

    private void ordenacaoComQuantidade() {
        dropDownOrdenacao.addChoice(RegistroItemNotaFiscal.PROP_PRODUTO, BundleManager.getString("produto"));
        dropDownOrdenacao.addChoice(RegistroNotaFiscal.PROP_VALOR_TOTAL, BundleManager.getString("valorTotal"));
        dropDownOrdenacao.addChoice(RegistroItemNotaFiscal.PROP_QUANTIDADE, BundleManager.getString("quantidadeCompradaAbv"));
    }
}
