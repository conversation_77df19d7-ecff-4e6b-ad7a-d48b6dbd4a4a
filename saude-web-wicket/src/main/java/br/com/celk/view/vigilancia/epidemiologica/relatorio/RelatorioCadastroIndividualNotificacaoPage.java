package br.com.celk.view.vigilancia.epidemiologica.relatorio;

import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.prontuario.basico.classificacaocid.autocomplete.AutoCompleteConsultaClassificacaoCid;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioCadastroIndividualNotificacaoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 935
 */
public class RelatorioCadastroIndividualNotificacaoPage extends RelatorioPage<RelatorioCadastroIndividualNotificacaoDTOParam> {

    @Override
    public void init(Form<RelatorioCadastroIndividualNotificacaoDTOParam> form) {
        RelatorioCadastroIndividualNotificacaoDTOParam proxy = on(RelatorioCadastroIndividualNotificacaoDTOParam.class);

        AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresas()));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(Permissions.EMPRESA));

        form.add(autoCompleteConsultaEmpresa);

        form.add(new AutoCompleteConsultaCid(path(proxy.getCid())));
        form.add(new AutoCompleteConsultaClassificacaoCid(path(proxy.getClassificacaoCids())));
        form.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus())));
        form.add(new PnlChoicePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown(path(proxy.getTipoArquivo())));
    }

    @Override
    public Class<RelatorioCadastroIndividualNotificacaoDTOParam> getDTOParamClass() {
        return RelatorioCadastroIndividualNotificacaoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioCadastroIndividualNotificacaoDTOParam param) throws ReportException {
        return BOFactory.getBO(VigilanciaReportFacade.class).relatorioCadastroIndividualNotificacao(param);
    }

    @Override
    public void antesGerarRelatorio(AjaxRequestTarget target) throws ValidacaoException {
        if (param.getCid() == null && param.getUsuarioCadsus() == null
                && (CollectionUtils.isEmpty(param.getEmpresas()) || param.getEmpresas().size() > 1)) {

            if (param.getPeriodo() == null || (param.getPeriodo().getDataInicial() == null && param.getPeriodo().getDataFinal() == null)) {
                throw new ValidacaoException("Informe ao menos um filtro para gerar o relatório.");
            }

            if ((param.getPeriodo().getDataInicial() == null || param.getPeriodo().getDataFinal() == null)
                    || DataUtil.getDiasDiferenca(param.getPeriodo().getDataInicial(), param.getPeriodo().getDataFinal()) > 30) {
                throw new ValidacaoException("Na ausência dos demais filtros, é necessário informar um período máximo de 30 dias.");
            }
        }
    }

    @Override
    public String getTituloPrograma() {
        return bundle("impressaoCadastroIndividualNotificacao");
    }

}
