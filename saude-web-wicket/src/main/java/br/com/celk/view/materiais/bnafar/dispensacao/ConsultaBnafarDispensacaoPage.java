package br.com.celk.view.materiais.bnafar.dispensacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.table.selection.DTOSelection;
import br.com.celk.component.table.selection.MultiSelectionPageableTable;
import br.com.celk.materiais.bnafar.consultaIntegracao.dispensacao.ConsultaIntegracaoBnafarDispensacaoDTO;
import br.com.celk.materiais.bnafar.consultaIntegracao.dispensacao.ConsultaIntegracaoBnafarDispensacaoDTOParam;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.dao.paginacao.DataPagingResultImpl;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Marques
 * Programa - 1093
 */
@Private
public class ConsultaBnafarDispensacaoPage extends ConsultaPage<ConsultaIntegracaoBnafarDispensacaoDTO, ConsultaIntegracaoBnafarDispensacaoDTOParam> {

    private ConsultaIntegracaoBnafarDispensacaoDTOParam param = new ConsultaIntegracaoBnafarDispensacaoDTOParam();

    private Empresa empresa;
    private UsuarioCadsus usuarioCadsus;
    private Produto produto;
    private DatePeriod dataDispensacao;
    private Long statusRegistro;

    private AbstractAjaxButton reenviar;
    private DlgConfirmacaoSimNao dlgMensagemConfirmacao;

    public ConsultaBnafarDispensacaoPage(PageParameters parameters) {
        super(parameters);
    }


    static private DataPagingResult getDataPagingSelectedDTO(DataPagingResult<ConsultaIntegracaoBnafarDispensacaoDTO> result) {
        List<ConsultaIntegracaoBnafarDispensacaoDTO> resultList = result.getList();
        List<DTOSelection<ConsultaIntegracaoBnafarDispensacaoDTO>> resultSelectList = new ArrayList<>();
        if (resultList != null) {
            for (ConsultaIntegracaoBnafarDispensacaoDTO dto : resultList) {
                resultSelectList.add(new DTOSelection<>(false, dto));
            }
        }
        DataPagingResult<DTOSelection<ConsultaIntegracaoBnafarDispensacaoDTO>> resultSelection = new DataPagingResultImpl<>(resultSelectList, result.getAmountResults());
        return resultSelection;
    }

    @Override
    public void initForm(Form form) {

        // Colocado um warn para avisar que o processamento está desabilitado pelo momento.
        ConsultaBnafarDispensacaoPage.this.warn(BundleManager.getString("msgProcessoDesabilitado"));

        form.add(new AutoCompleteConsultaEmpresa("empresa", new PropertyModel(this, "empresa")));
        form.add(new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus", new PropertyModel(this, "usuarioCadsus")));
        form.add(new AutoCompleteConsultaProduto("produto", new PropertyModel(this, "produto")));
        form.add(getEnumStatusRegistros());
        form.add(new PnlDatePeriod("dataDispensacao", new PropertyModel(this, "dataDispensacao")));

        getControls().add(reenviar = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                reprocessarItensSelecionados(getIntegracoesSelecionadas());
                getPageableTable().update(target);
            }
        });
        reenviar.add(new AttributeModifier("class", "btn-orange"));
        reenviar.add(new AttributeModifier("value", bundle("reenviar")));

      /*Setado para o Botão de reenviar ficar invisivel, pois o BNAFAR no momento não está enviando esse tipo de integração
         será reabilitado posteriormente quando vir a integracao da entrada pelo RNDS*/
        reenviar.setVisible(false);

        AbstractAjaxButton reenviarTodosComErro = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException {
                confirmarReenvio(target);
                getPageableTable().update(target);
            }
        };
        reenviarTodosComErro.add(new AttributeModifier("class", "btn-blue"));
        reenviarTodosComErro.add(new AttributeModifier("value", bundle("reenviarTodosComErro")));
        reenviarTodosComErro.add(new AttributeModifier("style", "margin-left: 5px;"));

          /*Setado para o Botão de reenviar ficar invisivel, pois o BNAFAR no momento não está enviando esse tipo de integração
         será reabilitado posteriormente quando vir a integracao da entrada pelo RNDS*/
        reenviarTodosComErro.setVisible(false);

        getControls().add(reenviarTodosComErro);

        getLinkNovo().setVisible(false);
        add(form);
    }

    private void confirmarReenvio(AjaxRequestTarget target) throws ValidacaoException {
        if (isProcessando()) {
            throw new ValidacaoException("Já existe um processo de reenvio de erros em execução. Aguarde a conclusão para novo reenvio.");
        }
        if (dlgMensagemConfirmacao == null) {
            addModal(target, dlgMensagemConfirmacao = new DlgConfirmacaoSimNao(newModalId(), bundle("msgConfirmacaoReenvioErrosBnafarX", "Dispensação")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    reprocessarTodosItensComErro(target);
                }
            });
        }
        dlgMensagemConfirmacao.show(target);
    }

    private DropDown getEnumStatusRegistros() {

        DropDown dropDown = new DropDown("statusRegistro", new PropertyModel(this, "statusRegistro"));

        dropDown.addChoice(null, BundleManager.getString("todos"));
        dropDown.addChoice(BnafarHelper.StatusRegistro.ENVIADO.value(), BnafarHelper.StatusRegistro.ENVIADO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.PROCESSADO.value(), BnafarHelper.StatusRegistro.PROCESSADO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.ERRO.value(), BnafarHelper.StatusRegistro.ERRO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.ERRO_SEM_REENVIO.value(), BnafarHelper.StatusRegistro.ERRO_SEM_REENVIO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.CANCELADO.value(), BnafarHelper.StatusRegistro.CANCELADO.descricao());

        return dropDown;
    }

    @Override
    public List getColumns(List<IColumn> columns) {

        ConsultaIntegracaoBnafarDispensacaoDTO proxy = on(ConsultaIntegracaoBnafarDispensacaoDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(BundleManager.getString("dataDispensacao"), proxy.getDataDispensacao()));
        columns.add(createColumn(BundleManager.getString("estabelecimento"), proxy.getEmpresa().getDescricao()));
        columns.add(createColumn(BundleManager.getString("usuarioCadsus"), proxy.getUsuarioCadsus().getNome()));
        columns.add(createColumn(BundleManager.getString("produto"), proxy.getProduto().getDescricao()));
        columns.add(createColumn(BundleManager.getString("ultimoEnvio"), proxy.getBnafarDispensacao().getDataUltimoEnvio()));
        columns.add(createColumn(BundleManager.getString("situacao"), proxy.getDescricaoSituacao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ConsultaIntegracaoBnafarDispensacaoDTO>() {
            @Override
            public void customizeColumn(ConsultaIntegracaoBnafarDispensacaoDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarDispensacaoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarDispensacaoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesBnafarDispensacaoPage(modelObject.getBnafarDispensacao(), true));
                    }
                });
                if (rowObject.getBnafarDispensacao().getStatusRegistro().equals(BnafarHelper.StatusRegistro.ERRO.value())  && false) {
                      /*Setado para o Botão de reenviar ficar invisivel, pois o BNAFAR no momento não está enviando esse tipo de integração
                          será reabilitado posteriormente quando vir a integracao da entrada pelo RNDS*/
                    addAction(ActionType.REENVIAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarDispensacaoDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarDispensacaoDTO modelObject) throws ValidacaoException, DAOException {
                            BOFactory.getBO(MaterialBasicoFacade.class).reenviarBnafarDispensacao(modelObject.getBnafarDispensacaoElo().getBnafarDispensacaoIntegracao().getCodigo());
                            getPageableTable().update(target);
                        }
                    });
                }
                addAction(ActionType.CANCELAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarDispensacaoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarDispensacaoDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactory.getBO(MaterialBasicoFacade.class).alterarStatusBnafarDispensacao(modelObject.getBnafarDispensacao().getCodigo(), modelObject.getBnafarDispensacaoElo().getCodigo(), BnafarHelper.StatusRegistro.CANCELADO.value());
                        getPageableTable().update(target);
                    }
                }).setVisible(rowObject.getBnafarDispensacao().getStatusRegistro().equals(BnafarHelper.StatusRegistro.ERRO.value()));
            }
        };
    }

    private void reprocessarTodosItensComErro(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        info(target, "Processamento iniciado! Em alguns instantes você receberá uma mensagem com mais informações.");
        BOFactory.getBO(MaterialBasicoFacade.class).processarReenvioErrosBnafar(BnafarHelper.TipoSincronizacao.DISPENSASAO);
    }

    private boolean isProcessando() {
        return LoadManager.getInstance(AsyncProcess.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AsyncProcess.STATUS_AGUARDANDO_PROCESSAMENTO, AsyncProcess.STATUS_PROCESSANDO)))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_TIPO, AsyncProcess.TIPO_PROCESSO))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_DATA_REGISTRO, BuilderQueryCustom.QueryParameter.MAIOR, Data.removeDias(DataUtil.getDataAtual(), 1)))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_NOME_PROCESSO, BnafarHelper.getDescricaoProcessoAssincrono(BnafarHelper.TipoSincronizacao.DISPENSASAO)))
                .start().exists();
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<ConsultaIntegracaoBnafarDispensacaoDTO, ConsultaIntegracaoBnafarDispensacaoDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaIntegracaoBnafarDispensacaoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                DataPagingResult<ConsultaIntegracaoBnafarDispensacaoDTO> result = BOFactoryWicket.getBO(MaterialBasicoFacade.class).queryConsultaIntegracaoBnafarDispensacao(dataPaging);
                return getDataPagingSelectedDTO(result);
            }
        };
    }

    private List<ConsultaIntegracaoBnafarDispensacaoDTO> getIntegracoesSelecionadas() {
        return ((MultiSelectionPageableTable) getPageableTable()).getSelectedObjects();
    }

    @Override
    public ConsultaIntegracaoBnafarDispensacaoDTOParam getParameters() {

        ConsultaIntegracaoBnafarDispensacaoDTOParam param = new ConsultaIntegracaoBnafarDispensacaoDTOParam();

        param.setEmpresa(empresa);
        param.setUsuarioCadsus(usuarioCadsus);
        param.setProduto(produto);
        param.setDataDispensacao(dataDispensacao);
        param.setStatusRegistro(statusRegistro);

        return param;
    }

    @Override
    public PageableTable newPageableTable(String tableId, List<IColumn> columns, IPagerProvider pagerProvider, int rowsPerPage) {
        return new MultiSelectionPageableTable(tableId, columns, pagerProvider);
    }

    private void reprocessarItensSelecionados(List<ConsultaIntegracaoBnafarDispensacaoDTO> bnafarDispensacaoList) throws DAOException, ValidacaoException {
        if (CollectionUtils.isEmpty(bnafarDispensacaoList)) return;

        ArrayList<Long> codigosReenvio = new ArrayList<>();
        for (ConsultaIntegracaoBnafarDispensacaoDTO modelObject : bnafarDispensacaoList) {
            if (modelObject.getBnafarDispensacaoElo() != null && modelObject.getBnafarDispensacaoElo().getBnafarDispensacaoIntegracao() != null) {
                codigosReenvio.add(modelObject.getBnafarDispensacaoElo().getBnafarDispensacaoIntegracao().getCodigo());
            }
        }
        if (CollectionUtils.isEmpty(codigosReenvio)) return;

        BOFactory.getBO(MaterialBasicoFacade.class).reenviarBnafarDispensacao(codigosReenvio);
    }

    @Override
    public Class getCadastroPage() {
        return this.getClass();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consulta_bnafar_dispensacao");
    }
}
