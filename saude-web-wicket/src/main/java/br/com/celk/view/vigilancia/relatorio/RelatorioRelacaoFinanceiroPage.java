package br.com.celk.view.vigilancia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.tiposolicitacao.autocomplete.AutoCompleteConsultaTipoSolicitacao;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoFinanceiroDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 760
 */
@Private
public class RelatorioRelacaoFinanceiroPage extends RelatorioPage<RelatorioRelacaoFinanceiroDTOParam> {

    private DropDown<Integer> cbxTipoRelatorio;
    private DropDown cbxFormaApresentacao;
    private DropDown cbxOrdenacao;

    @Override
    public void init(Form<RelatorioRelacaoFinanceiroDTOParam> form) {
        RelatorioRelacaoFinanceiroDTOParam proxy = on(RelatorioRelacaoFinanceiroDTOParam.class);

        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getVigilanciaPessoa())));
        form.add(new AutoCompleteConsultaTipoSolicitacao(path(proxy.getTipoSolicitacao())));
        form.add(getDropDownTipoDocumento(path(proxy.getTipoDocumento())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getSituacao()), RelatorioRelacaoFinanceiroDTOParam.Situacao.values(), bundle("todas")));
        form.add(cbxFormaApresentacao = DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioRelacaoFinanceiroDTOParam.FormaApresentacao.values()));
        form.add(new RequiredPnlChoicePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoPeriodo()), RelatorioRelacaoFinanceiroDTOParam.TipoPeriodo.values()));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getIsento()), bundle("ambos")));
        form.add(cbxOrdenacao = DropDownUtil.getEnumDropDown(path(proxy.getOrdenacao()), RelatorioRelacaoFinanceiroDTOParam.Ordenacao.values()));
        form.add(cbxTipoRelatorio = getDropDownTipoRelatorio());

        cbxTipoRelatorio.setComponentValue(ReportProperties.DETALHADO);
        cbxTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (cbxTipoRelatorio.getComponentValue().equals(ReportProperties.RESUMIDO)) {
                    cbxFormaApresentacao.setEnabled(false);
                    cbxOrdenacao.setEnabled(false);
                } else {
                    cbxFormaApresentacao.setEnabled(true);
                    cbxOrdenacao.setEnabled(true);
                }
                ajaxRequestTarget.add(cbxFormaApresentacao);
                ajaxRequestTarget.add(cbxOrdenacao);
            }
        });
    }

    private DropDown getDropDownTipoDocumento(String proxy) {
        DropDown cbxTipoDocumento = new DropDown(proxy);
        cbxTipoDocumento.addChoice(null, bundle("todas"));

        List<TipoSolicitacao.TipoDocumento> tipoDocumentoSortedLst = Lambda.sort(
                Arrays.asList(TipoSolicitacao.TipoDocumento.values()),
                on(TipoSolicitacao.TipoDocumento.class),
                new Comparator<TipoSolicitacao.TipoDocumento>() {
                    @Override
                    public int compare(TipoSolicitacao.TipoDocumento td1, TipoSolicitacao.TipoDocumento td2) {
                        return td1.descricao().compareTo(td2.descricao());
                    }
                }
        );

        for (TipoSolicitacao.TipoDocumento tipoDocumento : tipoDocumentoSortedLst) {
            cbxTipoDocumento.addChoice(tipoDocumento.value(), tipoDocumento.descricao());
        }

        return cbxTipoDocumento;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoFinanceira");
    }

    @Override
    public Class<RelatorioRelacaoFinanceiroDTOParam> getDTOParamClass() {
        return RelatorioRelacaoFinanceiroDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoFinanceiroDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRelacaoFinanceiro(param);
    }

    private DropDown getDropDownTipoRelatorio() {
        DropDown<Integer> dropDown = new DropDown<Integer>("tipoRelatorio");

        dropDown.addChoice(ReportProperties.DETALHADO, BundleManager.getString("detalhado"));
        dropDown.addChoice(ReportProperties.RESUMIDO, BundleManager.getString("resumido"));

        return dropDown;
    }

}
