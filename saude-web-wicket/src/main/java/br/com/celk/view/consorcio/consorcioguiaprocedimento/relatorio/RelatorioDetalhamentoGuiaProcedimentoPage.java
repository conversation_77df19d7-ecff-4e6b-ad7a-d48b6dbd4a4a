package br.com.celk.view.consorcio.consorcioguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoGuiaProcedimentoDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.util.Arrays;


/**
 *
 * <AUTHOR>
 * Programa - 125
 */
@Private

public class RelatorioDetalhamentoGuiaProcedimentoPage extends RelatorioPage<RelatorioDetalhamentoGuiaProcedimentoDTOParam> {
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaConsorciado;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaConsorciado = new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaCidade("cidade"));
        form.add(new AutoCompleteConsultaTipoConta("tipoConta"));
        form.add(new UpperField("nomePaciente"));
        form.add(DropDownUtil.getEnumDropDown("tipoData", RelatorioDetalhamentoGuiaProcedimentoDTOParam.TipoData.values()));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(new CheckBox("situacaoAberta"));
        form.add(new CheckBox("situacaoCancelada"));
        form.add(new CheckBox("situacaoPaga"));
        form.add(new CheckBox("situacaoUtilizada"));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioDetalhamentoGuiaProcedimentoDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getIEnumDropDown("tipoOrdenacao", RelatorioDetalhamentoGuiaProcedimentoDTOParam.TipoOrdenacao.values(), false));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioDetalhamentoGuiaProcedimentoDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("modeloImpressao", RelatorioDetalhamentoGuiaProcedimentoDTOParam.ModeloImpressao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoResumo", RelatorioDetalhamentoGuiaProcedimentoDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getNaoSimDropDown("apresentarChave"));
    }
    
    @Override
    public Class<RelatorioDetalhamentoGuiaProcedimentoDTOParam> getDTOParamClass() {
        return RelatorioDetalhamentoGuiaProcedimentoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioDetalhamentoGuiaProcedimentoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioDetalhamentoGuiaProcedimento(param);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhamentoGuiaProcedimento");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaConsorciado;
    }

}
