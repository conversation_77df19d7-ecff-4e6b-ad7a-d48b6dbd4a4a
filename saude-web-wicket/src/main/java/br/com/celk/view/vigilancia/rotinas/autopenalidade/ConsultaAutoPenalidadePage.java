package br.com.celk.view.vigilancia.rotinas.autopenalidade;

import br.com.celk.boleto.dto.boletocloud.BoletoDTO;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgData;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.dialog.DlgAutoPenalidadeReceber;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.EmissaoBoletoVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.VigilanciaFinanceiroBoletoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidadeItem;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.bo.vigilancia.interfaces.VigilanciaAnexosHelper;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.io.File;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 616
 */
public class ConsultaAutoPenalidadePage extends ConsultaPage<AutoPenalidade, List<BuilderQueryCustom.QueryParameter>> {

    private Class classVoltar;
    private Long numeroAutoPenalidade;
    private String autuado;
    private Long situacao;
    private DatePeriod periodo;
    private DlgConfirmacaoSimNao<AutoPenalidade> dlgConfirmacaoSimNao;
    private DropDown cbxSituacao;
    private DlgAutoPenalidadeReceber dlgAutoPenalidadeReceber;
    private Component btnVoltar;
    private DlgData<AutoPenalidade> dlgDataVencimento;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro;

    public ConsultaAutoPenalidadePage() {
        super();
    }

    public ConsultaAutoPenalidadePage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        try {
            configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
        } catch (ValidacaoException e) {
            Loggable.vigilancia.error(e.getMessage(), e);
        }

        form.add(new InputField("numeroAutoPenalidade"));
        form.add(new InputField("autuado"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(cbxSituacao = DropDownUtil.getIEnumDropDown("situacao", AutoPenalidade.Situacao.values(), true));
        cbxSituacao.addAjaxUpdateValue();
        cbxSituacao.setOutputMarkupId(true);

        setExibeExpandir(true);
        getLinkNovo().setVisible(false);

        getControls().add(btnVoltar = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(classVoltar);
            }

            @Override
            public boolean isVisible() {
                return classVoltar != null;
            }
        }.setDefaultFormProcessing(false));

        btnVoltar.add(new AttributeModifier("type", "button"));
        btnVoltar.add(new AttributeModifier("class", "arrow-left"));
        btnVoltar.add(new AttributeModifier("value", bundle("voltar")));

        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());

    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        AutoPenalidade proxy = on(AutoPenalidade.class);
        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("numeroAutoPenalidadeAbv"), proxy.getNumeroFormatado()));
        columns.add(createSortableColumn(bundle("numProcessoAdministrativo"), proxy.getProcessoAdministrativo().getNumeroProcessoFormatado()));
        columns.add(createSortableColumn(bundle("dataPenalidade"), proxy.getDataPenalidade()));
        columns.add(createSortableColumn(bundle("autuado"), proxy.getAutuado()));
        columns.add(createSortableColumn(bundle("prazoRecurso"), proxy.getPrazoRecurso()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacaoFormatado()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AutoPenalidade>() {

            @Override
            public void customizeColumn(final AutoPenalidade rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<AutoPenalidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidade modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAutoPenalidadePage(modelObject));
                    }
                }).setEnabled(!AutoPenalidade.Situacao.CONCLUIDO.value().equals(rowObject.getSituacao()));
                ;

                addAction(ActionType.REMOVER, rowObject, new IModelAction<AutoPenalidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidade modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).removerAutoPenalidade(modelObject);
                        getPageableTable().populate();
                        getPageableTable().update(target);
                    }
                }).setEnabled(!AutoPenalidade.Situacao.CONCLUIDO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AutoPenalidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidade modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAutoPenalidadePage(modelObject, true));
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<AutoPenalidade>() {
                    @Override
                    public DataReport action(AutoPenalidade modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoPenalidade(modelObject.getCodigo(), modelObject.getNumeroFormatado());
                    }
                });

                ModelActionLinkPanel acaoReceber = addAction(ActionType.ANEXO, rowObject, new IModelAction<AutoPenalidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidade modelObject) throws ValidacaoException, DAOException {
                        addModal(target, dlgAutoPenalidadeReceber = new DlgAutoPenalidadeReceber(newModalId(), modelObject) {
                            @Override
                            public void onRecebimentoAuto(AjaxRequestTarget target, AutoPenalidade object) throws DAOException, ValidacaoException {
                                actionRecebimentoAutoPenalidade(object);
                                getPageableTable().update(target);
                            }
                        });
                        dlgAutoPenalidadeReceber.show(target);
                    }
                });
                acaoReceber.setTitleBundleKey("receberAuto");
                acaoReceber.setEnabled(AutoPenalidade.Situacao.AGUARDANDO_RECEBIMENTO.value().equals(rowObject.getSituacao())
                        || AutoPenalidade.Situacao.AGUARDANDO_RECURSO_CONFIRMAR_RECEBIMENTO.value().equals(rowObject.getSituacao()));
                acaoReceber.setIcon(Icon.CALENDAR_EDIT);

                ModelActionLinkPanel acaoConcluir = addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<AutoPenalidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidade modelObject) throws ValidacaoException, DAOException {
//                        if (dlgConfirmacaoSimNao == null) {
                        addModal(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao<AutoPenalidade>(newModalId(), BundleManager.getString("desejaFinalizar")) {
                            @Override
                            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                actionFinalizaAutoPenalidade(getObject());
                                getPageableTable().populate(target);
                            }
                        });
                        dlgConfirmacaoSimNao.setObject(modelObject);
                        dlgConfirmacaoSimNao.show(target);
//                        }
                    }
                });
                acaoConcluir.setEnabled(AutoPenalidade.Situacao.AGUARDANDO_RECURSO_CONFIRMAR_RECEBIMENTO.value().equals(rowObject.getSituacao())
                        || AutoPenalidade.Situacao.AGUARDANDO_RECURSO.value().equals(rowObject.getSituacao()));
                acaoConcluir.setTitleBundleKey("finalizarArquivar");

                ModelActionLinkPanel acaoBoleto = addAction(ActionType.MONEY, rowObject, new IModelAction<AutoPenalidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidade modelObject) throws ValidacaoException, DAOException {
                        VigilanciaFinanceiro vigilanciaFinanceiro = VigilanciaHelper.getVigilanciaFinanceiro(modelObject);
                        if (vigilanciaFinanceiro != null) {
                            try {
                                imprimirBoleto(target, vigilanciaFinanceiro);
                            } catch (Exception e) {
                                br.com.ksisolucoes.util.log.Loggable.log.error(e);
                            }
                        } else {
                            showDlgDataVencimentoBoleto(target, modelObject);
                        }
                    }
                });
                acaoBoleto.setTitleBundleKey("boleto");
                acaoBoleto.setVisible(isActionPermitted(Permissions.BOLETO) &&
                        ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca()));
                acaoBoleto.setIcon(Icon.CODE_BAR);
            }
        };
    }

    private void actionFinalizaAutoPenalidade(AutoPenalidade object) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(VigilanciaFacade.class).finalizarAutoPenalidade(object, true);
    }

    private void actionRecebimentoAutoPenalidade(AutoPenalidade object) throws ValidacaoException, DAOException {
        ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        if (ConfiguracaoVigilanciaEnum.TipoDatabaseCalculoRecurso.RECEBIMENTO.value().equals(configuracaoVigilancia.getTipoDatabaseCalculoPrazoRecurso()) && object.getDataRecebimento() == null) {
            object.setSituacao(AutoPenalidade.Situacao.AGUARDANDO_RECEBIMENTO.value());
        } else if (ConfiguracaoVigilanciaEnum.TipoDatabaseCalculoRecurso.RECEBIMENTO.value().equals(configuracaoVigilancia.getTipoDatabaseCalculoPrazoRecurso()) && object.getDataRecebimento() != null) {
            object.setSituacao(AutoPenalidade.Situacao.AGUARDANDO_RECURSO.value());
        }
        BOFactoryWicket.save(object);
        getSession().getFeedbackMessages().info(this, bundle("msgDataRecebimentoSalvaComSucesso"));
    }

    private void showDlgDataVencimentoBoleto(AjaxRequestTarget target, AutoPenalidade autoPenalidade) {
        if (dlgDataVencimento == null) {
            addModal(target, dlgDataVencimento = new DlgData<AutoPenalidade>(newModalId(), bundle("msgConfirmeDataVencimento"), "dataVencimento") {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Date date) throws ValidacaoException, DAOException {
                    VigilanciaFinanceiroBoletoDTO dto = new VigilanciaFinanceiroBoletoDTO();
                    dto.setAutoPenalidade(getObject());
                    dto.setValorBoleto(getValorBoletoAutoPenalidade(getObject()));
                    dto.setDataVencimento(date);
                    List<VigilanciaFinanceiro> vigilanciaFinanceiroList = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarEmissaoBoletoAuto(dto);

                    if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
                        try {
                            imprimirBoleto(target, vigilanciaFinanceiroList.get(0));
                        } catch (Exception e) {
                            br.com.ksisolucoes.util.log.Loggable.log.error(e);
                            throw new ValidacaoException(e);
                        }
                    }
                }
            });
        }
        dlgDataVencimento.setObject(autoPenalidade);
        dlgDataVencimento.show(target, DataUtil.getDataAtual());
    }

    private Double getValorBoletoAutoPenalidade(AutoPenalidade autoPenalidade) throws ValidacaoException {
        List<AutoPenalidadeItem> list = LoadManager.getInstance(AutoPenalidadeItem.class)
                .addProperty(AutoPenalidadeItem.PROP_CODIGO)
                .addProperty(AutoPenalidadeItem.PROP_VALOR_MULTA)
                .addParameter(new QueryCustom.QueryCustomParameter(AutoPenalidadeItem.PROP_AUTO_PENALIDADE, autoPenalidade))
                .addParameter(new QueryCustom.QueryCustomParameter(AutoPenalidadeItem.PROP_VALOR_MULTA, QueryCustom.QueryCustomParameter.IS_NOT_NULL))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(list)) {
            Double sum = Lambda.sum(list, Lambda.on(AutoPenalidadeItem.class).getValorMulta());
            if (Coalesce.asDouble(sum) > 0D) {
                return new Dinheiro(sum).doubleValue();
            } else {
                throw new ValidacaoException("Boleto não pode ser gerado com valor zero");
            }
        } else {
            throw new ValidacaoException("Não existem penalidades com valor de multa informado");
        }
    }

    private void imprimirBoleto(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws Exception {
        File boleto = null;
        if (vigilanciaFinanceiro.getAnexoBoleto() != null) {
            boleto = VigilanciaAnexosHelper.getFileAnexoBoleto(vigilanciaFinanceiro);
            FileUtils.buscarArquivoFtp(vigilanciaFinanceiro.getAnexoBoleto().getCaminho(), boleto.getAbsolutePath());
        } else if (vigilanciaFinanceiro.getTokenBoleto() != null) {
            BoletoDTO boletoDTO = BOFactoryWicket.getBO(BasicoFacade.class).consultarBoleto(vigilanciaFinanceiro.getTokenBoleto());
            boleto = boletoDTO.getBoleto();
            if (boleto != null) {
                FinanceiroVigilanciaHelper.anexarBoleto(vigilanciaFinanceiro, boleto);
                BOFactoryWicket.save(vigilanciaFinanceiro);
            }
        } else if (Coalesce.asDouble(vigilanciaFinanceiro.getValor()) > 0D) {
            EmissaoBoletoVigilanciaDTO dto = new EmissaoBoletoVigilanciaDTO();
            dto.setVigilanciaFinanceiro(vigilanciaFinanceiro);

            boleto = BOFactoryWicket.getBO(VigilanciaReportFacade.class).gerarBoletoVigilancia(dto);
        }
        if (boleto != null) {
            String fileToBase64 = FileUtils.getFileToBase64(boleto);
            Files.deleteIfExists(boleto.toPath());
            ajaxPreviewBlank.initiatePdfBase64(target, fileToBase64);
        }
    }


    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return AutoPenalidade.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(AutoPenalidade.class).getProperties(),
                        new HQLProperties(Estabelecimento.class, AutoPenalidade.PROP_ESTABELECIMENTO_AUTUADO).getProperties(),
                        new HQLProperties(VigilanciaPessoa.class, AutoPenalidade.PROP_VIGILANCIA_PESSOA).getProperties(),
                        new HQLProperties(ProcessoAdministrativo.class, AutoPenalidade.PROP_PROCESSO_ADMINISTRATIVO).getProperties(),
                        new HQLProperties(VigilanciaEndereco.class, AutoPenalidade.PROP_VIGILANCIA_ENDERECO).getProperties(),
                        new HQLProperties(AutoIntimacao.class, AutoPenalidade.PROP_AUTO_INTIMACAO).getProperties(),
                        new HQLProperties(AutoInfracao.class, AutoPenalidade.PROP_AUTO_INFRACAO).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(AutoPenalidade.PROP_NUMERO), false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> lstParametros = new ArrayList<BuilderQueryCustom.QueryParameter>();
        lstParametros.add(new QueryCustom.QueryCustomParameter(AutoPenalidade.PROP_NUMERO, numeroAutoPenalidade));
        lstParametros.add(new QueryCustom.QueryCustomParameter(AutoPenalidade.PROP_AUTUADO, QueryCustom.QueryCustomParameter.ILIKE, autuado));
        lstParametros.add(new QueryCustom.QueryCustomParameter(AutoPenalidade.PROP_DATA_PENALIDADE, periodo));
        lstParametros.add(new QueryCustom.QueryCustomParameter(AutoPenalidade.PROP_SITUACAO, situacao));
        return lstParametros;
    }

    public void setClassVoltar(Class classVoltar) {
        this.classVoltar = classVoltar;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAutoPenalidadePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaAutoPenalidade");
    }
}
