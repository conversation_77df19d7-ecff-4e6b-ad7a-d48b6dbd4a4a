package br.com.celk.view.vigilancia.requerimentovigilancia;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PendenciasVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 23/05/17.
 * Programa - 763
 */
@Private
public class PendenciasRecepcaoPage extends BasePage {

    private List<PendenciasVigilanciaDTO> pendenciasVigilanciaList = new ArrayList<PendenciasVigilanciaDTO>();
    private SelectionTable<PendenciasVigilanciaDTO> tblRequerimentos;
    private static final String CSS_FILE = "PendenciasRecepcaoPage.css";

    public PendenciasRecepcaoPage() {
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(tblRequerimentos = new SelectionTable("tblRequerimentos", getColumnsCalendario(), getCollectionProviderCalendario()));
        tblRequerimentos.addSelectionAction(new ISelectionAction<PendenciasVigilanciaDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, PendenciasVigilanciaDTO object) {
                actionTableRequerimentos(object);
            }
        });
        tblRequerimentos.populate();

        form.add(new AbstractAjaxButton("btnAtualizar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                carregarPendencias(target);
            }
        });

        form.add(new AbstractAjaxButton("btnConsultaRequerimentos") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaRequerimentoVigilanciaPage(PendenciasRecepcaoPage.class));
            }
        });

        add(form);

        try {
            carregarPendencias(null);
        } catch (DAOException | ValidacaoException e) {
            Loggable.log.error(e);
        }
    }

    private void actionTableRequerimentos(PendenciasVigilanciaDTO object) {
        RequerimentoVigilancia rv = object.getRequerimentoVigilancia();

        if (RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_ANALISE.value().equals(identificarDescricaoSituacao(rv))) {
            setResponsePage(new ConsultaRequerimentoVigilanciaPage(
                    RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_ANALISE.value(),
                    rv.getSituacaoOcorrencia(),
                    null,
                    false,
                    rv.getSituacaoAnaliseProjetos(),
                    RepositoryComponentDefault.SIM_LONG,
                    PendenciasRecepcaoPage.class));
        } else if (RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_APROVACAO.value().equals(identificarDescricaoSituacao(rv))) {
            setResponsePage(new ConsultaRequerimentoVigilanciaPage(
                    RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_APROVACAO.value(),
                    rv.getSituacaoOcorrencia(),
                    null,
                    false,
                    rv.getSituacaoAnaliseProjetos(),
                    RepositoryComponentDefault.NAO_LONG,
                    PendenciasRecepcaoPage.class));
        } else {
            setResponsePage(new ConsultaRequerimentoVigilanciaPage(
                    rv.getSituacao(),
                    rv.getSituacaoOcorrencia(),
                    rv.getSituacaoAprovacao(),
                    false,
                    rv.getSituacaoAnaliseProjetos(),
                    RepositoryComponentDefault.NAO_LONG,
                    PendenciasRecepcaoPage.class));
        }
    }

    private Long identificarDescricaoSituacao(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia.getDescricaoSituacao().equals(Bundle.getStringApplication("rotulo_pendente_aguardando_analise"))) {
            return RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_ANALISE.value();
        }

        if (requerimentoVigilancia.getDescricaoSituacao().equals(Bundle.getStringApplication("rotulo_pendente_aguardando_aprovacao"))) {
            return RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_APROVACAO.value();
        }

        return null;
    }

    private List<IColumn> getColumnsCalendario() {
        List<IColumn> columns = new ArrayList<IColumn>();
        PendenciasVigilanciaDTO proxy = on(PendenciasVigilanciaDTO.class);

        columns.add(createColumn(BundleManager.getString("pendencia"), proxy.getRequerimentoVigilancia().getDescricaoSituacao()));
        columns.add(createColumn(BundleManager.getString("quantidade"), proxy.getQuantidade()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderCalendario() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return pendenciasVigilanciaList;
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("pendenciasRecepcao");
    }

    private void carregarPendencias(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        pendenciasVigilanciaList = BOFactoryWicket.getBO(VigilanciaFacade.class).consultarPendenciasRecepcaoVigilancia();

        if (target != null) {
            tblRequerimentos.update(target);
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(PendenciasFiscalPage.class, CSS_FILE)));
    }
}