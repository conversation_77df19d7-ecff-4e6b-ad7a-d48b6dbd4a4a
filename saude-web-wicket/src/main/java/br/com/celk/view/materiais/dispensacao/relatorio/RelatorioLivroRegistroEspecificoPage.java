package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.RequiredMesAnoField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.GraficoDistribuicaoMensalExameAgendamentosDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioLivroRegistroEspecificoDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 926
 */
@Private
public class RelatorioLivroRegistroEspecificoPage extends RelatorioPage<RelatorioLivroRegistroEspecificoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DropDown<String> dropDownTipoReceita;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private RequiredMesAnoField mesAnoInicial;
    private RequiredMesAnoField mesAnoFinal;
    private Date dataInicial;
    private Date dataFinal;
    private DropDown dropDownFormaApresentacao;


    @Override
    public void init(Form form) {
        RelatorioLivroRegistroEspecificoDTOParam proxy = on(RelatorioLivroRegistroEspecificoDTOParam.class);
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa", true).setLabel(new Model(WicketMethods.bundle("empresa"))));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxy.getProduto())));

        autoCompleteConsultaProduto.setMedicamento(SubGrupo.MEDICAMENTO_SIM);
        autoCompleteConsultaProduto.setMostraJudicial(SubGrupo.MOSTRA_JUDICIAL_NAO);
        autoCompleteConsultaProduto.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO);
        autoCompleteConsultaProduto.setEnabled(false);

        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("responsavel", true));
        form.add(dropDownFormaApresentacao = DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioLivroRegistroEspecificoDTOParam.FormaApresentacao.values(), ""));

        autoCompleteConsultaProfissional.setEnabled(false);
        autoCompleteConsultaProfissional.setLabel(new Model(WicketMethods.bundle("farmaceuticoResponsavel")));
        autoCompleteConsultaProfissional.setCodigoGrupoTabelaCbo("2234");

        form.add(mesAnoInicial = new RequiredMesAnoField("dataInicial", new PropertyModel(this, "dataInicial")));
        form.add(mesAnoFinal = new RequiredMesAnoField("dataFinal", new PropertyModel(this, "dataFinal")));

        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                dropDownGrupoProduto.setEnabled(false);
                dropDownSubGrupo.setEnabled(false);
                target.add(dropDownGrupoProduto, dropDownSubGrupo);
            }
        });

        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                dropDownGrupoProduto.setEnabled(true);
                dropDownSubGrupo.setEnabled(true);
                target.add(dropDownGrupoProduto, dropDownSubGrupo);
            }
        });

        dropDownFormaApresentacao.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                mesAnoInicial.limpar(target);
                mesAnoFinal.limpar(target);
                mesAnoInicial.setEnabled(dropDownFormaApresentacao.getComponentValue() == null);
                mesAnoFinal.setEnabled(dropDownFormaApresentacao.getComponentValue() == null);
                target.add(mesAnoInicial, mesAnoFinal);
                target.appendJavaScript(JScript.initMasks());
            }
        });

        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(getDropDownTipoReceita());


        autoCompleteConsultaEmpresa.setMultiplaSelecao(false);
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                recarregarEmpresa(empresa);
                autoCompleteConsultaProduto.limpar(target);
                autoCompleteConsultaProfissional.limpar(target);
                autoCompleteConsultaProduto.setEnabled(true);
                autoCompleteConsultaProduto.setEmpresas(Collections.singletonList(empresa));
                autoCompleteConsultaProfissional.setCodigoEmpresa(empresa.getCodigo());
//                autoCompleteConsultaProfissional.setCodigoTabelaCbo("");
                autoCompleteConsultaProfissional.setPeriodoEmpresa(true);
                autoCompleteConsultaProfissional.setEnabled(true);
                target.add(autoCompleteConsultaProduto, autoCompleteConsultaProfissional);
                target.focusComponent(autoCompleteConsultaProduto);
            }
        });

        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa empresa) {
                autoCompleteConsultaProduto.limpar(target);
                autoCompleteConsultaProfissional.limpar(target);
                autoCompleteConsultaProduto.setEnabled(false);
                autoCompleteConsultaProfissional.setEnabled(false);
                dropDownGrupoProduto.setEnabled(true);
                dropDownSubGrupo.setEnabled(true);
                target.add(autoCompleteConsultaProfissional, dropDownGrupoProduto, dropDownSubGrupo);
            }
        });

    }

    public DropDown<String> getDropDownTipoReceita() {
        if (dropDownTipoReceita == null) {
            dropDownTipoReceita = new DropDown<String>("tipoReceita");
            dropDownTipoReceita.addChoice(null, BundleManager.getString("todos"));
            dropDownTipoReceita.addChoice(TipoReceita.RECEITA_AMARELA, BundleManager.getString("amarela"));
            dropDownTipoReceita.addChoice(TipoReceita.RECEITA_AZUL, BundleManager.getString("azul"));
            dropDownTipoReceita.addChoice(TipoReceita.RECEITA_BRANCA, BundleManager.getString("branca"));
            dropDownTipoReceita.addChoice(TipoReceita.RECEITA_BASICA, BundleManager.getString("basica"));
            dropDownTipoReceita.addChoice(TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO, BundleManager.getString("prescricaoAtedimento"));
        }
        return dropDownTipoReceita;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new RequiredDropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new RequiredDropDown<GrupoProduto>("grupoProduto");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class).addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO)).addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO)).addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO)).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo())).addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO)).start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, "");
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class).addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO)).addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO)).addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO)).start().getList();

            dropDownGrupoProduto.addChoice(null, "");

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }

    private void recarregarEmpresa(Empresa empresa) {
        empresa = LoadManager.getInstance(Empresa.class).addProperties(new HQLProperties(Empresa.class).getProperties()).addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(Empresa.PROP_CIDADE)).getProperties()).addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(Empresa.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties()).setId(empresa.getCodigo()).start().getVO();
        autoCompleteConsultaEmpresa.getModel().setObject(empresa);
    }

    @Override
    public void antesGerarRelatorio(AjaxRequestTarget target) throws ValidacaoException {
        if (dropDownFormaApresentacao.getComponentValue() != null) {
            if (dropDownFormaApresentacao.getComponentValue().equals(RelatorioLivroRegistroEspecificoDTOParam.FormaApresentacao.MENSAL)) {
                param.setDataInicial(Data.getDataParaPrimeiroDiaMes(new Date()));
                param.setDataFinal(Data.getDataParaUltimoDiaMes(new Date()));
            } else if (dropDownFormaApresentacao.getComponentValue().equals(RelatorioLivroRegistroEspecificoDTOParam.FormaApresentacao.DIARIO)) {
                param.setDataInicial(new Date());
                param.setDataFinal(new Date());
            }
        } else {
            DatePeriod datePeriod = Data.adjustRangeHour(dataInicial, Data.adjustMonthToLastDay(dataFinal));
            Long mesesDiferenca = DataUtil.getMesesDiferenca(datePeriod.getDataInicial(), datePeriod.getDataFinal());
            if (mesesDiferenca > 3) {
                throw new ValidacaoException(BundleManager.getString("periodoSelecaoMaximoXMeses", "3"));
            }
            param.setDataInicial(datePeriod.getDataInicial());
            param.setDataFinal(datePeriod.getDataFinal());
        }
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioLivroRegistroEspecificoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioLivroRegistroEspecificoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioLivroRegistroEspecifico(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("livroRegistroEspecifico");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
