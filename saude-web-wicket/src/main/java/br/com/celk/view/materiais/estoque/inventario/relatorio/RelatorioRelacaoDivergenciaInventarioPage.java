package br.com.celk.view.materiais.estoque.inventario.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.inventario.autocomplete.AutoCompleteConsultaInventario;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoDivergenciaInventarioDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.ControleInventario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 906
 */
@Private
public class RelatorioRelacaoDivergenciaInventarioPage extends RelatorioPage<RelatorioRelacaoDivergenciaInventarioDTOParam> {

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown dropDownFormaApresentacao;
    private PnlChoicePeriod pnlChoicePeriod;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaUsuario autoCompleteConsultaUsuario;
    private AutoCompleteConsultaInventario autoCompleteConsultaInventario;

    @Override
    public void init(Form form) {
        RelatorioRelacaoDivergenciaInventarioDTOParam proxy = on(RelatorioRelacaoDivergenciaInventarioDTOParam.class);

        configuraComponentes(proxy);

        form.add(autoCompleteConsultaEmpresa);

        form.add(autoCompleteConsultaUsuario);
        form.add(autoCompleteConsultaInventario);
        form.add(getDropDownGrupo(path(proxy.getGrupoProduto())));
        form.add(getDropDownSubGrupo(path(proxy.getSubGrupo())));
        form.add(dropDownFormaApresentacao);
        form.add(pnlChoicePeriod);
    }

    private void configuraComponentes(RelatorioRelacaoDivergenciaInventarioDTOParam proxy) {
        pnlChoicePeriod = new PnlChoicePeriod(path(proxy.getPeriodo()));
        pnlChoicePeriod.setDefaultOutro();

        autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresas()));
        autoCompleteConsultaEmpresa.setOperadorValor(true);

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);

        autoCompleteConsultaInventario = new AutoCompleteConsultaInventario(path(proxy.getInventario()));
        if (!isPermissaoEmpresa) {
            try {
                List<Long> empresasUsuario = BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
                List<Empresa> empresas = LoadManager.getInstance(Empresa.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, empresasUsuario))
                        .start().getList();

                autoCompleteConsultaInventario.setEmpresaList(empresas);
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            } catch (ValidacaoException e) {
                Loggable.log.error(e.getMessage());
            }
        }

        autoCompleteConsultaUsuario = new AutoCompleteConsultaUsuario(path(proxy.getUsuario()));
        autoCompleteConsultaUsuario.setOperadorValor(true);

        dropDownFormaApresentacao = new DropDown(path(proxy.getFormaApresentacao()));
        dropdownFAaddchoices();

    }

    private void dropdownFAaddchoices() {
        dropDownFormaApresentacao.removeAllChoices();
        dropDownFormaApresentacao.addChoice(ReportProperties.FORMA_APRESENTACAO_GERAL, Bundle.getStringApplication("rotulo_geral"));
        dropDownFormaApresentacao.addChoice(ControleInventario.PROP_INVENTARIO, Bundle.getStringApplication("rotulo_inventario"));
    }

    private DropDown<GrupoProduto> getDropDownGrupo(String id) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>(id);

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();

                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo(String id) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>(id);

            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoDivergenciaInventarioDTOParam.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoDivergenciasInventario");
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoDivergenciaInventarioDTOParam param) throws ReportException {
        if (dropDownGrupoProduto.getComponentValue() != null) {
            param.setGruposProdutoInformado();
        } else {
            param.setGruposProduto(null);
        }
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioDivergenciaInventario(param);
    }
}
