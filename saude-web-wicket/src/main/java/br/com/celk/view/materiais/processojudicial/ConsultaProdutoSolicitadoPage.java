package br.com.celk.view.materiais.processojudicial;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.materiais.interceptor.LoadInterceptorExistsProdutoSolicitadoItem;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitado;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 520
 */
@Private
public class ConsultaProdutoSolicitadoPage extends ConsultaPage<ProdutoSolicitado, List<BuilderQueryCustom.QueryParameter>> {

    private InputField txtNomePaciente;
    private InputField txtRota;
    private String rota;
    private String nomePaciente;
    private Empresa empresa;
    private Profissional profissional;
    private Produto produto;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(txtNomePaciente = new InputField("nomePaciente"));
        form.add(txtRota = new InputField("rota"));
        form.add(new AutoCompleteConsultaEmpresa("empresa"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto").setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO));
        autoCompleteConsultaProduto.setIncluirInativos(false);
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ProdutoSolicitado proxy = on(ProdutoSolicitado.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("paciente"), proxy.getUsuarioCadsus().getNome(), proxy.getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("unidadeOrigemAbv"), proxy.getEmpresa()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("tipoSolicitacao"), proxy.getTipoSolicitacaoProduto().getDescricao()));
        columns.add(createSortableColumn(bundle("rota"), proxy.getDescricaoRota()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ProdutoSolicitado>() {
            @Override
            public void customizeColumn(ProdutoSolicitado rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ProdutoSolicitado>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProdutoSolicitado modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroProdutoSolicitadoPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ProdutoSolicitado>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProdutoSolicitado modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(MaterialBasicoFacade.class).deletarProdutoSolicitado(modelObject);
                        getPageableTable().update(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ProdutoSolicitado>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProdutoSolicitado modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroProdutoSolicitadoPage(modelObject, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public void consultaCustomizeViewProperties(Map<String, String> properties) {
                properties.put(BundleManager.getString("paciente"), VOUtils.montarPath(VOUtils.montarPath(ProdutoSolicitado.PROP_USUARIO_CADSUS)));
                properties.put(BundleManager.getString("empresa"), ProdutoSolicitado.PROP_EMPRESA);
                properties.put(BundleManager.getString("profissional"), ProdutoSolicitado.PROP_PROFISSIONAL);
            }

            @Override
            public Class getClassConsulta() {
                return ProdutoSolicitado.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(ProdutoSolicitado.class).getProperties(),
                        new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(ProdutoSolicitado.PROP_USUARIO_CADSUS)).getProperties());
            }

        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), true);
            }

            @Override
            public List<LoadInterceptor> getInterceptors() {
                LoadInterceptor interceptor = new LoadInterceptorExistsProdutoSolicitadoItem(produto);
                return Arrays.asList(interceptor);
            }

        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (nomePaciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))))));
        }

        parameters.add(new QueryCustom.QueryCustomParameter(ProdutoSolicitado.PROP_EMPRESA, empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(ProdutoSolicitado.PROP_PROFISSIONAL, profissional));
        parameters.add(new QueryCustom.QueryCustomParameter(ProdutoSolicitado.PROP_STATUS, ProdutoSolicitado.STATUS_ATIVO));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProdutoSolicitado.PROP_DESCRICAO_ROTA), BuilderQueryCustom.QueryParameter.ILIKE, rota));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroProdutoSolicitadoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaSolicitacaoProdutos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtNomePaciente;
    }
}
