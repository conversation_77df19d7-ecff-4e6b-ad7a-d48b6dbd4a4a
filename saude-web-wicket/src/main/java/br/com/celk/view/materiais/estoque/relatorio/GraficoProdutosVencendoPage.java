package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.materiais.grupoproduto.autocomplete.AutoCompleteConsultaGrupoProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryGraficoProdutosVencendoDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import br.com.celk.component.dropdown.DropDown;

/**
 *
 * <AUTHOR>
 * Programa - 147
 */
@Private

public class GraficoProdutosVencendoPage extends RelatorioPage<QueryGraficoProdutosVencendoDTOParam> {

    private AutoCompleteConsultaGrupoProduto autoCompleteConsultaGrupoProduto;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaGrupoProduto = new AutoCompleteConsultaGrupoProduto("grupoProduto"));
        form.add(getCbxPeriodo());
        form.add(DropDownUtil.getSimNaoDropDown("separarUnidade"));
        
        autoCompleteConsultaGrupoProduto.setMultiplaSelecao(true);
        autoCompleteConsultaGrupoProduto.setOperadorValor(true);
    }
    
    private DropDown getCbxPeriodo(){
        DropDown cbxPeriodo = new DropDown("meses");
        
        cbxPeriodo.addChoice(1, BundleManager.getString("1mes"));
        cbxPeriodo.addChoice(2, BundleManager.getString("2mes"));
        cbxPeriodo.addChoice(3, BundleManager.getString("3mes"));
        cbxPeriodo.addChoice(6, BundleManager.getString("6mes"));
        cbxPeriodo.addChoice(12, BundleManager.getString("1ano"));
        
        return cbxPeriodo;
    }

    @Override
    public Class<QueryGraficoProdutosVencendoDTOParam> getDTOParamClass() {
        return QueryGraficoProdutosVencendoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(QueryGraficoProdutosVencendoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).graficoProdutosVencendo(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("graficoProdutosVencendo");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaGrupoProduto.getTxtDescricao().getTextField();
    }

}
