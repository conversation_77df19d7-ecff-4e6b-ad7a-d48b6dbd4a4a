package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioExtratoPacienteDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 * Programa - 271
 */
@Private

public class RelatorioExtratoPacientePage extends RelatorioPage<RelatorioExtratoPacienteDTOParam> {

    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus("paciente", true));
        form.add(DropDownUtil.getEnumDropDown("tipoRelatorio", RelatorioExtratoPacienteDTOParam.TipoRelatorio.values()));
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("tipoPeriodo", RelatorioExtratoPacienteDTOParam.TipoPeriodo.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoPreco", RelatorioExtratoPacienteDTOParam.TipoPreco.values()));
        form.add(DropDownUtil.getSimNaoDropDown("mostrarValores"));

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioExtratoPacienteDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioExtratoPacienteDTOParam param) throws ReportException {
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        param.setAcessoRestrito(!isPermissaoEmpresa);
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioExtratoPaciente(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("extratoPaciente");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaUsuarioCadsus.getTxtDescricao().getTextField();
    }
}
