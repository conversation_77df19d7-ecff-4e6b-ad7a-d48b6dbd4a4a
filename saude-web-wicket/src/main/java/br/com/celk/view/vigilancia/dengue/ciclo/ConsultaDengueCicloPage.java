package br.com.celk.view.vigilancia.dengue.ciclo;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueCiclo;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import br.com.ksisolucoes.vo.vigilancia.dengue.RegistroDiarioAntivetorial;
import br.com.ksisolucoes.vo.vigilancia.dengue.base.BaseDengueCiclo;
import br.com.ksisolucoes.vo.vigilancia.dengue.base.BaseRegistroDiarioAntivetorial;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 644
 */
public class ConsultaDengueCicloPage extends ConsultaPage<DengueCiclo, List<BuilderQueryCustom.QueryParameter>> {

    private Long ciclo;
    private Date dataSemanaInicial;
    private Date dataSemanaFinal;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<Long>("ciclo"));
        form.add(new DateChooser("dataSemanaInicial"));
        form.add(new DateChooser("dataSemanaFinal"));

        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DengueCiclo proxy = on(DengueCiclo.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("ciclo"), proxy.getCodigo()));
        columns.add(createSortableColumn(BundleManager.getString("semanaInicial"), proxy.getDataSemanaInicial()));
        columns.add(createSortableColumn(BundleManager.getString("semanaFinal"), proxy.getDataSemanaFinal()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<DengueCiclo>() {
            @Override
            public void customizeColumn(final DengueCiclo rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<DengueCiclo>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueCiclo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroDengueCicloPage(rowObject, false, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<DengueCiclo>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueCiclo modelObject) throws ValidacaoException, DAOException {
                        excluirAction(target, rowObject);
                    }
                });
            }

        };
    }

    private void excluirAction(AjaxRequestTarget target, DengueCiclo rowObject) throws ValidacaoException, DAOException {
        validaExclusao(rowObject);

        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
        getPageableTable().populate(target);
    }

    private void validaExclusao(DengueCiclo rowObject) throws ValidacaoException {
        boolean hasRegistroDiarioAntivetorial = LoadManager.getInstance(RegistroDiarioAntivetorial.class)
                .addParameter(new QueryCustom.QueryCustomParameter(BaseRegistroDiarioAntivetorial.PROP_CICLO, rowObject))
                .start().exists();
        if(hasRegistroDiarioAntivetorial) {
            throw new ValidacaoException(BundleManager.getString("msg_nao_pode_excluir_ciclo"));
        }
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return DengueCiclo.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(BaseDengueCiclo.PROP_CODIGO, false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseDengueCiclo.PROP_CODIGO), ciclo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseDengueCiclo.PROP_DATA_SEMANA_INICIAL), dataSemanaInicial));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseDengueCiclo.PROP_DATA_SEMANA_FINAL), dataSemanaFinal));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroDengueCicloPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDengueCiclo");
    }
}
