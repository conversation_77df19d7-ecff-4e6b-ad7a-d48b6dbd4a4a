package br.com.celk.view.vigilancia.externo.view.login;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.StringUtil;
import br.com.celk.view.vigilancia.externo.template.base.BasePageVigilanciaExterno;
import br.com.celk.view.vigilancia.externo.view.components.feedback.FeedBackVigilancia;
import br.com.celk.view.vigilancia.externo.view.components.feedback.IFeedBackVigilancia;
import static br.com.celk.view.vigilancia.externo.view.login.VigilanciaLoginPage.TIPO_CPF;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroUsuarioVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.StatelessForm;
import org.apache.wicket.markup.html.link.StatelessLink;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.Url;
import org.apache.wicket.request.cycle.RequestCycle;

/**
 * <AUTHOR>
 */
public class VigilanciaEsqueceuSenhaPage extends BasePageVigilanciaExterno implements IFeedBackVigilancia {

    private InputField txtCpf;
    private InputField txtCnpj;
    private FeedBackVigilancia feedBackVigilancia;
    private WebMarkupContainer containerForm;
    private WebMarkupContainer containerBtn;
    private Label label;
    private String cpf;
    private String cnpj;
    private RadioButtonGroup radioGroupTipoLogin;
    public static final Long TIPO_CPF = 0L;
    public static final Long TIPO_CNPJ = 1L;
    private Long tipoLogin;

    @Override
    protected void onInitialize() {
        super.onInitialize();

        StatelessForm form = null;
        setStatelessHint(true);

        form = new StatelessForm<CadastroUsuarioVigilanciaDTO>("form") {
            @Override
            protected void onSubmit() {
                enviar();
            }
        };
        form.setModel(new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);


        feedBackVigilancia = new FeedBackVigilancia("feedBack");
        form.add(feedBackVigilancia);
        feedBackVigilancia.setOutputMarkupId(true);
        feedBackVigilancia.setEscapeModelStrings(false);

        label = new Label("label", Model.of(BundleManager.getString("msgInformeCpfCnpjRedefnirSenha")));
        txtCpf = new InputField("cpf");
        txtCnpj = new InputField("cnpj");
        containerForm = new WebMarkupContainer("containerForm");
        containerForm.add(label);
        containerForm.add(txtCpf);
        containerForm.add(txtCnpj);
        
        txtCpf.setOutputMarkupPlaceholderTag(true);
        txtCnpj.setOutputMarkupPlaceholderTag(true);
        
        txtCnpj.setVisible(false);
        tipoLogin = TIPO_CPF;

        containerForm.add(radioGroupTipoLogin = new RadioButtonGroup("tipoLogin"));

        radioGroupTipoLogin.add(new AjaxRadio("cpfItem", new Model(TIPO_CPF)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCampoLogin(target, tipoLogin);
            }
        });
        radioGroupTipoLogin.add(new AjaxRadio("cnpjItem", new Model(TIPO_CNPJ)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                habilitarCampoLogin(target, tipoLogin);
            }
        });
        
        containerForm.add(new StatelessLink("btnVoltar") {
            @Override
            public void onClick() {
                setResponsePage(VigilanciaLoginPage.class);
            }
        });

        containerBtn = new WebMarkupContainer("containerBtn");
        containerBtn.setVisible(false);
        containerBtn.add(new StatelessLink("btnPaginaInicial") {
            @Override
            public void onClick(){
                setResponsePage(VigilanciaLoginPage.class);
            }
        });


        form.add(containerBtn);
        form.add(containerForm);

        add(form);
    }

    private void habilitarCampoLogin(AjaxRequestTarget target, Long tipoLogin){
        if(TIPO_CPF.equals(tipoLogin)){
            txtCpf.setVisible(true);
            txtCnpj.setVisible(false);
        }else{
            txtCpf.setVisible(false);
            txtCnpj.setVisible(true);
        }
        target.add(txtCpf);
        target.add(txtCnpj);
        target.appendJavaScript(JScript.initMasks());
    }    
    
    private void enviar() {
        try {
            String url = RequestCycle.get().getUrlRenderer().renderFullUrl(Url.parse(urlFor(VigilanciaNovaSenhaPage.class, null).toString()));

            String email = null;
            if(TIPO_CPF.equals(tipoLogin)){
                email = BOFactoryWicket.getBO(VigilanciaFacade.class).redefinirSenhaVigilancia(url, cpf);
            }else{
                email = BOFactoryWicket.getBO(VigilanciaFacade.class).redefinirSenhaVigilancia(url, cnpj);
            }

            success(BundleManager.getString("enviadoEmailrecuperacaoSenha", email));
            // Limpa os campos após submissão para evitar exposição de dados
            limparCamposFormulario();
            containerForm.setVisible(false);
            containerBtn.setVisible(true);
        } catch (ValidacaoException ex) {
            error(ex.getMessage());
        } catch (DAOException e) {
            Loggable.vigilancia.error("Erro ao processar recuperação de senha", e);
            // Usa mensagem genérica para não revelar informações sobre usuários
            error("Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha.");
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("recuperacaoSenha");
    }

    @Override
    public String getSufixoVigilancia() {
        return "";
    }

    @Override
    public FeedBackVigilancia getFeedBackVigilancia() {
        return feedBackVigilancia;
    }

    /**
     * Limpa os campos do formulário após submissão para evitar
     * exposição de dados sensíveis
     */
    private void limparCamposFormulario() {
        this.cpf = null;
        this.cnpj = null;
    }
}
