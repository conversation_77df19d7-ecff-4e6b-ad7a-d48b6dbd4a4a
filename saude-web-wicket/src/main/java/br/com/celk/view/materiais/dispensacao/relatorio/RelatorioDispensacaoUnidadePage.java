package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProdutoMulti;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioDispensacaoUnidadeDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 * Programa - 181
 */
@Private
public class RelatorioDispensacaoUnidadePage extends RelatorioPage<RelatorioDispensacaoUnidadeDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<String> dropDownTipoPreco;
    private DropDown<String> dropDownFormaApresentacao;
    private DropDown<String> dropDownOrdenacao;
    private DropDown<String> dropDownTipoOrdenacao;
    private AutoCompleteConsultaProdutoMulti autoCompleteConsultaProdutoMulti;
    private DropDown<TipoReceita> dropDownTipoReceita;

    @Override
    public void init(Form form) {
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("unidadesDispensadoras"));
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);

        form.add(autoCompleteConsultaProdutoMulti = new AutoCompleteConsultaProdutoMulti("produtos"));
        autoCompleteConsultaProdutoMulti.setIncluirInativo(true);

        form.add(new AutoCompleteConsultaEmpresa("unidadesOrigem"));
        form.add(new AutoCompleteConsultaProfissional("profissionalDispensador"));
        form.add(new AutoCompleteConsultaProfissional("profissionalPrescritor"));
        form.add(new AutoCompleteConsultaUsuarioCadsus("usuariosCadsus"));
        form.add(new RequiredPnlDatePeriod("period").setLabel(new Model<String>(bundle("periodo"))));
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownTipoPreco());
        form.add(getOrdenacao());
        form.add(getTipoOrdenacao());
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));
        form.add(DropDownUtil.getNaoSimLongDropDown("listarDispensacoesProcedSelecionados"));
        form.add(getDropDownTipoReceita());
    }

    public DropDown<String> getTipoOrdenacao() {
        if (dropDownTipoOrdenacao == null) {
            dropDownTipoOrdenacao = new DropDown<String>("tipoOrdenacao");

            dropDownTipoOrdenacao.addChoice(QuerySorter.DECRESCENTE, BundleManager.getString("decrescente"));
            dropDownTipoOrdenacao.addChoice(QuerySorter.CRESCENTE, BundleManager.getString("crescente"));

        }
        return dropDownTipoOrdenacao;
    }

    public DropDown<String> getOrdenacao() {
        if (dropDownOrdenacao == null) {
            dropDownOrdenacao = new DropDown<String>("ordenacao");
            this.dropDownOrdenacao.addChoice(BundleManager.getString("quantidade"), BundleManager.getString("quantidade"));
            this.dropDownOrdenacao.addChoice(BundleManager.getString("valorTotal"), BundleManager.getString("valorTotal"));
            this.dropDownOrdenacao.addChoice(BundleManager.getString("numeroDispensacoesAbv"), BundleManager.getString("numeroDispensacoesAbv"));

        }
        return dropDownOrdenacao;
    }

    public DropDown<String> getDropDownFormaApresentacao() {
        if (dropDownFormaApresentacao == null) {
            dropDownFormaApresentacao = new DropDown<String>("formaApresentacao");
            dropDownFormaApresentacao.addChoice(BundleManager.getString("unidadeDispensadora"), BundleManager.getString("unidadeDispensadora"));
            dropDownFormaApresentacao.addChoice(BundleManager.getString("unidadeOrigem2"), BundleManager.getString("unidadeOrigem"));
            dropDownFormaApresentacao.addChoice(BundleManager.getString("profissional"), BundleManager.getString("profissional"));
            dropDownFormaApresentacao.addChoice(BundleManager.getString("produto"), BundleManager.getString("produto"));
            dropDownFormaApresentacao.addChoice(BundleManager.getString("usuarioCadsus"), BundleManager.getString("usuarioCadsus"));
            dropDownFormaApresentacao.addChoice(BundleManager.getString("pacienteProduto"), BundleManager.getString("pacienteProduto"));

        }
        return dropDownFormaApresentacao;
    }

    public DropDown<String> getDropDownTipoPreco() {
        if (dropDownTipoPreco == null) {
            dropDownTipoPreco = new DropDown<String>("tipoPreco");
            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_PRECO_MEDIO, BundleManager.getString("precoMedio"));
            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_ULTIMO_PRECO, BundleManager.getString("ultimoPreco"));

        }
        return dropDownTipoPreco;
    }

    public DropDown<TipoReceita> getDropDownTipoReceita() {
        if (dropDownTipoReceita == null) {
            dropDownTipoReceita = new DropDown<TipoReceita>("tipoReceita");
            dropDownTipoReceita.addChoice(null, BundleManager.getString("todas"));

            List<TipoReceita> tiposReceita = LoadManager.getInstance(TipoReceita.class)
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(TipoReceita.PROP_DESCRICAO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(tiposReceita)) {
                for (TipoReceita tr : tiposReceita) {
                    dropDownTipoReceita.addChoice(tr, tr.getDescricao());
                }
            }
        }
        return dropDownTipoReceita;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioDispensacaoUnidadeDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioDispensacaoUnidadeDTOParam param) throws ReportException {
        param.setPossuiImpressaoXls(true);
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioDispensacaoUnidade(param, true);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoDispensacoes");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
