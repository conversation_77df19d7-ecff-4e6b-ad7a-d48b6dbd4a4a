package br.com.celk.view.consorcio.consorcioguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.atendimento.procedimentogrupo.pnl.PnlConsultaProcedimentoGrupo;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.consorcioprocedimento.autocomplete.AutoCompleteConsorcioProcedimento;
import br.com.celk.view.consorcio.procedimento.autocomplete.AutoCompleteConsultaConsorcioGrupo;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoProcedimentosDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

import java.util.Arrays;
import java.util.List;


/**
 *
 * <AUTHOR>
 * Programa - 129
 */
@Private

public class RelatorioResumoProcedimentosPage extends RelatorioPage<RelatorioResumoProcedimentosDTOParam> {
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private PnlConsultaProcedimentoGrupo pnlConsultaProcedimentoGrupo;
    private DropDown<ProcedimentoSubGrupo> cbxProcedimentoSubGrupo;
    private DropDown<ProcedimentoFormaOrganizacao> cbxProcedimentoFormaOrganizacao;
    private String procedimentoGrupo;
    private String procedimentoSubGrupo;
    private String procedimentoFormaOrganizacao;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaConsorcioGrupo("consorcioGrupo"));
        form.add(new AutoCompleteConsorcioProcedimento("consorcioProcedimento"));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaCidade("cidade"));
        form.add(new AutoCompleteConsultaTipoConta("tipoConta"));

//        form.add(new AutoCompleteConsultaUsuarioCadsus("paciente"));

        form.add(pnlConsultaProcedimentoGrupo = new PnlConsultaProcedimentoGrupo("procedimentoGrupo", false));
        form.add(cbxProcedimentoSubGrupo = new DropDown("procedimentoSubGrupo"));
        form.add(cbxProcedimentoFormaOrganizacao = new DropDown("procedimentoFormaOrganizacao"));

        form.add(DropDownUtil.getEnumDropDown("tipoData", RelatorioResumoProcedimentosDTOParam.TipoData.values()));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(new CheckBox("situacaoAberta"));
        form.add(new CheckBox("situacaoCancelada"));
        form.add(new CheckBox("situacaoPaga"));
        form.add(new CheckBox("situacaoUtilizada"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioResumoProcedimentosDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoResumo", RelatorioResumoProcedimentosDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioResumoProcedimentosDTOParam.Ordenacao.values()));

        pnlConsultaProcedimentoGrupo.add(new ConsultaListener<ProcedimentoGrupo>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoGrupo object) {
                if (pnlConsultaProcedimentoGrupo.getComponentValue() == null){
                    cbxProcedimentoFormaOrganizacao.limpar(target);
                    cbxProcedimentoFormaOrganizacao.removeAllChoices();
                    cbxProcedimentoSubGrupo.limpar(target);
                    cbxProcedimentoSubGrupo.removeAllChoices();

                }
                eventoProcedimentoGrupo(target, object);
            }
        });
        pnlConsultaProcedimentoGrupo.setLabel(Model.of(BundleManager.getString("grupo")));
        cbxProcedimentoSubGrupo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                eventoProcedimentoSubGrupo(target, cbxProcedimentoSubGrupo.getModelObject());
            }
        });

    }

    private void eventoProcedimentoSubGrupo(AjaxRequestTarget target, ProcedimentoSubGrupo object) {
        if (target != null) {
            cbxProcedimentoFormaOrganizacao.limpar(target);
            cbxProcedimentoFormaOrganizacao.removeAllChoices();
        }
        if (object != null) {
            cbxProcedimentoFormaOrganizacao.addChoice(null, BundleManager.getString("todos"));
            List<ProcedimentoFormaOrganizacao> formaOrganizacoes = LoadManager.getInstance(ProcedimentoFormaOrganizacao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoFormaOrganizacao.PROP_ID, ProcedimentoFormaOrganizacaoPK.PROP_CODIGO_PROCEDIMENTO_GRUPO), object.getId().getCodigoGrupo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoFormaOrganizacao.PROP_ID, ProcedimentoFormaOrganizacaoPK.PROP_CODIGO_PROCEDIMENTO_SUB_GRUPO), object.getId().getCodigo()))
                    .start().getList();
            for (ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao1 : formaOrganizacoes) {
                cbxProcedimentoFormaOrganizacao.addChoice(procedimentoFormaOrganizacao1, procedimentoFormaOrganizacao1.getDescricaoFormatado());
            }
        }
    }

    private void eventoProcedimentoGrupo(AjaxRequestTarget target, ProcedimentoGrupo object) {
        if (target != null) {
            cbxProcedimentoSubGrupo.limpar(target);
            cbxProcedimentoSubGrupo.removeAllChoices();
        }

        if (object != null) {
            cbxProcedimentoSubGrupo.addChoice(null, BundleManager.getString("todos"));
            List<ProcedimentoSubGrupo> subGrupos = LoadManager.getInstance(ProcedimentoSubGrupo.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoSubGrupo.PROP_ID, ProcedimentoSubGrupoPK.PROP_CODIGO_GRUPO), object.getCodigo()))
                    .start().getList();
            for (ProcedimentoSubGrupo procedimentoSubGrupo1 : subGrupos) {
                cbxProcedimentoSubGrupo.addChoice(procedimentoSubGrupo1, procedimentoSubGrupo1.getDescricaoFormatado());
            }
        }
    }

    @Override
    public Class<RelatorioResumoProcedimentosDTOParam> getDTOParamClass() {
        return RelatorioResumoProcedimentosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoProcedimentosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioResumoProcedimentos(param);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoProcedimentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa;
    }

}
