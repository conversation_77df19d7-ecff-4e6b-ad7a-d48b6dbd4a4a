package br.com.celk.view.vigilancia.grupoestabelecimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.grupoestabelecimento.customize.CustomizeConsultaGrupoEstabelecimento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Programa - 219
 */
@Private
public class ConsultaGrupoEstabelecimentoPage extends ConsultaPage<GrupoEstabelecimento, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;

    public ConsultaGrupoEstabelecimentoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(GrupoEstabelecimento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(GrupoEstabelecimento.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("classificacao"), VOUtils.montarPath(GrupoEstabelecimento.PROP_CLASSIFICACAO, ClassificacaoGrupoEstabelecimento.PROP_DESCRICAO)));

        return columns;
    }

    private CustomColumn<GrupoEstabelecimento> getCustomColumn() {
        return new CustomColumn<GrupoEstabelecimento>() {

            @Override
            public Component getComponent(String componentId, final GrupoEstabelecimento rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroGrupoEstabelecimentoPage(rowObject, false, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public boolean isConsultarVisible() {
                        return false;
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaGrupoEstabelecimento()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(GrupoEstabelecimento.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstabelecimento.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroGrupoEstabelecimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaGrupoEstabelecimento");
    }
}