package br.com.celk.view.materiais.bnafar.estoque;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.table.selection.DTOSelection;
import br.com.celk.component.table.selection.MultiSelectionPageableTable;
import br.com.celk.materiais.bnafar.consultaIntegracao.estoque.ConsultaIntegracaoBnafarPosEstoqueDTO;
import br.com.celk.materiais.bnafar.consultaIntegracao.estoque.ConsultaIntegracaoBnafarPosEstoqueDTOParam;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.dao.paginacao.DataPagingResultImpl;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoque;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Marques
 * Programa - 1094
 */
@Private
public class ConsultaBnafarPosEstoquePage extends ConsultaPage<ConsultaIntegracaoBnafarPosEstoqueDTO, ConsultaIntegracaoBnafarPosEstoqueDTOParam> {

    private Produto produto;
    private DatePeriod dataPosEstoque;
    private Long statusRegistro;
    private Long lote;

    private AbstractAjaxButton reenviar;

    private DlgConfirmacaoSimNao dlgMensagemConfirmacao;

    public ConsultaBnafarPosEstoquePage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.add(new AutoCompleteConsultaProduto("produto", new PropertyModel(this, "produto")));
        form.add(getEnumStatusRegistros());
        form.add(new PnlDatePeriod("dataPosEstoque", new PropertyModel(this, "dataPosEstoque")));
        form.add(new InputField<Long>("lote", new PropertyModel<Long>(this, "lote")));

        getControls().add(reenviar = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                reprocessarItensSelecionados(getIntegracoesSelecionadas());
                getPageableTable().update(target);
            }
        });
        reenviar.add(new AttributeModifier("class", "btn-orange"));
        reenviar.add(new AttributeModifier("value", bundle("reenviar")));


        AbstractAjaxButton reenviarTodosComErro = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmarReenvio(target);
                getPageableTable().update(target);
            }
        };
        reenviarTodosComErro.add(new AttributeModifier("class", "btn-blue"));
        reenviarTodosComErro.add(new AttributeModifier("value", bundle("reenviarTodosComErro")));
        reenviarTodosComErro.add(new AttributeModifier("style", "margin-left: 5px;"));
        getControls().add(reenviarTodosComErro);
        getLinkNovo().setVisible(false);
        add(form);
    }

    private void confirmarReenvio(AjaxRequestTarget target) throws ValidacaoException {
        if (isProcessando()) {
            throw new ValidacaoException("Já existe um processo de reenvio de erros em execução. Aguarde a conclusão para novo reenvio.");
        }
        if (dlgMensagemConfirmacao == null) {
            addModal(target, dlgMensagemConfirmacao = new DlgConfirmacaoSimNao(newModalId(), bundle("msgConfirmacaoReenvioErrosBnafarX", BnafarHelper.TipoSincronizacao.SAIDA.descricao())) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    reprocessarTodosItensComErro(target);
                }
            });
        }
        dlgMensagemConfirmacao.show(target);
    }

    private void reprocessarTodosItensComErro(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        info(target, "Processamento iniciado! Em alguns instantes você receberá uma mensagem com mais informações.");
        BOFactory.getBO(MaterialBasicoFacade.class).processarReenvioErrosBnafar(BnafarHelper.TipoSincronizacao.POSICAO_ESTOQUE);
    }

    private boolean isProcessando() {
        return LoadManager.getInstance(AsyncProcess.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AsyncProcess.STATUS_AGUARDANDO_PROCESSAMENTO, AsyncProcess.STATUS_PROCESSANDO)))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_TIPO, AsyncProcess.TIPO_PROCESSO))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_DATA_REGISTRO, BuilderQueryCustom.QueryParameter.MAIOR, Data.removeDias(DataUtil.getDataAtual(), 1)))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_NOME_PROCESSO, BnafarHelper.getDescricaoProcessoAssincrono(BnafarHelper.TipoSincronizacao.POSICAO_ESTOQUE)))
                .start().exists();
    }

    private DropDown getEnumStatusRegistros() {

        DropDown dropDown = new DropDown("statusRegistro", new PropertyModel(this, "statusRegistro"));

        dropDown.addChoice(null, BundleManager.getString("todos"));
        dropDown.addChoice(BnafarHelper.StatusRegistro.ENVIADO.value(), BnafarHelper.StatusRegistro.ENVIADO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.PROCESSADO.value(), BnafarHelper.StatusRegistro.PROCESSADO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.ERRO.value(), BnafarHelper.StatusRegistro.ERRO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.ERRO_SEM_REENVIO.value(), BnafarHelper.StatusRegistro.ERRO_SEM_REENVIO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.CANCELADO.value(), BnafarHelper.StatusRegistro.CANCELADO.descricao());

        return dropDown;
    }

    @Override
    public List getColumns(List<IColumn> columns) {

        ConsultaIntegracaoBnafarPosEstoqueDTO proxy = on(ConsultaIntegracaoBnafarPosEstoqueDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(BundleManager.getString("competencia"), proxy.getDataPosEstoque()));
        columns.add(createColumn(BundleManager.getString("produto"), proxy.getProduto().getDescricao()));
        columns.add(createColumn(BundleManager.getString("lote"), proxy.getBnafarPosEstoqueElo().getBnafarPosEstoqueIntegracao().getCodigo()));
        columns.add(createColumn(BundleManager.getString("ultimoEnvio"), proxy.getBnafarPosEstoque().getDataUltimoEnvio()));
        columns.add(createColumn(BundleManager.getString("situacao"), proxy.getDescricaoSituacao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ConsultaIntegracaoBnafarPosEstoqueDTO>() {
            @Override
            public void customizeColumn(ConsultaIntegracaoBnafarPosEstoqueDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarPosEstoqueDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarPosEstoqueDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesBnafarPosEstoquePage(modelObject.getBnafarPosEstoque(), true));
                    }
                });
                addAction(ActionType.REENVIAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarPosEstoqueDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarPosEstoqueDTO modelObject) throws ValidacaoException, DAOException {
                        ArrayList<BnafarPosEstoque> arr = new ArrayList<>();
                        arr.add(modelObject.getBnafarPosEstoque());
                        BOFactory.getBO(MaterialBasicoFacade.class).reenviarBnafarPosEstoque(arr);
                        getPageableTable().update(target);
                    }
                }).setVisible(rowObject.getBnafarPosEstoque().getStatusRegistro().equals(BnafarHelper.StatusRegistro.ERRO.value()));
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<ConsultaIntegracaoBnafarPosEstoqueDTO, ConsultaIntegracaoBnafarPosEstoqueDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaIntegracaoBnafarPosEstoqueDTOParam> dataPaging) throws DAOException, ValidacaoException {
                DataPagingResult<ConsultaIntegracaoBnafarPosEstoqueDTO> result = BOFactoryWicket.getBO(MaterialBasicoFacade.class).queryConsultaIntegracaoBnafarPosicaoEstoque(dataPaging);
                return getDataPagingSelectedDTO(result);
            }
        };
    }

    static private DataPagingResult getDataPagingSelectedDTO(DataPagingResult<ConsultaIntegracaoBnafarPosEstoqueDTO> result) {
        List<ConsultaIntegracaoBnafarPosEstoqueDTO> resultList = result.getList();
        List<DTOSelection<ConsultaIntegracaoBnafarPosEstoqueDTO>> resultSelectList = new ArrayList<>();
        if (resultList != null) {
            for (ConsultaIntegracaoBnafarPosEstoqueDTO dto : resultList) {
                resultSelectList.add(new DTOSelection<>(false, dto));
            }
        }
        DataPagingResult<DTOSelection<ConsultaIntegracaoBnafarPosEstoqueDTO>> resultSelection = new DataPagingResultImpl<>(resultSelectList, result.getAmountResults());
        return resultSelection;
    }

    private List<ConsultaIntegracaoBnafarPosEstoqueDTO> getIntegracoesSelecionadas() {
        return ((MultiSelectionPageableTable) getPageableTable()).getSelectedObjects();
    }

    @Override
    public ConsultaIntegracaoBnafarPosEstoqueDTOParam getParameters() {

        ConsultaIntegracaoBnafarPosEstoqueDTOParam param = new ConsultaIntegracaoBnafarPosEstoqueDTOParam();

        param.setProduto(produto);
        param.setDataPosEstoque(dataPosEstoque);
        param.setStatusRegistro(statusRegistro);
        param.setLote(lote);

        return param;
    }

    @Override
    public PageableTable newPageableTable(String tableId, List<IColumn> columns, IPagerProvider pagerProvider, int rowsPerPage) {
        return new MultiSelectionPageableTable(tableId, columns, pagerProvider);
    }

    private void reprocessarItensSelecionados(List<ConsultaIntegracaoBnafarPosEstoqueDTO> bnafarPosEstoqueList) throws DAOException, ValidacaoException {
        if (CollectionUtils.isEmpty(bnafarPosEstoqueList)) return;

        ArrayList<BnafarPosEstoque> bnafarPosEstoques = new ArrayList<>();
        for (ConsultaIntegracaoBnafarPosEstoqueDTO modelObject : bnafarPosEstoqueList) {
            if (modelObject.getBnafarPosEstoqueElo() != null && modelObject.getBnafarPosEstoqueElo().getBnafarPosEstoqueIntegracao() != null) {
                bnafarPosEstoques.add(modelObject.getBnafarPosEstoque());
            }
        }
        if (CollectionUtils.isEmpty(bnafarPosEstoques)) return;

        BOFactory.getBO(MaterialBasicoFacade.class).reenviarBnafarPosEstoque(bnafarPosEstoques);
    }

    @Override
    public Class getCadastroPage() {
        return this.getClass();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consulta_bnafar_pos_estoque");
    }
}