package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AjaxReportLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.unidade.autocomplete.AutoCompleteConsultaUnidade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.ImpressaoEtiquetaEmbarqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.ImpressaoEtiquetaVolumeDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoEntrega;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 1032
 */
@Private
public class ImpressaoEtiquetaEmbarquePage extends BasePage {

    private Form form;

    private Empresa unidade;
    private AutoCompleteConsultaEmpresa txtUnidade;

    private Long quantidadeRotulos;
    private LongField txtQuantidadeRotulos;

    private String numeroPedido;
    private InputField txtNumeroPedido;

    @Override
    protected void postConstruct() {
        super.postConstruct();

        form = new Form("form", new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(txtUnidade = new AutoCompleteConsultaEmpresa("unidade"));

        form.add(txtNumeroPedido = new InputField("numeroPedido"));
        txtNumeroPedido.addAjaxUpdateValue();
        form.add(txtQuantidadeRotulos = new LongField("quantidadeRotulos"));
        txtQuantidadeRotulos.setVMax(999L);
        txtQuantidadeRotulos.addAjaxUpdateValue();

        form.add(new AjaxReportLink("btnImprimir") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                if (numeroPedido == null || numeroPedido.trim().isEmpty()) {
                    throw new ValidacaoException(BundleManager.getString("msgInformeNumeroPedido"));
                }

                if (quantidadeRotulos == null) {
                    throw new ValidacaoException(BundleManager.getString("informeQuantidade"));
                }

                Empresa empresa = LoadManager.getInstance(Empresa.class)
                        .setId(unidade.getCodigo())
                        .start().getVO();

                ImpressaoEtiquetaEmbarqueDTOParam param = new ImpressaoEtiquetaEmbarqueDTOParam();
                param.setQuantidadeEtiquetas(quantidadeRotulos);
                param.setNumeroPedido(numeroPedido);
                param.setEmpresa(unidade);

                return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioImpressaoEtiquetaEmbarque(param);
            }
        });

        add(form);

    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("impressaoEtiquetasEmbarque");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtNumeroPedido;
    }

}
