package br.com.celk.view.vigilancia.cva.relatorios;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.report.vigilancia.cva.dto.RelatorioRelacaoMicrochipagemDTOParam;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaEstado;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaPais;
import br.com.celk.view.vigilancia.cva.autocomplete.AutoCompleteConsultaEspecieAnimal;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.cva.animal.CvaAnimal;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 606
 */
@Private
public class RelatorioRelacaoMicrochipagemPage extends RelatorioPage<RelatorioRelacaoMicrochipagemDTOParam> {

    @Override
    public void init(Form<RelatorioRelacaoMicrochipagemDTOParam> form) {
        RelatorioRelacaoMicrochipagemDTOParam proxy = on(RelatorioRelacaoMicrochipagemDTOParam.class);

        form.add(new AutoCompleteConsultaPais(path(proxy.getPais())));
        form.add(new AutoCompleteConsultaEstado(path(proxy.getEstado())));
        form.add(new AutoCompleteConsultaCidade(path(proxy.getCidade())));
        form.add(new InputField(path(proxy.getBairro())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), CvaAnimal.Status.values(), true));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipo()), CvaAnimal.TipoAnimal.values(), true));
        form.add(new AutoCompleteConsultaEspecieAnimal(path(proxy.getEspecie())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSexo()), RepositoryComponentDefault.SexoAnimal.values(), true, bundle("ambos")));
        form.add(DropDownUtil.getSimNaoDropDown(path(proxy.getMicrochipagem()), true, bundle("ambos")));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioRelacaoMicrochipagemDTOParam.FormaApresentacao.values()));
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoAnimaisCadastrados");
    }

    @Override
    public Class<RelatorioRelacaoMicrochipagemDTOParam> getDTOParamClass() {
        return RelatorioRelacaoMicrochipagemDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoMicrochipagemDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relacaoMicrochipagem(param);
    }

}
