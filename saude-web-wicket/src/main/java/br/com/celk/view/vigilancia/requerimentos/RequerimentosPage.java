package br.com.celk.view.vigilancia.requerimentos;

import br.com.celk.component.behavior.AjaxTimerDelayComponentBehavior;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.resources.Resources;
import br.com.celk.system.Application;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.login.NodeButtonEventListener;
import br.com.celk.view.vigilancia.alvara.DlgEscolherTipoAlvara;
import br.com.celk.view.vigilancia.externo.util.sessao.VigilanciaLoginHelper;
import br.com.celk.view.vigilancia.externo.view.home.dialog.DlgDocumentosNecessarios;
import br.com.celk.view.vigilancia.externo.view.recursos.VigilanciaRecursoPage;
import br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoProrrogacaoPrazoRecursoExternoPage;
import br.com.celk.view.vigilancia.processoadministrativo.DlgInformarChaveProcesso;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.RequerimentoAnaliseProjetosPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.RequerimentoVistoriaProjetoBasicoArquiteturaPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetoArquitetonicoSanitario.RequerimentoProjetoArquitetonicoSanitarioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario.RequerimentoHabitesePadraoPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario.RequerimentoProjetoHidrossanitarioPadraoPage;
import br.com.celk.view.vigilancia.requerimentos.autorizacaosanitaria.RequerimentoAutorizacaoSanitariaPage;
import br.com.celk.view.vigilancia.requerimentos.baixaveiculo.RequerimentoBaixaVeiculoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoEnderecoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoRazaoSocialPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAtividadeEconomicaPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoRepresentanteLegalPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.dialog.DlgEscolherTipoContratoSocial;
import br.com.celk.view.vigilancia.requerimentos.declaracaoveracidade.RequerimentoDeclaracaoVeracidadePage;
import br.com.celk.view.vigilancia.requerimentos.dialog.DlgEscolherTipoDeclaracaoVisa;
import br.com.celk.view.vigilancia.requerimentos.dialog.DlgEscolherTipoInspecaoSanitaria;
import br.com.celk.view.vigilancia.requerimentos.dialog.DlgEscolherTipoProjeto;
import br.com.celk.view.vigilancia.requerimentos.dialog.DlgSelecionarLivroControleVigilancia;
import br.com.celk.view.vigilancia.requerimentos.inspecaosanitaria.RequerimentoInspecaoSanitariaPage;
import br.com.celk.view.vigilancia.requerimentos.licencatransporte.RequerimentoLicencaTransportePage;
import br.com.celk.view.vigilancia.requerimentos.receituario.RequerimentoReceitaAPage;
import br.com.celk.view.vigilancia.requerimentos.receituario.RequerimentoReceitaTalidomidaPage;
import br.com.celk.view.vigilancia.requerimentos.receituario.dialog.DlgEscolherTipoReceituario;
import br.com.celk.view.vigilancia.requerimentovigilancia.ConsultaRequerimentoVigilanciaFiscalPage;
import br.com.celk.view.vigilancia.requerimentovigilancia.ConsultaRequerimentoVigilanciaPage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.DlgEscolherTipoResponsabilidadeTecnica;
import br.com.celk.view.vigilancia.responsabilidadetecnica.baixa.RequerimentoBaixaResponsabilidadeTecnicaPage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.inclusao.RequerimentoInclusaoResponsabilidadePage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.nadaconsta.RequerimentoCertidaoNadaConstaPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.AcessoRecursoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao.TipoRequerimento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.ResourceReference;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Created by maicon on 17/05/16.
 * Programa - 684
 */
public class RequerimentosPage extends BasePage {

    private Form<RequerimentoVigilancia> form;
    private Form containerListView;
    private TipoSolicitacao tipoSolicitacaoSelecionado;
    private ListView<ListView<TipoSolicitacao>> listView;

    private List<TipoSolicitacao> tipoSolicitacaoPermitedList = new ArrayList<TipoSolicitacao>();
    private List<TipoSolicitacao> tipoSolicitacaoList = new ArrayList<TipoSolicitacao>();

    private InputField txtBuscaRequerimento;
    private InputField txtBuscaProtocolo;
    private String buscaRequerimento;
    private String buscaProtocolo;
    private List<NodeButtonEventListener> nodeButtonListeners = new ArrayList<NodeButtonEventListener>();
    private AjaxTimerDelayComponentBehavior ajaxTimerDelayComponentBehavior;

    private DlgEscolherTipoAlvara dlgEscolherTipoAlvara;
    private DlgEscolherTipoInspecaoSanitaria dlgEscolherTipoInspecaoSanitaria;
    private DlgEscolherTipoDeclaracaoVisa dlgEscolherTipoDeclaracaoVisa;
    private DlgEscolherTipoResponsabilidadeTecnica dlgEscolherTipoResponsabilidadeTecnica;
    private DlgEscolherTipoContratoSocial dlgEscolherTipoContratoSocial;
    private DlgSelecionarLivroControleVigilancia dlgSelecionarLivroControleVigilancia;
    private DlgEscolherTipoReceituario dlgEscolherTipoReceituario;
    private Class clazz;

    private DlgDocumentosNecessarios dlgDocumentosNecessarios;
    private AbstractAjaxLink btnDocumentosNecessarios;
    private DlgInformarChaveProcesso dlgInformarChave;
    private DlgEscolherTipoProjeto dlgEscolherTipoProjeto;
    private ConfiguracaoVigilancia configuracaoVigilancia = null;

    public RequerimentosPage() {
    }

    public RequerimentosPage(Class clazz) {
        this.clazz = clazz;
    }

    @Override
    protected void postConstruct() {
        super.postConstruct();
        try {
            this.configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        if (clazz == null) {
            clazz = RequerimentosPage.class;
        }
        buscarTipoSolicitacao();
        form = new Form("form", new CompoundPropertyModel(new RequerimentoVigilancia()));
        form.add(txtBuscaRequerimento = new InputField("buscaRequerimento", new PropertyModel(this, "buscaRequerimento")));
        form.add(txtBuscaProtocolo = new InputField("buscaProtocolo", new PropertyModel(this, "buscaProtocolo")));
        txtBuscaProtocolo.addAjaxUpdateValue();
        form.add(new AbstractAjaxLink("ico") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                carregarRequerimentoVigilancia(target);
            }
        });
        form.add(new AbstractAjaxButton("abrirConsultaPage") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                direcionarConsultaPage();
            }
        });

        form.add(btnDocumentosNecessarios = new AbstractAjaxLink("btnDocumentosNecessarios") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgDocumentosNecessarios(target);
            }
        });

        txtBuscaRequerimento.addAjaxUpdateValue();
        ajaxTimerDelayComponentBehavior = new AjaxTimerDelayComponentBehavior() {
            @Override
            public void onAction(AjaxRequestTarget target, String value) {
                buscarRequerimento(target, value);
            }
        };

        txtBuscaRequerimento.add(ajaxTimerDelayComponentBehavior);
        listView = getListView();
        if (configuracaoVigilancia == null) {
            listView.getList().clear();
        }
        listView.setOutputMarkupId(true);
        containerListView = new Form("containerListView");
        containerListView.setOutputMarkupId(true);
        containerListView.add(listView);
        form.add(containerListView);

        WebMarkupContainer panelMsg = new WebMarkupContainer("panelMsg");
        String msg = "";
        if (CollectionUtils.isAllEmpty(listView.getList())) {
            if (configuracaoVigilancia == null) {
                msg = "msgVerifiqueconfigVigilancia";
            } else {
                msg = "msgNenhumTipoSolicitacaoAtivoCadastrado";
            }
            Label lblMsg = new Label("msg", BundleManager.getString(msg));
            panelMsg.add(lblMsg);
            panelMsg.setVisible(true);
        } else {
            panelMsg.setVisible(false);
        }

        btnDocumentosNecessarios.setVisible(isActionPermitted(Permissions.VISUALIZAR_DOCUMENTOS_NECESSARIOS));

        form.add(panelMsg);
        add(form);
    }

    private void direcionarConsultaPage() throws ValidacaoException {
        if (clazz != null) {
            try {
                Page page = (Page) clazz.newInstance();
                if (page instanceof ConsultaRequerimentoVigilanciaFiscalPage) {
                    if (new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaRequerimentoVigilanciaFiscalPage.class.getName())) {
                        setResponsePage(page);
                    }
                } else {
                    if (new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaRequerimentoVigilanciaPage.class.getName())) {
                        setResponsePage(new ConsultaRequerimentoVigilanciaPage());
                    }
                    if (new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaRequerimentoVigilanciaFiscalPage.class.getName())) {
                        setResponsePage(new ConsultaRequerimentoVigilanciaFiscalPage());
                    } else {
                        throw new ValidacaoException("Sem permissão de acesso ao Programa");
                    }
                }
            } catch (InstantiationException e) {
                Loggable.log.error(e.getMessage());
            } catch (IllegalAccessException e) {
                Loggable.log.error(e.getMessage());
            }
        } else {
            if (new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaRequerimentoVigilanciaPage.class.getName())) {
                setResponsePage(new ConsultaRequerimentoVigilanciaPage());
            } else {
                throw new ValidacaoException("Sem permissão de acesso ao Programa");
            }
        }
    }

    private void dlgDocumentosNecessarios(AjaxRequestTarget target) {
        if (dlgDocumentosNecessarios == null) {
            addModal(target, dlgDocumentosNecessarios = new DlgDocumentosNecessarios(newModalId()) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        dlgDocumentosNecessarios.show(target, tipoSolicitacaoPermitedList);
    }

    private ListView<ListView<TipoSolicitacao>> getListView() {
        LoadableDetachableModel model = new LoadableDetachableModel<List<List<TipoSolicitacao>>>() {
            @Override
            protected List<List<TipoSolicitacao>> load() {
                List<List<TipoSolicitacao>> matrizTipoSolicitacao = new ArrayList<List<TipoSolicitacao>>();
                List<TipoSolicitacao> colunaTipoSolicitacao;

                for (Iterator<TipoSolicitacao> iterator = tipoSolicitacaoList.iterator(); iterator.hasNext(); ) {
                    colunaTipoSolicitacao = new ArrayList<TipoSolicitacao>();
                    for (int i = 0; i < 3; i++) {
                        if (iterator.hasNext()) {
                            colunaTipoSolicitacao.add(iterator.next());
                        }
                    }
                    matrizTipoSolicitacao.add(colunaTipoSolicitacao);
                }

                return matrizTipoSolicitacao;
            }
        };

        ListView listView = new ListView("nodes", model) {
            @Override
            protected void populateItem(final ListItem item) {

                LoadableDetachableModel modelLinha = new LoadableDetachableModel<List<TipoSolicitacao>>() {
                    @Override
                    protected List<TipoSolicitacao> load() {
                        return (List<TipoSolicitacao>) item.getModelObject();
                    }
                };

                ListView listViewLinha = new ListView("nodesLinha", modelLinha) {
                    @Override
                    protected void populateItem(final ListItem item) {
                        item.setOutputMarkupId(true);
                        NodeButton nodeButton;

                        item.add(nodeButton = new NodeButton("btnNode", (TipoSolicitacao) item.getModelObject()) {
                            @Override
                            public String getDescricao() {
                                return ((TipoSolicitacao) item.getModel().getObject()).getDescricao();
                            }

                            @Override
                            public ResourceReference getImageNode() {
                                return Resources.getResourceReferenceTipoSolicitacao(((TipoSolicitacao) item.getModel().getObject()).getEnumTipoRequerimento());
                            }

                            @Override
                            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                RequerimentosPage.this.tipoSolicitacaoSelecionado = (TipoSolicitacao) getNode();
                                resolverSelecao(target);
                            }
                        });
                        NodeButtonEventListener createListener = nodeButton.createListener();
                        nodeButtonListeners.add(createListener);
                    }
                };
                item.add(listViewLinha);
            }
        };
        return listView;
    }

    private void buscarRequerimento(AjaxRequestTarget target, String value) {
        tipoSolicitacaoList.clear();
        if (StringUtils.trimToNull(value) == null) {
            tipoSolicitacaoList.addAll(tipoSolicitacaoPermitedList);
        } else {
            for (TipoSolicitacao ts : tipoSolicitacaoPermitedList) {
                if (StringUtil.removeAcentos(ts.getDescricao()).toUpperCase().contains(StringUtil.removeAcentos(value).toUpperCase())) {
                    tipoSolicitacaoList.add(ts);
                }
            }
        }
        target.add(containerListView);
    }

    private void buscarTipoSolicitacao() {
        List<TipoSolicitacao> list = LoadManager.getInstance(TipoSolicitacao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoSolicitacao.PROP_ATIVO, RepositoryComponentDefault.SIM_LONG))
                .addSorter(new QueryCustom.QueryCustomSorter(TipoSolicitacao.PROP_DESCRICAO))
                .start().getList();

        for (TipoSolicitacao tipoSolicitacao : list) {
            if (TipoRequerimento.ALVARA.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.ALVARA_INICIAL, Permissions.ALVARA_PARTICIPANTE_EVENTO, Permissions.ALVARA_REVALIDACAO, Permissions.AUTORIZACAO_SANITARIA);
            } else if (TipoRequerimento.CONTRATO_SOCIAL.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.ALTERACAO_RESPONSABILIDADE_LEGAL, Permissions.ALTERACAO_ATIVIDADE_ECONOMICA, Permissions.ALTERACAO_ENDERECO, Permissions.ALTERACAO_RAZAO_SOCIAL);
            } else if (TipoRequerimento.RESPONSABILIDADE_TECNICA.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.BAIXA_RESPONSABILIDADE_TECNICA, Permissions.ENTRADA_RESPONSABILIDADE_TECNICA, Permissions.CERTIDAO_NADA_CONSTA);
            } else if (TipoRequerimento.LICENCA_TRANSPORTE.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.LICENCA_TRANSPORTE);
            } else if (TipoRequerimento.EXUMACAO_RESTOS_MORTAIS.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.EXUMACAO_RESTOS_MORTAIS);
            } else if (TipoRequerimento.PRORROGACAO_PRAZO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.PRORROGACAO_PRAZO);
            } else if (TipoRequerimento.INSPECAO_SANITARIA.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.INSPECAO_SANITARIA_ROTINA, Permissions.INSPECAO_SANITARIA_AFE_ANVISA);
            } else if (TipoRequerimento.DECLARACAO_CARTORIO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.DECLARACAO_CARTORIO);
            } else if (TipoRequerimento.REQUISICAO_RECEITUARIO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.REQUISICAO_RECEITUARIO_A, Permissions.REQUISICAO_RECEITUARIO_B, Permissions.REQUISICAO_RECEITUARIO_TALIDOMIDA);
            } else if (TipoRequerimento.ABERTURA_FECHAMENTO_LIVRO_CONTROLE.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.ABERTURA_FECHAMENTO_LIVRO_CONTROLE);
            } else if (TipoRequerimento.DECLARACAO_VISA.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.DECLARACAO_VISA);
            } else if (TipoRequerimento.PEDIDO_DOCUMENTO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.PEDIDO_DOCUMENTO);
            } else if (TipoRequerimento.ANALISE_PROJETOS.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.PROJETO_BASICO_ARQUITETURA, Permissions.VISTORIA_LAUDO_CONFORMIDADE_PBA, Permissions.ANALISE_PROJETO_HIDROSSANITARIO, Permissions.VISTORIA_HABITESE_SANITARIO, Permissions.PROJETO_ARQUITETONICO_SANITARIO);
            } else if (TipoRequerimento.VACINACAO_EXTRAMURO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.VACINACAO_EXTRAMURO);
            } else if (TipoRequerimento.BAIXA_ESTABELECIMENTO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.BAIXA_ESTABELECIMENTO);
            } else if (TipoRequerimento.BAIXA_VEICULO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.BAIXA_VEICULO);
            } else if (TipoRequerimento.TREINAMENTO_ALIMENTO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.TREINAMENTOS_ALIMENTO);
            } else if (TipoRequerimento.SOLICITACAO_JURIDICA.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.RESTITUICAO_TAXA);
            } else if (TipoRequerimento.DENUNCIA_RECLAMACAO.value().equals(tipoSolicitacao.getTipoRequerimento())) {
                addTipoSolicitacaoIFPermitted(tipoSolicitacao, Permissions.DENUNCIA_RECLAMACAO);
            }
        }
    }

    private void addTipoSolicitacaoIFPermitted(TipoSolicitacao tipoSolicitacao, Permissions... permissions) {
        for (Permissions permission : permissions) {
            if (isActionPermitted(permission)) {
                tipoSolicitacaoList.add(tipoSolicitacao);
                tipoSolicitacaoPermitedList.add(tipoSolicitacao);
                return;
            }
        }
    }

    private void carregarRequerimentoVigilancia(AjaxRequestTarget target) throws ValidacaoException {
        RequerimentoVigilancia requerimentoVigilancia = buscarRequerimentoVigilanciaByProtocolo(buscaProtocolo);
        if (requerimentoVigilancia != null) {
            resolverRequerimento(requerimentoVigilancia, target);
        } else {
            throw new ValidacaoException(BundleManager.getString("msgNaoFoiPossivelEncontrarProtocolo"));
        }
    }

    private RequerimentoVigilancia buscarRequerimentoVigilanciaByProtocolo(String protocolo) {
        if (StringUtil.getDigits(protocolo).isEmpty()) return null;

        return VigilanciaHelper.getRequerimentoVigilanciaByProtocolo(Long.valueOf(StringUtil.getDigits(protocolo)));
    }

    private void resolverRequerimento(RequerimentoVigilancia requerimentoVigilancia, AjaxRequestTarget target) throws ValidacaoException {
        Long tipoDocumento = requerimentoVigilancia.getTipoDocumento();
        if (tipoDocumento != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(tipoDocumento)) {
                case ANALISE_PROJETOS:
                    setResponsePage(new RequerimentoAnaliseProjetosPage(requerimentoVigilancia, false, clazz, false));
                    break;
                case EXUMACAO_RESTOS_MORTAIS:
                    setResponsePage(new RequerimentoExumacaoPage(requerimentoVigilancia, false, clazz));
                    break;
                case BAIXA_RESPONSABILIDADE_TECNICA:
                    setResponsePage(new RequerimentoBaixaResponsabilidadeTecnicaPage(requerimentoVigilancia, false, clazz));
                    break;
                case ENTRADA_RESPONSABILIDADE_TECNICA:
                    setResponsePage(new RequerimentoInclusaoResponsabilidadePage(requerimentoVigilancia, false, clazz));
                    break;
                case CERTIDAO_NADA_CONSTA:
                    setResponsePage(new RequerimentoCertidaoNadaConstaPage(requerimentoVigilancia, false, clazz));
                    break;
                case LICENCA_SANITARIA:
                case ALVARA_INICIAL:
                    setResponsePage(new RequerimentoAlvaraInicialPage(requerimentoVigilancia, false, clazz, false));
                    break;
                case ALVARA_CADASTRO_EVENTO:
                    setResponsePage(new RequerimentoCadastroEventoPage(requerimentoVigilancia, false, clazz, false));
                    break;
                case ALVARA_PARTICIPANTE_EVENTO:
                    setResponsePage(new RequerimentoEventoPage(requerimentoVigilancia, false, clazz, false));
                    break;
                case ALTERACAO_RESPONSABILIDADE_LEGAL:
                    setResponsePage(new RequerimentoRepresentanteLegalPage(requerimentoVigilancia, false, clazz));
                    break;
                case ALTERACAO_ATIVIDADE_ECONOMICA:
                    setResponsePage(new RequerimentoAtividadeEconomicaPage(requerimentoVigilancia, false, clazz));
                    break;
                case PRORROGACAO_PRAZO:
                    responsePageProrrogacaoPrazo(target, requerimentoVigilancia, false, clazz);
                    break;
                case INSPECAO_SANITARIA_AFE_ANVISA:
                        setResponsePage(new RequerimentoInspecaoSanitariaPage(requerimentoVigilancia, false, clazz, true, false));
                    break;
                case INSPECAO_SANITARIA_COMUM:
                    setResponsePage(new RequerimentoInspecaoSanitariaPage(requerimentoVigilancia, false, clazz, false, false));
                    break;
                case REVALIDACAO_LICENCA_SANITARIA:
                case ALVARA_REVALIDACAO:
                    setResponsePage(new RequerimentoRevalidacaoAlvaraPage(requerimentoVigilancia, false, clazz, false));
                    break;
                case AUTORIZACAO_SANITARIA:
                    setResponsePage(new RequerimentoAutorizacaoSanitariaPage(requerimentoVigilancia, false, clazz));
                    break;
                case DECLARACAO_CARTORIO:
                    setResponsePage(new RequerimentoDeclaracaoVeracidadePage(requerimentoVigilancia, false, clazz));
                    break;
                case LICENCA_TRANSPORTE:
                    setResponsePage(new RequerimentoLicencaTransportePage(requerimentoVigilancia, false, clazz));
                    break;
                case DECLARACAO_VISA_PRODUTOS:
                    setResponsePage(new RequerimentoDeclaracaoVisaProdutosPage(requerimentoVigilancia, false, clazz));
                    break;
                case DECLARACAO_VISA_ISENCAO_TAXAS:
                    setResponsePage(new RequerimentoDeclaracaoVisaIsencaoTaxasPage(requerimentoVigilancia, false, clazz));
                    break;
                case REQUISICAO_RECEITUARIO_A:
                    setResponsePage(new RequerimentoReceitaAPage(requerimentoVigilancia, false, clazz));
                    break;
                case REQUISICAO_RECEITUARIO_B:
                    setResponsePage(new RequerimentoReceitaBPage(requerimentoVigilancia, false, clazz));
                    break;
                case REQUISICAO_RECEITUARIO_TALIDOMIDA:
                    setResponsePage(new RequerimentoReceitaTalidomidaPage(requerimentoVigilancia, false, clazz));
                    break;
                case ABERTURA_LIVRO_CONTROLE:
                    setResponsePage(new RequerimentoLivroAberturaPage(requerimentoVigilancia, false, clazz));
                    break;
                case FECHAMENTO_LIVRO_CONTROLE:
                    setResponsePage(new RequerimentoLivroFechamentoPage(requerimentoVigilancia, false, false, clazz));
                    break;
                case DECLARACAO_VISA_OUTROS:
                    setResponsePage(new RequerimentoDeclaracaoVisaOutrosPage(requerimentoVigilancia, false, clazz));
                    break;
                case ALTERACAO_ENDERECO:
                    setResponsePage(new RequerimentoAlteracaoEnderecoPage(requerimentoVigilancia, false, clazz));
                    break;
                case ALTERACAO_RAZAO_SOCIAL:
                    setResponsePage(new RequerimentoAlteracaoRazaoSocialPage(requerimentoVigilancia, false, clazz));
                    break;
                case PEDIDO_DOCUMENTO:
                    setResponsePage(new RequerimentoPedidoDocumentoPage(requerimentoVigilancia, false, clazz));
                    break;
                case VACINACAO_EXTRAMURO:
                    setResponsePage(new RequerimentoVacinacaoExtramuroPage(requerimentoVigilancia, false, clazz));
                    break;
                case BAIXA_ESTABELECIMENTO:
                    setResponsePage(new RequerimentoBaixaEstabelecimentoPage(requerimentoVigilancia, false, clazz));
                    break;
                case BAIXA_VEICULO:
                    setResponsePage(new RequerimentoBaixaVeiculoPage(requerimentoVigilancia, false, clazz));
                    break;
                case PROJETO_BASICO_ARQUITETURA:
                    setResponsePage(new RequerimentoAnaliseProjetosPage(requerimentoVigilancia, false, clazz, false));
                    break;
                case VISTORIA_LAUDO_CONFORMIDADE_PBA:
                    setResponsePage(new RequerimentoVistoriaProjetoBasicoArquiteturaPage(requerimentoVigilancia, false, clazz, false));
                    break;
                case TREINAMENTOS_ALIMENTO:
                    setResponsePage(new RequerimentoTreinamentoPage(requerimentoVigilancia, false, clazz));
                    break;
                case RESTITUICAO_TAXA:
                    setResponsePage(new RequerimentoRestituicaoTaxaPage(requerimentoVigilancia, false, clazz));
                    break;
                case DENUNCIA_RECLAMACAO:
                    setResponsePage(new RequerimentoDenunciaReclamacaoPage(requerimentoVigilancia, clazz));
                    break;
                case ANALISE_PROJETO_HIDROSSANITARIO:
                    setResponsePage(new RequerimentoProjetoHidrossanitarioPadraoPage(requerimentoVigilancia, (false && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), clazz));
                    break;
                case VISTORIA_HABITESE_SANITARIO:
                    setResponsePage(new RequerimentoHabitesePadraoPage(requerimentoVigilancia, (false && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), clazz));
                    break;
                case PROJETO_ARQUITETONICO_SANITARIO:
                    setResponsePage(new RequerimentoProjetoArquitetonicoSanitarioPage(requerimentoVigilancia, (false && isActionPermitted(Permissions.PROJETO_ARQUITETONICO_SANITARIO)), clazz));
                    break;

                default:
                    break;
            }
        }
        throw new ValidacaoException(BundleManager.getString("msgNaoFoiPossivelEncontrarRequerimento"));
    }

    private void resolverSelecao(AjaxRequestTarget target) {
        if (TipoRequerimento.ALVARA.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            if (dlgEscolherTipoAlvara == null) {
                addModal(target, dlgEscolherTipoAlvara = new DlgEscolherTipoAlvara(newModalId(), clazz, isActionPermitted(Permissions.ALVARA_INICIAL), isActionPermitted(Permissions.LICENCA_SANITARIA), isActionPermitted(Permissions.ALVARA_PARTICIPANTE_EVENTO), isActionPermitted(Permissions.ALVARA_CADASTRO_EVENTO), isActionPermitted(Permissions.ALVARA_REVALIDACAO), isActionPermitted(Permissions.AUTORIZACAO_SANITARIA), isActionPermitted(Permissions.REVALIDACAO_LICENCA_SANITARIA)));
            }
            dlgEscolherTipoAlvara.show(target, tipoSolicitacaoSelecionado);
        } else if (TipoRequerimento.RESPONSABILIDADE_TECNICA.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            if (dlgEscolherTipoResponsabilidadeTecnica == null) {
                addModal(target, dlgEscolherTipoResponsabilidadeTecnica = new DlgEscolherTipoResponsabilidadeTecnica(newModalId(), clazz, isActionPermitted(Permissions.BAIXA_RESPONSABILIDADE_TECNICA), isActionPermitted(Permissions.ENTRADA_RESPONSABILIDADE_TECNICA), isActionPermitted(Permissions.CERTIDAO_NADA_CONSTA)));
            }
            dlgEscolherTipoResponsabilidadeTecnica.show(target, tipoSolicitacaoSelecionado);
        } else if (TipoRequerimento.CONTRATO_SOCIAL.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            if (dlgEscolherTipoContratoSocial == null) {
                addModal(target, dlgEscolherTipoContratoSocial = new DlgEscolherTipoContratoSocial(newModalId(), clazz, isActionPermitted(Permissions.ALTERACAO_RESPONSABILIDADE_LEGAL), isActionPermitted(Permissions.ALTERACAO_ATIVIDADE_ECONOMICA), isActionPermitted(Permissions.ALTERACAO_ENDERECO), isActionPermitted(Permissions.ALTERACAO_RAZAO_SOCIAL)));
            }
            dlgEscolherTipoContratoSocial.show(target, tipoSolicitacaoSelecionado);
        } else if (TipoRequerimento.ABERTURA_FECHAMENTO_LIVRO_CONTROLE.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            if (dlgSelecionarLivroControleVigilancia == null) {
                addModal(target, dlgSelecionarLivroControleVigilancia = new DlgSelecionarLivroControleVigilancia(newModalId(), clazz));
            }
            dlgSelecionarLivroControleVigilancia.show(target, tipoSolicitacaoSelecionado);
        } else if (TipoRequerimento.REQUISICAO_RECEITUARIO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            if (dlgEscolherTipoReceituario == null) {
                addModal(target, dlgEscolherTipoReceituario = new DlgEscolherTipoReceituario(newModalId(), clazz, isActionPermitted(Permissions.REQUISICAO_RECEITUARIO_A), isActionPermitted(Permissions.REQUISICAO_RECEITUARIO_B), isActionPermitted(Permissions.REQUISICAO_RECEITUARIO_TALIDOMIDA)));
            }
            dlgEscolherTipoReceituario.show(target, tipoSolicitacaoSelecionado);
        } else if (TipoRequerimento.DECLARACAO_VISA.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            if (dlgEscolherTipoDeclaracaoVisa == null) {
                addModal(target, dlgEscolherTipoDeclaracaoVisa = new DlgEscolherTipoDeclaracaoVisa(newModalId(), clazz));
            }
            dlgEscolherTipoDeclaracaoVisa.show(target, tipoSolicitacaoSelecionado);
        } else if (TipoRequerimento.EXUMACAO_RESTOS_MORTAIS.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoExumacaoPage(tipoSolicitacaoSelecionado, clazz));
        } else if (TipoRequerimento.INSPECAO_SANITARIA.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            if (dlgEscolherTipoInspecaoSanitaria == null) {
                addModal(target, dlgEscolherTipoInspecaoSanitaria = new DlgEscolherTipoInspecaoSanitaria(newModalId(), clazz, isActionPermitted(Permissions.INSPECAO_SANITARIA_ROTINA), isActionPermitted(Permissions.INSPECAO_SANITARIA_AFE_ANVISA)));
            }
            dlgEscolherTipoInspecaoSanitaria.show(target, tipoSolicitacaoSelecionado);
        } else if (TipoRequerimento.PRORROGACAO_PRAZO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            responsePageProrrogacaoPrazo(target, clazz);
        } else if (TipoRequerimento.DECLARACAO_CARTORIO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoDeclaracaoVeracidadePage(tipoSolicitacaoSelecionado, clazz));
        } else if (TipoRequerimento.ANALISE_PROJETOS.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            if (dlgEscolherTipoProjeto == null) {
                addModal(target, dlgEscolherTipoProjeto = new DlgEscolherTipoProjeto(newModalId(), clazz,
                        isActionPermitted(Permissions.PROJETO_BASICO_ARQUITETURA),
                        isActionPermitted(Permissions.VISTORIA_LAUDO_CONFORMIDADE_PBA),
                        isActionPermitted(Permissions.ANALISE_PROJETO_HIDROSSANITARIO),
                        isActionPermitted(Permissions.VISTORIA_HABITESE_SANITARIO),
                        isActionPermitted(Permissions.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO),
                        isActionPermitted(Permissions.HABITE_SE_DECLARATORIO),
                        isActionPermitted(Permissions.PROJETO_ARQUITETONICO_SANITARIO)
                        ));
            }
            dlgEscolherTipoProjeto.show(target, tipoSolicitacaoSelecionado);
        } else if (TipoRequerimento.LICENCA_TRANSPORTE.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoLicencaTransportePage(tipoSolicitacaoSelecionado, clazz));
        } else if (TipoRequerimento.PEDIDO_DOCUMENTO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoPedidoDocumentoPage(tipoSolicitacaoSelecionado, clazz));
        } else if (TipoRequerimento.VACINACAO_EXTRAMURO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoVacinacaoExtramuroPage(tipoSolicitacaoSelecionado, clazz));
        } else if (TipoRequerimento.BAIXA_ESTABELECIMENTO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoBaixaEstabelecimentoPage(tipoSolicitacaoSelecionado, clazz));
        } else if (TipoRequerimento.BAIXA_VEICULO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoBaixaVeiculoPage(tipoSolicitacaoSelecionado, false, clazz));
        } else if (TipoRequerimento.TREINAMENTO_ALIMENTO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoTreinamentoPage(tipoSolicitacaoSelecionado, clazz));
        } else if (TipoRequerimento.SOLICITACAO_JURIDICA.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoRestituicaoTaxaPage(tipoSolicitacaoSelecionado, clazz));
        } else if (TipoRequerimento.DENUNCIA_RECLAMACAO.value().equals(tipoSolicitacaoSelecionado.getTipoRequerimento())) {
            setResponsePage(new RequerimentoDenunciaReclamacaoPage(tipoSolicitacaoSelecionado, clazz));
        }
    }

    private void responsePageProrrogacaoPrazo(AjaxRequestTarget target, final RequerimentoVigilancia rv,
                                              final boolean viewOnly, final Class clazz) {
        if (dlgInformarChave == null) {
            addModal(target, dlgInformarChave = new DlgInformarChaveProcesso(newModalId(), "") {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String chave) throws ValidacaoException, DAOException {
                    RequerimentoProrrogacaoPrazoPage page = new RequerimentoProrrogacaoPrazoPage(rv, viewOnly, clazz);
                    page.instanceFromAuto(target);
                    setResponsePage(page);
                }
            });
        }

        dlgInformarChave.show(target);

    }

    private void responsePageProrrogacaoPrazo(AjaxRequestTarget target, final Class clazz) {
        if (dlgInformarChave == null) {
            addModal(target, dlgInformarChave = new DlgInformarChaveProcesso(newModalId(), "") {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String chave) throws ValidacaoException, DAOException {
                    AcessoRecursoDTO acessoRecursoDTO = VigilanciaLoginHelper.buscarVOChave(chave, true);
                    if (CollectionUtils.isNotNullEmpty(acessoRecursoDTO.getAutoIntimacaoList())) {
                        if (acessoRecursoDTO.getAutoIntimacaoList().size() == 1) {
                            RequerimentoProrrogacaoPrazo requerimentoProrrogacaoPrazoExistente = AutosHelper.existsProrrogacaoAuto(acessoRecursoDTO.getAutoIntimacaoList().get(0));
                            if (requerimentoProrrogacaoPrazoExistente != null) {
                                RequerimentoProrrogacaoPrazoRecursoExternoPage page = new RequerimentoProrrogacaoPrazoRecursoExternoPage(requerimentoProrrogacaoPrazoExistente.getRequerimentoVigilancia(), false, VigilanciaRecursoPage.class);
                                page.warn(target, BundleManager.getString("autoIntimacaoJaPossuiRequerimentoProrrogacaoPrazo"));
                                setResponsePage(page);
                                return;
                            }
                            RequerimentoProrrogacaoPrazoPage page = new RequerimentoProrrogacaoPrazoPage(acessoRecursoDTO.getAutoIntimacaoList().get(0), clazz);
                            page.instanceFromAuto(target);
                            setResponsePage(page);
                        } else {

                        }
                    }
                }
            });
        }
        dlgInformarChave.show(target);

    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("requerimentoProtocolo");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(JavaScriptHeaderItem.forReference(Application.get().getJavaScriptLibrarySettings().getJQueryReference()));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_MIGRATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DATA_TABLE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_VALIDATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MASKEDINPUT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUTS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TREEVIEW));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JGROWL));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_PRINT_ELEMENT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DIRTY_FORMS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FORM));
        response.render(OnDomReadyHeaderItem.forScript("$('body').find('form').keydown(function(ev){\n"
                + "        if(ev.which == 13 && ev.target.nodeName!='TEXTAREA') ev.preventDefault();\n"
                + "    });"));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_UNIDADE_LOGIN));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_SCROLL_INTO_VIEW));
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(txtBuscaRequerimento)));

        response.render(OnDomReadyHeaderItem.forScript(JScript.timerDelayComponent(txtBuscaRequerimento, ajaxTimerDelayComponentBehavior.getCallbackScript().toString())));
    }
}
