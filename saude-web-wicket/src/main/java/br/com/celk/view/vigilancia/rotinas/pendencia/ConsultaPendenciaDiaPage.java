package br.com.celk.view.vigilancia.rotinas.pendencia;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoDenunciaReclamacaoPage;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.CadastroAutoIntimacaoPage;
import br.com.celk.view.vigilancia.rotinas.autoinfracao.CadastroAutoInfracaoPage;
import br.com.celk.view.vigilancia.rotinas.pendencia.dlg.DlgConcluirPendencia;
import br.com.celk.view.vigilancia.rotinas.pendencia.dlg.DlgInformarProfissionais;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia;
import br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDiaFiscal;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 675
 */
public class ConsultaPendenciaDiaPage extends ConsultaPage<PendenciaDia, List<QueryCustom.QueryCustomParameter>> {

    private DatePeriod dataPendencia;
    private Profissional profissional;
    private Long situacao;

    private DlgInformarProfissionais dlgInformarProfissionais;
    private DlgConcluirPendencia dlgConcluirPendencia;

    @Override
    public void initForm(Form form) {

        form.add(new PnlDatePeriod("dataPendencia", new PropertyModel(this, "dataPendencia")));
        dataPendencia = new DatePeriod();
        dataPendencia.setDataFinal(DataUtil.getDataAtual());

        DropDown cbxSituacao;
        form.add(cbxSituacao = DropDownUtil.getIEnumDropDown("situacao", new PropertyModel(this, "situacao"), PendenciaDia.Situacao.values()));
        cbxSituacao.addAjaxUpdateValue();

        form.add(new AutoCompleteConsultaProfissional("profissional", new PropertyModel(this, "profissional")));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        PendenciaDia proxy = on(PendenciaDia.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("codigoRotina"), proxy.getCodigoRotina()));
        columns.add(createSortableColumn(bundle("tipoRotina"), proxy.getTipoRotina(), proxy.getDescricaoTipoRotina()));
        columns.add(createSortableColumn(bundle("dataPendencia"), proxy.getDataPendencia()));
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacao(), proxy.getDescricaoStatus()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<PendenciaDia>() {
            @Override
            public void customizeColumn(final PendenciaDia rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<PendenciaDia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PendenciaDia modelObject) throws ValidacaoException, DAOException {
                        consultarAction(modelObject);
                    }
                });

                addAction(ActionType.ADICIONAR, rowObject, new IModelAction<PendenciaDia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PendenciaDia modelObject) throws ValidacaoException, DAOException {
                        if (dlgInformarProfissionais == null) {
                            addModal(target, dlgInformarProfissionais = new DlgInformarProfissionais(newModalId()) {

                                @Override
                                public void onConfirmar(AjaxRequestTarget target) {
                                    getPageableTable().populate();
                                    getPageableTable().update(target);
                                }
                            });
                        }
                        dlgInformarProfissionais.show(target, rowObject);
                    }
                }).setIcon(Icon.ROUND_PLUS).setEnabled(rowObject.getSituacao().equals(PendenciaDia.Situacao.PENDENTE.value()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<PendenciaDia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PendenciaDia modelObject) throws ValidacaoException, DAOException {
                        List<PendenciaDiaFiscal> lstFiscal = LoadManager.getInstance(PendenciaDiaFiscal.class)
                                .addProperties(new HQLProperties(PendenciaDiaFiscal.class).getProperties())
                                .addProperties(new HQLProperties(Profissional.class, PendenciaDiaFiscal.PROP_PROFISSIONAL).getProperties())
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PendenciaDiaFiscal.PROP_PENDENCIA_DIA, PendenciaDia.PROP_CODIGO), modelObject.getCodigo()))
                                .start().getList();
                        for (PendenciaDiaFiscal pdf : lstFiscal) {
                            BOFactoryWicket.delete(pdf);
                        }
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().populate();
                        getPageableTable().update(target);
                    }
                }).setEnabled(rowObject.getSituacao().equals(PendenciaDia.Situacao.PENDENTE.value()));
                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<PendenciaDia>() {
                    @Override
                    public void action(AjaxRequestTarget target, PendenciaDia modelObject) throws ValidacaoException, DAOException {
                        if (dlgConcluirPendencia == null) {
                            addModal(target, dlgConcluirPendencia = new DlgConcluirPendencia(newModalId()) {
                                @Override
                                public void onConfirmar(AjaxRequestTarget target, PendenciaDia pd, Date dataConclusao) throws DAOException, ValidacaoException {
                                    pd.setDataFinalizacao(dataConclusao);
                                    pd.setSituacao(PendenciaDia.Situacao.FINALIZADO.value());
                                    BOFactoryWicket.save(pd);
                                    getPageableTable().populate();
                                    getPageableTable().update(target);
                                }
                            });
                        }
                        dlgConcluirPendencia.show(target, rowObject);
                    }
                }).setEnabled(rowObject.getSituacao().equals(PendenciaDia.Situacao.PENDENTE.value()));

            }
        };
    }

    private void consultarAction(PendenciaDia modelObject) {
        if (PendenciaDia.TipoRotina.AUTOINFRACAO.value().equals(modelObject.getTipoRotina())) {
            AutoInfracao ai = LoadManager.getInstance(AutoInfracao.class)
                    .addProperties(new HQLProperties(AutoInfracao.class).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, AutoInfracao.PROP_VIGILANCIA_ENDERECO).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, AutoInfracao.PROP_ESTABELECIMENTO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_CODIGO, modelObject.getCodigoRotina()))
                    .start().getVO();
            setResponsePage(new CadastroAutoInfracaoPage(ai, true, getPageParameters(), ConsultaPendenciaDiaPage.class));
        } else if (PendenciaDia.TipoRotina.AUTOINTIMACAO.value().equals(modelObject.getTipoRotina())) {
            AutoIntimacao ai = LoadManager.getInstance(AutoIntimacao.class)
                    .addProperties(new HQLProperties(AutoIntimacao.class).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, AutoIntimacao.PROP_VIGILANCIA_ENDERECO).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, AutoIntimacao.PROP_ESTABELECIMENTO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_CODIGO, modelObject.getCodigoRotina()))
                    .start().getVO();
            setResponsePage(new CadastroAutoIntimacaoPage(ai, true, getPageParameters(), ConsultaPendenciaDiaPage.class));
        } else if (PendenciaDia.TipoRotina.DENUNCIA.value().equals(modelObject.getTipoRotina())) {
            Denuncia denuncia = LoadManager.getInstance(Denuncia.class)
                    .addProperties(new HQLProperties(Denuncia.class).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, Denuncia.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Denuncia.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_TIPO_DOCUMENTO), TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value()))
                    .setId(modelObject.getCodigoRotina())
                    .start().getVO();
            setResponsePage(new RequerimentoDenunciaReclamacaoPage(denuncia.getRequerimentoVigilancia(), false, ConsultaPendenciaDiaPage.class));
        } else if (PendenciaDia.TipoRotina.PENDENCIA_DIA.value().equals(modelObject.getTipoRotina())) {
            setResponsePage(new CadastroPendenciaDiaPage(modelObject, true));
        }
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return PendenciaDia.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(PendenciaDia.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(PendenciaDia.PROP_DESCRICAO), true);
            }

            @Override
            public List getInterceptors() {
                return Arrays.asList(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        if (profissional != null) {
                            hql.addToWhereWhithAnd("exists (select 1 from PendenciaDiaFiscal pdf where pdf.PendenciaDia.codigo = " + alias + ".codigo"
                                    + " and pdf.profissional.codigo = " + profissional.getCodigo() + ")");
                        }
                    }
                });
            }
        };
    }

    @Override
    public List<QueryCustom.QueryCustomParameter> getParameters() {
        List<QueryCustom.QueryCustomParameter> parametros = new ArrayList<>();
        parametros.add(new QueryCustom.QueryCustomParameter(PendenciaDia.PROP_DATA_PENDENCIA, dataPendencia));
        if (situacao == null) {
            parametros.add(new QueryCustom.QueryCustomParameter(PendenciaDia.PROP_SITUACAO, PendenciaDia.Situacao.PENDENTE.value()));
        } else {
            parametros.add(new QueryCustom.QueryCustomParameter(PendenciaDia.PROP_SITUACAO, situacao));
        }
        return parametros;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroPendenciaDiaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaPendenciaDia");
    }
}
