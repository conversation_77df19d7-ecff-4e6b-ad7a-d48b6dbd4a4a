package br.com.celk.view.materiais.tipodocumentos;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudCloneColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.materiais.tipodocumento.customize.CustomizeConsultaTipoDocumento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.clone.DefinerPropertiesCloning;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 73
 */
@Private
public class ConsultaTipoDocumentoPage extends ConsultaPage<TipoDocumento, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long codigo;
    private String sigla;

    public ConsultaTipoDocumentoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<Long>("codigo"));
        form.add(new InputField<String>("descricao"));
        form.add(new InputField<String>("sigla"));

        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(TipoDocumento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(TipoDocumento.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(TipoDocumento.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("sigla"), VOUtils.montarPath(TipoDocumento.PROP_FLAG_SIGLA)));

        return columns;
    }

    private CustomColumn<TipoDocumento> getCustomColumn() {
        return new CustomColumn<TipoDocumento>() {

            @Override
            public Component getComponent(String componentId, final TipoDocumento rowObject) {
                return new CrudCloneColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoDocumentoPage(rowObject,false,true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoDocumentoPage(rowObject, true));
                    }
                    
                    @Override
                    public void onClonar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        TipoDocumento newTipoDocumento = (TipoDocumento) new DefinerPropertiesCloning().define(rowObject);
                        newTipoDocumento.setCodigo(null);
                        setResponsePage(new CadastroTipoDocumentoPage(newTipoDocumento));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaTipoDocumento()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TipoDocumento.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoDocumento.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, codigo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoDocumento.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoDocumento.PROP_FLAG_SIGLA), BuilderQueryCustom.QueryParameter.ILIKE, sigla));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoDocumentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTiposDeDocumento");
    }
}
