package br.com.celk.view.materiais.estoque.inventario.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.basico.localizacaoestrutura.autocomplete.AutoCompleteConsultaLocalizacaoEstrutura;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoInventarioDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 474
 */
@Private
public class RelatorioRelacaoProdutosContagemPage extends RelatorioPage<RelatorioRelacaoInventarioDTOParam> {

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown dropDownFormaApresentacao;
    private DropDown dropDownTipoResumo;
    private DropDown dropDownOrdenacao;
    private DropDown dropDownEstoqueFisico;
    private DropDown dropDownAtivoInativo;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaLocalizacaoEstrutura autoCompleteConsultaLocalizacaoEstrutura;
    private AutoCompleteConsultaLocalizacao autoCompleteConsultaLocalizacao;

    @Override
    public void init(Form form) {
        RelatorioRelacaoInventarioDTOParam proxy = on(RelatorioRelacaoInventarioDTOParam.class);

        initComponents(proxy);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getLstEmpresa()), true));
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setLabel(new Model<>(bundle("unidade")));

        form.add(autoCompleteConsultaProduto = (AutoCompleteConsultaProduto) new AutoCompleteConsultaProduto(path(proxy.getLstProduto())).setOperadorValor(true));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(autoCompleteConsultaLocalizacao = new AutoCompleteConsultaLocalizacao(path(proxy.getLocalizacao())));
        autoCompleteConsultaLocalizacao.setOperadorValor(true);
        form.add(autoCompleteConsultaLocalizacaoEstrutura = new AutoCompleteConsultaLocalizacaoEstrutura(path(proxy.getLocalizacaoEstrutura())));
        if (isUtilizaLocalizacaoEstoque()) {
            autoCompleteConsultaLocalizacaoEstrutura.setExibirApenasVisivelInventario(true);
        } else {
            autoCompleteConsultaLocalizacaoEstrutura.setExibirApenasPadrao(true);
        }
        autoCompleteConsultaLocalizacaoEstrutura.setOperadorValor(true);
        autoCompleteConsultaLocalizacaoEstrutura.setEnabled(false);
        form.add(getDropDownGrupo(path(proxy.getGrupoProdutoSubGrupo())));
        form.add(getDropDownSubGrupo(path(proxy.getSubGrupo())));
        form.add(dropDownEstoqueFisico);
        form.add(dropDownTipoResumo);
        form.add(dropDownFormaApresentacao);
        form.add(dropDownOrdenacao);
        form.add(dropDownAtivoInativo);
    }

    private void initComponents(RelatorioRelacaoInventarioDTOParam proxy) {
        this.dropDownEstoqueFisico = new DropDown(path(proxy.getEstoqueFisico()));
        this.dropDownEstoqueFisico.addChoice(new Long(ReportProperties.ESTOQUE_SIM), Bundle.getStringApplication("rotulo_sim"));
        this.dropDownEstoqueFisico.addChoice(new Long(ReportProperties.ESTOQUE_NAO), Bundle.getStringApplication("rotulo_nao"));

        this.dropDownFormaApresentacao = new DropDown(path(proxy.getFormaApresentacao()));
        this.dropDownFormaApresentacao.addChoice(new Long(ReportProperties.AGRUPAR_GRUPO), Bundle.getStringApplication("rotulo_grupo"));
        this.dropDownFormaApresentacao.addChoice(new Long(ReportProperties.AGRUPAR_PRODUTO), Bundle.getStringApplication("rotulo_produto"));
        this.dropDownFormaApresentacao.addChoice(new Long(ReportProperties.AGRUPAR_LOTE), Bundle.getStringApplication("rotulo_lote"));
        this.dropDownFormaApresentacao.addChoice(new Long(ReportProperties.LOCALIZACAO), Bundle.getStringApplication("rotulo_localizacao"));

        this.dropDownTipoResumo = new DropDown(path(proxy.getTipoResumo()));
        this.dropDownTipoResumo.addChoice(new Long(ReportProperties.CONTAGEM_NORMAL), Bundle.getStringApplication("rotulo_contagem_normal"));
        this.dropDownTipoResumo.addChoice(new Long(ReportProperties.CONTAGEM_SEMI_CEGA), Bundle.getStringApplication("rotulo_contagem_semi_cega"));
        this.dropDownTipoResumo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarCamposContagemNormal(target);
            }
        });



        this.dropDownOrdenacao = new DropDown(path(proxy.getOrdenacao()));
        this.dropDownOrdenacao.addChoice(Produto.PROP_CODIGO, Bundle.getStringApplication("rotulo_codigo"));
        this.dropDownOrdenacao.addChoice(Produto.PROP_DESCRICAO, Bundle.getStringApplication("rotulo_descricao"));

        this.dropDownAtivoInativo = new DropDown(path(proxy.getFlagAtivoEstoqueEmpresa()));
        this.dropDownAtivoInativo.addChoice(RepositoryComponentDefault.AMBOS, Bundle.getStringApplication("rotulo_ambos"));
        this.dropDownAtivoInativo.addChoice(RepositoryComponentDefault.SIM, Bundle.getStringApplication("rotulo_ativo"));
        this.dropDownAtivoInativo.addChoice(RepositoryComponentDefault.NAO, Bundle.getStringApplication("rotulo_inativo"));
    }

    private void habilitarCamposContagemNormal(AjaxRequestTarget target) {
        boolean habilitarCampos = !ReportProperties.CONTAGEM_SEMI_CEGA.equals(dropDownTipoResumo.getComponentValue());
        if(habilitarCampos) {
            dropDownEstoqueFisico.setComponentValue(ReportProperties.ESTOQUE_NAO);
            dropDownFormaApresentacao.setComponentValue(ReportProperties.AGRUPAR_GRUPO);
            dropDownOrdenacao.setComponentValue(Produto.PROP_CODIGO);
            dropDownAtivoInativo.setComponentValue(RepositoryComponentDefault.AMBOS);
            autoCompleteConsultaLocalizacao.setEnabled(true);
            autoCompleteConsultaLocalizacaoEstrutura.setEnabled(false);
        } else {
            dropDownEstoqueFisico.setComponentValue(ReportProperties.ESTOQUE_NAO);
            dropDownFormaApresentacao.setComponentValue(ReportProperties.LOCALIZACAO);
            dropDownOrdenacao.setComponentValue(Produto.PROP_DESCRICAO);
            dropDownAtivoInativo.setComponentValue(RepositoryComponentDefault.SIM);
            autoCompleteConsultaLocalizacao.setEnabled(false);
            autoCompleteConsultaLocalizacaoEstrutura.setEnabled(true);
        }
        dropDownEstoqueFisico.setEnabled(habilitarCampos);
        dropDownFormaApresentacao.setEnabled(habilitarCampos);
        dropDownOrdenacao.setEnabled(habilitarCampos);
        dropDownAtivoInativo.setEnabled(habilitarCampos);
        autoCompleteConsultaLocalizacao.limpar(target);
        autoCompleteConsultaLocalizacaoEstrutura.limpar(target);
        target.add(dropDownEstoqueFisico);
        target.add(dropDownFormaApresentacao);
        target.add(dropDownOrdenacao);
        target.add(dropDownAtivoInativo);
        target.add(autoCompleteConsultaLocalizacao);
        target.add(autoCompleteConsultaLocalizacaoEstrutura);
    }

    private DropDown<GrupoProduto> getDropDownGrupo(String id) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>(id);

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();

                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo(String id) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>(id);

            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoInventarioDTOParam.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoProdutosContagem");
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoInventarioDTOParam param) throws ReportException {
        param.setUtilizaLocalicacaoEstoque(isUtilizaLocalizacaoEstoque());
        return BOFactory.getBO(EstoqueReportFacade.class).relatorioRelacaoInventario(param);
    }

    private boolean isUtilizaLocalizacaoEstoque() {
        String utilizaLocalizacaoEstoque = null;
        try {
            utilizaLocalizacaoEstoque = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }

        return RepositoryComponentDefault.SIM.equals(utilizaLocalizacaoEstoque);
    }
}
