package br.com.celk.view.materiais.dispensacao.prescricaoatendimento;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interceptor.LoadInterceptorUltimoAtendimentoPaciente;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaReceituariosAbertosDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingImpl;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemComponente;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 257
 */
@Private
public class ConsultaPrescricaoAtendimentoPage extends BasePage {
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaBaixa;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaDispensacao;
    private Empresa empresaBaixa;
    private Empresa empresaDispensacao;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private String codigoBarras;
    private InputField txtCodigoBarras;
    private boolean isCodigoBarra;
    private boolean permissaoVisualizarApenasPrescricaoEstabelecimento;

    public ConsultaPrescricaoAtendimentoPage() throws DAOException {
        init();
    }
    
    private void init() {
        Form form = new Form("form");

        permissaoVisualizarApenasPrescricaoEstabelecimento = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        form.add(autoCompleteConsultaEmpresaDispensacao = new AutoCompleteConsultaEmpresa("empresaDispensacao", new PropertyModel(this, "empresaDispensacao"), false));
        autoCompleteConsultaEmpresaDispensacao.setLabel(Model.of(bundle("farmaciaDispensacao")));
        if (permissaoVisualizarApenasPrescricaoEstabelecimento) {
            autoCompleteConsultaEmpresaDispensacao.setValidaUsuarioEmpresa(true);
            if(!SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster()) {
                autoCompleteConsultaEmpresaDispensacao.setComponentValue(SessaoAplicacaoImp.getInstance().getEmpresa());
            }
        }

        form.add(autoCompleteConsultaEmpresaBaixa = new AutoCompleteConsultaEmpresa("empresaBaixa", new PropertyModel(this, "empresaBaixa"), true));
        autoCompleteConsultaEmpresaBaixa.setValidaUsuarioEmpresa(true);
        autoCompleteConsultaEmpresaBaixa.setComponentValue(SessaoAplicacaoImp.getInstance().getEmpresa());
        autoCompleteConsultaEmpresaBaixa.setLabel(Model.of(bundle("farmaciaSaida")));
        form.add(txtCodigoBarras = new InputField("codigoBarras", new PropertyModel(this, "codigoBarras")));
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus", new LoadableObjectModel<UsuarioCadsus>(UsuarioCadsus.class, false), false) {
            @Override
            public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
            }
        });
        
        autoCompleteConsultaUsuarioCadsus.add(new ConsultaListener() {
            
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Object object) {
                isCodigoBarra = false;
            }
        });

        form.add(new AbstractAjaxButton("btnAvancar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                avancar();
            }
        });
        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaReceituarioItemPage(empresaDispensacao, permissaoVisualizarApenasPrescricaoEstabelecimento));
            }
        });
        
        txtCodigoBarras.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                try {
                    isCodigoBarra = true;
                    avancar();
                } catch (DAOException ex) {
                    Logger.getLogger(ConsultaPrescricaoAtendimentoPage.class.getName()).log(Level.SEVERE, null, ex);
                } catch (ValidacaoException ex) {
                    warn(target, ex.getMessage());
                }
            }
            
            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
            }
        });
        
        add(form);
    }
    
    public QueryConsultaReceituariosAbertosDTOParam getParameters() {
        QueryConsultaReceituariosAbertosDTOParam parameters = new QueryConsultaReceituariosAbertosDTOParam();
        
        parameters.setPaciente((UsuarioCadsus) autoCompleteConsultaUsuarioCadsus.getModelObject());
        if (isCodigoBarra) {
            parameters.setTipoReceita(Arrays.asList(TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO,
                    TipoReceita.RECEITA_SOLICITACAO_MATERIAIS,
                    TipoReceita.RECEITA_AMARELA,
                    TipoReceita.RECEITA_AZUL,
                    TipoReceita.RECEITA_BRANCA,
                    TipoReceita.RECEITA_CONTROLADAS,
                    TipoReceita.RECEITA_MAGISTRAL,
                    TipoReceita.RECEITA_BASICA));
        } else {
            parameters.setTipoReceita(Arrays.asList(TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO, TipoReceita.RECEITA_SOLICITACAO_MATERIAIS));
        }
        if (codigoBarras != null) {
            parameters.setCodigoBarras(Long.parseLong(codigoBarras.trim()));
        }
        parameters.setEmpresaDispensacao(empresaDispensacao);
        parameters.setPermissaoVisualizarApenasPrescricaoEstabelecimento(permissaoVisualizarApenasPrescricaoEstabelecimento);

        return parameters;
    }
    
    private void avancar() throws DAOException, ValidacaoException {
        
        if (autoCompleteConsultaEmpresaBaixa.getComponentValue() == null) {
            throw new ValidacaoException(bundle("msgInformeFarmaciaSaida"));
        }
        
        if (codigoBarras == null && autoCompleteConsultaUsuarioCadsus.getComponentValue() == null) {
            throw new ValidacaoException(bundle("informeCodigoDeBarrasOuPaciente"));
        }
        
        String validaSaldo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ValidaSaldoDispensacaoPrescricao");
        
        DataPaging<QueryConsultaReceituariosAbertosDTOParam> dataPaging = new DataPagingImpl<QueryConsultaReceituariosAbertosDTOParam>(DataPaging.Type.ALVO_LIST);
        dataPaging.setParam(getParameters());
        DataPagingResult<Receituario> result = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarReceituariosAbertos(dataPaging);
        
        if (result.getList().isEmpty()) {
            if (autoCompleteConsultaUsuarioCadsus.getComponentValue() == null) {
                Receituario receituarioNaoDisponivel = LoadManager.getInstance(Receituario.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_USUARIO_CADSUS, autoCompleteConsultaUsuarioCadsus.getModelObject()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_TIPO_RECEITA, TipoReceita.PROP_TIPO_RECEITA), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO, TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)))
                        .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_CODIGO, Long.parseLong(codigoBarras.trim())))
                        .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_SITUACAO, Receituario.Situacao.PRESCRITO.value()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_ATENDIMENTO, Atendimento.PROP_STATUS), BuilderQueryCustom.QueryParameter.MENOR, Atendimento.STATUS_FINALIZADO))
                        .start().getVO();
                if (receituarioNaoDisponivel != null) {
                    throw new ValidacaoException(bundle("msgAtendimentoNaoFinalizado"));
                } else {
                    throw new ValidacaoException(bundle("naoExistePrescricaoCodigoBarras"));
                }
            }
            
            Atendimento atendimento = LoadManager.getInstance(Atendimento.class)
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_ATENDIMENTO_PRINCIPAL, Atendimento.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_EMPRESA, Empresa.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_CONVENIO, Convenio.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_CONVENIO, Convenio.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_DISPENSACAO_POR_TURNO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE, Atendimento.STATUS_CANCELADO))
                    .addInterceptor(new LoadInterceptorUltimoAtendimentoPaciente((UsuarioCadsus) autoCompleteConsultaUsuarioCadsus.getModelObject()))
                    .start().getVO();
            
            if (atendimento == null) {
                throw new ValidacaoException(bundle("nenhumAtendimentoReituarioEncontradoParaPaciente"));
            }
            atendimento.setUsuarioCadsus((UsuarioCadsus) autoCompleteConsultaUsuarioCadsus.getModelObject());
            if (RepositoryComponentDefault.SIM.equals(validaSaldo)) {
                if (!isActionPermitted(Permissions.CADASTRAR, SelecaoPrescricaoAtendimentoPage.class)) {
                    throw new ValidacaoException(bundle("nenhumReceituarioAbertoEncontradoPaciente"));
                }
            }
            
            Long maxCiclo = LoadManager.getInstance(AtendimentoInformacao.class)
                    .addGroup(new QueryCustom.QueryCustomGroup(AtendimentoInformacao.PROP_SEQUENCIA_CICLO, BuilderQueryCustom.QueryGroup.MAX))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoInformacao.PROP_ATENDIMENTO_PRINCIPAL, Atendimento.PROP_CODIGO), atendimento.getAtendimentoPrincipal().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoInformacao.PROP_STATUS_ATENDIMENTO), BuilderQueryCustom.QueryParameter.DIFERENTE, AtendimentoInformacao.StatusAtendimento.CANCELADO.value()))
                    .start().getVO();
            
            AtendimentoInformacao atendimentoInformacao = LoadManager.getInstance(AtendimentoInformacao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoInformacao.PROP_ATENDIMENTO_PRINCIPAL, Atendimento.PROP_CODIGO), atendimento.getAtendimentoPrincipal().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoInformacao.PROP_STATUS_ATENDIMENTO), BuilderQueryCustom.QueryParameter.DIFERENTE, AtendimentoInformacao.StatusAtendimento.CANCELADO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoInformacao.PROP_SEQUENCIA_CICLO), Coalesce.asLong(maxCiclo)))
                    .start().getVO();
            if (atendimentoInformacao == null) {
                throw new ValidacaoException(bundle("nenhumAtendimentoEncontradoParaPaciente"));
            }
            if (AtendimentoInformacao.StatusAtendimento.ABERTO.value().equals(atendimentoInformacao.getStatusAtendimento())) {
                setResponsePage(new IniciarDispensacaoPrescricaoAtendimentoPage(atendimento, empresaBaixa));
            } else {
                Long limiteParaDispensarPrescricoesAtendimentoFechado = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("LimiteParaDispensarPrescricoesAtendimentoFechado");
                Date dataInicioValidade = Data.removeMinutos(DataUtil.getDataAtual(), (limiteParaDispensarPrescricoesAtendimentoFechado.intValue() * 60));
                if (atendimentoInformacao.getDataSaida().before(dataInicioValidade)) {
                    throw new ValidacaoException(bundle("nenhumAtendimentoDentroValidadeEncontradoParaPaciente"));
                } else {
                    setResponsePage(new IniciarDispensacaoPrescricaoAtendimentoPage(atendimento, empresaBaixa));
                }
            }
        } else {
            List<Receituario> lstReceituario = result.getList();
            boolean contemSaldo = false;
            for (Receituario receituario : lstReceituario) {
                List<ReceituarioItem> lstReceituarioItem = LoadManager.getInstance(ReceituarioItem.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_CODIGO), receituario.getCodigo()))
                        .start().getList();
                if (contemSaldoDisponivelDispensar(lstReceituarioItem)) {
                    contemSaldo = true;
                }
            }
            
            if (result.getList().size() == 1 && codigoBarras != null) {
                if (RepositoryComponentDefault.SIM.equals(validaSaldo)) {
                    if (!contemSaldo) {
                        throw new ValidacaoException(bundle("todosItensPrescricaoDispensados"));
                    }
                    setResponsePage(new DispensacaoReceituarioPage(result.getList().get(0), empresaBaixa));
                } else {
                    setResponsePage(new DispensacaoPrescricaoAtendimentoPage(result.getList().get(0), empresaBaixa));
                }
            } else {
                if (RepositoryComponentDefault.SIM.equals(validaSaldo)) {
                    if (!contemSaldo) {
                        throw new ValidacaoException(bundle("todosItensPrescricaoDispensados"));
                    }
                }
                setResponsePage(new SelecaoPrescricaoAtendimentoPage(result.getList(), (UsuarioCadsus) autoCompleteConsultaUsuarioCadsus.getModelObject(), empresaBaixa));
            }
        }
    }
    
    private boolean contemSaldoDisponivelDispensar(List<ReceituarioItem> list) {
        for (ReceituarioItem receituarioItem : list) {
            if (receituarioItem.getProduto() == null) {
                if (RepositoryComponentDefault.NAO_LONG.equals(receituarioItem.getFlagPadronizado())) {
                    if (Coalesce.asLong(receituarioItem.getQuantidadeDispensada()) < Coalesce.asLong(receituarioItem.getQuantidadePrescrita())) {
                        return true;
                    }
                    continue;
                }
                List<ReceituarioItemComponente> lstComponentes = LoadManager.getInstance(ReceituarioItemComponente.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItemComponente.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_CODIGO), receituarioItem.getCodigo()))
                        .start().getList();
                if (lstComponentes != null && !lstComponentes.isEmpty()) {
                    for (ReceituarioItemComponente componente : lstComponentes) {
                        if (componente.getQuantidadeDispensada() == null || componente.getQuantidadeDispensada() < componente.getQuantidadePrescrita()) {
                            return true;
                        }
                    }
                }
            } else if (Coalesce.asLong(receituarioItem.getQuantidadeDispensada()) < Coalesce.asLong(receituarioItem.getQuantidadePrescrita())) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtCodigoBarras;
    }
    
    @Override
    public String getTituloPrograma() {
        return bundle("dispensacaoPrescricao");
    }
}
