package br.com.celk.view.consorcio.consorcioguiaprocedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.consorcio.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 111
 */
@Private

public class ConfirmacaoGuiaProcedimentoStep1Page extends BasePage{

    private InputField txtNumeroChave;
    private WebMarkupContainer tableContainer;
    private Table tblGuiasAgendadas;

    private String numeroChave;
    private List<ConsorcioGuiaProcedimento> guias;
    private boolean isGuiaDigital;

    public ConfirmacaoGuiaProcedimentoStep1Page() {
        try {
            isGuiaDigital = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("guiaDigital"));
        } catch (DAOException e) {
            e.printStackTrace();
        }
        init();
    }
    
    private void init(){
        carregarGuiasAgendadas();
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        form.add(txtNumeroChave = new RequiredInputField("numeroChave"));
        
        form.add(new AbstractAjaxButton("btnCarregarGuia") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                carregarGuia(target);
            }
        });

        WebMarkupContainer tableContainer = new WebMarkupContainer("tableContainer");
        tableContainer.add(tblGuiasAgendadas = new Table("tblGuiasAgendadas", getColumns(), getCollectionProvider()));
        tblGuiasAgendadas.populate();
        tableContainer.setVisible(!isGuiaDigital);
        form.add(tableContainer);

        add(form);
    }
    
    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(ConsorcioGuiaProcedimento.class);
        
        columns.add(new DateColumn(BundleManager.getString("horario"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_AGENDAMENTO)).setPattern("HH:mm"));
        columns.add(columnFactory.createColumn(BundleManager.getString("nGuia"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CODIGO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("paciente"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_NOME_PACIENTE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("dataNascimento"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_NASCIMENTO)));
        
        return columns;
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return guias;
            }
        };
    }
    
    private void carregarGuiasAgendadas(){
            guias = LoadManager.getInstance(ConsorcioGuiaProcedimento.class)
                    .addProperties(new HQLProperties(ConsorcioGuiaProcedimento.class).getProperties())
                    .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO))
                    .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                    .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                    .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_AGENDAMENTO), Data.adjustRangeHour(Data.getDataAtual())))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR), ApplicationSession.get().getSessaoAplicacao().getEmpresa()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value(), ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.AGENDADO.value())))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_AGENDAMENTO)))
                    .start().getList();
    }
    
    private void carregarGuia(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        ConsorcioGuiaProcedimento consorcioGuiaProcedimento = LoadManager.getInstance(ConsorcioGuiaProcedimento.class)
                .addProperties(new HQLProperties(ConsorcioGuiaProcedimento.class).getProperties())
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO))
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO))
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CIDADE, Cidade.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CIDADE, Cidade.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_NUMERO_CHAVE), StringUtilKsi.getDigits(numeroChave)))
                .start().getVO();

        String confirmarGuiasComDataAgendamentoPosteriorAtual = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("confirmarGuiasComDataAgendamentoPosteriorAtual");

        if (consorcioGuiaProcedimento == null) {
            throw new ValidacaoException(BundleManager.getString("chaveInvalidaVerifiqueNumeroInformado"));
        } else if(!consorcioGuiaProcedimento.getConsorcioPrestador().getEmpresaPrestador().equals(ApplicationSession.get().getSessaoAplicacao().getEmpresa())){
            throw new ValidacaoException(BundleManager.getString("guiaDefinidaNaoPertenceEstePrestador"));
        } else if(consorcioGuiaProcedimento.getStatus().equals(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.A_PAGAR.value())){
            throw new ValidacaoException(BundleManager.getString("guiaJaUtilizada"));
        } else if (consorcioGuiaProcedimento.getDataAgendamento() != null && RepositoryComponentDefault.NAO.equals(confirmarGuiasComDataAgendamentoPosteriorAtual) && DataUtil.getDataAtualSemHora().compareTo(DataUtil.zerarHora(consorcioGuiaProcedimento.getDataAgendamento())) < 0) {
            throw new ValidacaoException(BundleManager.getString("msgGuiaNaoPodeSerConfirmadaAntesDataAgendamentoX", new SimpleDateFormat("dd/MM/yyyy").format(consorcioGuiaProcedimento.getDataAgendamento())));
        }

        setResponsePage(new ConfirmacaoGuiaProcedimentoStep2Page(consorcioGuiaProcedimento,false, isGuiaDigital));
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("confirmacaoUtilizacaoGuiaProcedimentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtNumeroChave;
    }

}
