package br.com.celk.view.vigilancia.questionariopopulacaocaesgatos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.vigilancia.dto.RelatorioRelacaoPopulacaoCaesGatosDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 1176
 */
@Private
public class RelatorioRelacaoPopulacaoCaesGatosPage extends RelatorioPage<RelatorioRelacaoPopulacaoCaesGatosDTOParam> {


    @Override
    public void init(Form<RelatorioRelacaoPopulacaoCaesGatosDTOParam> form) {
        RelatorioRelacaoPopulacaoCaesGatosDTOParam proxy = on(RelatorioRelacaoPopulacaoCaesGatosDTOParam.class);

        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new InputField(path(proxy.getBairro())));
        form.add(getDropDownTipoAnimal(path(proxy.getTipoAnimal())));
        form.add(getDropDownSexo(path(proxy.getSexo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioRelacaoPopulacaoCaesGatosDTOParam.FormaApresentacao.values()));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));

    }

    private DropDown<Long> getDropDownTipoAnimal(String id) {
        DropDown<Long> dropDownTipoAnimal = new DropDown(id);
        dropDownTipoAnimal.addChoice(null, bundle("ambos"));
        dropDownTipoAnimal.addChoice(0l, bundle("cachorro"));
        dropDownTipoAnimal.addChoice(1l, bundle("gato"));
        return dropDownTipoAnimal;
    }

    private DropDown<String> getDropDownSexo(String id) {
        DropDown<String> dropDownSexo = DropDownUtil.getIEnumDropDown(id, RepositoryComponentDefault.SexoAnimal.values(), true, bundle("ambos"));
        return dropDownSexo;
    }

    @Override
    public Class<RelatorioRelacaoPopulacaoCaesGatosDTOParam> getDTOParamClass() {
        return RelatorioRelacaoPopulacaoCaesGatosDTOParam.class;
    }

    @Override
    public Object getDataReport(RelatorioRelacaoPopulacaoCaesGatosDTOParam param) throws ReportException, ValidacaoException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRelacaoPopulacaoCaesGatos(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoPopulacaoCaesGatos");
    }
}
