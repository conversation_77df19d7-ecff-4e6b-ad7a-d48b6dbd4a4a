package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioResumoEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 60
 */
@Private
public class RelatorioResumoEstoquePage extends RelatorioPage<RelatorioResumoEstoqueDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaMovimentacao;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaOrigem;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaLocalizacao autoCompleteConsultaLocalizacao;
    private AutoCompleteConsultaCentroCusto autoCompleteConsultaCentroCusto;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<Long> dropDownTipoRelatorio;
    private DropDown dropDownTipoPreco;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresaMovimentacao = new AutoCompleteConsultaEmpresa("lstEmpresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(autoCompleteConsultaEmpresaOrigem = new AutoCompleteConsultaEmpresa("lstEmpresaDestino"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("lstProduto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(autoCompleteConsultaLocalizacao = new AutoCompleteConsultaLocalizacao("lstLocalizacao"));
        form.add(autoCompleteConsultaCentroCusto = new AutoCompleteConsultaCentroCusto("lstCentroCusto"));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getDropDownTipoRelatorio());
        form.add(getDropDownTipoMovimentacao());//na tela tipo
        form.add(getDropDownTipo()); // na tela avalia tipo de docto
        form.add(getDropDownFormaApresentacao());
        form.add(DropDownUtil.getNaoSimDropDown("agruparEmpresa")); // na tela avalia tipo de docto
        form.add(getDropDownOrdenacao());
        form.add(DropDownUtil.getTipoRelatorioDropDown("tipoArquivo"));
        form.add(getDropDownTipoPreco());

        autoCompleteConsultaEmpresaMovimentacao.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresaMovimentacao.setOperadorValor(true);
        autoCompleteConsultaEmpresaOrigem.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresaOrigem.setOperadorValor(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setOperadorValor(true);
        autoCompleteConsultaLocalizacao.setMultiplaSelecao(true);
        autoCompleteConsultaLocalizacao.setOperadorValor(true);
        autoCompleteConsultaCentroCusto.setMultiplaSelecao(true);
        autoCompleteConsultaCentroCusto.setOperadorValor(true);
        dropDownTipoPreco.setComponentValue(RepositoryComponentDefault.TipoPreco.PRECO_MEDIO);

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioResumoEstoqueDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoEstoqueDTOParam param) throws ReportException {
        OperadorValor<List<GrupoProduto>> grupoProdutoVazio = new OperadorValor<List<GrupoProduto>>();
        param.setLstGrupoProduto(grupoProdutoVazio);
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioResumoEstoque(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoMovimentacaoEstoque");
    }

    public DropDown getDropDownTipo() {
        DropDown dropDown = new DropDown("tipo");
        dropDown.addChoice(new Long(ReportProperties.TIPO_NAO), BundleManager.getString("nao"));
        dropDown.addChoice(new Long(ReportProperties.TIPO_SIM), BundleManager.getString("sim"));

        return dropDown;
    }

    public DropDown getDropDownTipoRelatorio() {
        dropDownTipoRelatorio = new DropDown<>("tipoRelatorio");
        dropDownTipoRelatorio.addChoice(new Long(ReportProperties.DETALHADO), BundleManager.getString("detalhado"));
        dropDownTipoRelatorio.addChoice(new Long(ReportProperties.RESUMIDO), BundleManager.getString("resumido"));
        dropDownTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                Long opcao = dropDownTipoRelatorio.getComponentValue();
                if (new Long(ReportProperties.DETALHADO).equals(opcao)) {
                    dropDownTipoPreco.setEnabled(true);
                } else {
                    dropDownTipoPreco.setEnabled(false);
                    dropDownTipoPreco.setComponentValue(RepositoryComponentDefault.TipoPreco.PRECO_MEDIO);
                }
                target.add(dropDownTipoPreco);
            }
        });

        return dropDownTipoRelatorio;
    }

    public DropDown getDropDownTipoMovimentacao() { //na tela tipo
        DropDown dropDown = new DropDown("tipoMovimentacao");
        dropDown.addChoice(ReportProperties.AMBOS, BundleManager.getString("ambos"));
        dropDown.addChoice(TipoDocumento.IS_ENTRADA, BundleManager.getString("entrada"));
        dropDown.addChoice(TipoDocumento.IS_SAIDA, BundleManager.getString("saida"));

        return dropDown;
    }

    private DropDown getDropDownTipoPreco() {
        if (dropDownTipoPreco == null) {
            dropDownTipoPreco = DropDownUtil.getEnumDropDown("tipoPreco", RepositoryComponentDefault.TipoPreco.values());
            dropDownTipoPreco.add(new Tooltip().setText("msgTooltipTipoPreco"));
        }
        return dropDownTipoPreco;
    }

    public DropDown getDropDownOrdenacao() {
        DropDown dropDown = new DropDown("ordenacao");
        dropDown.addChoice(Produto.PROP_CODIGO, BundleManager.getString("codigo"));
        dropDown.addChoice(Produto.PROP_DESCRICAO, BundleManager.getString("descricao"));

        return dropDown;
    }

    public DropDown getDropDownFormaApresentacao() {
        DropDown dropDown = new DropDown("formaApresentacao");
        dropDown.addChoice(new Long(ReportProperties.GERAL), BundleManager.getString("geral"));
        dropDown.addChoice(new Long(ReportProperties.AGRUPAR_GRUPO), BundleManager.getString("grupo"));
        dropDown.addChoice(new Long(ReportProperties.AGRUPAR_EMPRESA), BundleManager.getString("unidadeDestinoOrigem"));
        dropDown.addChoice(new Long(ReportProperties.AGRUPAR_CENTRO_CUSTO), BundleManager.getString("centroCusto"));

        return dropDown;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }
}
