package br.com.celk.view.vigilancia.questionarioclassificacao;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.questionarioclassificacao.customize.CustomizeConsultaFatorRiscoSanitario;
import br.com.celk.view.vigilancia.tabelacnae.autocomplete.AutoCompleteConsultaTabelaCnae;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Programa - 1168
 */
public class ConsultaFatorRiscoSanitarioPage extends ConsultaPage<FatorRiscoCnae, List<BuilderQueryCustom.QueryParameter>> {

    private TabelaCnae tabelaCnae;
    private AutoCompleteConsultaTabelaCnae autoCompleteConsultaTabelaCnae;
    private Long ativo = RepositoryComponentDefault.SIM_LONG;
    public ConsultaFatorRiscoSanitarioPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaTabelaCnae = new AutoCompleteConsultaTabelaCnae("tabelaCnae", new PropertyModel<TabelaCnae>(this, "tabelaCnae")));
        form.add(DropDownUtil.getSimNaoLongDropDown("ativo", true, false));

        setExibeExpandir(true);

        getPageableTable().getDataProvider().setParameters(getParameters());

    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(FatorRiscoCnae.class);
        FatorRiscoCnae proxy = on(FatorRiscoCnae.class);

        columns.add(getActionColumn());
        columns.add(columnFactory.createSortableColumn(bundle("cnae"),path(proxy.getTabelaCnae().getCnae())));
        columns.add(columnFactory.createSortableColumn(bundle("descricao"), path(proxy.getTabelaCnae().getDescricaoAtividadeFormatado())));
        columns.add(columnFactory.createSortableColumn(bundle("classificacao"),path(proxy.getClassificacaoRisco().getDescricao())));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<FatorRiscoCnae>() {
            @Override
            public void customizeColumn(final FatorRiscoCnae rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<FatorRiscoCnae>() {
                    @Override
                    public void action(AjaxRequestTarget target, FatorRiscoCnae modelObject) throws ValidacaoException, DAOException {
                        List<PerguntaFatorRiscoCnaePre> listPre = getListPre(modelObject);
                        List<PerguntaFatorRiscoCnaeCondicao> listCondicao = getListCondicao(modelObject);
                        setResponsePage(new CadastroFatorRiscoSanitarioPage(modelObject, listPre, listCondicao));
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<FatorRiscoCnae>() {
                    @Override
                    public void action(AjaxRequestTarget target, FatorRiscoCnae modelObject) throws ValidacaoException, DAOException {
                        List<PerguntaFatorRiscoCnaePre> listPre = getListPre(modelObject);
                        List<PerguntaFatorRiscoCnaeCondicao> listCondicao = getListCondicao(modelObject);
                        setResponsePage(new CadastroFatorRiscoSanitarioPage(modelObject, listPre, listCondicao,false));
                    }
                });
                addAction(ActionType.REATIVAR, rowObject, new IModelAction<FatorRiscoCnae>() {
                    @Override
                    public void action(AjaxRequestTarget target, FatorRiscoCnae modelObject) throws ValidacaoException, DAOException {
                        modelObject.setStatus(RepositoryComponentDefault.SIM_LONG);
                        BOFactoryWicket.save(modelObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(rowObject.getStatus().equals(RepositoryComponentDefault.NAO_LONG));
                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<FatorRiscoCnae>() {
                    @Override
                    public void action(AjaxRequestTarget target, FatorRiscoCnae modelObject) throws ValidacaoException, DAOException {
                        modelObject.setStatus(RepositoryComponentDefault.NAO_LONG);
                        BOFactoryWicket.save(modelObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(rowObject.getStatus().equals(RepositoryComponentDefault.SIM_LONG));
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaFatorRiscoSanitario()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(FatorRiscoCnae.PROP_DATA_CRIACAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FatorRiscoCnae.PROP_TABELA_CNAE),tabelaCnae));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FatorRiscoCnae.PROP_STATUS), ativo));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroFatorRiscoSanitarioPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDeFatorRiscoSanitario");
    }

    private List<PerguntaFatorRiscoCnaePre> getListPre(FatorRiscoCnae fatorRiscoCnae) {
        return LoadManager.getInstance(PerguntaFatorRiscoCnaePre.class)
                .addProperties(new HQLProperties(PerguntaFatorRiscoCnaePre.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(PerguntaFatorRiscoCnaePre.PROP_FATOR_RISCO_CNAE, fatorRiscoCnae))
                .start().getList();
    }

    private List<PerguntaFatorRiscoCnaeCondicao> getListCondicao(FatorRiscoCnae fatorRiscoCnae) {
        return LoadManager.getInstance(PerguntaFatorRiscoCnaeCondicao.class)
                .addProperties(new HQLProperties(PerguntaFatorRiscoCnaeCondicao.class).getProperties())
                .addProperties(new HQLProperties(ClassificacaoGrupoEstabelecimento.class, PerguntaFatorRiscoCnaeCondicao.PROP_CLASSIFICACAO_RISCO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(PerguntaFatorRiscoCnaeCondicao.PROP_FATOR_RISCO_CNAE, fatorRiscoCnae))
                .start().getList();
    }
}
