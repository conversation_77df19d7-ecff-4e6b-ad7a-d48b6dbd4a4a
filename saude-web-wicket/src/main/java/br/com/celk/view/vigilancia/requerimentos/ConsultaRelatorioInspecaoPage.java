package br.com.celk.view.vigilancia.requerimentos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.motivovisita.AutoCompleteConsultaMotivoVista;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.CadastroAutoIntimacaoPage;
import br.com.celk.view.vigilancia.rotinas.autoinfracao.CadastroAutoInfracaoPage;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaRelatorioInspecaoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioInspecaoSanitariaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 787
 */
@Private
public class ConsultaRelatorioInspecaoPage extends ConsultaPage<RelatorioInspecao, QueryConsultaRelatorioInspecaoDTOParam> {

    private QueryConsultaRelatorioInspecaoDTOParam param;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(param = new QueryConsultaRelatorioInspecaoDTOParam()));
        QueryConsultaRelatorioInspecaoDTOParam proxy = on(QueryConsultaRelatorioInspecaoDTOParam.class);

        form.add(new InputField<String>(path(proxy.getNumRelaorioInspecao())));
        form.add(new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getVigilanciaPessoa())));
        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new PnlDatePeriod(path(proxy.getPeriodoCadastro())));
        form.add(new AutoCompleteConsultaMotivoVista(path(proxy.getMotivoVisita())));
        form.add(new DateChooser(path(proxy.getDataInspecao())));

        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        RelatorioInspecao proxy = on(RelatorioInspecao.class);
        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("numero"), proxy.getNumRelatorioInspecaoFormatado()));
        columns.add(createColumn(bundle("numRelaorioInspecao"), proxy.getNumRelatorioInspecao()));
        columns.add(createSortableColumn(bundle("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("dataInspecao"), proxy.getDataInspecao()));
        columns.add(createSortableColumn(bundle("inspecionado"), proxy.getDescricaoInspecionado()));
        columns.add(createSortableColumn(bundle("motivoVisita"), proxy.getMotivoVisita().getDescricao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<RelatorioInspecao>() {

            @Override
            public void customizeColumn(final RelatorioInspecao rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<RelatorioInspecao>() {
                    @Override
                    public void action(AjaxRequestTarget target, RelatorioInspecao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRelatorioInspecaoPage(modelObject, ConsultaRelatorioInspecaoPage.class));
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<RelatorioInspecao>() {
                    @Override
                    public void action(AjaxRequestTarget target, RelatorioInspecao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRelatorioInspecaoPage(modelObject, true, ConsultaRelatorioInspecaoPage.class));
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<RelatorioInspecao>() {
                    @Override
                    public DataReport action(RelatorioInspecao modelObject) throws ReportException {
                        RelatorioInspecaoSanitariaDTOParam paramInspecao = new RelatorioInspecaoSanitariaDTOParam();
                        paramInspecao.setRelatorioInspecao(modelObject);
                        paramInspecao.setRequerimentoVigilancia(modelObject.getRequerimentoVigilancia());
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(paramInspecao);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<RelatorioInspecao>() {
                    @Override
                    public void action(AjaxRequestTarget target, RelatorioInspecao modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).removerRelatorioInspecao(rowObject);
                        getPageableTable().populate();
                        getPageableTable().update(target);
                    }
                });

                addAction(ActionType.EVOLUIR, rowObject, new IModelAction<RelatorioInspecao>() {
                    @Override
                    public void action(AjaxRequestTarget target, RelatorioInspecao modelObject) throws ValidacaoException, DAOException {
                        redirecionarAutoIntimacao(target, modelObject);
                    }
                }).setTitleBundleKey("autoIntimacao").setEnabled(rowObject.getAutoInfracao() == null && !AutosHelper.hasAutoIntimacao(rowObject));

                addAction(ActionType.NEW_DOC, rowObject, new IModelAction<RelatorioInspecao>() {
                    @Override
                    public void action(AjaxRequestTarget target, RelatorioInspecao modelObject) throws ValidacaoException, DAOException {
                        redirecionarAutoInfracao(target, modelObject);
                    }
                }).setTitleBundleKey("autoInfracao").setEnabled(rowObject.getAutoInfracao() == null && !AutosHelper.hasAutoInfracao(rowObject));

            }
        };
    }

    private void redirecionarAutoIntimacao(AjaxRequestTarget target, RelatorioInspecao relatorioInspecao) {
        CadastroAutoIntimacaoPage cadAutoIntimacao = new CadastroAutoIntimacaoPage(relatorioInspecao) {
            @Override
            public Class getResponsePage() {
                return ConsultaRelatorioInspecaoPage.class;
            }
        };

        cadAutoIntimacao.instanceFromRelatorioInspecao(target);

        setResponsePage(cadAutoIntimacao);
    }

    private void redirecionarAutoInfracao(AjaxRequestTarget target, RelatorioInspecao relatorioInspecao) {
        CadastroAutoInfracaoPage cadAutoInfracao = new CadastroAutoInfracaoPage(relatorioInspecao) {
            @Override
            public Class getResponsePage() {
                return ConsultaRelatorioInspecaoPage.class;
            }
        };

        cadAutoInfracao.instanceFromRelatorioInspecao(target);

        setResponsePage(cadAutoInfracao);
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<RelatorioInspecao, QueryConsultaRelatorioInspecaoDTOParam>() {
            @Override
            public DataPagingResult<RelatorioInspecao> executeQueryPager(DataPaging<QueryConsultaRelatorioInspecaoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setAscending(((SingleSortState) getPagerProvider().getSortState()).getSort().isAscending());
                dataPaging.getParam().setPropSort(((SingleSortState<String>) getPagerProvider().getSortState()).getSort().getProperty());
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarRelatorioInspecao(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(RelatorioInspecao.PROP_CODIGO), false);
            }
        };
    }
    
    @Override
    public QueryConsultaRelatorioInspecaoDTOParam getParameters() {
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRelatorioInspecaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRelatoriosInspecao");
    }

}
