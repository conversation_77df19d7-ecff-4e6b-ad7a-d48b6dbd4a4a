package br.com.celk.view.patrimonio.bempatrimonio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.patrimonio.bempatrimonio.customize.CustomizeConsultaBemPatrimonio;
import br.com.celk.view.patrimonio.bempatrimonio.dlg.DlgAlterarBemPatrimonio;
import br.com.celk.view.patrimonio.setor.autocomplete.AutoCompleteConsultaSetor;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.patrimonio.interfaces.facade.PatrimonioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.patrimonio.BemPatrimonio;
import br.com.ksisolucoes.vo.patrimonio.BemPatrimonioOcorrencia;
import br.com.ksisolucoes.vo.patrimonio.Setor;
import static ch.lambdaj.Lambda.*;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 381
 */
@Private
public class ConsultaBensPatrimonioPage extends ConsultaPage<BemPatrimonio, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long referencia;
    private Empresa empresa;
    private Setor setor;

    private DlgRemoverBemPatrimonio dlgRemoverBemPatrimonio;

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaSetor autoCompleteConsultaSetor;

    private DlgAlterarBemPatrimonio dlgAlterarBemPatrimonio;
    private DlgMotivoObject<BemPatrimonio> dlgMotivo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa");
        autoCompleteConsultaSetor = new AutoCompleteConsultaSetor("setor");

        form.add(new InputField("descricao"));
        form.add(autoCompleteConsultaEmpresa);
        form.add(autoCompleteConsultaSetor);
        form.add(new InputField("referencia"));

        addModal(dlgRemoverBemPatrimonio = new DlgRemoverBemPatrimonio(newModalId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, BemPatrimonio bemPatrimonio, String descricao, String motivo) throws ValidacaoException, DAOException {
                deletar(target, bemPatrimonio, descricao, motivo);
            }
        });

        setExibeExpandir(true);
    }

    private void deletar(AjaxRequestTarget target, BemPatrimonio bemPatrimonio, String descricao, String motivo) throws DAOException, ValidacaoException {
        if (motivo == null) {
            throw new ValidacaoException(BundleManager.getString("informeMotivo"));
        }
        descricao = descricao + ", " + bundle("motivoConcatenar") + motivo;

        BOFactoryWicket.getBO(PatrimonioFacade.class).cancelarBemPatrimonio(bemPatrimonio, descricao);

        getPageableTable().update(target);
        info(target, bundle("ocorrenciaBemSucesso"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        BemPatrimonio proxy = on(BemPatrimonio.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("codigo"), proxy.getReferencia()));
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        columns.add(createSortableColumn(bundle("dataAquisicao"), proxy.getDataAquisicao()));
        columns.add(createSortableColumn(bundle("estabelecimento"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("setor"), proxy.getSetor().getDescricao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<BemPatrimonio>() {
            @Override
            public void customizeColumn(BemPatrimonio rowObject) {
                ArrayList<ModelActionLinkPanel> lstAction;
                ModelActionLinkPanel actionEditar = addAction(ActionType.EDITAR, rowObject, new IModelAction<BemPatrimonio>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemPatrimonio modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroBemPatrimonioPage(modelObject, true));
                    }
                });
                ModelActionLinkPanel actionCancelar = addAction(ActionType.CANCELAR, rowObject, new IModelAction<BemPatrimonio>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemPatrimonio modelObject) throws ValidacaoException, DAOException {
                        dlgRemoverBemPatrimonio.setModelObject(target, modelObject);
                        dlgRemoverBemPatrimonio.show(target);
                        getPageableTable().update(target);
                    }
                });
                actionCancelar.setVisible(!BemPatrimonio.Status.PROP_STATUS_CANCELADO.value().equals(rowObject.getStatus()));

                ModelActionLinkPanel actionReverter = addAction(ActionType.REVERTER, rowObject, new IModelAction<BemPatrimonio>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemPatrimonio modelObject) throws ValidacaoException, DAOException {
                        viewDlgMotivo(target, modelObject);
                    }
                });
                actionReverter.setVisible(BemPatrimonio.Status.PROP_STATUS_CANCELADO.value().equals(rowObject.getStatus()));
                actionReverter.setTitleBundleKey("reverterCancelamento");

                actionCancelar.setQuestionDialogBundleKey(null);
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<BemPatrimonio>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemPatrimonio modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new ConsultaBemPatrimonioPage(modelObject));
                    }
                });
                addAction(ActionType.OCORRENCIA, rowObject, new IModelAction<BemPatrimonio>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemPatrimonio modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new ConsultaBemPatrimonioOcorrenciaPage(modelObject));
                    }
                });
                ModelActionLinkPanel actionBaixar = addAction(ActionType.BAIXA, rowObject, new IModelAction<BemPatrimonio>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemPatrimonio modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroBemPatrimonioBaixaPage(modelObject));
                    }
                });
                ModelActionLinkPanel actionTransferir = addAction(ActionType.TRANSFERENCIA, rowObject, new IModelAction<BemPatrimonio>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemPatrimonio modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroBemPatrimonioTransferenciaPage(modelObject));
                    }
                });
                actionTransferir.setEnabled(BemPatrimonio.Status.PROP_STATUS_ATIVO.value().equals(rowObject.getStatus()));
                
                ModelActionLinkPanel actionAlterar = addAction(ActionType.ALTERAR_BEM_PATRIMONIO, rowObject, new IModelAction<BemPatrimonio>() {
                    @Override
                    public void action(AjaxRequestTarget target, BemPatrimonio modelObject) throws ValidacaoException, DAOException {
                        viewDlgAlterarBemPatrimonio(target, modelObject);
                    }
                });
                actionAlterar.setTitleBundleKey("alterarCodigoBemPatrimonio");

                lstAction = new ArrayList<ModelActionLinkPanel>();
                lstAction.add(actionAlterar);
                lstAction.add(actionEditar);
                lstAction.add(actionCancelar);
                lstAction.add(actionBaixar);
                lstAction.add(actionTransferir);
                customizeActions(lstAction, rowObject);
            }
        };
    }

    private void customizeActions(ArrayList<ModelActionLinkPanel> lstAction, BemPatrimonio object) {
        for (ModelActionLinkPanel malp : lstAction) {
            if (object.getStatus().equals(BemPatrimonio.Status.PROP_STATUS_ATIVO.value())) {
                malp.setEnabled(true);
            } else {
                malp.setEnabled(false);
            }
        }
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaBemPatrimonio()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(BemPatrimonio.PROP_STATUS, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(BemPatrimonio.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(BemPatrimonio.PROP_EMPRESA, empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(BemPatrimonio.PROP_SETOR, setor));
        parameters.add(new QueryCustom.QueryCustomParameter(BemPatrimonio.PROP_REFERENCIA, referencia));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroBemPatrimonioPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaBensPatrimonio");
    }

    public void viewDlgAlterarBemPatrimonio(AjaxRequestTarget target, BemPatrimonio bemPatrimonio) {
        if (dlgAlterarBemPatrimonio == null) {
            addModal(target, dlgAlterarBemPatrimonio = new DlgAlterarBemPatrimonio(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, BemPatrimonio bemPatrimonio, Long novaReferencia) {
                    BemPatrimonioOcorrencia ocorrencia = new BemPatrimonioOcorrencia();
                    ocorrencia.setBemPatrimonio(bemPatrimonio);
                    ocorrencia.setDataOcorrencia(Data.getDataAtual());
                    ocorrencia.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
                    ocorrencia.setDescricao(bundle("numeroPatrimonioAlteradoDePara_X", bemPatrimonio.getReferencia(), novaReferencia));
                    try {
                        BOFactoryWicket.save(ocorrencia);
                        bemPatrimonio.setReferencia(novaReferencia);
                        BOFactoryWicket.save(bemPatrimonio);
                    } catch (DAOException ex) {
                        Logger.getLogger(ConsultaBensPatrimonioPage.class.getName()).log(Level.SEVERE, null, ex);
                    } catch (ValidacaoException ex) {
                        Logger.getLogger(ConsultaBensPatrimonioPage.class.getName()).log(Level.SEVERE, null, ex);
                    }
                    getPageableTable().populate();
                    getPageableTable().update(target);
                }
            });
        }
        dlgAlterarBemPatrimonio.show(target, bemPatrimonio);
    }

    public void viewDlgMotivo(AjaxRequestTarget target, BemPatrimonio bemPatrimonio) {
        if (dlgMotivo == null) {
            addModal(target, dlgMotivo = new DlgMotivoObject<BemPatrimonio>(newModalId(), bundle("reverterCancelamento")) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, BemPatrimonio bemPatrimonio) throws ValidacaoException, DAOException {
                    BemPatrimonioOcorrencia ocorrencia = new BemPatrimonioOcorrencia();
                    ocorrencia.setBemPatrimonio(bemPatrimonio);
                    ocorrencia.setDataOcorrencia(Data.getDataAtual());
                    ocorrencia.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
                    ocorrencia.setDescricao(bundle("desfazerCancelamentoBemPatrimonio_X", motivo));
                    try {
                        BOFactoryWicket.save(ocorrencia);
                        bemPatrimonio.setStatus(BemPatrimonio.Status.PROP_STATUS_ATIVO.value());
                        BOFactoryWicket.save(bemPatrimonio);
                    } catch (DAOException ex) {
                        Logger.getLogger(ConsultaBensPatrimonioPage.class.getName()).log(Level.SEVERE, null, ex);
                    } catch (ValidacaoException ex) {
                        Logger.getLogger(ConsultaBensPatrimonioPage.class.getName()).log(Level.SEVERE, null, ex);
                    }
                    getPageableTable().populate();
                    getPageableTable().update(target);
                }
            });
        }
        dlgMotivo.setObject(bemPatrimonio);
        dlgMotivo.show(target);
    }
}
