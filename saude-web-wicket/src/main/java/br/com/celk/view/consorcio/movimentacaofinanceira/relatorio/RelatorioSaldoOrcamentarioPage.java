package br.com.celk.view.consorcio.movimentacaofinanceira.relatorio;

import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioSaldoOrcamentarioDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Arrays;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 * Programa - 305
 */
@Private

public class RelatorioSaldoOrcamentarioPage extends RelatorioPage<RelatorioSaldoOrcamentarioDTOParam> {
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaConsorciado;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaConsorciado = new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));        
        form.add(new AutoCompleteConsultaTipoConta("tipoConta").setValidarVisivelConsorciado(true));
        form.add(DropDownUtil.getAnoDropDown("ano", false));
    }
    
    @Override
    public Class<RelatorioSaldoOrcamentarioDTOParam> getDTOParamClass() {
        return RelatorioSaldoOrcamentarioDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioSaldoOrcamentarioDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioSaldoOrcamentario(param);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("saldoOrcamentario");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaConsorciado;
    }

}
