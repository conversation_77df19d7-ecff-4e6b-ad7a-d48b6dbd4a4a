package br.com.celk.view.vigilancia.dengue.armadilhavisita.relatorio;

import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.dengue.autocomplete.AutoCompleteConsultaDengueLocalidade;
import br.com.celk.view.vigilancia.dengue.autocomplete.AutoCompleteConsultaDengueTipoImovel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.vigilancia.dengue.armadilhavisita.dto.ResumoVisitaArmadilhaDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueAreaVigilancia;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilha;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueCiclo;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueMicroAreaVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 653
 */
public class ResumoVisitaArmadilhaPage extends RelatorioPage<ResumoVisitaArmadilhaDTOParam> {

    private DropDown cbxArea;
    private DropDown cbxMicroArea;
    private AutoCompleteConsultaDengueLocalidade autoCompleteConsultaLocalidade;

    @Override
    public void init(Form<ResumoVisitaArmadilhaDTOParam> form) {

        ResumoVisitaArmadilhaDTOParam proxy = on(ResumoVisitaArmadilhaDTOParam.class);

        form.add(new AutoCompleteConsultaDengueTipoImovel(path(proxy.getTipoImovel())));
        form.add(getDropDownArea(path(proxy.getArea())));
        form.add(getDropDownMicroArea(path(proxy.getMicroArea())));
        form.add(autoCompleteConsultaLocalidade = new AutoCompleteConsultaDengueLocalidade(path(proxy.getLocalidade())));
        autoCompleteConsultaLocalidade.setEnabled(false);
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoArmadilha()), DengueArmadilha.TipoArmadilha.values(), true, bundle("todos")));
        form.add(new PnlDatePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), ResumoVisitaArmadilhaDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoResumo()), ResumoVisitaArmadilhaDTOParam.TipoResumo.values()));

    }

    public DropDown getDropDownArea(String id) {
        cbxArea = new DropDown(id);
        cbxArea.addAjaxUpdateValue();

        List<DengueAreaVigilancia> lstArea = LoadManager.getInstance(DengueAreaVigilancia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DengueAreaVigilancia.PROP_SITUACAO, DengueAreaVigilancia.Situacao.ATIVO.value()))
                .start().getList();

        cbxArea.addChoice(null, bundle("selecioneArea"));
        if (CollectionUtils.isNotNullEmpty(lstArea)) {
            for (DengueAreaVigilancia area : lstArea) {
                cbxArea.addChoice(area, area.getDescricao());
            }
        }

        cbxArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                cbxMicroArea.removeAllChoices(target);
                cbxMicroArea.addChoice(null, bundle("selecioneMicroArea"));
                DengueAreaVigilancia area = (DengueAreaVigilancia) cbxArea.getComponentValue();
                if (area != null) {
                    List<DengueMicroAreaVigilancia> lstMicroArea = LoadManager.getInstance(DengueMicroAreaVigilancia.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(DengueMicroAreaVigilancia.PROP_SITUACAO, DengueMicroAreaVigilancia.Situacao.ATIVO.value()))
                            .addParameter(new QueryCustom.QueryCustomParameter(DengueMicroAreaVigilancia.PROP_DENGUE_AREA_VIGILANCIA, area))
                            .start().getList();
                    if (CollectionUtils.isNotNullEmpty(lstMicroArea)) {
                        for (DengueMicroAreaVigilancia microAreaVigilancia : lstMicroArea) {
                            cbxMicroArea.addChoice(microAreaVigilancia, microAreaVigilancia.getDescricao());
                        }
                    }
                }else{
                    autoCompleteConsultaLocalidade.limpar(target);
                    autoCompleteConsultaLocalidade.setEnabled(false);
                    target.add(autoCompleteConsultaLocalidade);
                }
                target.add(cbxArea);
            }
        });

        return cbxArea;
    }

    public DropDown getDropDownMicroArea(String id) {
        cbxMicroArea = new DropDown(id);
        cbxMicroArea.addAjaxUpdateValue();
        cbxMicroArea.addChoice(null, bundle("selecioneMicroArea"));
        cbxMicroArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                DengueMicroAreaVigilancia microArea = (DengueMicroAreaVigilancia) cbxMicroArea.getComponentValue();
                if (microArea != null) {
                    autoCompleteConsultaLocalidade.limpar(target);
                    autoCompleteConsultaLocalidade.setEnabled(true);
                    autoCompleteConsultaLocalidade.setMicroArea(microArea);
                    target.add(autoCompleteConsultaLocalidade);
                } else {
                    autoCompleteConsultaLocalidade.setEnabled(false);
                    autoCompleteConsultaLocalidade.limpar(target);
                    target.add(autoCompleteConsultaLocalidade);
                }
            }
        });
        return cbxMicroArea;
    }

    public DropDown getDripDownCiclo(String id) {
        DropDown dropDownCiclo = new DropDown(id);
        dropDownCiclo.addChoice(null, "");

        List<DengueCiclo> lstCiclo = LoadManager.getInstance(DengueCiclo.class).start().getList();

        for (DengueCiclo ciclo : lstCiclo) {
            String descricao = ciclo.getCodigo().toString()+ "/" + String.valueOf(DataUtil.getAno());
            dropDownCiclo.addChoice(ciclo, descricao);
        }
        return dropDownCiclo;
    }

    @Override
    public Class<ResumoVisitaArmadilhaDTOParam> getDTOParamClass() {
        return ResumoVisitaArmadilhaDTOParam.class;

    }

    @Override
    public DataReport getDataReport(ResumoVisitaArmadilhaDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).resumoVisitaArmadilha(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("resumoVisitaArmadilha");
    }

}
