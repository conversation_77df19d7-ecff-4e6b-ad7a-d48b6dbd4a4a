package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.faixaetaria.autocomplete.AutoCompleteConsultaFaixaEtaria;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProdutoMulti;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioDispensacaoMedicamentoParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.io.IOException;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 178
 */
@Private
public class RelatorioDispensacaoMedicamentoPage extends RelatorioPage<RelatorioDispensacaoMedicamentoParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<String> dropDownVisualizaPreco;
    private DropDown<String> dropDownTipoPreco;
    private DropDown dropDownFormaApresentacao;
    private DropDown dropDownTipoResumo;
    private DropDown<Long> dropDownTipoRelatorio;
    private DropDown<String> dropDownOrdenacao;
    private DropDown<String> dropDownTipoOrdenacao;
    private AutoCompleteConsultaFaixaEtaria autoCompleteConsultaFaixaEtaria;
    private DropDown<String> dropDownVisualizarQuantidade;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private AutoCompleteConsultaProdutoMulti autoCompleteConsultaProdutoMulti;
    private DropDown<String> dropDownVisualizarPosologia;
    private DropDown<EquipeArea> dropDownEquipeArea;
    private DropDown<EquipeMicroArea> dropDownEquipeMicroArea;
    private DropDown<TipoReceita> dropDownTipoReceita;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresaDispensadoraList")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);

        form.add(new AutoCompleteConsultaEmpresa("empresaOrigemList")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));

        form.add(new AutoCompleteConsultaUsuarioCadsus("usuarioCadsusDestinoList")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));

        form.add(autoCompleteConsultaProdutoMulti = new AutoCompleteConsultaProdutoMulti("produtoList"));
        autoCompleteConsultaProdutoMulti.setIncluirInativo(true);
        form.add(new AutoCompleteConsultaProfissional("profissionalDispensadorList")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissionalPrescritorList")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));

        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(dropDownVisualizaPreco = DropDownUtil.getNaoSimDropDown("visualizarPreco"));
        dropDownVisualizaPreco.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (param != null) {
                    if (RepositoryComponentDefault.SIM.equals(param.getVisualizarPreco())) {
                        dropDownTipoPreco.setEnabled(true);
                    } else {
                        dropDownTipoPreco.setEnabled(false);
                        dropDownTipoPreco.setComponentValue(EstoqueEmpresa.PROP_PRECO_MEDIO);
                    }
                    target.add(dropDownTipoPreco);
                }
            }
        });

        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(getDropDownTipoPreco());
        form.add(getDropDownFormaApresentacao());
        form.add(getTipoRelatorio());
        form.add(getOrdenacao());
        form.add(getTipoResumo());
        form.add(getTipoOrdenacao());
        form.add(DropDownUtil.getSimNaoDropDown("agruparEmpresa"));
        form.add(getDropDownEquipeArea("equipeArea"));
        form.add(getDropDownEquipeMicroArea("equipeMicroArea"));
        form.add(getDropDownTipoReceita());
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));
        form.add(DropDownUtil.getNaoSimLongDropDown("listarDispensacoesProcedSelecionados"));

        form.add(dropDownVisualizarPosologia = DropDownUtil.getNaoSimDropDown("visualizarPosologia"));
        form.add(dropDownVisualizarQuantidade = DropDownUtil.getSimNaoDropDown("visualizarQuantidade"));
        form.add(autoCompleteConsultaFaixaEtaria = new AutoCompleteConsultaFaixaEtaria("faixaEtaria"));
        dropDownTipoPreco.setEnabled(false);
        dropDownTipoResumo.setEnabled(false);
        dropDownVisualizarQuantidade.setEnabled(false);
    }

    public DropDown<String> getTipoOrdenacao() {
        if (dropDownTipoOrdenacao == null) {
            dropDownTipoOrdenacao = new DropDown<String>("tipoOrdenacao");

            dropDownTipoOrdenacao.addChoice(QuerySorter.CRESCENTE, BundleManager.getString("crescente"));
            dropDownTipoOrdenacao.addChoice(QuerySorter.DECRESCENTE, BundleManager.getString("decrescente"));
        }

        return dropDownTipoOrdenacao;
    }

    public DropDown getTipoResumo() {
        if (dropDownTipoResumo == null) {
            dropDownTipoResumo = DropDownUtil.getIEnumDropDown("tipoResumo", RelatorioDispensacaoMedicamentoParam.TipoResumo.values());
        }

        return dropDownTipoResumo;
    }

    public DropDown<String> getOrdenacao() {
        if (dropDownOrdenacao == null) {
            dropDownOrdenacao = new DropDown<String>("ordem");

            tipoOrdenacaoDetalhado();
        }

        return dropDownOrdenacao;
    }

    public DropDown<Long> getTipoRelatorio() {
        if (dropDownTipoRelatorio == null) {
            dropDownTipoRelatorio = new DropDown<Long>("tipoRelatorio");
            dropDownTipoRelatorio.addChoice(new Long(ReportProperties.DETALHADO), BundleManager.getString("detalhado"));
            dropDownTipoRelatorio.addChoice(new Long(ReportProperties.RESUMIDO), BundleManager.getString("resumido"));

            dropDownTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (param != null) {
                        if (new Long(ReportProperties.RESUMIDO).equals(param.getTipoRelatorio())) {
                            dropDownTipoResumo.setEnabled(true);
                            dropDownOrdenacao.removeAllChoices();
                            dropDownVisualizarQuantidade.setEnabled(true);
                            dropDownVisualizarPosologia.setEnabled(false);

                            tipoOrdenacaoResumido();
                        } else {
                            dropDownTipoResumo.setEnabled(false);
                            dropDownTipoResumo.setComponentValue(RelatorioDispensacaoMedicamentoParam.PRODUTO);
                            dropDownOrdenacao.removeAllChoices();
                            dropDownVisualizarQuantidade.setEnabled(false);
                            dropDownVisualizarPosologia.setEnabled(true);

                            tipoOrdenacaoDetalhado();
                        }

                        target.add(dropDownTipoResumo);
                        target.add(dropDownOrdenacao);
                        target.add(dropDownVisualizarQuantidade);
                        target.add(dropDownVisualizarPosologia);
                    }
                }
            });
        }
        return dropDownTipoRelatorio;
    }

    public DropDown getDropDownFormaApresentacao() {
        if (dropDownFormaApresentacao == null) {
            dropDownFormaApresentacao = DropDownUtil.getIEnumDropDown("formaApresentacao", RelatorioDispensacaoMedicamentoParam.FormaApresentacao.values());
        }

        return dropDownFormaApresentacao;
    }

    public DropDown getDropDownTipoPreco() {
        if (dropDownTipoPreco == null) {
            dropDownTipoPreco = new DropDown<String>("tipoPreco");

            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_PRECO_MEDIO, BundleManager.getString("precoMedio"));
            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_ULTIMO_PRECO, BundleManager.getString("precoCusto"));
        }

        return dropDownTipoPreco;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));
            for (GrupoProduto grupoProduto : grupos) {
                dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
            }

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                    }
                    dropDownSubGrupo.limpar(target);
                }
            });
        }
        return this.dropDownGrupoProduto;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<EquipeArea> getDropDownEquipeArea(String id) {
        if (dropDownEquipeArea == null) {
            dropDownEquipeArea = new DropDown<>(id);

            List<EquipeArea> areas = LoadManager.getInstance(EquipeArea.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), SessaoAplicacaoImp.getInstance().getEmpresa().getCidade()))
                    .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                    .start().getList();

            dropDownEquipeArea.addChoice(null, BundleManager.getString("todas"));
            for (EquipeArea area: areas) {
                dropDownEquipeArea.addChoice(area, area.getDescricao());
            }

            dropDownEquipeArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownEquipeMicroArea.removeAllChoices();
                    dropDownEquipeMicroArea.addChoice(null, BundleManager.getString("todas"));
                    if (param.getEquipeArea() != null) {
                        List<EquipeMicroArea> microAreas = LoadManager.getInstance(EquipeMicroArea.class)
                                .addProperty(EquipeMicroArea.PROP_CODIGO)
                                .addProperty(EquipeMicroArea.PROP_MICRO_AREA)
                                .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))

                                .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, param.getEquipeArea()))
                                .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                                .start().getList();
                        for (EquipeMicroArea microArea : microAreas) {
                            dropDownEquipeMicroArea.addChoice(microArea, Coalesce.asString(microArea.getDescricaoMicroArea()));
                        }
                    }
                    target.add(dropDownEquipeMicroArea);
                }
            });
        }
        return dropDownEquipeArea;
    }

    private DropDown<EquipeMicroArea> getDropDownEquipeMicroArea(String id) {
        if (dropDownEquipeMicroArea == null) {
            dropDownEquipeMicroArea = new DropDown<>(id);
            dropDownEquipeMicroArea.addChoice(null, BundleManager.getString("selecioneArea"));
        }
        return dropDownEquipeMicroArea;
    }

    public DropDown<TipoReceita> getDropDownTipoReceita() {
        if (dropDownTipoReceita == null) {
            dropDownTipoReceita = new DropDown<TipoReceita>("tipoReceita");
            dropDownTipoReceita.addChoice(null, BundleManager.getString("todas"));

            List<TipoReceita> tiposReceita = LoadManager.getInstance(TipoReceita.class)
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(TipoReceita.PROP_DESCRICAO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();
            if (br.com.celk.util.CollectionUtils.isNotNullEmpty(tiposReceita)) {
                for (TipoReceita tr : tiposReceita) {
                    dropDownTipoReceita.addChoice(tr, tr.getDescricao());
                }
            }
        }
        return dropDownTipoReceita;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioDispensacaoMedicamentoParam.class;
    }

    @Override
    public void gerarRelatorio(AjaxRequestTarget target) throws IOException, ReportException, ValidacaoException {
        if ((RelatorioDispensacaoMedicamentoParam.FAIXA_ETARIA.equals(param.getFormaApresentacao()) || RelatorioDispensacaoMedicamentoParam.FAIXA_ETARIA.equals(param.getTipoResumo())) && autoCompleteConsultaFaixaEtaria.getComponentValue() == null) {
            throw new ReportException("O campo Faixa Etária é Obrigatório!");
        }
        super.gerarRelatorio(target);
    }

    @Override
    public DataReport getDataReport(RelatorioDispensacaoMedicamentoParam param) throws ReportException {
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioDispensacaoMedicamento(param, true);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioDispensacaoMedicamentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    private void tipoOrdenacaoDetalhado() {
        this.dropDownOrdenacao.addChoice(DispensacaoMedicamento.REF, BundleManager.getString("numeroDispensacao"));
        this.dropDownOrdenacao.addChoice(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, BundleManager.getString("dataPrescricao"));
        this.dropDownOrdenacao.addChoice(DispensacaoMedicamentoItem.PROP_PRODUTO, BundleManager.getString("produto"));
    }

    private void tipoOrdenacaoResumido() {
        this.dropDownOrdenacao.addChoice(null, BundleManager.getString("tipoResumo"));
        this.dropDownOrdenacao.addChoice(DispensacaoMedicamentoItem.PROP_QUANTIDADE_DISPENSADA, BundleManager.getString("quantidadeDispensadaAbv"));
    }

}
