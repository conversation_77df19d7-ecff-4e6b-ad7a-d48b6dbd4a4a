package br.com.celk.view.materiais.laudomedicamentoespecial;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.unidadesaude.exames.modelolaudo.CadastroModeloLaudoExamePage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.LaudoMedicamentosEspeciais;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 909
 */
@Private
public class ConsultaLaudoMedicamentoEspecialPage extends ConsultaPage<LaudoMedicamentosEspeciais, List<BuilderQueryCustom.QueryParameter>> {

    private String nomePaciente;
    private Empresa unidadeSolicitante;
    private DatePeriod periodoSolicitacao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField("nomePaciente"));
        form.add(new PnlDatePeriod("periodoSolicitacao"));
        form.add(new AutoCompleteConsultaEmpresa("unidadeSolicitante"));

        getLinkNovo().setVisible(false);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(LaudoMedicamentosEspeciais.class);

        LaudoMedicamentosEspeciais proxy = on(LaudoMedicamentosEspeciais.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(bundle("paciente"), path(proxy.getAtendimento().getUsuarioCadsus().getNome()), path(proxy.getAtendimento().getUsuarioCadsus().getNomeSocial())));
        columns.add(columnFactory.createSortableColumn(bundle("telefone"), path(proxy.getAtendimento().getUsuarioCadsus().getCelularOuTelefoneFormatado())));
        columns.add(columnFactory.createSortableColumn(bundle("unidadeSolicitante"), path(proxy.getAtendimento().getEmpresa().getDescricao())));
        columns.add(columnFactory.createSortableColumn(bundle("dataSolicitacaoAbv"), path(proxy.getDataAlteracao())));
        columns.add(columnFactory.createSortableColumn(bundle("situacao"), path(proxy.getDescricaoStatus())));

        return columns;
    }

    private MultipleActionCustomColumn<LaudoMedicamentosEspeciais> getCustomColumn() {
        return new MultipleActionCustomColumn<LaudoMedicamentosEspeciais>() {
            @Override
            public void customizeColumn(final LaudoMedicamentosEspeciais rowObject) {
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<LaudoMedicamentosEspeciais>() {
                    @Override
                    public DataReport action(LaudoMedicamentosEspeciais modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioLaudoMedicamentosEspeciais(modelObject);
                    }
                }).setEnabled(!LaudoMedicamentosEspeciais.Status.CANCELADO.value().equals(rowObject.getStatus()));
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return LaudoMedicamentosEspeciais.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(LaudoMedicamentosEspeciais.class).getProperties(),
                        new String[]{
                    VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),
                    VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO),
                    VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL),
                    VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CELULAR),
                    VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_TELEFONE),
                    VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO)
                });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_DATA_ALTERACAO), false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        if (nomePaciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))))));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA), unidadeSolicitante));
        if(periodoSolicitacao != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoMedicamentosEspeciais.PROP_DATA_ALTERACAO), Data.adjustRangeHour(periodoSolicitacao)));
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroModeloLaudoExamePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaLme");
    }
}
