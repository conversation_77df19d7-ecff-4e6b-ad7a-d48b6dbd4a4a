package br.com.celk.view.materiais.horus;

import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.component.table.ITable;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.materiais.horus.RegistroHorusProcesso;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;

import static ch.lambdaj.Lambda.*;

import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.materiais.horus.interfaces.dto.ConsultaDadosEnviadosHorusDTO;
import br.com.ksisolucoes.bo.materiais.horus.interfaces.dto.ConsultaDadosEnviadosHorusDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;

import static br.com.ksisolucoes.system.methods.CoreMethods.*;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso.TipoSincronizacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 * Programa - 548
 */

public class ConsultaDadosEnviadosHorusPage extends BasePage {

    private Form<ConsultaDadosEnviadosHorusDTOParam> form;
    private WebMarkupContainer fieldBusca;

    private PageableTable<ConsultaDadosEnviadosHorusDTO> tblConsultaDispensacao;
    private PageableTable<ConsultaDadosEnviadosHorusDTO> tblConsultaEntrada;
    private PageableTable<ConsultaDadosEnviadosHorusDTO> tblConsultaSaida;
    private PageableTable<ConsultaDadosEnviadosHorusDTO> tblConsultaPosicaoEstoque;
    private ConsultaDadosEnviadosHorusDTOParam param;
    private DropDown<Long> dropDownTipo;
    private DropDown<Long> dropDownStatus;
    private MesAnoField competencia;
    private ProcurarButton btnProcurar;
    private SincronizacaoHorusProcesso sincronizacaoHorusProcesso;
    private boolean isVoltar;

    public ConsultaDadosEnviadosHorusPage() {
        initForm();
    }

    ConsultaDadosEnviadosHorusPage(SincronizacaoHorusProcesso sincronizacaoHorusProcesso, boolean isVoltar) {
        this.sincronizacaoHorusProcesso = sincronizacaoHorusProcesso;
        this.isVoltar = isVoltar;
        initForm();
    }

    private void initForm() {

        ConsultaDadosEnviadosHorusDTOParam proxy = on(ConsultaDadosEnviadosHorusDTOParam.class);
        param = new ConsultaDadosEnviadosHorusDTOParam();
        getForm();
        form.add(fieldBusca = new WebMarkupContainer("fieldBusca"));


        fieldBusca.setEnabled(true);
        fieldBusca.add(getDropDownTipoSincronizacao(proxy));
        fieldBusca.add(getDropDownStatus(proxy));
        fieldBusca.add(new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        fieldBusca.add(new AutoCompleteConsultaProduto(path(proxy.getProduto())));
        fieldBusca.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getPaciente())));
        fieldBusca.add(competencia = new MesAnoField(path(proxy.getCompetencia())));

        adicionaTables();

        form.add(btnProcurar = new ProcurarButton("btnProcurar") {

            @Override
            public ConsultaDadosEnviadosHorusDTOParam getParam() {
                return param;
            }

            @Override
            public void procurar() {
                //Executado quando carrega a pagina ouo vem da integração
                if (sincronizacaoHorusProcesso != null) {
                    param.setSincronizacaoHorusProcesso(sincronizacaoHorusProcesso);
                    TipoSincronizacao tipo = TipoSincronizacao.valueOf(sincronizacaoHorusProcesso.getTipoSincronizacao());
                    switch (tipo) {
                        case POSICAO_ESTOQUE: super.setTargetComponent(tblConsultaPosicaoEstoque); break;
                        case SAIDA: super.setTargetComponent(tblConsultaSaida); break;
                        case DISPENSASAO: super.setTargetComponent(tblConsultaDispensacao); break;
                        default: super.setTargetComponent(tblConsultaEntrada); break;
                    }
                } else {
                    super.setTargetComponent(tblConsultaEntrada);
                }
                super.procurar();
            }

            @Override
            public void antesProcurar(AjaxRequestTarget target) throws ValidacaoException {
                super.setTargetComponent(getTabelaSincronizacao(target));
            }

            @Override
            public void procurar(AjaxRequestTarget target) {
                //Executado quando clica no botao
                param.setSincronizacaoHorusProcesso(null);
                super.procurar(target);

            }

        });

        form.add(new VoltarButton("btnVoltar") {

            @Override
            public boolean isVisible() {
                return isVoltar;
            }

        });

        add(form);

        carregaDadosIniciais(sincronizacaoHorusProcesso);
    }

    private void carregaDadosIniciais(SincronizacaoHorusProcesso shp) {
        if (Objects.nonNull(shp)) {
            fieldBusca.setEnabled(false);
            dropDownTipo.setComponentValue(shp.getTipoSincronizacao().longValue());
            dropDownStatus.setComponentValue(shp.getStatus().longValue());
            competencia.setComponentValue(shp.getCompetencia());
            btnProcurar.setVisible(false);
            setVisible(TipoSincronizacao.valueOf(shp.getTipoSincronizacao()), null);
        } else {
            param.setTipoSincronizacao(TipoSincronizacao.ENTRADA.value());
            setVisible(TipoSincronizacao.ENTRADA, null);
        }
        btnProcurar.procurar();
    }

    private void setVisible(TipoSincronizacao tipoSincronizacao, AjaxRequestTarget target) {
        tblConsultaEntrada.add(new AttributeModifier("style", "display: " + (tipoSincronizacao.equals(TipoSincronizacao.ENTRADA) ? "block" : "none")));
        tblConsultaPosicaoEstoque.add(new AttributeModifier("style", "display: " + (tipoSincronizacao.equals(TipoSincronizacao.POSICAO_ESTOQUE) ? "block" : "none")));
        tblConsultaDispensacao.add(new AttributeModifier("style", "display: " + (tipoSincronizacao.equals(TipoSincronizacao.DISPENSASAO) ? "block" : "none")));
        tblConsultaSaida.add(new AttributeModifier("style", "display: " + (tipoSincronizacao.equals(TipoSincronizacao.SAIDA) ? "block" : "none")));
        if (Objects.nonNull(target)) {
            target.add(tblConsultaEntrada, tblConsultaPosicaoEstoque, tblConsultaDispensacao, tblConsultaSaida);
        }
    }

    private void adicionaTables() {
        form.add(tblConsultaDispensacao = new PageableTable("tblConsultaDispensacao", getColumns(SincronizacaoHorusProcesso.TipoSincronizacao.DISPENSASAO), getPagerProvider()));
        tblConsultaDispensacao.setOutputMarkupId(true);
        tblConsultaDispensacao.populate();

        form.add(tblConsultaSaida = new PageableTable("tblConsultaSaida", getColumns(SincronizacaoHorusProcesso.TipoSincronizacao.SAIDA), getPagerProvider()));
        tblConsultaSaida.setOutputMarkupId(true);
        tblConsultaSaida.populate();

        form.add(tblConsultaEntrada = new PageableTable("tblConsultaEntrada", getColumns(SincronizacaoHorusProcesso.TipoSincronizacao.ENTRADA), getPagerProvider()));
        tblConsultaEntrada.setOutputMarkupId(true);
        tblConsultaEntrada.populate();

        form.add(tblConsultaPosicaoEstoque = new PageableTable("tblConsultaPosicaoEstoque", getColumns(SincronizacaoHorusProcesso.TipoSincronizacao.POSICAO_ESTOQUE), getPagerProvider()));
        tblConsultaPosicaoEstoque.setOutputMarkupId(true);
        tblConsultaPosicaoEstoque.populate();
    }

    private ITable getTabelaSincronizacao(AjaxRequestTarget target) {
        TipoSincronizacao tipo = TipoSincronizacao.valueOf(Long.parseLong(dropDownTipo.getValue()));
        if (Objects.nonNull(target)) setVisible(tipo, target);
        switch (tipo) {
            case POSICAO_ESTOQUE:
                return tblConsultaPosicaoEstoque;
            case SAIDA:
                return tblConsultaSaida;
            case DISPENSASAO:
                return tblConsultaDispensacao;
            default:
                return tblConsultaEntrada;
        }
    }

    private DropDown getDropDownTipoSincronizacao(ConsultaDadosEnviadosHorusDTOParam proxy) {
        dropDownTipo = new DropDown<Long>(path(proxy.getTipoSincronizacao()));
        SincronizacaoHorusProcesso.TipoSincronizacao[] values = SincronizacaoHorusProcesso.TipoSincronizacao.values();

        for (SincronizacaoHorusProcesso.TipoSincronizacao tipoSincronizacao : values) {
            dropDownTipo.addChoice(tipoSincronizacao.value(), tipoSincronizacao.descricao());
        }
        return dropDownTipo;
    }

    private DropDown getDropDownStatus(ConsultaDadosEnviadosHorusDTOParam proxy) {
        dropDownStatus = new DropDown<Long>(path(proxy.getStatus()));
        SincronizacaoHorusProcesso.Status[] values = SincronizacaoHorusProcesso.Status.values();
        dropDownStatus.addChoice(null, " ");

        for (SincronizacaoHorusProcesso.Status status : values) {
            if(!status.equals(SincronizacaoHorusProcesso.Status.SEM_REGISTRO))
                dropDownStatus.addChoice(status.value(), status.descricao());
        }
        return dropDownStatus;
    }

    private void getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<ConsultaDadosEnviadosHorusDTOParam>(param = new ConsultaDadosEnviadosHorusDTOParam()));
        }
    }

    public List getColumns(SincronizacaoHorusProcesso.TipoSincronizacao tipoSincronizacao) {
        ConsultaDadosEnviadosHorusDTO proxy = on(ConsultaDadosEnviadosHorusDTO.class);
        List<IColumn> columns = new ArrayList<IColumn>();
        columns.add(new DateColumn(BundleManager.getString("competencia"), path(proxy.getCompetencia())).setPattern("MM/yyyy"));
        columns.add(createSortableColumn(BundleManager.getString("catmat"), proxy.getNuProduto()));
        columns.add(createSortableColumn(BundleManager.getString("produto"), proxy.getProduto().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("lote"), proxy.getLote()));
        columns.add(createSortableColumn(BundleManager.getString("unidade"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("cnes"), proxy.getEmpresa().getCnes()));
        if (Objects.nonNull(tipoSincronizacao) && tipoSincronizacao.equals(SincronizacaoHorusProcesso.TipoSincronizacao.SAIDA)) {
            columns.add(createSortableColumn(BundleManager.getString("rotulo_unidade_destino"), proxy.getEmpresaDestino().getDescricao()));
            columns.add(createSortableColumn(BundleManager.getString("cnes"), proxy.getEmpresaDestino().getCnes()));
        }
        columns.add(createSortableColumn(BundleManager.getString("quantidade_abv_2"), proxy.getQuantidade()));
        if (Objects.nonNull(tipoSincronizacao) && tipoSincronizacao.equals(SincronizacaoHorusProcesso.TipoSincronizacao.DISPENSASAO)) {
            columns.add(createSortableColumn(BundleManager.getString("paciente"), proxy.getPaciente().getNome()));
        }

        if (Objects.isNull(tipoSincronizacao)) {
            columns.add(createSortableColumn(BundleManager.getString("valorUnitario"), proxy.getValorUnitario()));
        } else if (Objects.nonNull(tipoSincronizacao) && (tipoSincronizacao.equals(SincronizacaoHorusProcesso.TipoSincronizacao.ENTRADA)
                || tipoSincronizacao.equals(SincronizacaoHorusProcesso.TipoSincronizacao.POSICAO_ESTOQUE))) {
            columns.add(createSortableColumn(BundleManager.getString("valorUnitario"), proxy.getValorUnitario()));
        }
        columns.add(new DateColumn(BundleManager.getString("dataRegistroAbrev"), path(proxy.getDataRegistro())));
        columns.add(new DateColumn(BundleManager.getString("dataValidadeAbv"), path(proxy.getDataValidade())));
        columns.add(createSortableColumn(BundleManager.getString("status"), proxy.getStatus(), proxy.getDescricaoStatus()));
        return columns;
    }

    public IPagerProvider getPagerProvider() {
        return new QueryPagerProvider<ConsultaDadosEnviadosHorusDTO, ConsultaDadosEnviadosHorusDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(MaterialBasicoFacade.class).consultarDadosEnviadosHorus(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
               return new SortParam(VOUtils.montarPath(RegistroHorusProcesso.PROP_PRODUTO, Produto.PROP_DESCRICAO), true);
            }
           @Override
           public void customizeParam(ConsultaDadosEnviadosHorusDTOParam param) {
                if(Objects.nonNull(param) && Objects.nonNull(getSort())) {
                    param.setCampoOrdenacao(getSort().getProperty());
                    param.setIsAscending(getSort().isAscending());
                }
           }


        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDadosEnviadosHorus");
    }

}
