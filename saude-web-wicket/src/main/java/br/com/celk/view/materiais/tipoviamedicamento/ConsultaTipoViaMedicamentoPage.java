package br.com.celk.view.materiais.tipoviamedicamento;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.materiais.tipoviamedicamento.customize.CustomizeConsultaTipoViaMedicamento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 69
 */
@Private
public class ConsultaTipoViaMedicamentoPage extends ConsultaPage<TipoViaMedicamento, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private String referencia;

    public ConsultaTipoViaMedicamentoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new UpperField("referencia"));
        form.add(new UpperField("descricao"));

        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(TipoViaMedicamento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(TipoViaMedicamento.PROP_REFERENCIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(TipoViaMedicamento.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("sigla"), VOUtils.montarPath(TipoViaMedicamento.PROP_SIGLA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("ordem"), VOUtils.montarPath(TipoViaMedicamento.PROP_ORDEM)));

        return columns;
    }

    private CustomColumn<TipoViaMedicamento> getCustomColumn() {
        return new CustomColumn<TipoViaMedicamento>() {

            @Override
            public Component getComponent(String componentId, final TipoViaMedicamento rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoViaMedicamentoPage(rowObject, false, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoViaMedicamentoPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaTipoViaMedicamento()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TipoViaMedicamento.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoViaMedicamento.PROP_REFERENCIA), BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, referencia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoViaMedicamento.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoViaMedicamentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaDoTipoDeViaDeMedicamento");
    }
}
