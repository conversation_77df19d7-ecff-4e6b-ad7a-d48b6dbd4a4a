package br.com.celk.view.materiais.estoque.inventario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgMotivoArea;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacaoestrutura.autocomplete.AutoCompleteConsultaLocalizacaoEstrutura;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.ControleInventario;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;
import br.com.ksisolucoes.vo.entradas.estoque.Localizacao;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;


/**
 * <AUTHOR> Américo
 * Programa - 899
 */
@Private
public class ConsultaInventarioPage extends ConsultaPage<Inventario, List<BuilderQueryCustom.QueryParameter>> {

    private String descricaoInventario;
    private Empresa estabelecimento;
    private LocalizacaoEstrutura localizacaoEstrutura;
    private DatePeriod periodo;
    private Long situacao;

    private PnlDatePeriod pnlDatePeriado;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEstabelecimento;
    private AutoCompleteConsultaLocalizacaoEstrutura autoCompleteConsultaLocalizacaoEstrutura;
    private InputField inputDescricao;
    private DropDown dropDownSituacao;
    private DlgMotivoArea<Inventario> dlgMotivoArea;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(inputDescricao = new InputField("descricaoInventario"));
        form.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEmpresa("estabelecimento"));
        form.add(autoCompleteConsultaLocalizacaoEstrutura = new AutoCompleteConsultaLocalizacaoEstrutura("localizacaoEstrutura").setExibirApenasVisivelSim(true));
        form.add(dropDownSituacao = DropDownUtil.getIEnumDropDown("situacao", Inventario.Situacao.values(), true, BundleManager.getString("todos"), false, false, true));
        form.add(pnlDatePeriado = new PnlDatePeriod("periodo"));
        autoCompleteConsultaEstabelecimento.setEnabled(false);
        autoCompleteConsultaEstabelecimento.setComponentValue(((Empresa)SessaoAplicacaoImp.getInstance().getEmpresa()));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        Inventario proxy = on(Inventario.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricaoInventario()));
        columns.add(createSortableColumn(bundle("dataInventario"), proxy.getDataInventario()));
        columns.add(createSortableColumn(bundle("estabelecimento"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("localizacao"), proxy.getLocalizacaoEstrutura().getMascara()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacao(), proxy.getDescricaoSituacao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<Inventario>() {
            @Override
            public void customizeColumn(final Inventario rowObject) {
                boolean existeControleInventario = LoadManager.getInstance(ControleInventario.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_INVENTARIO, rowObject))
                        .addParameter(new QueryCustom.QueryCustomParameter(ControleInventario.PROP_RO_EMPRESA, ((Empresa)SessaoAplicacaoImp.getInstance().getEmpresa())))
                        .exists();

                addAction(ActionType.EDITAR, rowObject, new IModelAction<Inventario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Inventario modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroInventarioPage(modelObject));
                    }
                }).setEnabled(Inventario.Situacao.ABERTO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<Inventario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Inventario modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().populate(target);
                    }
                }).setEnabled(Inventario.Situacao.ABERTO.value().equals(rowObject.getSituacao()) && !existeControleInventario);

                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<Inventario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Inventario modelObject) throws ValidacaoException, DAOException {
                        desativarInventario(target, modelObject);
                    }
                }).setTitleBundleKey("fechar").setQuestionDialogBundleKey("desejaRealmenteFechar")
                        .setVisible(Inventario.Situacao.ABERTO.value().equals(rowObject.getSituacao()))
                        .setEnabled(Inventario.Situacao.ABERTO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.REATIVAR, rowObject, new IModelAction<Inventario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Inventario modelObject) throws ValidacaoException, DAOException {
                        modelObject.setSituacao(Inventario.Situacao.ABERTO.value());
                        BOFactoryWicket.save(modelObject);
                        getPageableTable().populate(target);
                    }
                }).setTitleBundleKey("reabrir").setQuestionDialogBundleKey("desejaRealmenteReabrir")
                        .setVisible(Inventario.Situacao.FECHADO.value().equals(rowObject.getSituacao()) ||
                                Inventario.Situacao.PROCESSADO.value().equals(rowObject.getSituacao()))
                        .setEnabled(Inventario.Situacao.FECHADO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<Inventario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Inventario modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroInventarioPage(modelObject, true));
                    }
                });
            }

        };
    }

    private void desativarInventario(AjaxRequestTarget target, Inventario inventario) {
        if (dlgMotivoArea == null) {
            addModal(target, dlgMotivoArea = new DlgMotivoArea(newModalId(), BundleManager.getString("motivoFechamento")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, Object object) throws ValidacaoException, DAOException {
                    Inventario in = (Inventario) object;
                    in.setSituacao(Inventario.Situacao.FECHADO.value());
                    in.setDataFechamento(DataUtil.getDataAtual());
                    in.setUsuarioFechamento(SessaoAplicacaoImp.getInstance().getUsuario());
                    in.setMotivoFechamento(motivo);
                    BOFactoryWicket.save(in);
                    getPageableTable().populate(target);
                }
            });
        }
        dlgMotivoArea.setObject(inventario);
        dlgMotivoArea.show(target);
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return Inventario.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(Inventario.class).getProperties(),
                        new HQLProperties(LocalizacaoEstrutura.class, Inventario.PROP_LOCALIZACAO_ESTRUTURA).getProperties(),
                        new String[]{
                                VOUtils.montarPath(Inventario.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_LOCALIZACAO, Localizacao.PROP_CODIGO),
                                VOUtils.montarPath(Inventario.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_LOCALIZACAO, Localizacao.PROP_SIGLA),
                                VOUtils.montarPath(Inventario.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_LOCALIZACAO, Localizacao.PROP_DESCRICAO),
                        });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Inventario.PROP_DATA_INVENTARIO, false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();

        parameters.add(new QueryCustom.QueryCustomParameter(Inventario.PROP_DESCRICAO_INVENTARIO, BuilderQueryCustom.QueryParameter.ILIKE, descricaoInventario));
        parameters.add(new QueryCustom.QueryCustomParameter(Inventario.PROP_EMPRESA, BuilderQueryCustom.QueryParameter.IGUAL, estabelecimento));
        parameters.add(new QueryCustom.QueryCustomParameter(Inventario.PROP_LOCALIZACAO_ESTRUTURA, BuilderQueryCustom.QueryParameter.IGUAL, localizacaoEstrutura));
        parameters.add(new QueryCustom.QueryCustomParameter(Inventario.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.IGUAL, situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(Inventario.PROP_DATA_INVENTARIO, periodo));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroInventarioPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaInventario");
    }
}
