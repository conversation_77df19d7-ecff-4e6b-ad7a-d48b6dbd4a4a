package br.com.celk.view.materiais.recebimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.tipodocumento.autocomplete.AutoCompleteConsultaTipoDocumento;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.dto.RelatorioRegistroItemNotaFiscalParam;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.facade.RegistroNotaFiscalReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 102
 */
@Private
public class RelatorioRegistroItemNotaFiscalPage extends RelatorioPage<RelatorioRegistroItemNotaFiscalParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private AutoCompleteConsultaPessoa autoCompleteConsultaPessoa;
    private AutoCompleteConsultaTipoDocumento autoCompleteConsultaTipoDocumento;
    private DropDown<String> dropDownOrdenacao;
    private DropDown<Long> dropDownTipoRelatorio;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("lstEmpresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("lstProduto"));
        form.add(autoCompleteConsultaPessoa = new AutoCompleteConsultaPessoa("lstFornecedor"));
        form.add(autoCompleteConsultaTipoDocumento = new AutoCompleteConsultaTipoDocumento("lstTipoDocumento"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(new InputField<Long>("numeroNota"));
        form.add(getDropDownTipoData());
        form.add(getDropDownTipoRelatorio());
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownOrdenacao());
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));

        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaProduto.setIncluirInativos(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setOperadorValor(true);
        autoCompleteConsultaPessoa.setMultiplaSelecao(true);
        autoCompleteConsultaPessoa.setOperadorValor(true);
        autoCompleteConsultaTipoDocumento.setMultiplaSelecao(true);
        autoCompleteConsultaTipoDocumento.setOperadorValor(true);

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRegistroItemNotaFiscalParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRegistroItemNotaFiscalParam param) throws ReportException {
        return BOFactoryWicket.getBO(RegistroNotaFiscalReportFacade.class).getRelatorioRegistroItemNotaFiscal(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoNotasFiscais");
    }

    public DropDown getDropDownTipoRelatorio() {
        if (dropDownTipoRelatorio == null) {
            dropDownTipoRelatorio = new DropDown<Long>("tipoRelatorio");
            dropDownTipoRelatorio.addChoice(new Long(ReportProperties.DETALHADO), BundleManager.getString("detalhado"));
            dropDownTipoRelatorio.addChoice(new Long(ReportProperties.RESUMIDO), BundleManager.getString("resumido"));

            dropDownTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget art) {
                    if (new Long(ReportProperties.DETALHADO).equals(dropDownTipoRelatorio.getComponentValue())) {
                        dropDownOrdenacao.removeAllChoices();
                        ordenacaoDetalhado();
                        art.add(dropDownOrdenacao);
                    } else if (new Long(ReportProperties.RESUMIDO).equals(dropDownTipoRelatorio.getComponentValue())) {
                        dropDownOrdenacao.removeAllChoices();
                        ordenacaoResumido();
                        art.add(dropDownOrdenacao);
                    }
                }
            });
        }

        return dropDownTipoRelatorio;
    }

    public DropDown getDropDownOrdenacao() {
        if (dropDownOrdenacao == null) {
            dropDownOrdenacao = new DropDown<String>("ordenacao");
            ordenacaoDetalhado();
        }
        return dropDownOrdenacao;
    }

    public DropDown getDropDownFormaApresentacao() {
        DropDown dropDown = new DropDown("formaApresentacao");
        dropDown.addChoice(RegistroItemNotaFiscal.PROP_PRODUTO, BundleManager.getString("produto"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_FORNECEDOR, BundleManager.getString("fornecedor"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_PORTARIA, BundleManager.getString("dataEntrada"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_EMISSAO, BundleManager.getString("dataEmissao"));
        dropDown.addChoice(Produto.PROP_SUB_GRUPO, BundleManager.getString("grupoProdutoSubGrupo"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_TIPO_DOCUMENTO, BundleManager.getString("tipoDocumento"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_NOTA_FISCAL, BundleManager.getString("notaFiscal"));

        return dropDown;
    }

    public DropDown getDropDownTipoData() {
        DropDown dropDown = new DropDown("tipoData");
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_EMISSAO, BundleManager.getString("emissao"));
        dropDown.addChoice(RegistroNotaFiscal.PROP_DATA_PORTARIA, BundleManager.getString("entrada"));

        return dropDown;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }

    private void ordenacaoDetalhado() {
        dropDownOrdenacao.addChoice(RegistroNotaFiscal.PROP_NUMERO_NOTA_FISCAL, BundleManager.getString("documento"));
        dropDownOrdenacao.addChoice(RegistroNotaFiscal.PROP_DATA_PORTARIA, BundleManager.getString("dataEntrada"));
        dropDownOrdenacao.addChoice(RegistroNotaFiscal.PROP_DATA_EMISSAO, BundleManager.getString("dataEmissao"));
        dropDownOrdenacao.addChoice(RegistroItemNotaFiscal.PROP_PRODUTO, BundleManager.getString("produto"));
        dropDownOrdenacao.addChoice(RegistroNotaFiscal.PROP_FORNECEDOR, BundleManager.getString("fornecedor"));
    }

    private void ordenacaoResumido() {
        dropDownOrdenacao.addChoice(RegistroNotaFiscal.PROP_NUMERO_NOTA_FISCAL, BundleManager.getString("documento"));
        dropDownOrdenacao.addChoice(RegistroNotaFiscal.PROP_FORNECEDOR, BundleManager.getString("fornecedor"));
        dropDownOrdenacao.addChoice(RegistroNotaFiscal.PROP_DATA_EMISSAO, BundleManager.getString("dataEmissao"));
    }
}
