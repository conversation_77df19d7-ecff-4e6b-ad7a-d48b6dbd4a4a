package br.com.celk.view.vigilancia.escalaplantao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.unidadesaude.esus.relatorios.dto.RelatorioCondicaoMoradiaResumidoDTOParam;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryRelatorioRelacaoEscalaPlantaoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> S. Schmoeller
 * Programa - 958
 */
@Private
public class RelatorioRelacaoEscalaPlantaoPage extends RelatorioPage<QueryRelatorioRelacaoEscalaPlantaoDTOParam> {

    @Override
    public void init(Form<QueryRelatorioRelacaoEscalaPlantaoDTOParam> form) {
        QueryRelatorioRelacaoEscalaPlantaoDTOParam proxy = on(QueryRelatorioRelacaoEscalaPlantaoDTOParam.class);

        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), QueryRelatorioRelacaoEscalaPlantaoDTOParam.FormaApresentacao.values()));
//        form.add(DropDownUtil.getIEnumDropDown("formaApresentacao", RelatorioCondicaoMoradiaResumidoDTOParam.FormaApresentacao.values()));

        form.add(new PnlChoicePeriod(path(proxy.getPeriodo())));

    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioRelacaoMotivo");
    }

    @Override
    public Class<QueryRelatorioRelacaoEscalaPlantaoDTOParam> getDTOParamClass() {
        return QueryRelatorioRelacaoEscalaPlantaoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(QueryRelatorioRelacaoEscalaPlantaoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRelacaoEscalaPlantao((param));
    }

}
