package br.com.celk.view.vigilancia.dengue.consulta;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.googlemaps.GoogleMapsConf;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.enderecocoordenadas.LatitudeLongitudeEndereco;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.maps.DadosGoogleMapsHelper;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilha;
import br.com.ksisolucoes.vo.vigilancia.dengue.DenguePontoEstrategico;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.wicketstuff.gmap.GMap;
import org.wicketstuff.gmap.api.GLatLng;
import org.wicketstuff.gmap.api.GMarker;
import org.wicketstuff.gmap.api.GMarkerOptions;

import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 651
 */
@Private
public class ConsultaArmadilhaPontoEstrategicoPage extends BasePage {

    private Form form;
    private Long tipo;
    private Long situacao;
    private PnlDatePeriod datas;
    private DatePeriod periodo;
    private AbstractAjaxButton btnFiltrar;
    private GMap map;

    public ConsultaArmadilhaPontoEstrategicoPage() {
        init();
    }

    private void init() {
        getForm().add(getDropDownTipo("tipo"));
        getForm().add(getDropDownSituacao("situacao"));
        getForm().add(getPeriodo("periodo"));
        getForm().add(getBtnFiltrar("btnFiltrar"));
        getForm().add(getMap());

        add(form);
    }

    public AbstractAjaxButton getBtnFiltrar(String id) {
        btnFiltrar = new AbstractAjaxButton(id) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                filtrar(target);
            }
        };

        return btnFiltrar;
    }

    public DropDown getDropDownTipo(String id) {
        DropDown ddTipo = new DropDown(id);

        ddTipo.addChoice(DadosGoogleMapsHelper.TIPO_ARMADILHA, bundle("armadilha"));
        ddTipo.addChoice(DadosGoogleMapsHelper.TIPO_PONTO_ESTRATEGICO, bundle("pontoEstrategico"));
        ddTipo.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                tipo = Long.valueOf(ddTipo.getValue());
            }
        });

        return ddTipo;
    }

    private PnlDatePeriod getPeriodo(String id) {
        datas = new PnlDatePeriod(id);
        return datas;
    }

    public DropDown getDropDownSituacao(String id) {
        DropDown ddSituacao = new DropDown(id);

        ddSituacao.addChoice(DadosGoogleMapsHelper.SITUACAO_ATIVO, bundle("ativo"));
        ddSituacao.addChoice(DadosGoogleMapsHelper.SITUACAO_INATIVO, bundle("inativo"));

        ddSituacao.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                situacao = Long.valueOf(ddSituacao.getValue());
            }
        });

        return ddSituacao;
    }

    public GMap getMap() {
        map = new GMap("map", GoogleMapsConf.MAP_API_KEY, "https");
        map.setOutputMarkupId(true);
        map.setStreetViewControlEnabled(true);
        map.setScaleControlEnabled(true);
        map.setScrollWheelZoomEnabled(true);
        map.setDraggingEnabled(true);
        map.setPanControlEnabled(true);
        map.setDoubleClickZoomEnabled(false);
        map.setMapTypeControlEnabled(true);

        GLatLng center;
        LatitudeLongitudeEndereco latitudeLongitudeEndereco = new LatitudeLongitudeEndereco(SessaoAplicacaoImp.getInstance().getEmpresa().getCidade());
        if(latitudeLongitudeEndereco.getLatitude() != 0.0 && latitudeLongitudeEndereco.getLongitude() != 0.0) {
            center = new GLatLng(latitudeLongitudeEndereco.getLatitude(), latitudeLongitudeEndereco.getLongitude());
        } else {
            center = new GLatLng(-28.7282759, -49.3674942); // loc. de Criciúma
        }
        map.setCenter(center);
        map.setZoom(10);

        return map;
    }

    public void filtrar(AjaxRequestTarget target) {
        map.removeAllOverlays();

        if ((datas.getDataInicial() != null && datas.getDataInicial().getConvertedInput() != null) &&
                datas.getDataFinal() != null && datas.getDataFinal().getConvertedInput() != null) {
            periodo = new DatePeriod(datas.getDataInicial().getConvertedInput(), datas.getDataFinal().getConvertedInput());
        }

        if (DadosGoogleMapsHelper.TIPO_PONTO_ESTRATEGICO.equals(tipo)) {
            filterPontoEstrategico();

        } else if (DadosGoogleMapsHelper.TIPO_ARMADILHA.equals(tipo)) {
            filterDengueArmadilha();
        }

        target.add(map);
    }

    public Form getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(this));
        }
        return form;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaArmadilhaPontoEstrategico");
    }

    private void criarPonto(GLatLng latlng, String infoPonto, GMap map) {
        GMarkerOptions options = new GMarkerOptions(map, latlng, infoPonto);
        options.bouncy(true);
        options.draggable(true);
        options.autoPan(true);
        options.clickable(true);
        map.addOverlay(new GMarker(options));
    }

    private void criarInfoPonto(StringBuilder sb, Long cod, Date dataCadastro) {
        sb.append("Codigo: ");
        sb.append(cod.toString());
        sb.append(" | ");
        sb.append(Data.formatarDataHora(dataCadastro));
        sb.append("\n\n");
    }

    private List<DengueArmadilha> loadListArmadilhas() {
        LoadManager load = LoadManager.getInstance(DengueArmadilha.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DengueArmadilha.PROP_SITUACAO, situacao))
                .addParameter(new QueryCustom.QueryCustomParameter(DengueArmadilha.PROP_LATITUDE, QueryCustom.QueryCustomParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(DengueArmadilha.PROP_LONGITUDE, QueryCustom.QueryCustomParameter.IS_NOT_NULL));
        if (DadosGoogleMapsHelper.SITUACAO_ATIVO.equals(situacao)) {
            load.addParameter(new QueryCustom.QueryCustomParameter(DengueArmadilha.PROP_DATA_CADASTRO, periodo));
        } else if (DadosGoogleMapsHelper.SITUACAO_INATIVO.equals(situacao)) {
            load.addParameter(new QueryCustom.QueryCustomParameter(DengueArmadilha.PROP_DATA_INATIVACAO, periodo));
        }

        return load.start().getList();
    }

    private void filterDengueArmadilha() {
        List<DengueArmadilha> lstArmadilha = loadListArmadilhas();
        Group<DengueArmadilha> groupByLatitude = Lambda.group(lstArmadilha, by(on(DengueArmadilha.class).getLatitude()));

        for (Group<DengueArmadilha> grupoDa : groupByLatitude.subgroups()) {
            StringBuilder sb = new StringBuilder();
            GLatLng latlng = null;

            for (DengueArmadilha da : grupoDa.findAll()) {
                latlng = new GLatLng(da.getLatitude(), da.getLongitude());
                criarInfoPonto(sb, da.getCodigo(), da.getDataCadastro());
            }

            criarPonto(latlng, sb.toString(), map);
        }
    }

    private List<DenguePontoEstrategico> loadListPontoEstrategico() {
        LoadManager load = LoadManager.getInstance(DenguePontoEstrategico.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DenguePontoEstrategico.PROP_SITUACAO, situacao))
                .addParameter(new QueryCustom.QueryCustomParameter(DenguePontoEstrategico.PROP_LATITUDE, QueryCustom.QueryCustomParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(DenguePontoEstrategico.PROP_LONGITUDE, QueryCustom.QueryCustomParameter.IS_NOT_NULL));
        if (DadosGoogleMapsHelper.SITUACAO_ATIVO.equals(situacao)) {
            load.addParameter(new QueryCustom.QueryCustomParameter(DenguePontoEstrategico.PROP_DATA_CADASTRO, periodo));
        } else if (DadosGoogleMapsHelper.SITUACAO_INATIVO.equals(situacao)) {
            load.addParameter(new QueryCustom.QueryCustomParameter(DenguePontoEstrategico.PROP_DATA_INATIVACAO, periodo));
        }

        return load.setMaxResults(50).start().getList();
    }

    private void filterPontoEstrategico() {
        List<DenguePontoEstrategico> lstPontoEstrategico = loadListPontoEstrategico();

        Group<DenguePontoEstrategico> groupByLatitude = Lambda.group(lstPontoEstrategico, by(on(DenguePontoEstrategico.class).getLatitude()));

        for (Group<DenguePontoEstrategico> groupPe: groupByLatitude.subgroups()) {
            StringBuilder sb = new StringBuilder();
            GLatLng latlng = null;

            for (DenguePontoEstrategico pe : groupPe.findAll()) {
                latlng = new GLatLng(pe.getLatitude(), pe.getLongitude());
                criarInfoPonto(sb, pe.getCodigo(), pe.getDataCadastro());
            }

            criarPonto(latlng, sb.toString(), map);
        }
    }
}
