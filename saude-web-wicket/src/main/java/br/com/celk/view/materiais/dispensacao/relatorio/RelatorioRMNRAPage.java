package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRMNRADTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 263
 */
@Private
public class RelatorioRMNRAPage extends RelatorioPage<RelatorioRMNRADTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DisabledInputField<String> txtCrf;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    private String mesAno;
    private InputField<String> txtMesAno;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa", true));
        form.add(txtMesAno = new InputField<String>("mesAno", new PropertyModel<String>(this, "mesAno")));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissionalResponsavel", true));
        form.add(txtCrf = new DisabledInputField<String>("crf"));
        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                if (object != null) {
                    Profissional pro = LoadManager.getInstance(Profissional.class)
                            .addProperties(new HQLProperties(Profissional.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_CODIGO, object.getCodigo()))
                            .start().getVO();
                    txtCrf.setComponentValue(pro.getNumeroRegistro());
                    target.add(txtCrf);
                } else {
                    txtCrf.limpar(target);
                }
            }
        });
        this.txtMesAno.setComponentValue(Data.formatarMesAno(Data.getDataAtual()));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRMNRADTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRMNRADTOParam param) throws ReportException {
        param.setAnoMes(Data.parserMounthYear(mesAno));
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioRMNRAAsync(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("notificacaoReceitaA");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
