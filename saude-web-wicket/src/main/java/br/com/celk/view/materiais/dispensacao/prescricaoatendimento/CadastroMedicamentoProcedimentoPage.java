package br.com.celk.view.materiais.dispensacao.prescricaoatendimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;

import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.CadastroMedicamentoProcedimentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import org.apache.wicket.markup.html.form.Form;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;
import static ch.lambdaj.Lambda.*;
import br.com.ksisolucoes.vo.entradas.dispensacao.MedicamentoProcedimento;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetenciaPK;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 312
 */
@Private
public class CadastroMedicamentoProcedimentoPage extends BasePage {

    private Form<CadastroMedicamentoProcedimentoDTO> form;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetencia;
    private Table<MedicamentoProcedimento> tblMedicamentoProcedimento;

    public CadastroMedicamentoProcedimentoPage() {
        init();
    }

    private void init() {
        form = new Form<CadastroMedicamentoProcedimentoDTO>("form", new CompoundPropertyModel<CadastroMedicamentoProcedimentoDTO>(new CadastroMedicamentoProcedimentoDTO()));
        CadastroMedicamentoProcedimentoDTO proxy = on(CadastroMedicamentoProcedimentoDTO.class);

        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxy.getMedicamentoProcedimento().getProduto()), true));
        form.add(autoCompleteConsultaProcedimentoCompetencia = new AutoCompleteConsultaProcedimentoCompetencia(path(proxy.getProcedimentoCompetencia()), true).setValidarProcedimentoRegistro(Arrays.asList(ProcedimentoRegistroCadastro.BPA_CONSOLIDADO)));
        autoCompleteConsultaProduto.setIncluirInativos(false);
        form.add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        form.add(tblMedicamentoProcedimento = new Table("tblMedicamentoProcedimento", getColumns(), getCollectionProvider()));
        tblMedicamentoProcedimento.populate();

        add(form);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        CadastroMedicamentoProcedimentoDTO dto = form.getModelObject();

        dto.getMedicamentoProcedimento().setProcedimento(dto.getProcedimentoCompetencia().getId().getProcedimento());

        MedicamentoProcedimento mp = LoadManager.getInstance(MedicamentoProcedimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(MedicamentoProcedimento.PROP_PRODUTO, dto.getMedicamentoProcedimento().getProduto()))
                .start().getVO();

        if (mp != null) {
            throw new ValidacaoException(bundle("existeMedicamentoProcedimento"));
        }

        BOFactoryWicket.save(dto.getMedicamentoProcedimento());

        limpar(target);
        tblMedicamentoProcedimento.update(target);
    }

    private void limpar(AjaxRequestTarget target) {
        autoCompleteConsultaProduto.limpar(target);
        autoCompleteConsultaProcedimentoCompetencia.limpar(target);
        form.setModelObject(new CadastroMedicamentoProcedimentoDTO());
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        MedicamentoProcedimento proxy = on(MedicamentoProcedimento.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("medicamento"), proxy.getProduto().getDescricao()));
        columns.add(createSortableColumn(bundle("procedimento"), proxy.getProcedimento().getDescricaoFormatado()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<MedicamentoProcedimento>() {
            @Override
            public void customizeColumn(MedicamentoProcedimento rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<MedicamentoProcedimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, MedicamentoProcedimento modelObject) throws ValidacaoException, DAOException {
                        editarItem(target, modelObject);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<MedicamentoProcedimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, MedicamentoProcedimento modelObject) throws ValidacaoException, DAOException {
                        removerItem(target, modelObject);
                    }
                });
            }
        };
    }

    private void editarItem(AjaxRequestTarget target, MedicamentoProcedimento object) throws ValidacaoException {
        CadastroMedicamentoProcedimentoDTO dto = new CadastroMedicamentoProcedimentoDTO();
        dto.setMedicamentoProcedimento(object);

        ProcedimentoCompetenciaPK pk = new ProcedimentoCompetenciaPK();
        pk.setProcedimento(object.getProcedimento());
        pk.setDataCompetencia((Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO));

        ProcedimentoCompetencia pc = new ProcedimentoCompetencia();
        pc.setId(pk);

        dto.setProcedimentoCompetencia(pc);

        limpar(target);
        form.setModelObject(dto);
    }

    private void removerItem(AjaxRequestTarget target, MedicamentoProcedimento modelObject) throws DAOException, ValidacaoException {
        BOFactoryWicket.delete(modelObject);
        limpar(target);
        tblMedicamentoProcedimento.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                LoadManager load = LoadManager.getInstance(MedicamentoProcedimento.class);
                if (getSort() != null) {
                    load.addSorter(new QueryCustom.QueryCustomSorter((String) getSort().getProperty(), getSort().isAscending() ? "asc" : "desc"));
                }

                return load.start().getList();
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>(VOUtils.montarPath(MedicamentoProcedimento.PROP_PRODUTO, Produto.PROP_DESCRICAO), true);
            }
        };

    }

    @Override
    public String getTituloPrograma() {
        return bundle("medicamentoProcedimento");
    }
}
