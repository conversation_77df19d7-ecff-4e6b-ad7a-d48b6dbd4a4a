package br.com.celk.view.materiais.produtoconsorcio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.materiais.medicamento.customize.CustomizeConsultaMedicamento;
import br.com.celk.view.materiais.produtoconsorcio.customcolumn.ProdutoConsorcioColumnPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 * Programa - 179
 */
@Private
public class ConsultaProdutoConsorcioPage extends ConsultaPage<Produto, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private String referencia;
    private GrupoProduto grupoProdutoSubGrupo;
    private SubGrupo subGrupo;
    private Long disponivelPedidoLicitacao;

    private Long situacao = 1L;
    private DropDown<Produto> dropDownSituacao;
    
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        
        form.add(new UpperField("referencia"));
        form.add(new InputField("descricao"));
        form.add(dropDownSituacao = DropDownUtil.getIEnumDropDown("situacao", Produto.Situacao.values(), false, false, false,true));
        dropDownSituacao.addChoice(null,BundleManager.getString("ambos"));

        form.add(DropDownUtil.getSimNaoLongDropDown("disponivelPedidoLicitacao", true, false, bundle("ambos")));

        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());

        setExibeExpandir(true);
    }
    
    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(Produto.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(Produto.PROP_REFERENCIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Produto.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("grupo"), VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("subGrupo"), VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidade"), VOUtils.montarPath(Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), Produto.PROP_FLAG_ATIVO, Produto.PROP_DESCRICAO_SITUACAO));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("fabricante"), VOUtils.montarPath(Produto.PROP_FABRICANTE, FabricanteMedicamento.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("disponivelPedidoLicitacaoAbv"), Produto.PROP_FLAG_DISPONIVEL_PEDIDO_LICITACAO, Produto.PROP_FLAG_DISPONIVEL_PEDIDO_LICITACAO_FORMATADO));

        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<Produto>() {

            @Override
            public Component getComponent(String componentId, Produto rowObject) {
                return new ProdutoConsorcioColumnPanel(componentId, rowObject) {

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        getPageableTable().update(target);
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
       return new CustomizeConsultaPagerProvider(new CustomizeConsultaMedicamento()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Produto.PROP_DESCRICAO, true);
            }
        };
    }
    
    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_REFERENCIA), BuilderQueryCustom.QueryParameter.IGUAL, referencia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, grupoProdutoSubGrupo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_SUB_GRUPO), BuilderQueryCustom.QueryParameter.IGUAL, subGrupo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_FLAG_ATIVO), BuilderQueryCustom.QueryParameter.IGUAL, situacao));
//        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_MEDICAMENTO), BuilderQueryCustom.QueryParameter.DIFERENTE, SubGrupo.MEDICAMENTO_SIM));
//        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_FLAG_VACINA), BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.SIM_LONG));
//        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_TIPO_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, TipoProduto.TIPO_PRODUTO_MATERIAL));

        parameters.add(new QueryCustom.QueryCustomParameter(Produto.PROP_FLAG_DISPONIVEL_PEDIDO_LICITACAO, disponivelPedidoLicitacao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroProdutoConsorcioPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaProdutosConsorcio");
    }
    
    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto!=null) {
                            List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                    .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                    .start().getList();

                            if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                                dropDownSubGrupo.removeAllChoices();
                                dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                                for (SubGrupo subGrupo : subGrupos) {
                                    dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                                }
                            }
                    }
                    target.add(dropDownSubGrupo);
                }
            });
            
                List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                        .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                        .start().getList();

                dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));
                
                if (CollectionUtils.isNotNullEmpty(grupos)) {
                    for (GrupoProduto grupoProduto : grupos) {
                        dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                    }
                }
        }
        return this.dropDownGrupoProduto;
    }
    
    private DropDown<SubGrupo> getDropDownSubGrupo(){
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }
        return this.dropDownSubGrupo;
    }
}
