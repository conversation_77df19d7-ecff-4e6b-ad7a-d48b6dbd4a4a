package br.com.celk.view.materiais.emprestimo.lancamentos.registroemprestimo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.materiais.emprestimo.cadastro.autocomplete.AutoCompleteConsultaTipoEmprestimo;
import br.com.celk.bo.emprestimo.interfaces.dto.ConsultaEmprestimoDTOParam;
import br.com.celk.bo.emprestimo.interfaces.facade.EmprestimoFacade;
import br.com.celk.bo.emprestimo.interfaces.facade.EmprestimoReportFacade;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.view.materiais.emprestimo.lancamentos.registroemprestimo.dialog.DlgCancelarLancamentoEmprestimo;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;

import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimo;
import br.com.ksisolucoes.vo.emprestimo.TipoEmprestimo;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 467
 */
@Private
public class ConsultaEmprestimoPage extends ConsultaPage<LancamentoEmprestimo, ConsultaEmprestimoDTOParam> {

    private ConsultaEmprestimoDTOParam param;
    private DlgCancelarLancamentoEmprestimo dlgCancelarLancamentoEmprestimo;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel<ConsultaEmprestimoDTOParam>(param = new ConsultaEmprestimoDTOParam()));

        form.add(new InputField<String>("pacienteEstabelecimento"));
        form.add(new AutoCompleteConsultaTipoEmprestimo("tipoEmprestimo", false));
        form.add(new AutoCompleteConsultaProduto("produto", false));
        form.add(new PnlDatePeriod("periodo"));
        form.add(new InputField<Long>("codigo"));
        form.add(getDropDownSituacao("situacao"));

        setExibeExpandir(true);
    }
    
    private DropDown getDropDownSituacao(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(null, bundle("todos"));
        dropDown.addChoice(LancamentoEmprestimo.Status.PENDENTE.value(), bundle("pendente"));
        dropDown.addChoice(LancamentoEmprestimo.Status.DEVOLVIDO.value(), bundle("devolvido"));
        dropDown.addChoice(LancamentoEmprestimo.Status.CANCELADO.value(), bundle("cancelado"));

        return dropDown;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        LancamentoEmprestimo proxy = on(LancamentoEmprestimo.class);
        
        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("codigo"), proxy.getCodigo()));
        columns.add(new DateTimeColumn<LancamentoEmprestimo>(bundle("data"), path(proxy.getDataEmprestimo()), path(proxy.getDataEmprestimo())).setPattern("dd/MM/yyyy"));
        columns.add(createSortableColumn(bundle("pacienteEstabelecimento"), proxy.getNomePacienteEstabelecimento()));
        columns.add(createSortableColumn(bundle("tipoEmprestimo"), proxy.getTipoEmprestimo().getDescricaoTipoEmprestimo()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        return columns;
    }
    
    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<LancamentoEmprestimo>() {
            @Override
            public void customizeColumn(LancamentoEmprestimo rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<LancamentoEmprestimo>() {

                    @Override
                    public void action(AjaxRequestTarget target, LancamentoEmprestimo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new RegistroEmprestimoPage(modelObject));
                    }

                }).setEnabled(LancamentoEmprestimo.Status.PENDENTE.value().equals(rowObject.getStatus()));
                
                addAction(ActionType.CANCELAR, rowObject, new IModelAction<LancamentoEmprestimo>() {

                    @Override
                    public void action(AjaxRequestTarget target, LancamentoEmprestimo modelObject) throws ValidacaoException, DAOException {
                        initDlgConfirmacaoCancelar(target, modelObject);
                    }

                }).setQuestionDialogBundleKey(null).setEnabled(LancamentoEmprestimo.Status.PENDENTE.value().equals(rowObject.getStatus()) 
                        || LancamentoEmprestimo.Status.PARCIAL.value().equals(rowObject.getStatus()));
                
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<LancamentoEmprestimo>() {

                    @Override
                    public void action(AjaxRequestTarget target, LancamentoEmprestimo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesEmprestimoPage(modelObject));
                    }

                });

                try {
                    addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<LancamentoEmprestimo>() {
                        @Override
                        public DataReport action(LancamentoEmprestimo lancamentoEmprestimo) throws ReportException {
                            return BOFactoryWicket.getBO(EmprestimoReportFacade.class).comprovanteEmprestimo(lancamentoEmprestimo);
                        }
                    }).setTitleBundleKey("comprovante")
                            .setEnabled((BOFactory.getBO(CommomFacade.class)
                                    .modulo(Modulos.MATERIAIS)
                                    .getParametro("imprimirComprovanteEmprestimo")
                                    .equals(RepositoryComponentDefault.NAO) && !LancamentoEmprestimo.Status.CANCELADO.value().equals(rowObject.getStatus())
                                    && TipoEmprestimo.FlagTipoEmprestimo.SAIDA.value().equals(rowObject.getTipoEmprestimo().getTipoEmprestimo()))
                                    || (!BOFactory.getBO(CommomFacade.class)
                                    .modulo(Modulos.MATERIAIS)
                                    .getParametro("imprimirComprovanteEmprestimo")
                                    .equals(RepositoryComponentDefault.NAO)));

                } catch (DAOException e) {
                    throw new RuntimeException(e);
                }
            }
        };
    }
    
    private void initDlgConfirmacaoCancelar(AjaxRequestTarget target, LancamentoEmprestimo modelObject){
        if (dlgCancelarLancamentoEmprestimo == null) {
            addModal(target, dlgCancelarLancamentoEmprestimo = new DlgCancelarLancamentoEmprestimo(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, LancamentoEmprestimo lancamentoEmprestimo, String motivo) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(EmprestimoFacade.class).cancelarRegistroEmprestimo(lancamentoEmprestimo, motivo);
                    
                    getPageableTable().update(target);
                }
            });
        }
        dlgCancelarLancamentoEmprestimo.show(target, modelObject);
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<LancamentoEmprestimo, ConsultaEmprestimoDTOParam>() {

            @Override
            public DataPagingResult<LancamentoEmprestimo> executeQueryPager(DataPaging<ConsultaEmprestimoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(EmprestimoFacade.class).getConsultaEmprestimoQueryPager(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(LancamentoEmprestimo.PROP_CODIGO, false);
            }

            @Override
            public void customizeParam(ConsultaEmprestimoDTOParam param) {
                SingleSortState<String> sortState = (SingleSortState) getPagerProvider().getSortState();
        
                param.setCampoOrdenacao(sortState.getSort().getProperty());
                param.setTipoOrdenacao(sortState.getSort().isAscending()?"asc":"desc");
            }
        };
    }

    @Override
    public ConsultaEmprestimoDTOParam getParameters() {
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return RegistroEmprestimoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaEmprestimo");
    }
}