package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProdutoMulti;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioClassificacaoEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.Arrays;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 1027
 */
@Private
public class RelatorioClassificacaoEstoquePage extends RelatorioPage<RelatorioClassificacaoEstoqueDTOParam> {

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
//    private DropDown<Long> dropDownVisualizarConsumo;
//    private LongField txtMesesCalcMediaConsumo;

    @Override
    public void init(Form form) {
        RelatorioClassificacaoEstoqueDTOParam proxy = on(RelatorioClassificacaoEstoqueDTOParam.class);

        AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa()));
        autoCompleteConsultaEmpresa.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL, Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO, Empresa.TIPO_ESTABELECIMENTO_FARMACIA));

        AutoCompleteConsultaProdutoMulti autoCompleteConsultaProdutoMulti = new AutoCompleteConsultaProdutoMulti(path(proxy.getLstProduto()));
        autoCompleteConsultaProdutoMulti.setIncluirInativo(true);

        form.add(autoCompleteConsultaEmpresa);
        form.add(autoCompleteConsultaProdutoMulti);
        form.add(getDropDownGrupo(path(proxy.getGrupoProdutoSubGrupo())));
        form.add(getDropDownSubGrupo(path(proxy.getSubGrupo())));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getSituacaoEstoque()), RelatorioClassificacaoEstoqueDTOParam.SituacaoEstoque.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getCurvaDemanda()), RelatorioClassificacaoEstoqueDTOParam.CurvaDemanda.values(), "Todos"));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getCriticidade()), RelatorioClassificacaoEstoqueDTOParam.Criticidade.values(), "Todos"));
    }

    public DropDown getDropDownOrdenacao(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(ReportProperties.ORDENAR_DESCRICAO, BundleManager.getString("descricao"));
        dropDown.addChoice(ReportProperties.ORDENAR_CODIGO, BundleManager.getString("codigo"));

        return dropDown;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo(String id) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>(id);
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo(String id) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>(id);
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        SubGrupo proxy = on(SubGrupo.class);
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(path(proxy.getId().getCodigo()))
                                .addProperty(path(proxy.getId().getCodigoGrupoProduto()))
                                .addProperty(path(proxy.getDescricao()))
                                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getCodigoGrupoProduto()), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDescricao())))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(GrupoProduto.PROP_CODIGO)
                    .addProperty(GrupoProduto.PROP_DESCRICAO)
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioClassificacaoEstoqueDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioClassificacaoEstoqueDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioClassificacaoEstoque(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioClassificacaoEstoque");
    }
}
