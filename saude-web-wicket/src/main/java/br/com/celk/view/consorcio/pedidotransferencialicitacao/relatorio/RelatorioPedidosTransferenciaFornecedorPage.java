package br.com.celk.view.consorcio.pedidotransferencialicitacao.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioPedidosTransferenciaFornecedorDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Arrays;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;


/**
 *
 * <AUTHOR>
 * Programa - 276
 */
@Private
 
public class RelatorioPedidosTransferenciaFornecedorPage extends RelatorioPage<RelatorioPedidosTransferenciaFornecedorDTOParam> {
    
    private DropDown<String> dropDownFlagListaProdutos;
    private DropDown dropDownTipoEstoque;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaPessoa("fornecedor"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioPedidosTransferenciaFornecedorDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoResumo", RelatorioPedidosTransferenciaFornecedorDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getEnumDropDown("baseCalculo", RelatorioPedidosTransferenciaFornecedorDTOParam.BaseCalculo.values()));
        form.add(dropDownFlagListaProdutos = (DropDown<String>) DropDownUtil.getSimNaoDropDown("flagListaProdutos").add(new AjaxFormComponentUpdatingBehavior("onchange") {
                    @Override
                    protected void onUpdate(AjaxRequestTarget target) {
                        if(RepositoryComponentDefault.SIM.equals(dropDownFlagListaProdutos.getComponentValue())){
                            dropDownTipoEstoque.setEnabled(true);
                        } else{
                            dropDownTipoEstoque.setEnabled(false);
                        }
                        
                        target.add(dropDownTipoEstoque);
                    }
                })
        );
        form.add(dropDownTipoEstoque = DropDownUtil.getEnumDropDown("tipoEstoque", RelatorioPedidosTransferenciaFornecedorDTOParam.TipoEstoque.values()));

        form.add(new CheckBox("situacaoAberto"));
        form.add(new CheckBox("situacaoCancelado"));
        form.add(new CheckBox("situacaoEncaminhado"));
        form.add(new CheckBox("situacaoSeparado"));
        form.add(new CheckBox("situacaoFechado"));
    }

    @Override
    public Class<RelatorioPedidosTransferenciaFornecedorDTOParam> getDTOParamClass() {
        return RelatorioPedidosTransferenciaFornecedorDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioPedidosTransferenciaFornecedorDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioPedidosTransferenciaFornecedor(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoPedidosTransferencia");
    }
}
