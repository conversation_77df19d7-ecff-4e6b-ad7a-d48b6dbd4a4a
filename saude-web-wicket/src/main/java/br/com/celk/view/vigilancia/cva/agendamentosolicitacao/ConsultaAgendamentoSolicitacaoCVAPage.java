package br.com.celk.view.vigilancia.cva.agendamentosolicitacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.vigilancia.cva.autocomplete.AutoCompleteConsultaEspecieAnimal;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaAtividadeVeterinaria;
import br.com.celk.view.vigilancia.solicitacaoAgendamento.dialog.DlgDadosParaAgendamento;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.AgendamentoSolicitacaoCVADTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaAgendamentoSolicitacaoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RegistroAtividadeAnimal;
import br.com.ksisolucoes.vo.vigilancia.SolicitacaoAgendamentoCVA;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 * Programa - 603
 */
@Private
public class ConsultaAgendamentoSolicitacaoCVAPage extends BasePage {

    private Form<ConsultaAgendamentoSolicitacaoDTOParam> form;
    private ConsultaAgendamentoSolicitacaoDTOParam param = new ConsultaAgendamentoSolicitacaoDTOParam();
    private PageableTable<AgendamentoSolicitacaoCVADTO> tblAgendamentoSolicitacao;
    private DatePeriod periodo;
    private Empresa estabelecimento;
    private Profissional profissional;
    private Long urgencia;

    private DlgMotivoObject<SolicitacaoAgendamentoCVA> dlgMotivo;
    private DlgDadosParaAgendamento dlgDadosParaAgendamento;
    
    private IPagerProvider pagerProvider;
    private PageParameters parameters;
    private boolean procurar;

    public ConsultaAgendamentoSolicitacaoCVAPage(PageParameters parameters) {
        super(parameters);
        this.procurar = true;
        this.parameters = parameters;
        init();
    }
    
    public ConsultaAgendamentoSolicitacaoCVAPage(PageParameters parameters, boolean procurar) {
        super(parameters);
        this.parameters = parameters;
        this.procurar = procurar;
        init();
    }

    private void init() {
        ConsultaAgendamentoSolicitacaoDTOParam proxy = on(ConsultaAgendamentoSolicitacaoDTOParam.class);
        
        getForm().add(new AutoCompleteConsultaAtividadeVeterinaria(path(proxy.getAtividadeVeterinaria())));
        getForm().add(new AutoCompleteConsultaEspecieAnimal(path(proxy.getEspecieAnimal())));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getSexo()), RegistroAtividadeAnimal.Sexo.values(), true, bundle("ambos")));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getUrgencia()), RepositoryComponentDefault.SimNaoLong.values(), true, bundle("ambos")));
        getForm().add(new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        getForm().add(new InputField(path(proxy.getResponsavel())));
        
        getForm().add(tblAgendamentoSolicitacao = new PageableTable("tblAgendamentoSolicitacao", getColumns(), getPagerProvider()));
        
        ProcurarButton procurarButton;
        getForm().add(procurarButton = new ProcurarButton<ConsultaAgendamentoSolicitacaoDTOParam>("btnProcurar", tblAgendamentoSolicitacao) {
            @Override
            public ConsultaAgendamentoSolicitacaoDTOParam getParam() {
                return param;
            }
        });

        if (procurar) {
            procurarButton.procurar();
        }
        
        add(getForm());
    }
    
    private Form<ConsultaAgendamentoSolicitacaoDTOParam> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<ConsultaAgendamentoSolicitacaoDTOParam>(param));
        }
        return this.form;
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendamentoSolicitacaoCVADTO proxy = on(AgendamentoSolicitacaoCVADTO.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("dataSolicitacao"), proxy.getSolicitacaoAgendamentoCVA().getDataSolicitacao()));
        columns.add(createSortableColumn(bundle("proprietarioResponsavel"), proxy.getSolicitacaoAgendamentoCVA().getResponsavel()));
        columns.add(createSortableColumn(bundle("atividade"), proxy.getSolicitacaoAgendamentoCVA().getAtividadeVeterinaria().getDescricao()));
        columns.add(createSortableColumn(bundle("urgencia"), proxy.getSolicitacaoAgendamentoCVA().getFlagUrgente(), proxy.getSolicitacaoAgendamentoCVA().getDescricaoUrgencia()));
        columns.add(new DateColumn(bundle("dataContato"), path(proxy.getDataUltimaOcorrencia())).setPattern("dd/MM/yyyy - HH:mm"));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AgendamentoSolicitacaoCVADTO>() {
            @Override
            public void customizeColumn(final AgendamentoSolicitacaoCVADTO rowObject) {
                addAction(ActionType.AGENDAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAgendamentoSolicitacaoCVAPage(parameters, rowObject));
                    }
                });
            }
        };
    }

    public IPagerProvider getPagerProvider() {
        if (this.pagerProvider == null) {
            this.pagerProvider = new QueryPagerProvider<AgendamentoSolicitacaoCVADTO, ConsultaAgendamentoSolicitacaoDTOParam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarAgendamentoSolicitacaoCVAPendente(dataPaging);
                }

                @Override
                public void customizeParam(ConsultaAgendamentoSolicitacaoDTOParam param) {
                    SingleSortState<String> sortState = (SingleSortState) getPagerProvider().getSortState();

                    if (sortState.getSort() != null) {
                        param.setCampoOrdenacao(sortState.getSort().getProperty());
                        param.setTipoOrdenacao(sortState.getSort().isAscending() ? "asc" : "desc");
                    }
                }
            };
        }

        return this.pagerProvider;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaAgendamentoSolicitacoesCVA");
    }
}
