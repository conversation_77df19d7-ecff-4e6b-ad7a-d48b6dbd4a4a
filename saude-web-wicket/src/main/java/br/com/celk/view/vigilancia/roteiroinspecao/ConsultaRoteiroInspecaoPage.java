package br.com.celk.view.vigilancia.roteiroinspecao;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroInspecao;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 * Programa - 615
 */
@Private
public class ConsultaRoteiroInspecaoPage extends ConsultaPage<RoteiroInspecao, List<BuilderQueryCustom.QueryParameter>> {

    private String nomeRoteiro;

    public ConsultaRoteiroInspecaoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("nomeRoteiro"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        RoteiroInspecao proxy = on(RoteiroInspecao.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("subtitulo"), proxy.getNomeRoteiro()));

        return columns;
    }

    private CustomColumn<RoteiroInspecao> getCustomColumn() {
        return new CustomColumn<RoteiroInspecao>() {
            @Override
            public Component getComponent(String componentId, final RoteiroInspecao rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRoteiroInspecaoPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRoteiroInspecaoPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public String[] getProperties() {
                return new HQLProperties(RoteiroInspecao.class).getProperties();
            }

            @Override
            public Class getClassConsulta() {
                return RoteiroInspecao.class;
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(RoteiroInspecao.PROP_DATA_CADASTRO, false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(RoteiroInspecao.PROP_NOME_ROTEIRO, BuilderQueryCustom.QueryParameter.ILIKE, nomeRoteiro));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRoteiroInspecaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRoteiroInspecao");
    }
}
