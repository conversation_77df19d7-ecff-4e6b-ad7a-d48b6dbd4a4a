package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProdutoMulti;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioDispensacaoUnidadeDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import org.apache.wicket.markup.html.form.Form;


/**
 * <AUTHOR>
 * Programa - 133
 */
@Private

public class ResumoDispensacoesPage extends RelatorioPage<RelatorioDispensacaoUnidadeDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaUnidadesDispensadoras;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaUnidadesOrigem;
    private AutoCompleteConsultaProdutoMulti autoCompleteConsultaProdutoMulti;
    private PnlChoicePeriod pnlChoicePeriod;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaUnidadesDispensadoras = new AutoCompleteConsultaEmpresa("unidadesDispensadoras"));
        autoCompleteConsultaUnidadesDispensadoras.setMultiplaSelecao(true);
        autoCompleteConsultaUnidadesDispensadoras.setOperadorValor(true);

        form.add(autoCompleteConsultaUnidadesOrigem = new AutoCompleteConsultaEmpresa("unidadesOrigem"));

        form.add(autoCompleteConsultaProdutoMulti = new AutoCompleteConsultaProdutoMulti("produtos"));
        autoCompleteConsultaProdutoMulti.setIncluirInativo(true);

        form.add(pnlChoicePeriod = new RequiredPnlChoicePeriod("period"));
        form.add(getDropDownFormaApresentacao());
    }

    private DropDown getDropDownFormaApresentacao() {
        DropDown dropDown = new DropDown("formaApresentacao");

        dropDown.addChoice(Bundle.getStringApplication("rotulo_produto"), BundleManager.getString("produto"));
        dropDown.addChoice(Bundle.getStringApplication("rotulo_unidade_dispensadora"), BundleManager.getString("unidadeDispensadora"));
        dropDown.addChoice(Bundle.getStringApplication("rotulo_unidade_origem"), BundleManager.getString("unidadeOrigem"));

        return dropDown;
    }

    @Override
    public Class<RelatorioDispensacaoUnidadeDTOParam> getDTOParamClass() {
        return RelatorioDispensacaoUnidadeDTOParam.class;
    }

    @Override
    public void customDTOParam(RelatorioDispensacaoUnidadeDTOParam param) {
        param.setOrdenacao(Bundle.getStringApplication("rotulo_valor_total"));
        param.setTipoOrdenacao(BuilderQueryCustom.QuerySorter.DECRESCENTE);
        param.setTipoPreco(EstoqueEmpresa.PROP_ULTIMO_PRECO);
    }

    @Override
    public DataReport getDataReport(RelatorioDispensacaoUnidadeDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioDispensacaoUnidadeAsync(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoDispensacoes");
    }

}
