package br.com.celk.view.consorcio.pagamentoguiaprocedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.Arrays;


/**
 *
 * <AUTHOR>
 * Programa - 118
 */
@Private

public class PagamentoGuiaProcedimentoStep1Page extends BasePage{

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private InputField txtGuiasAberto;
    private InputField txtGuiasUtilizadas;
    
    private Empresa empresaConsorcio;
    private Empresa empresaPrestador;
    private Long guiasAberto;
    private Long guiasUtilizadas;
    private TipoConta tipoContaProcedimentosFisica;
    private TipoConta tipoConta;
    private AutoCompleteConsultaTipoConta autoCompleteConsultaTipoConta;

    public PagamentoGuiaProcedimentoStep1Page() {
        init();
    }

    private void init(){
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        form.add(new DisabledInputField("empresaConsorcio.descricao"));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresaPrestador", true).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)));
        form.add(txtGuiasAberto = new DisabledInputField("guiasAberto"));
        form.add(txtGuiasUtilizadas = new DisabledInputField("guiasUtilizadas"));
        form.add(autoCompleteConsultaTipoConta = new AutoCompleteConsultaTipoConta("tipoConta"));
        
        form.add(new AbstractAjaxButton("btnAvancar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                avancar(target);
            }

        });
        
        add(form);
        
        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                autoCompleteConsultaTipoConta.clearInput();
                autoCompleteConsultaTipoConta.limpar(target);
                tipoConta = null;
                if (object!=null) {
                        guiasAberto = LoadManager.getInstance(ConsorcioGuiaProcedimento.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR), object))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value()))
                                .addGroup(new QueryCustom.QueryCustomGroup(ConsorcioGuiaProcedimento.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                                .start().getVO();
                        guiasUtilizadas = LoadManager.getInstance(ConsorcioGuiaProcedimento.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR), object))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), ConsorcioGuiaProcedimento.StatusGuiaProcedimento.A_PAGAR.value()))
                                .addGroup(new QueryCustom.QueryCustomGroup(ConsorcioGuiaProcedimento.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                                .start().getVO();
                        target.add(txtGuiasAberto);
                        target.add(txtGuiasUtilizadas);
                } else {
                    txtGuiasAberto.limpar(target);
                    txtGuiasUtilizadas.limpar(target);
                }
            }
        });

        autoCompleteConsultaTipoConta.add(new ConsultaListener<TipoConta>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoConta object) {
                if (object!=null) {
                    guiasAberto = LoadManager.getInstance(ConsorcioGuiaProcedimento.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR), empresaPrestador))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_TIPO_CONTA), object))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value()))
                            .addGroup(new QueryCustom.QueryCustomGroup(ConsorcioGuiaProcedimento.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                            .start().getVO();
                    guiasUtilizadas = LoadManager.getInstance(ConsorcioGuiaProcedimento.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR), empresaPrestador))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_TIPO_CONTA), object))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), ConsorcioGuiaProcedimento.StatusGuiaProcedimento.A_PAGAR.value()))
                            .addGroup(new QueryCustom.QueryCustomGroup(ConsorcioGuiaProcedimento.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                            .start().getVO();
                    target.add(txtGuiasAberto);
                    target.add(txtGuiasUtilizadas);
                } else {
                    txtGuiasAberto.limpar(target);
                    txtGuiasUtilizadas.limpar(target);
                }
            }
        });
        
        try{
            boolean valido = true;
            IParameterModuleContainer modulo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO);
            
            empresaConsorcio = modulo.getParametro("consorcioPadrao");
            if (empresaConsorcio == null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "consorcioPadrao"));
                valido = false;
            }
            TipoMovimentacao tipoMovimentacao = modulo.getParametro("tipoMovimentoPagamentoPrestador");
            if (tipoMovimentacao==null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoMovimentoPagamentoPrestador"));
                valido = false;
            } else if(tipoMovimentacao.getTipoMovimento().equals(TipoMovimentacao.TipoMovimento.CREDITO.value())){
                warn(BundleManager.getString("tipoMovimentoPagamentoPrestadorDefinidoParametroDeveSerTipoDebito"));
                valido = false;
            }
            
            Double aliquotaIR = modulo.getParametro("aliquotaImpostoRenda");
            if (aliquotaIR==null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "aliquotaImpostoRenda"));
                valido = false;
            }
            Double aliquotaINSS = modulo.getParametro("aliquotaPadraoINSS");
            if (aliquotaINSS==null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "aliquotaPadraoINSS"));
                valido = false;
            }
            Double aliquotaISS = modulo.getParametro("aliquotaPadraoISS");
            if (aliquotaISS==null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "aliquotaPadraoISS"));
                valido = false;
            }
            TipoConta tipoContaImposto = modulo.getParametro("tipoContaImposto");
            if (tipoContaImposto==null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoContaImposto"));
                valido = false;
            }
            TipoMovimentacao tipoMovimentoTransferenciaImpostoRenda = modulo.getParametro("tipoMovimentoTransferenciaImpostoRenda");
            if (tipoMovimentoTransferenciaImpostoRenda==null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoMovimentoTransferenciaImpostoRenda"));
                valido = false;
            }
            TipoMovimentacao tipoMovimentoEntradaImpostoRenda = modulo.getParametro("tipoMovimentoEntradaImpostoRenda");
            if (tipoMovimentoEntradaImpostoRenda==null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoMovimentoEntradaImpostoRenda"));
                valido = false;
            }
            TipoMovimentacao tipoMovimentoEntradaINSS = modulo.getParametro("tipoMovimentoEntradaImpostoINSS");
            if (tipoMovimentoEntradaINSS==null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoMovimentoEntradaImpostoINSS"));
                valido = false;
            }
            TipoMovimentacao tipoMovimentoEntradaISS = modulo.getParametro("tipoMovimentoEntradaImpostoISS");
            if (tipoMovimentoEntradaISS==null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoMovimentoEntradaImpostoISS"));
                valido = false;
            }
            tipoContaProcedimentosFisica = modulo.getParametro("tipoContaProcedimentosFisico");
            if (tipoContaProcedimentosFisica != null) {
                Double aliquotaINSSPatronal = modulo.getParametro("aliquotaPadraoINSSPatronal");
                if (!(Coalesce.asDouble(aliquotaINSSPatronal) > 0D)) {
                    warn(BundleManager.getString("parametroXNaoEstaDefinido", "aliquotaPadraoINSSPatronal"));
                    valido = false;
                }
                TipoMovimentacao tipoMovimentoTransferenciaINSSPatronal = modulo.getParametro("tipoMovimentoTransferenciaINSSPatronal");
                if (tipoMovimentoTransferenciaINSSPatronal==null) {
                    warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoMovimentoTransferenciaINSSPatronal"));
                    valido = false;
                }
                TipoMovimentacao tipoMovimentoEntradaINSSPatronal = modulo.getParametro("tipoMovimentoEntradaINSSPatronal");
                if (tipoMovimentoEntradaINSSPatronal==null) {
                    warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoMovimentoEntradaINSSPatronal"));
                    valido = false;
                }
            }

//            String utilizarDescontoGlosa = modulo.getParametro("utilizarDescontoGlosa");
//            if(utilizarDescontoGlosa != null && RepositoryComponentDefault.SIM.equals(utilizarDescontoGlosa)) {
//                TipoMovimentacao tipoMovimentoTransferenciaGlosa = modulo.getParametro("tipoMovimentoTransferenciaGlosa");
//                if (tipoMovimentoTransferenciaGlosa == null) {
//                    warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoMovimentoTransferenciaGlosa"));
//                    valido = false;
//                }
//                TipoMovimentacao tipoMovimentoEntradaGlosa = modulo.getParametro("tipoMovimentoEntradaGlosa");
//                if (tipoMovimentoEntradaGlosa == null) {
//                    warn(BundleManager.getString("parametroXNaoEstaDefinido", "tipoMovimentoEntradaGlosa"));
//                    valido = false;
//                }
//            }
            if (!valido) {
                form.setEnabled(false);
            }
        } catch (DAOException ex){
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("pagamentoGuiasUtilizadas");
    }

    private void avancar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (guiasUtilizadas<=0L) {
            throw new ValidacaoException(BundleManager.getString("prestadorSelecionadoNaoPossuiGuiasUtilizadas"));
        }
        ConsorcioPrestador consorcioPrestador = LoadManager.getInstance(ConsorcioPrestador.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ConsorcioPrestador.PROP_EMPRESA_CONSORCIO, empresaConsorcio))
                .addParameter(new QueryCustom.QueryCustomParameter(ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, empresaPrestador))
                .start().getVO();

        
        boolean valid = true;

        if(tipoContaProcedimentosFisica != null){
            if(consorcioPrestador.getBancoPrestador() == null){
                warn(target, BundleManager.getString("prestadorNaoPossuiInformacoesBancariasFavorAtualizar"));
                valid = false;
            }
        }

        if (consorcioPrestador.getTipoCobrancaImpostoRenda()==null) {
            warn(target, BundleManager.getString("prestadorNaoPossuiTipoCobrancaImpostoRendaDefinido"));
            valid = false;
        } else if(ConsorcioPrestador.TipoCobrancaImposto.TABELA_REFERENCIA.value().equals(consorcioPrestador.getTipoCobrancaImpostoRenda())){
            AliquotaImposto aliquotaImpostoRenda = getAliquotaImposto(AliquotaImposto.TipoImposto.IMPOSTO_RENDA);
            if (aliquotaImpostoRenda == null) {
                warn(target, BundleManager.getString("tabelaReferenciaImpostoRendaNaoInformada"));
                valid = false;
            }
        }
        if (consorcioPrestador.getTipoCobrancaInss()==null) {
            warn(target, BundleManager.getString("prestadorNaoPossuiTipoCobrancaInssDefinido"));
            valid = false;
        } else if(ConsorcioPrestador.TipoCobrancaImposto.TABELA_REFERENCIA.value().equals(consorcioPrestador.getTipoCobrancaInss())){
            AliquotaImposto aliquotaInss = getAliquotaImposto(AliquotaImposto.TipoImposto.INSS);
            if (aliquotaInss == null) {
                warn(target, BundleManager.getString("tabelaReferenciaInssNaoInformada"));
                valid = false;
            }
        }
        if (consorcioPrestador.getTipoCobrancaIss()==null) {
            warn(target, BundleManager.getString("prestadorNaoPossuiTipoCobrancaIssDefinido"));
            valid = false;
        } else if(ConsorcioPrestador.TipoCobrancaImposto.TABELA_REFERENCIA.value().equals(consorcioPrestador.getTipoCobrancaIss())){
            AliquotaImposto aliquotaIss = getAliquotaImposto(AliquotaImposto.TipoImposto.ISS);
            if (aliquotaIss == null) {
                warn(target, BundleManager.getString("tabelaReferenciaIssNaoInformada"));
                valid = false;
            }
        }

        if (valid) {
            setResponsePage(new PagamentoGuiaProcedimentoStep2Page(empresaConsorcio, empresaPrestador, tipoConta));
        }
    }
    
    private AliquotaImposto getAliquotaImposto(AliquotaImposto.TipoImposto tipoImposto) throws DAOException, ValidacaoException{
        return LoadManager.getInstance(AliquotaImposto.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AliquotaImposto.PROP_ANO_EXERCICIO), Data.getAnoForGregorianCalendar(Data.getDataAtual())))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AliquotaImposto.PROP_TIPO_IMPOSTO), tipoImposto.value()))
                .start().getVO();
    }
    
}
