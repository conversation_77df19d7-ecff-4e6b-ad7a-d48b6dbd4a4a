package br.com.celk.view.materiais.materiais.consulta;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaConfiguracao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Ramos
 * Programa - 876
 */
@Private
public class ConsultaConfiguracaoPedidoPage extends ConsultaPage<PedidoTransferenciaConfiguracao, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa empresa;
    private boolean permissaoEmpresa;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    @Override
    public void initForm(Form form) {

        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        permissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!permissaoEmpresa);
    }


    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        PedidoTransferenciaConfiguracao proxy = on(PedidoTransferenciaConfiguracao.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("empresa"), proxy.getEmpresa().getDescricao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<PedidoTransferenciaConfiguracao>() {
            @Override
            public void customizeColumn(final PedidoTransferenciaConfiguracao rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<PedidoTransferenciaConfiguracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferenciaConfiguracao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroConfiguracaoPedidoPage(modelObject, false));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<PedidoTransferenciaConfiguracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferenciaConfiguracao modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(MaterialBasicoFacade.class).deletarPedidoTransferenciaConfiguracao(modelObject);
                        getPageableTable().populate(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<PedidoTransferenciaConfiguracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferenciaConfiguracao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroConfiguracaoPedidoPage(modelObject, true));
                    }
                });
            }
        };
    }


    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return PedidoTransferenciaConfiguracao.class;
            }

            @Override
            public String[] getProperties() {
                PedidoTransferenciaConfiguracao proxy = on(PedidoTransferenciaConfiguracao.class);

                return VOUtils.mergeProperties(new HQLProperties(PedidoTransferenciaConfiguracao.class).getProperties(),
                        new HQLProperties(Empresa.class, path(proxy.getEmpresa())).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(PedidoTransferenciaConfiguracao.PROP_EMPRESA, Empresa.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferenciaConfiguracao.PROP_EMPRESA, empresa));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroConfiguracaoPedidoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaConfiguracaoPedAlmoxarifado");
    }
}
