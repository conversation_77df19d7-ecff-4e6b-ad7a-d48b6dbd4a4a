package br.com.celk.view.materiais.estoque.calculoestoque;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.resources.Resources;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.estoque.calculoestoque.customize.CustomizeConsultaCalculoEstoque;
import br.com.celk.view.materiais.estoque.calculoestoque.tablecolumn.CalculoEstoqueColumnPanel;
import br.com.celk.view.materiais.estoque.calculoestoque.tablecolumn.NivelSegurancaColumn;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.produtosporunidade.CadastroProdutosPorUnidadePage;
import br.com.celk.view.materiais.produtosporunidade.customcolumn.ConsultaProdutosPorUnidadeColumnPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.materiais.estoque.interfaces.dto.CalculoEstoqueDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 1021
 */
@Private
public class CalculoEstoquePage extends ConsultaPage<EstoqueEmpresa, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa empresa;
    private Produto produto;
    private Localizacao localizacao;
    private GrupoProduto grupoProduto;
    private SubGrupo subGrupo;
    private Long flagEstoqueZerado;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private CheckBoxLongValue ckEstoqueZerado;

    public CalculoEstoquePage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        autoCompleteConsultaEmpresa.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL, Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO,Empresa.TIPO_ESTABELECIMENTO_FARMACIA));
        autoCompleteConsultaEmpresa.setComponentValue(SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa());

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);


        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(false);


        form.add(dropDownSubGrupo = DropDownUtil.getDropDownSubGrupo(dropDownSubGrupo));
        form.add(dropDownGrupoProduto = DropDownUtil.getDropDownGrupo(dropDownGrupoProduto, dropDownSubGrupo, autoCompleteConsultaProduto));

        dropDownSubGrupo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                autoCompleteConsultaProduto.limpar(target);
                autoCompleteConsultaProduto.clearInput();
                autoCompleteConsultaProduto.setSubGrupoFixo(null);
                SubGrupo subGrupo = dropDownSubGrupo.getComponentValue();
                autoCompleteConsultaProduto.setSubGrupoFixo(subGrupo);
                autoCompleteConsultaProduto.setEnabled(true);
            }
        });

        form.add(ckEstoqueZerado = new CheckBoxLongValue("flagEstoqueZerado", 1L));

    }
    
    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(EstoqueEmpresa.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidade"), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_DESCRICAO), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("produto"), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_DESCRICAO), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estoque_fisico_abv"), VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_FISICO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("consumo_medio_dias"), VOUtils.montarPath(EstoqueEmpresa.PROP_CONSUMO_MEDIO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estoqueMinimoAbv"), VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_MINIMO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estoqueMaximoAbv"), VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_MAXIMO)));
        columns.add(new NivelSegurancaColumn("nivelSeguranca") {
            @Override
            public Double getEstoqueMinimoFromObject(Object object) {
                return ((EstoqueEmpresa) object).getEstoqueMinimo();
            }

            @Override
            public Double getEstoqueFisicoFromObject(Object object) {
                return ((EstoqueEmpresa) object).getEstoqueFisico();
            }
        });
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("curva"), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_CURVA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("criticidade"), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_CRITICIDADE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tempoReposicaoDias"), VOUtils.montarPath(EstoqueEmpresa.PROP_TEMPO_REPOSICAO)));

        return columns;
    }

    private CustomColumn<EstoqueEmpresa> getCustomColumn() {
        return new CustomColumn<EstoqueEmpresa>() {
            @Override
            public Component getComponent(String componentId, final EstoqueEmpresa rowObject) {
                return new CalculoEstoqueColumnPanel(componentId, rowObject) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new GerarCalculoEstoquePage(getDTO(rowObject)));
                    }
                };
            }
        };
    }

    private CalculoEstoqueDTO getDTO(EstoqueEmpresa rowObject) {
        CalculoEstoqueDTO dto = new CalculoEstoqueDTO();
        dto.setEmpresa(rowObject.getRoEmpresa());
        dto.setProduto(rowObject.getRoProduto());
        return dto;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaCalculoEstoque()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), BuilderQueryCustom.QueryParameter.IGUAL, empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, produto));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_LOCALIZACAO), BuilderQueryCustom.QueryParameter.IGUAL, localizacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO), BuilderQueryCustom.QueryParameter.IGUAL, subGrupo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, grupoProduto));
        if(ckEstoqueZerado.getValue().equals("") || ckEstoqueZerado.getValue().equals("0"))
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_FISICO), BuilderQueryCustom.QueryParameter.MAIOR, Double.MIN_VALUE));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return GerarCalculoEstoquePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("calculoEstoque");
    }
}
