package br.com.celk.view.materiais.dispensacao.cadastro;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.QueryCboProcedimentoParam;
import br.com.ksisolucoes.system.controle.SGKException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoCboProcedimento;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 * Programa - 385
 */
@Private
public class ConsultaCboProcedimentoPage extends ConsultaPage<DispensacaoCboProcedimento, QueryCboProcedimentoParam> {

    private QueryCboProcedimentoParam param;    
    private AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel<QueryCboProcedimentoParam>(param = new QueryCboProcedimentoParam()));
        form.add(autoCompleteConsultaTabelaCbo = (AutoCompleteConsultaTabelaCbo) new AutoCompleteConsultaTabelaCbo("tabelaCbo").setLabel(new Model<String>("cbo")));
        form.add(new AutoCompleteConsultaProcedimento("procedimento"));
        
        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(DispensacaoCboProcedimento.class);
        DispensacaoCboProcedimento proxy = on(DispensacaoCboProcedimento.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("cbo"), path(proxy.getTabelaCbo().getDescricao()), path(proxy.getTabelaCbo().getDescricaoFormatado())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("procedimento"), path(proxy.getProcedimento().getDescricao()), path(proxy.getProcedimento().getDescricaoFormatado())));
        
        return columns;
    }
    
    private CustomColumn<DispensacaoCboProcedimento> getCustomColumn() {
        return new CustomColumn<DispensacaoCboProcedimento>() {

            @Override
            public Component getComponent(String componentId, final DispensacaoCboProcedimento rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCboProcedimentoPage(rowObject,false,true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCboProcedimentoPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<DispensacaoCboProcedimento, QueryCboProcedimentoParam>() {

            @Override
            public DataPagingResult<DispensacaoCboProcedimento> executeQueryPager(DataPaging<QueryCboProcedimentoParam> dataPaging) throws DAOException, ValidacaoException {
                try {
                    if(((SingleSortState<String>)getPagerProvider().getSortState()).getSort() != null){
                        dataPaging.getParam().setCampoOrdenacao(((SingleSortState<String>)getPagerProvider().getSortState()).getSort().getProperty());
                        dataPaging.getParam().setTipoOrdenacao(((SingleSortState)getPagerProvider().getSortState()).getSort().isAscending()?"asc":"desc");
                    }
                    return BOFactoryWicket.getBO(BasicoFacade.class).consultarCboProcedimento(dataPaging);
                } catch (SGKException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return null;
            }
        };
    }

    @Override
    public QueryCboProcedimentoParam getParameters() {        
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroCboProcedimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaCboProcedimento");
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaTabelaCbo.getTxtDescricao().getTextField();
    }

}