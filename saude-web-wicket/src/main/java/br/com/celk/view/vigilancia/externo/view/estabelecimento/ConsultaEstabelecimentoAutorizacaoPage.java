package br.com.celk.view.vigilancia.externo.view.estabelecimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dialog.DlgMotivoArea;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.CadastroEstabelecimentoPage;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaEstabelecimentoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaEstabelecimentoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 719
 */
@Private
public class ConsultaEstabelecimentoAutorizacaoPage extends ConsultaPage<ConsultaEstabelecimentoDTO, ConsultaEstabelecimentoDTOParam> {

    private CompoundPropertyModel<ConsultaEstabelecimentoDTOParam> model;
    private InputField<String> txtRazaoSocial;
    private DlgMotivoArea dlgMotivoArea;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(model = new CompoundPropertyModel(new ConsultaEstabelecimentoDTOParam()));
        model.getObject().setSituacao(ConsultaEstabelecimentoDTOParam.SituacaoAutorizacao.PROVISORIO.value());
        ConsultaEstabelecimentoDTOParam proxy = on(ConsultaEstabelecimentoDTOParam.class);

        form.add(new AutoCompleteConsultaUsuario(path(proxy.getUsuarioResponsavel())));
        form.add(txtRazaoSocial = new InputField<>(path(proxy.getRazaoSocial())));
        form.add(new InputField(path(proxy.getFantasia())));
        form.add(new InputField(path(proxy.getCpfCnpj())));
        form.add(new PnlAtividadeEstabelecimento(path(proxy.getAtividadeEstabelecimento())));
        form.add(getDropDownSituacao(path(proxy.getSituacao())));

        setExibeExpandir(true);
        getLinkNovo().setVisible(false);
    }

    private DropDown getDropDownSituacao(String id) {
        DropDown dropDown = new DropDown(id);
        dropDown.addChoice(null, bundle("ambas"));
        dropDown.addChoice(ConsultaEstabelecimentoDTOParam.SituacaoAutorizacao.PROVISORIO.value(), ConsultaEstabelecimentoDTOParam.SituacaoAutorizacao.PROVISORIO.descricao());
        dropDown.addChoice(ConsultaEstabelecimentoDTOParam.SituacaoAutorizacao.NAO_AUTORIZADO.value(), ConsultaEstabelecimentoDTOParam.SituacaoAutorizacao.NAO_AUTORIZADO.descricao());
        return dropDown;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ConsultaEstabelecimentoDTO proxy = on(ConsultaEstabelecimentoDTO.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(BundleManager.getString("alvara"), proxy.getEstabelecimento().getAlvara(), proxy.getEstabelecimento().getAlvaraFormatado()));
        columns.add(createSortableColumn(BundleManager.getString("razaoSocial"), proxy.getEstabelecimento().getRazaoSocial()));
        columns.add(createSortableColumn(BundleManager.getString("fantasia"), proxy.getEstabelecimento().getFantasia()));
        columns.add(createColumn(BundleManager.getString("cpfCnpj"), proxy.getCnpjCpf()));
        columns.add(createColumn(BundleManager.getString("atividade"), proxy.getDescricaoAtividadePrincipal()));
        columns.add(createSortableColumn(BundleManager.getString("endereco"), proxy.getEstabelecimento().getVigilanciaEndereco().getLogradouro(), proxy.getEstabelecimento().getVigilanciaEndereco().getEnderecoFormatadoComCidade()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getEstabelecimento().getSituacao(), proxy.getEstabelecimento().getDescricaoSituacao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ConsultaEstabelecimentoDTO>() {
            @Override
            public void customizeColumn(ConsultaEstabelecimentoDTO rowObject) {
                addAction(ActionType.CURTIR, rowObject, new IModelAction<ConsultaEstabelecimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaEstabelecimentoDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).autorizarEstabelecimento(modelObject);
                        getPageableTable().populate(target);
                    }
                }).setTitleBundleKey("autorizar")
                        .setQuestionDialogBundleKey("desejaRealmenteAutorizarEstabelecimento")
                        .setEnabled(enableAutorizar(rowObject));

                addAction(ActionType.DESCURTIR, rowObject, new IModelAction<ConsultaEstabelecimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaEstabelecimentoDTO modelObject) throws ValidacaoException, DAOException {
                        naoAutorizar(target, modelObject);
                    }
                }).setTitleBundleKey("naoAutorizar")
                        .setEnabled(enableNaoAutorizar(rowObject));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ConsultaEstabelecimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaEstabelecimentoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEstabelecimentoPage(modelObject.getEstabelecimento(), true, null, null, null));
                    }
                });
            }
        };
    }

    public boolean enableAutorizar(ConsultaEstabelecimentoDTO rowObject) {
        return (Estabelecimento.Situacao.PROVISORIO.value().equals(rowObject.getEstabelecimento().getSituacao())
                || Estabelecimento.Situacao.NAO_AUTORIZADO.value().equals(rowObject.getEstabelecimento().getSituacao()))
                && isActionPermitted(Permissions.EDITAR);
    }

    public boolean enableNaoAutorizar(ConsultaEstabelecimentoDTO rowObject) {
        return (Estabelecimento.Situacao.PROVISORIO.value().equals(rowObject.getEstabelecimento().getSituacao())
                || Estabelecimento.Situacao.ATIVO.value().equals(rowObject.getEstabelecimento().getSituacao()))
                && isActionPermitted(Permissions.EDITAR);
    }

    public void naoAutorizar(AjaxRequestTarget target, ConsultaEstabelecimentoDTO dto) {
        if (dlgMotivoArea == null) {
            addModal(target, dlgMotivoArea = new DlgMotivoArea(newModalId(), BundleManager.getString("naoAutorizar")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, Object object) throws ValidacaoException, DAOException {
                    ConsultaEstabelecimentoDTO dtoEstab = (ConsultaEstabelecimentoDTO) object;
                    dtoEstab.getEstabelecimento().setMotivoNaoAutorizado(motivo);
                    BOFactoryWicket.getBO(VigilanciaFacade.class).naoAutorizarEstabelecimento(dtoEstab);
                    getPageableTable().populate(target);
                }
            });
        }
        dlgMotivoArea.setObject(dto);
        dlgMotivoArea.show(target);

    }

    @Override
    public IPagerProvider<ConsultaEstabelecimentoDTO, ConsultaEstabelecimentoDTOParam> getPagerProviderInstance() {
        return new QueryPagerProvider<ConsultaEstabelecimentoDTO, ConsultaEstabelecimentoDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaEstabelecimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarEstabelecimentoVigilanciaAutorizacao(dataPaging);
            }

            @Override
            public void customizeParam(ConsultaEstabelecimentoDTOParam param) {
                ConsultaEstabelecimentoAutorizacaoPage.this.model.getObject().setSortProp(getSort().getProperty());
                ConsultaEstabelecimentoAutorizacaoPage.this.model.getObject().setAscending(getSort().isAscending());
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam("estabelecimento.razaoSocial", true);
            }
        };
    }

    @Override
    public ConsultaEstabelecimentoDTOParam getParameters() {
        return model.getObject();
    }

    @Override
    public Class getCadastroPage() {
        return CadastroEstabelecimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("autorizacaoEstabelecimento");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtRazaoSocial;
    }
}
