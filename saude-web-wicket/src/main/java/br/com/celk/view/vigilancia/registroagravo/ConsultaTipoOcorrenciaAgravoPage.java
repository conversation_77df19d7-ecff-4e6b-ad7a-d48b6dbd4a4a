package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.agravo.TipoOcorrenciaAgravo;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 546
 */
@Private
public class ConsultaTipoOcorrenciaAgravoPage extends ConsultaPage<TipoOcorrenciaAgravo, List<BuilderQueryCustom.QueryParameter>>{

    private String descricao;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        TipoOcorrenciaAgravo proxy = on(TipoOcorrenciaAgravo.class);
        
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        return columns;
    }
    
    private CustomColumn<TipoOcorrenciaAgravo> getCustomColumn() {
        return new CustomColumn<TipoOcorrenciaAgravo>() {

            @Override
            public Component getComponent(String componentId, final TipoOcorrenciaAgravo rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoOcorrenciaAgravoPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoOcorrenciaAgravoPage(rowObject, true));
                    }
                };
            }
        };
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return TipoOcorrenciaAgravo.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(TipoOcorrenciaAgravo.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(TipoOcorrenciaAgravo.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(TipoOcorrenciaAgravo.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoOcorrenciaAgravoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTipoOcorrenciaAgravo");
    }
}