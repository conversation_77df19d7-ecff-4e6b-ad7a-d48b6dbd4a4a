package br.com.celk.view.materiais.localizacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Localizacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Programa - 72
 */
@Private
public class ConsultaLocalizacaoPage extends ConsultaPage<Localizacao, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private String sigla;

    public ConsultaLocalizacaoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        form.add(new InputField<String>("sigla"));

        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(Localizacao.class);

        columns.add(getActionColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Localizacao.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("sigla"), VOUtils.montarPath(Localizacao.PROP_SIGLA)));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<Localizacao>() {

            @Override
            public void customizeColumn(Localizacao rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<Localizacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, Localizacao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLocalizacaoPage(modelObject, false, true));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Localizacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, Localizacao modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(modelObject);
                        getPageableTable().populate(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<Localizacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, Localizacao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLocalizacaoPage(modelObject, true));
                    }
                });
                addAction(ActionType.CLONAR, rowObject, new IModelAction<Localizacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, Localizacao modelObject) throws ValidacaoException, DAOException {
                        Localizacao localizacao = VOUtils.cloneObject(modelObject);
                        localizacao.setSigla(null);
                        setResponsePage(new CadastroLocalizacaoPage(localizacao));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return Localizacao.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(Localizacao.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(Localizacao.PROP_CODIGO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Localizacao.PROP_SIGLA), BuilderQueryCustom.QueryParameter.ILIKE, sigla));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Localizacao.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroLocalizacaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaLocalizacao");
    }
}
