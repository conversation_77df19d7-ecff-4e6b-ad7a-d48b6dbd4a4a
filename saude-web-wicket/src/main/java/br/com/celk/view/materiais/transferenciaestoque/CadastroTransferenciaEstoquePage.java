package br.com.celk.view.materiais.transferenciaestoque;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.ajaxcalllistener.ConditionListenerLoading;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.lote.saida.PnlSaidaLote;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacaoestrutura.autocomplete.AutoCompleteConsultaLocalizacaoEstrutura;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.CadastroTransferenciaEstoqueDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioTransferenciaEstoqueDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import ch.lambdaj.Lambda;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 77
 */
@Private
public class CadastroTransferenciaEstoquePage extends CadastroPage<LancamentoMovimentoEstoque> {

    private Empresa unidadeOrigem;
    private Empresa unidadeDestino;
    private Deposito depositoOrigem;
    private Deposito depositoDestino;
    private Produto produto;
    private String numeroDocumento;
    private Double quantidade;
    private String observacao;
    private String grupoEstoque;
    private LocalizacaoEstrutura localizacaoEstruturaOrigem;
    private LocalizacaoEstrutura localizacaoEstruturaDestino;

    private PnlSaidaLote pnlSaidaLote;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaOrigem;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaDestino;
    private AutoCompleteConsultaDeposito autoCompleteConsultaDepositoOrigem;
    private AutoCompleteConsultaDeposito autoCompleteConsultaDepositoDestino;
    private InputField<Double> txtQuantidade;
    private InputField<String> txtNumeroDocumento;
    private AbstractAjaxButton btnLimpar;
    private InputArea<String> txaObservacao;
    private AutoCompleteConsultaLocalizacaoEstrutura autoCompleteConsultaLocalizacaoEstruturaOrigem;
    private AutoCompleteConsultaLocalizacaoEstrutura autoCompleteConsultaLocalizacaoEstruturaDestino;
    private WebMarkupContainer containerLocalizacaoOrigem;
    private WebMarkupContainer containerLocalizacaoDestino;
    private WebMarkupContainer containerCodigoBarras;
    private LongField txtCodigoBarras;
    private Long nrCodigoBarrasProduto;
    private List<CodigoBarrasProduto> codigoBarrasProdutoList = new ArrayList<>();
    private Table tblCodigoBarras;
    private List<ISortableColumn> columnsCodigoBarras;
    private ICollectionProvider collectionProviderCodigoBarras;
    private boolean adicionadoViaCodigoBarras;
    private String validarSituacaoCodigoBarrasProduto;
    private String utilizaLocalizacaoEstoque;
    private DlgImpressaoObject<CadastroTransferenciaEstoqueDTO> dlgConfirmacaoImpressao;

    public CadastroTransferenciaEstoquePage() {
    }

    public CadastroTransferenciaEstoquePage(LancamentoMovimentoEstoque object) {
        super(object);
    }

    public CadastroTransferenciaEstoquePage(Empresa unidadeDestino, Deposito depositoDestino) {
        this.unidadeDestino = unidadeDestino;
        this.depositoDestino = depositoDestino;
    }

    @Override
    public void init(Form form) {

        String utilizarLeitoraCodigoBarrasProduto = null;
        try {
            utilizarLeitoraCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizarLeitoraCodigoBarrasProduto");
            validarSituacaoCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("validarSituacaoCodigoBarrasProduto");
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        form.add(containerCodigoBarras = new WebMarkupContainer("containerCodigoBarras"));

        containerCodigoBarras.add(txtCodigoBarras = new LongField("codigoBarrasProduto", new PropertyModel(this, "nrCodigoBarrasProduto")));
        txtCodigoBarras.add(new AjaxFormComponentUpdatingBehavior("onChange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                try {
                    adicionarViaCodigoBarras(art);
                } catch (ValidacaoException ex) {
                    txtCodigoBarras.limpar(art);
                    art.focusComponent(txtCodigoBarras);
                    Loggable.log.error(ex.getMessage(), ex);
                    warn(art, ex.getMessage());
                } catch (DAOException ex) {
                    txtCodigoBarras.limpar(art);
                    art.focusComponent(txtCodigoBarras);
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                String condition = "!= 0";
                attributes.getAjaxCallListeners().add(new ConditionListenerLoading(txtCodigoBarras.getMarkupId(), condition));
            }
        });

        containerCodigoBarras.setVisible(RepositoryComponentDefault.SIM.equals(utilizarLeitoraCodigoBarrasProduto));

        form.add(autoCompleteConsultaEmpresaOrigem = new AutoCompleteConsultaEmpresa("unidadeOrigem", new PropertyModel<Empresa>(this, "unidadeOrigem"), true));
        form.add(autoCompleteConsultaDepositoOrigem = new AutoCompleteConsultaDeposito("depositoOrigem", new PropertyModel<Deposito>(this, "depositoOrigem"), true));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto", new PropertyModel<Produto>(this, "produto"), true));
        autoCompleteConsultaProduto.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO);
        autoCompleteConsultaProduto.setIncluirInativos(false);
        form.add(txtNumeroDocumento = new InputField<String>("numeroDocumento", new PropertyModel(this, "numeroDocumento")));
        form.add(txtQuantidade = new DoubleField("quantidade", new PropertyModel<Double>(this, "quantidade")));
        form.add(pnlSaidaLote = new PnlSaidaLote(("grupoEstoque"), new PropertyModel(this, "grupoEstoque")));
        pnlSaidaLote.setAutoCompleteConsultaProduto(autoCompleteConsultaProduto);
        pnlSaidaLote.setAutoCompleteConsultaDepositoOrigem(autoCompleteConsultaDepositoOrigem);
        pnlSaidaLote.setTxtQuantidade(txtQuantidade);
        pnlSaidaLote.setValidarLotesVencidos(false);
        pnlSaidaLote.registerEvents();
        pnlSaidaLote.addLoteListener(new PnlSaidaLote.iLoteListener() {
            @Override
            public void listener(AjaxRequestTarget target, MovimentoGrupoEstoqueItemDTO loteSelecionado) {
                localizacaoEstruturaDestino = null;
                autoCompleteConsultaLocalizacaoEstruturaOrigem.limpar(target);
                if (loteSelecionado != null && loteSelecionado.getLocalizacaoEstrutura() != null) {
                    localizacaoEstruturaOrigem = loteSelecionado.getLocalizacaoEstrutura();
                    target.add(autoCompleteConsultaLocalizacaoEstruturaOrigem);
                }
            }
        });

        form.add(autoCompleteConsultaEmpresaDestino = new AutoCompleteConsultaEmpresa("unidadeDestino", new PropertyModel<Empresa>(this, "unidadeDestino"), true));
        form.add(autoCompleteConsultaDepositoDestino = new AutoCompleteConsultaDeposito("depositoDestino", new PropertyModel<Deposito>(this, "depositoDestino"), true));

        if (unidadeDestino != null) {
            autoCompleteConsultaEmpresaDestino.setComponentValue(unidadeDestino);
        }

        if (depositoDestino != null) {
            autoCompleteConsultaDepositoDestino.setComponentValue(depositoDestino);
        }

        containerLocalizacaoOrigem = new WebMarkupContainer("containerLocalizacaoOrigem");

        containerLocalizacaoOrigem.setEnabled(false);
        containerLocalizacaoOrigem.setOutputMarkupId(true);
        form.add(containerLocalizacaoOrigem);

        containerLocalizacaoOrigem.add(autoCompleteConsultaLocalizacaoEstruturaOrigem = new AutoCompleteConsultaLocalizacaoEstrutura("localizacaoEstruturaOrigem", new PropertyModel(this, "localizacaoEstruturaOrigem"), true));

        containerLocalizacaoDestino = new WebMarkupContainer("containerLocalizacaoDestino");

        containerLocalizacaoDestino.setOutputMarkupId(true);
        form.add(containerLocalizacaoDestino);

        containerLocalizacaoDestino.add(autoCompleteConsultaLocalizacaoEstruturaDestino = new AutoCompleteConsultaLocalizacaoEstrutura("localizacaoEstruturaDestino", new PropertyModel(this, "localizacaoEstruturaDestino"), true));
        autoCompleteConsultaLocalizacaoEstruturaDestino.setExibirApenasVisivelSim(true);
        form.add(txaObservacao = new InputArea<String>("observacao", new PropertyModel(this, "observacao")));
        form.add(btnLimpar = new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
            }
        });

        form.add(tblCodigoBarras = new Table("tblCodigoBarras", getColumnsCodigoBarras(), getCollectionProviderCodigoBarras()));
        tblCodigoBarras.setScrollY("500px");
        tblCodigoBarras.populate();

        tblCodigoBarras.setVisible(RepositoryComponentDefault.SIM.equals(utilizarLeitoraCodigoBarrasProduto));


        autoCompleteConsultaLocalizacaoEstruturaDestino.setEnabled(false);
        btnLimpar.setDefaultFormProcessing(false);
        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                validaEstoque(target);
                autoCompleteConsultaEmpresaDestino.setProdutos(Arrays.asList(object.getCodigo()));
                autoCompleteConsultaEmpresaDestino.setEnabled(true);
                target.add(autoCompleteConsultaEmpresaDestino);
                autoCompleteConsultaDepositoDestino.setEnabled(true);
                target.add(autoCompleteConsultaDepositoDestino);
                autoCompleteConsultaLocalizacaoEstruturaDestino.setEnabled(true);
                target.add(autoCompleteConsultaLocalizacaoEstruturaDestino);
            }
        });

        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto produto) {
                autoCompleteConsultaEmpresaDestino.setEnabled(false);
                autoCompleteConsultaDepositoDestino.setEnabled(false);
                autoCompleteConsultaLocalizacaoEstruturaDestino.setEnabled(false);
                autoCompleteConsultaLocalizacaoEstruturaDestino.limpar(target);
            }
        });

        autoCompleteConsultaDepositoOrigem.add(new ConsultaListener<Deposito>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Deposito object) {
                validaEstoque(target);
                pnlSaidaLote.setDeposito(object);
            }
        });
        autoCompleteConsultaEmpresaOrigem.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                pnlSaidaLote.setEmpresa(object);
                autoCompleteConsultaProduto.setEnabled(true);
                autoCompleteConsultaProduto.setEmpresas(Arrays.asList(object));
                target.add(pnlSaidaLote);
                target.add(autoCompleteConsultaProduto);
                validaEstoque(target);
            }
        });

        autoCompleteConsultaEmpresaOrigem.setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA));
        autoCompleteConsultaEmpresaDestino.setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA));

        this.getBtnVoltar().setVisible(false);
        this.getBtnSalvar().setVisible(false);
        this.getControls().add(getSalvarButton());

        Empresa empresaLogada = br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa();
        this.autoCompleteConsultaEmpresaOrigem.setComponentValue(empresaLogada);
        autoCompleteConsultaProduto.setEmpresas(Arrays.asList(empresaLogada));

        autoCompleteConsultaEmpresaOrigem.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa empresa) {
                pnlSaidaLote.setEmpresa(null);
                autoCompleteConsultaProduto.setEnabled(false);
                autoCompleteConsultaProduto.limpar(target);
                autoCompleteConsultaEmpresaDestino.setEnabled(false);
                autoCompleteConsultaDepositoDestino.limpar(target);
                autoCompleteConsultaLocalizacaoEstruturaDestino.limpar(target);
                target.add(pnlSaidaLote);
            }
        });

        autoCompleteConsultaEmpresaDestino.setEnabled(false);
        autoCompleteConsultaDepositoDestino.setEnabled(false);
        autoCompleteConsultaLocalizacaoEstruturaDestino.setEnabled(false);

        if (RepositoryComponentDefault.NAO.equals(getUtilizaLocalizacaoEstoque())) {
            containerLocalizacaoOrigem.setVisible(false);
            containerLocalizacaoDestino.setVisible(false);
        }
    }

    private SubmitButton getSalvarButton() {
        SubmitButton btnSalvar = new SubmitButton(this.getControls().newChildId(), new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvarAction(target, (LancamentoMovimentoEstoque) form.getModel().getObject());
            }
        });
        btnSalvar.add(new AttributeModifier("class", "save"));
        btnSalvar.add(new AttributeModifier("value", bundle("salvarImprimir")));
        btnSalvar.setVisible(!isViewOnly());
        return btnSalvar;
    }

    public void salvarAction(AjaxRequestTarget target, LancamentoMovimentoEstoque object) throws DAOException, ValidacaoException {
        if (txtQuantidade.getComponentValue() == null || txtQuantidade.getComponentValue() == 0D) {
            throw new ValidacaoException(BundleManager.getString("msgQuantidadeDeveMaiorZero"));
        }

        if (txaObservacao.getComponentValue() == null) {
            throw new ValidacaoException(BundleManager.getString("informeObservacao"));
        }

        List<Long> empresasPermitidas = carregarEmpresaDestinoTransferencia(unidadeOrigem);
        if (CollectionUtils.isNotNullEmpty(empresasPermitidas)) {
            if (!empresasPermitidas.contains(unidadeDestino.getCodigo())) {
                throw new ValidacaoException(BundleManager.getString("unidadeDestinoNaoPermitida"));
            }
        }

        CadastroTransferenciaEstoqueDTO dto = new CadastroTransferenciaEstoqueDTO();
        dto.setLancamentoMovimentoEstoque(object);
        dto.setProduto(produto);
        dto.setQuantidade(quantidade);
        dto.setUnidadeDestino(unidadeDestino);
        dto.setUnidadeOrigem(unidadeOrigem);
        dto.setNumeroDocumento(numeroDocumento);
        dto.setObservacao(observacao);
        dto.setDepositoOrigem(depositoOrigem);
        dto.setDepositoDestino(depositoDestino);
        dto.setLoteSelecionado(pnlSaidaLote.getLoteSelecionado());
        dto.setLocalizacaoEstruturaDestino(localizacaoEstruturaDestino);
        dto.setJustificativaLotePosterior(pnlSaidaLote.getJustificativaLotePosterior());
        dto.setIdRecibo(getUltimoIdRecibo());

        LancamentoMovimentoEstoque saved = BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).salvarLancamentoMovimentoEstoque(dto);

        if (saved.getMovimentoEstoqueSet() != null) {
            impressaoAction(target, dto);
        }

    }

    private void impressaoAction(AjaxRequestTarget target, final CadastroTransferenciaEstoqueDTO dto) {
        StringBuilder mensagemImpressao = new StringBuilder(bundle("registroSalvoSucesso"));
        mensagemImpressao.append("\n");
        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObject<CadastroTransferenciaEstoqueDTO>(newModalId(), mensagemImpressao.toString()) {
            @Override
            public DataReport getDataReport(CadastroTransferenciaEstoqueDTO dto1) throws ReportException {
                RelatorioTransferenciaEstoqueDTOParam param = new RelatorioTransferenciaEstoqueDTOParam();
                param.setDto(dto);
                return BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).impressaoTransferenciaEstoque(param);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, CadastroTransferenciaEstoqueDTO object) throws ValidacaoException, DAOException {
                Page page = new CadastroTransferenciaEstoquePage(unidadeDestino, depositoDestino);
                setResponsePage(page);
                getSession().getFeedbackMessages().info(page, bundle("registro_salvo_sucesso"));
            }
        });

        dlgConfirmacaoImpressao.show(target, dto);
    }

    public Long getUltimoIdRecibo() {
        Long idRecibo = 0L;

        List<MovimentoEstoque> movimentos = LoadManager.getInstance(MovimentoEstoque.class)
                .addProperties(new HQLProperties(MovimentoEstoque.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(MovimentoEstoque.PROP_ID_RECIBO, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .addSorter(new QueryCustom.QueryCustomSorter(MovimentoEstoque.PROP_ID_RECIBO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .setMaxResults(1).start().getList();

        if (!movimentos.isEmpty()) {
            if (movimentos.get(0).getIdRecibo() != null) {
                idRecibo = movimentos.get(0).getIdRecibo();
                idRecibo += 1L;
            }
        }  else {
            idRecibo = 1L;
        }

        return idRecibo;

    }


    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresaOrigem.getTxtDescricao().getTextField();
    }

    @Override
    public Class<LancamentoMovimentoEstoque> getReferenceClass() {
        return LancamentoMovimentoEstoque.class;
    }

    @Override
    public Class getResponsePage() {
        return CadastroTransferenciaEstoquePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("lancamentoTransferenciaEstoque");
    }

    @Override
    public Page getResponsePageInstance(Object returnObject) {
        return new CadastroTransferenciaEstoquePage(unidadeDestino, depositoDestino);
    }

    public void validaEstoque(AjaxRequestTarget target) {
        Produto produto = this.produto;
        Empresa empresaOrigem = this.unidadeOrigem;
        Deposito depositoOrigem = this.depositoOrigem;
        if (produto == null || empresaOrigem == null || depositoOrigem == null) {
            txtQuantidade.limpar(target);
            txtQuantidade.setEnabled(false);
        } else {
            List<GrupoEstoque> gruposEstoque = LoadManager.getInstance(GrupoEstoque.class)
                    .setLazyMode(true)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), empresaOrigem))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto))
                    .addParameter(new QueryCustom.QueryCustomParameter(GrupoEstoque.PROP_RO_DEPOSITO, depositoOrigem))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(gruposEstoque)) {
                txtQuantidade.setEnabled(true);
                target.add(txtQuantidade);
            } else {
                txtQuantidade.limpar(target);
                txtQuantidade.setEnabled(false);
            }
        }
    }

    private void limpar(AjaxRequestTarget target) {
        autoCompleteConsultaEmpresaDestino.setEnabled(false);
        autoCompleteConsultaDepositoDestino.setEnabled(false);
        autoCompleteConsultaLocalizacaoEstruturaDestino.setEnabled(false);
        autoCompleteConsultaEmpresaOrigem.setEnabled(true);
        autoCompleteConsultaDepositoOrigem.setEnabled(true);
        autoCompleteConsultaProduto.setEnabled(true);
        txtQuantidade.setEnabled(true);
        pnlSaidaLote.limpar(target);
        autoCompleteConsultaProduto.limpar(target);
        autoCompleteConsultaEmpresaOrigem.limpar(target);
        autoCompleteConsultaEmpresaDestino.limpar(target);
        autoCompleteConsultaDepositoOrigem.limpar(target);
        autoCompleteConsultaDepositoDestino.limpar(target);
        autoCompleteConsultaLocalizacaoEstruturaDestino.limpar(target);
        autoCompleteConsultaLocalizacaoEstruturaOrigem.limpar(target);
        txtQuantidade.limpar(target);
        txtNumeroDocumento.limpar(target);
        txaObservacao.limpar(target);
        txtCodigoBarras.limpar(target);
        this.autoCompleteConsultaEmpresaOrigem.setComponentValue(br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa());
        if (!br.com.celk.system.session.ApplicationSession.get().getSession().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario().isNivelMaster()) {
            this.autoCompleteConsultaEmpresaOrigem.setEnabled(false);
        }
        codigoBarrasProdutoList.clear();
        tblCodigoBarras.update(target);
        adicionadoViaCodigoBarras = false;
    }

    private void adicionarViaCodigoBarras(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (unidadeOrigem == null) {
            throw new ValidacaoException(bundle("msgInformeUnidadeOrigem"));
        }
        if (nrCodigoBarrasProduto != null) {
            CodigoBarrasProduto cbp = LoadManager.getInstance(CodigoBarrasProduto.class)
                    .addProperties(new HQLProperties(CodigoBarrasProduto.class).getProperties())
                    .addProperties(new HQLProperties(Produto.class, CodigoBarrasProduto.PROP_PRODUTO).getProperties())
                    .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_REFERENCIA, nrCodigoBarrasProduto.toString()))
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, CodigoBarrasProduto.Status.TRANSFERIDO.value()))
                    .setMaxResults(1).start().getVO();

            if (cbp == null) {
                resetarFocoCodigoBarras(target);
                throw new ValidacaoException(bundle("codigoBarrasInvalido"));
            } else {
                if (RepositoryComponentDefault.SIM.equals(validarSituacaoCodigoBarrasProduto)) {
                    if (cbp.isEtiquetaForaEstoque()) {
                        resetarFocoCodigoBarras(target);
                        throw new ValidacaoException(bundle("codigoBarrasJaDispensado"));
                    }
                }
            }

            if (depositoOrigem == null) {
                EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class)
                        .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, unidadeOrigem))
                        .start().getVO();

                if (empresaMaterial == null) {
                    throw new ValidacaoException("Não foi encontrado um depósito padrão definido para a empresa.");
                }

                depositoOrigem = empresaMaterial.getDeposito();
            }

            EstoqueEmpresaPK estoqueEmpresaPK = new EstoqueEmpresaPK();
            estoqueEmpresaPK.setEmpresa(unidadeOrigem);
            estoqueEmpresaPK.setProduto(cbp.getProduto());

            GrupoEstoquePK grupoEstoquePK = new GrupoEstoquePK();
            grupoEstoquePK.setGrupo(cbp.getGrupo());

            grupoEstoquePK.setCodigoDeposito(depositoOrigem.getCodigo());
            grupoEstoquePK.setEstoqueEmpresa(new EstoqueEmpresa(estoqueEmpresaPK));
            grupoEstoquePK.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());

            GrupoEstoque ge = LoadManager.getInstance(GrupoEstoque.class)
                    .addProperties(new HQLProperties(GrupoEstoque.class).getProperties())
                    .addProperties(new HQLProperties(EstoqueEmpresa.class, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA)).getProperties())
                    .setId(grupoEstoquePK)
                    .start().getVO();

            if (ge == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_encontrado_estoque_etiqueta_produto"));
            }

            if (adicionadoViaCodigoBarras) {
                if (produto != null && !produto.getCodigo().equals(cbp.getProduto().getCodigo())) {
                    throw new ValidacaoException(bundle("produtoDeveSerIgualJaInformado"));
                }

                if (grupoEstoque != null && !grupoEstoque.equals(cbp.getGrupo())) {
                    throw new ValidacaoException(bundle("loteDeveSerIgualJaInformado"));
                }

                if (depositoOrigem != null && !depositoOrigem.getCodigo().equals(ge.getRoDeposito().getCodigo())) {
                    throw new ValidacaoException(bundle("depositoDeveSerIgualJaInformado"));
                }
            } else {
                autoCompleteConsultaProduto.limpar(target);
                autoCompleteConsultaDepositoOrigem.limpar(target);
                autoCompleteConsultaLocalizacaoEstruturaOrigem.limpar(target);

                MovimentoGrupoEstoqueItemDTO dto = new MovimentoGrupoEstoqueItemDTO();
                dto.setDataValidade(ge.getDataValidade());
                dto.setDeposito(ge.getRoDeposito());
                dto.setEmpresa(ge.getId().getEstoqueEmpresa().getId().getEmpresa());
                dto.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());
                dto.setEstoqueEncomendado(ge.getId().getEstoqueEmpresa().getEstoqueEncomendado());
                dto.setEstoqueFisico(ge.getId().getEstoqueEmpresa().getEstoqueFisico());
                dto.setEstoqueReservado(ge.getId().getEstoqueEmpresa().getEstoqueReservado());
                dto.setGrupoEstoque(ge.getId().getGrupo());
                dto.setProduto(cbp.getProduto());

                produto = dto.getProduto();
                depositoOrigem = ge.getRoDeposito();

                dto.setQuantidade(cbp.getQuantidadeProduto());
                txtQuantidade.setComponentValue(cbp.getQuantidadeProduto());

                dto.getLstCodigoBarrasProduto().add(cbp);
                grupoEstoque = ge.getId().getGrupo();

                pnlSaidaLote.setLoteSelecionado(dto);
                pnlSaidaLote.setModelObject(dto.getGrupoEstoque());

                localizacaoEstruturaOrigem = dto.getLocalizacaoEstrutura();

                autoCompleteConsultaEmpresaDestino.setEnabled(true);
                autoCompleteConsultaEmpresaDestino.setRequired(target, true);
                autoCompleteConsultaDepositoDestino.setEnabled(true);
                autoCompleteConsultaDepositoDestino.setRequired(target, true);

                autoCompleteConsultaLocalizacaoEstruturaDestino.setEnabled(true);
                autoCompleteConsultaLocalizacaoEstruturaDestino.setRequired(target, true);

                autoCompleteConsultaEmpresaOrigem.setEnabled(false);
                autoCompleteConsultaDepositoOrigem.setEnabled(false);
                autoCompleteConsultaLocalizacaoEstruturaOrigem.setEnabled(false);
                autoCompleteConsultaProduto.setEnabled(false);
                txtQuantidade.setEnabled(false);

                target.add(txtQuantidade);
                target.add(autoCompleteConsultaProduto);
                target.add(autoCompleteConsultaDepositoOrigem);
                target.add(autoCompleteConsultaLocalizacaoEstruturaOrigem);
                target.add(autoCompleteConsultaLocalizacaoEstruturaDestino);
                target.add(autoCompleteConsultaEmpresaOrigem);
                target.add(autoCompleteConsultaEmpresaDestino);
                target.add(autoCompleteConsultaDepositoDestino);
                target.add(pnlSaidaLote);
            }

            try {
                CrudUtils.adicionarItem(target, tblCodigoBarras, codigoBarrasProdutoList, cbp);
            } catch (ValidacaoException e) {
                throw e;
            }

            if (!adicionadoViaCodigoBarras) {
                adicionadoViaCodigoBarras = true;
            } else {
                quantidade = new Dinheiro(quantidade).somar(cbp.getQuantidadeProduto()).doubleValue();
                target.add(txtQuantidade);
            }
            txtCodigoBarras.limpar(target);
            target.focusComponent(txtCodigoBarras);
        }
    }

    private void resetarFocoCodigoBarras(AjaxRequestTarget target) {
        txtCodigoBarras.limpar(target);
        target.appendJavaScript(JScript.focusComponent(txtCodigoBarras));
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private List<IColumn> getColumnsCodigoBarras() {
        List<IColumn> columns = new ArrayList<IColumn>();
        CodigoBarrasProduto proxy = on(CodigoBarrasProduto.class);

        columns.add(getCustomColumnCodigoBarras());
        columns.add(createColumn(bundle("codigoBarras"), proxy.getReferencia()));
        columns.add((createColumn(bundle("quantidade"), proxy.getQuantidadeProduto())));
        return columns;
    }

    private ICollectionProvider getCollectionProviderCodigoBarras() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return codigoBarrasProdutoList;
            }
        };
    }

    private IColumn getCustomColumnCodigoBarras() {
        return new MultipleActionCustomColumn<CodigoBarrasProduto>() {
            @Override
            public void customizeColumn(CodigoBarrasProduto rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<CodigoBarrasProduto>() {
                    @Override
                    public void action(AjaxRequestTarget target, CodigoBarrasProduto modelObject) throws ValidacaoException, DAOException {
                        removerCodigoBarras(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerCodigoBarras(AjaxRequestTarget target, CodigoBarrasProduto codigoBarrasProduto) throws ValidacaoException, DAOException {
        if (codigoBarrasProdutoList.size() == 1) {
            limpar(target);
        }

        for (CodigoBarrasProduto barrasProduto : codigoBarrasProdutoList) {
            if (barrasProduto.getCodigo().equals(codigoBarrasProduto.getCodigo())) {

                try {
                    CrudUtils.removerItem(target, tblCodigoBarras, codigoBarrasProdutoList, codigoBarrasProduto);
                } catch (ValidacaoException | DAOException e) {
                    throw e;
                }
                quantidade = new Dinheiro(quantidade).subtrair(barrasProduto.getQuantidadeProduto()).doubleValue();
                target.add(txtQuantidade);
                break;
            }
        }
    }

    private List<Long> carregarEmpresaDestinoTransferencia(Empresa empresa) {
        List<EmpresaDestinoTransferencia> list = LoadManager.getInstance(EmpresaDestinoTransferencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaDestinoTransferencia.PROP_EMPRESA_ORIGEM, empresa))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(list)) {
            return Lambda.extract(list, Lambda.on(EmpresaDestinoTransferencia.class).getEmpresaDestino().getCodigo());
        } else {
            return null;
        }
    }

    public String getUtilizaLocalizacaoEstoque() {
        if (utilizaLocalizacaoEstoque == null) {
            try {
                utilizaLocalizacaoEstoque = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
            } catch (DAOException ex) {
                Logger.getLogger(CadastroTransferenciaEstoquePage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return utilizaLocalizacaoEstoque;
    }
}
