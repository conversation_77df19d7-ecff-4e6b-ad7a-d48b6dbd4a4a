package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.grupoproduto.pnl.PnlConsultaGrupoProduto;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioGraficoCruzamentoDispensacaoParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioGraficoDemonstrativoDispensacaoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.util.Arrays;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 * Programa - 185
 */
@Private

public class GraficoCruzamentoDispensacaoPage extends RelatorioPage<RelatorioGraficoCruzamentoDispensacaoParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<String> dropDownTipoDado;
    private DropDown<RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam> dropDownCategoria;
    private DropDown<RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam> dropDownSerie;
    private DropDown<String> dropDownTipoPreco;
    private InputField<Long> txtQuantidadeCategorias;
    private InputField<Long> txtQuantidadeSeries;
    private PnlConsultaGrupoProduto pnlConsultaGrupoProduto;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaUnidadeOrigem;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private WebMarkupContainer componenteFormaApresentacao;
    private Label lblFormaApresentacao;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;

    private String periodoInicial;
    private String periodoFinal;
    private GrupoProduto _grupoProduto;
    private UsuarioCadsus paciente;
    private Empresa unidadeOrigem;
    private Produto produto;
    private Profissional profissional;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresas")
                .setMultiplaSelecao(true).setOperadorValor(true));
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        form.add(txtQuantidadeCategorias = new RequiredInputField<Long>("quantidadeCategorias"));
        form.add(txtQuantidadeSeries = new InputField<Long>("quantidadeSeries"));
        txtQuantidadeCategorias.setComponentValue(10L);
        txtQuantidadeSeries.setComponentValue(10L);
        form.add(new RequiredInputField("periodoInicial", new PropertyModel(this, "periodoInicial")));
        form.add(new RequiredInputField("periodoFinal", new PropertyModel(this, "periodoFinal")));
        form.add(getDropDownCategoria());
        form.add(getDropDownSerie());
        form.add(getDropDownTipoDado());
        form.add(getDropDownTipoPreco());
        form.add(lblFormaApresentacao = new Label("lblFormaApresentacao", BundleManager.getString("grupoProduto")));
        lblFormaApresentacao.setOutputMarkupId(true);

        pnlConsultaGrupoProduto = new PnlConsultaGrupoProduto("componenteFormaApresentacao", new PropertyModel<GrupoProduto>(this, "_grupoProduto"));
        autoCompleteConsultaUnidadeOrigem = new AutoCompleteConsultaEmpresa("componenteFormaApresentacao", new PropertyModel<Empresa>(this, "unidadeOrigem"));
        autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("componenteFormaApresentacao", new PropertyModel<Produto>(this, "produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("componenteFormaApresentacao", new PropertyModel<Profissional>(this, "profissional"));
        autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus("componenteFormaApresentacao", new PropertyModel<UsuarioCadsus>(this, "paciente"));
        form.add(componenteFormaApresentacao = pnlConsultaGrupoProduto);

    }

    public DropDown<String> getDropDownTipoPreco() {
        if (dropDownTipoPreco == null) {
            dropDownTipoPreco = new DropDown<String>("tipoPreco");
            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_PRECO_MEDIO, BundleManager.getString("precoMedio"));
            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_ULTIMO_PRECO, BundleManager.getString("ultimoPreco"));
        }
        return dropDownTipoPreco;
    }

    public DropDown<RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam> getDropDownCategoria() {
        if (dropDownCategoria == null) {
            dropDownCategoria = new DropDown<RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam>("agrupamentoCategoria");
            dropDownCategoria.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("subGrupo"),
                    VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO)), BundleManager.getString("subGrupo"));
            dropDownCategoria.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("empresaOrigem"),
                    DispensacaoMedicamento.PROP_EMPRESA_ORIGEM), BundleManager.getString("empresaOrigem"));
            dropDownCategoria.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("profissional"),
                    DispensacaoMedicamento.PROP_PROFISSIONAL), BundleManager.getString("profissional"));
            dropDownCategoria.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("usuarioCadsus"),
                    DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO), BundleManager.getString("usuarioCadsus"));
            dropDownCategoria.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("produto"),
                    DispensacaoMedicamentoItem.PROP_PRODUTO), BundleManager.getString("produto"));

            dropDownCategoria.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    eventoCategoria(target);
                }

            });
        }
        return dropDownCategoria;
    }

    public DropDown<RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam> getDropDownSerie() {
        if (dropDownSerie == null) {
            dropDownSerie = new DropDown<RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam>("agrupamentoSerie");
            dropDownSerie.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("subGrupo"),
                    VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO)), BundleManager.getString("subGrupo"));
            dropDownSerie.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("empresaOrigem"),
                    DispensacaoMedicamento.PROP_EMPRESA_ORIGEM), BundleManager.getString("empresaOrigem"));
            dropDownSerie.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("profissional"),
                    DispensacaoMedicamento.PROP_PROFISSIONAL), BundleManager.getString("profissional"));
            dropDownSerie.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("usuarioCadsus"),
                    DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO), BundleManager.getString("usuarioCadsus"));
            dropDownSerie.addChoice(new RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam(
                    BundleManager.getString("produto"),
                    DispensacaoMedicamentoItem.PROP_PRODUTO), BundleManager.getString("produto"));
        }
        return dropDownSerie;
    }

    private void eventoCategoria(AjaxRequestTarget target) {
        RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam cat = dropDownCategoria.getComponentValue();

        if (cat != null) {
            if (VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO).equals(cat.getChave())) {
                if (componenteFormaApresentacao != pnlConsultaGrupoProduto) {
                    componenteFormaApresentacao.replaceWith(pnlConsultaGrupoProduto);
                    componenteFormaApresentacao = pnlConsultaGrupoProduto;
                    pnlConsultaGrupoProduto.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("grupoProduto"));
                    target.add(lblFormaApresentacao);
                }
            } else if (DispensacaoMedicamento.PROP_EMPRESA_ORIGEM.equals(cat.getChave())) {
                if (componenteFormaApresentacao != autoCompleteConsultaUnidadeOrigem) {
                    componenteFormaApresentacao.replaceWith(autoCompleteConsultaUnidadeOrigem);
                    componenteFormaApresentacao = autoCompleteConsultaUnidadeOrigem;
                    autoCompleteConsultaUnidadeOrigem.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("unidadeOrigem"));
                    target.add(lblFormaApresentacao);
                }
            } else if (DispensacaoMedicamento.PROP_PROFISSIONAL.equals(cat.getChave())) {
                if (componenteFormaApresentacao != autoCompleteConsultaProfissional) {
                    componenteFormaApresentacao.replaceWith(autoCompleteConsultaProfissional);
                    componenteFormaApresentacao = autoCompleteConsultaProfissional;
                    autoCompleteConsultaProfissional.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("profissional"));
                    target.add(lblFormaApresentacao);
                }
            } else if (DispensacaoMedicamentoItem.PROP_PRODUTO.equals(cat)) {
                if (componenteFormaApresentacao != autoCompleteConsultaProduto) {
                    componenteFormaApresentacao.replaceWith(autoCompleteConsultaProduto);
                    componenteFormaApresentacao = autoCompleteConsultaProduto;
                    autoCompleteConsultaProduto.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("produto"));
                    target.add(lblFormaApresentacao);
                }
            } else if (DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO.equals(cat.getChave())) {
                if (componenteFormaApresentacao != autoCompleteConsultaUsuarioCadsus) {
                    componenteFormaApresentacao.replaceWith(autoCompleteConsultaUsuarioCadsus);
                    componenteFormaApresentacao = autoCompleteConsultaUsuarioCadsus;
                    autoCompleteConsultaUsuarioCadsus.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("paciente"));
                    target.add(lblFormaApresentacao);
                }
            }
        }

    }

    public DropDown<String> getDropDownTipoDado() {
        if (dropDownTipoDado == null) {
            dropDownTipoDado = new DropDown<String>("tipoDado");
            dropDownTipoDado.addChoice(RelatorioGraficoDemonstrativoDispensacaoDTOParam.TIPO_VALOR, BundleManager.getString("valor"));
            dropDownTipoDado.addChoice(RelatorioGraficoDemonstrativoDispensacaoDTOParam.TIPO_PERCENTUAL, BundleManager.getString("percentual"));

        }
        return dropDownTipoDado;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioGraficoCruzamentoDispensacaoParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioGraficoCruzamentoDispensacaoParam param) throws ReportException {
        if (param.getAgrupamentoCategoria().getChave().equals(param.getAgrupamentoSerie().getChave())) {
            throw new ReportException(BundleManager.getString("msgCategoriaSerieNaoPodemSerIgual"));

        }

        param.setDataInicial(Data.getDataParaPrimeiroDiaMes(Data.parserMounthYear(periodoInicial)));
        param.setDataFinal(Data.getDataParaUltimoDiaMes(Data.parserMounthYear(periodoFinal)));

        RelatorioGraficoCruzamentoDispensacaoAgrupamentoParam cat = param.getAgrupamentoCategoria();

        param.setCategoriasFiltro(null);

        if (VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO).equals(cat.getChave()) && _grupoProduto != null) {
            param.setCategoriasFiltro(Arrays.<Object>asList(_grupoProduto));
        } else if (DispensacaoMedicamento.PROP_EMPRESA_ORIGEM.equals(cat.getChave()) && unidadeOrigem != null) {
            param.setCategoriasFiltro(Arrays.<Object>asList(unidadeOrigem));
        } else if (DispensacaoMedicamento.PROP_PROFISSIONAL.equals(cat.getChave()) && profissional != null) {
            param.setCategoriasFiltro(Arrays.<Object>asList(profissional));
        } else if (DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO.equals(cat.getChave()) && paciente != null) {
            param.setCategoriasFiltro(Arrays.<Object>asList(paciente));
        } else if (DispensacaoMedicamentoItem.PROP_PRODUTO.equals(cat.getChave()) && produto != null) {
            param.setCategoriasFiltro(Arrays.<Object>asList(produto));
        }

        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioGraficoCruzamentoDispensacaoAsync(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("graficoCruzamentoDispensacao");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

}
