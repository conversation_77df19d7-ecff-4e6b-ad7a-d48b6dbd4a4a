package br.com.celk.view.consorcio.movimentacaofinanceira;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.form.RadioGroup;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 *
 * <AUTHOR>
 * Programa - 120
 */
@Private

public class LancamentoMovimentacaoFinanceiraStep1Page extends BasePage{

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaTipoConta autoCompleteConsultaTipoConta;

    private PageableTable tblHistoricoMovimentacaoFinanceira;

    private Boolean tipoMovimentacao = true;
    private Empresa consorciado;
    private TipoConta tipoConta;
    private Long ano;
    private Boolean controlaSaldoPorAno;

    public LancamentoMovimentacaoFinanceiraStep1Page() {
        init();
    }

    private void init(){
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        
        RadioGroup radioGroup; 
        form.add(radioGroup = new RadioGroup("tipoMovimentacao"));

        radioGroup.add(new AjaxRadio("consorciado", new Model(true)) {

            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                autoCompleteConsultaTipoConta.limpar(target);
                autoCompleteConsultaTipoConta.setEnabled(false);
                autoCompleteConsultaEmpresa.limpar(target);
                autoCompleteConsultaEmpresa.setEnabled(true);
            }
        });
        radioGroup.add(new AjaxRadio("tipoConta", new Model(false)) {

            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                autoCompleteConsultaEmpresa.limpar(target);
                autoCompleteConsultaEmpresa.setEnabled(false);
                autoCompleteConsultaTipoConta.limpar(target);
                autoCompleteConsultaTipoConta.setEnabled(true);
            }
        });
        
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("consorciado", true).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)));
        form.add(autoCompleteConsultaTipoConta = new AutoCompleteConsultaTipoConta("tipoConta", true));

        form.add(getContainerAno());

        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                tblHistoricoMovimentacaoFinanceira.update(target);
            }
        });

        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                tblHistoricoMovimentacaoFinanceira.update(target);
            }
        });

        autoCompleteConsultaTipoConta.add(new ConsultaListener<TipoConta>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoConta object) {
                tblHistoricoMovimentacaoFinanceira.update(target);
            }
        });

        autoCompleteConsultaTipoConta.add(new RemoveListener<TipoConta>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoConta object) {
                tblHistoricoMovimentacaoFinanceira.update(target);
            }
        });

        form.add(new AbstractAjaxButton("btnAvancar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                avancar();
            }
        });

        tblHistoricoMovimentacaoFinanceira = new PageableTable("tblHistoricoMovimentacaoFinanceira", getColumnsHistoricoMovimentacaoFinanceira(), getPagerProviderInstance());
        tblHistoricoMovimentacaoFinanceira.populate();
        form.add(tblHistoricoMovimentacaoFinanceira);

        autoCompleteConsultaTipoConta.setEnabled(false);
        
        add(form);
    }

    private WebMarkupContainer getContainerAno() {
        WebMarkupContainer containerAno = new WebMarkupContainer("containerAno");
        DropDown<Long> cbxAno = DropDownUtil.getAnoDropDown("ano", true, false, SubContaAno.ANO_INICIAL, false);
        cbxAno.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                tblHistoricoMovimentacaoFinanceira.update(target);
            }
        });

        containerAno.add(cbxAno);
        containerAno.setVisible(controlaSaldoPorAno());
        this.ano = (long) DataUtil.getAno();
        return containerAno;
    }

    private boolean controlaSaldoPorAno() {
        if (controlaSaldoPorAno == null) {
            try {
                controlaSaldoPorAno = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("controlaSaldoPorAno"));
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            }
        }
        return controlaSaldoPorAno;
    }

    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return MovimentacaoFinanceira.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new String[] {
                        MovimentacaoFinanceira.PROP_DATA_MOVIMENTACAO,
                        MovimentacaoFinanceira.PROP_DESCRICAO_MOVIMENTACAO,
                        MovimentacaoFinanceira.PROP_VALOR,
                        VOUtils.montarPath(MovimentacaoFinanceira.PROP_TIPO_MOVIMENTACAO, TipoMovimentacao.PROP_REFERENCIA),
                        VOUtils.montarPath(MovimentacaoFinanceira.PROP_TIPO_MOVIMENTACAO, TipoMovimentacao.PROP_TIPO_MOVIMENTO),
                        VOUtils.montarPath(MovimentacaoFinanceira.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO),
                        VOUtils.montarPath(MovimentacaoFinanceira.PROP_SUB_CONTA, SubConta.PROP_TIPO_CONTA, TipoConta.PROP_REFERENCIA),
                        VOUtils.montarPath(MovimentacaoFinanceira.PROP_SUB_CONTA, SubConta.PROP_TIPO_CONTA, TipoConta.PROP_DESCRICAO),
                        VOUtils.montarPath(MovimentacaoFinanceira.PROP_SUB_CONTA_ANO, SubContaAno.PROP_ANO),
                        VOUtils.montarPath(MovimentacaoFinanceira.PROP_USUARIO, Usuario.PROP_NOME)
                });
            }

            @Override
            public void consultaCustomizeParameters(List<BuilderQueryCustom.QueryParameter> parameters) {
                MovimentacaoFinanceira proxy = on(MovimentacaoFinanceira.class);
                parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getSubConta().getConta().getConsorciado()), consorciado));
                parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getSubConta().getTipoConta()), tipoConta));
                if (controlaSaldoPorAno()) {
                    parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getSubContaAno().getAno()), ano));
                }
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(MovimentacaoFinanceira.PROP_DATA_MOVIMENTACAO, false);
            }
        };
    }

    private List<IColumn> getColumnsHistoricoMovimentacaoFinanceira() {
        List<IColumn> columns = new ArrayList<IColumn>();
        MovimentacaoFinanceira proxy = on(MovimentacaoFinanceira.class);

        columns.add(new DateColumn<MovimentacaoFinanceira>(bundle("data"), path(proxy.getDataMovimentacao())).setPattern("dd/MM/yyyy"));
        columns.add(createColumn(bundle("consorciado"), proxy.getSubConta().getConta().getConsorciado().getDescricao()));
        columns.add(createColumn(bundle("tipoConta"), proxy.getSubConta().getTipoConta().getDescricaoTipoConta()));
        if (controlaSaldoPorAno()) {
            columns.add(createColumn(bundle("exercicio"), proxy.getSubContaAno().getAno()));
        }
        columns.add(createColumn(bundle("movimentacao"), proxy.getTipoMovimentacao().getReferencia()));
        columns.add(createColumn(bundle("historico"), proxy.getDescricaoMovimentacao()));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getNome()));
        columns.add(createColumn(bundle("tipoMovimento"), proxy.getTipoMovimentacao().getTipoMovimentoFormatado()));
        columns.add(createColumn(bundle("valor"), proxy.getValorComTipoMovimentacaoFormatado()));

        return columns;
    }

    private void avancar() throws ValidacaoException, DAOException {
        if (tipoMovimentacao) {
            if (consorciado.getContaPadrao() == null) {
                throw new ValidacaoException(BundleManager.getString("consorciadoNaoPossuiContaPadraoDefinida"));
            }
            setResponsePage(new LancamentoMovimentacaoFinanceiraStep2Page(consorciado, ano));
        } else {
            setResponsePage(new LancamentoMovimentacaoFinanceiraStep3Page(tipoConta, ano));
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("movimentacaoFinanceira");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
    
}
