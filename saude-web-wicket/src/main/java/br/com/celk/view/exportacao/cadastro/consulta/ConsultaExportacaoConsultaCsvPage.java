package br.com.celk.view.exportacao.cadastro.consulta;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.exportacao.ExportacaoConsultaCsv;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 1105
 */
@Private
public class ConsultaExportacaoConsultaCsvPage extends ConsultaPage<ExportacaoConsultaCsv, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField("descricao"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ExportacaoConsultaCsv proxy = on(ExportacaoConsultaCsv.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));

        return columns;
    }

    private CustomColumn<ExportacaoConsultaCsv> getCustomColumn() {
        return new CustomColumn<ExportacaoConsultaCsv>() {

            @Override
            public Component getComponent(String componentId, final ExportacaoConsultaCsv rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) {
                        setResponsePage(new CadastroExportacaoConsultaCsvPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                        BOFactoryWicket.delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) {
                        setResponsePage(new CadastroExportacaoConsultaCsvPage(rowObject, true));
                    }
                };
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return ExportacaoConsultaCsv.class;
            }

            @Override
            public String[] getProperties() {
                return new HQLProperties(ExportacaoConsultaCsv.class).getProperties();
            }

        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ExportacaoConsultaCsv.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();
        parameters.add(new QueryCustom.QueryCustomParameter(ExportacaoConsultaCsv.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroExportacaoConsultaCsvPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultas");
    }
}
