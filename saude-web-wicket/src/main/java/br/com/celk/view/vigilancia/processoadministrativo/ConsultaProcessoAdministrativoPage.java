package br.com.celk.view.vigilancia.processoadministrativo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgMotivoArea;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.CustomColorSelectionTableRow;
import br.com.celk.component.table.TableColorEnum;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.SelectionPageableTable;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.processoadministrativo.decisao.CadastroDecisaoProcessoAdministrativoPage;
import br.com.celk.view.vigilancia.processoadministrativo.dialog.DlgAcoesProcessoAdministrativo;
import br.com.celk.view.vigilancia.processoadministrativo.dialog.DlgPendenciasProcessoAdministrativo;
import br.com.celk.view.vigilancia.processoadministrativo.dialog.DlgSolicitacaoDocumentoProcessoAdministrativo;
import br.com.celk.view.vigilancia.processoadministrativo.parecer.CadastroParecerProcessoAdministrativoPage;
import br.com.celk.view.vigilancia.rotinas.DlgReceberDecisaoProcessoAdministrativo;
import br.com.celk.view.vigilancia.rotinas.DlgRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.rotinas.autoinfracao.ConsultaAutoInfracaoPage;
import br.com.celk.view.vigilancia.rotinas.automulta.ConsultaAutoMultaPage;
import br.com.celk.view.vigilancia.rotinas.autopenalidade.CadastroAutoPenalidadePage;
import br.com.celk.view.vigilancia.rotinas.autopenalidade.ConsultaAutoPenalidadePage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.FiscalAutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMultaFiscal;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.resource.CssResourceReference;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 874
 */
@Private
public class ConsultaProcessoAdministrativoPage extends BasePage {

    private Form<ProcessoAdministrativoDTOParam> form;
    private SelectionPageableTable<ProcessoAdministrativo> table;
    private QueryPagerProvider<ProcessoAdministrativo, ProcessoAdministrativoDTOParam> dataProvider;
    private DlgAcoesProcessoAdministrativo dlgAcoesProcessoAdministrativo;
    private ProcurarButton btnProcurar;
    private DlgRequerimentoVigilanciaAnexo dlgRequerimentoVigilanciaAnexo;
    private DlgSolicitacaoDocumentoProcessoAdministrativo dlgOcorrenciaProcessoAdministrativo;
    private Class classeVoltar;
    private DlgPendenciasProcessoAdministrativo dlgPendenciasProcesso;
    private DlgConfirmacaoSimNao dlgConfirmacaoFinalizar;
    private DlgMotivoArea dlgTextoCertidaoProcessoAdministrativo;
    private PnlChoicePeriod pnlChoicePeriod;
    private DlgReceberDecisaoProcessoAdministrativo dlgReceberDecisao;
    private boolean exibirSomentesProcessosFiscal;

    private AjaxPreviewBlank ajaxPreviewBlank;

    private static final String CSS_FILE = "ConsultaProcessoAdministrativoPage.css";
    private DropDown<Long> dropDownSituacao;
    private DlgConfirmacaoSimNao<VigilanciaFinanceiro> dlgValidacaoBoleto;

    public ConsultaProcessoAdministrativoPage() {
        init();
    }

    public ConsultaProcessoAdministrativoPage(Class classeVoltar) {
        this.classeVoltar = classeVoltar;
        init();
    }

    public ConsultaProcessoAdministrativoPage(ProcessoAdministrativoDTOParam param, Class classeVoltar) {
        getForm().getModel().setObject(param);
        this.classeVoltar = classeVoltar;
        init();
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        response.render(CssHeaderItem.forReference(new CssResourceReference(ConsultaProcessoAdministrativoPage.class, CSS_FILE)));
    }

    private void init() {
        exibirSomentesProcessosFiscal = isActionPermitted(Permissions.VISUALIZAR_TODOS);

        ProcessoAdministrativoDTOParam proxy = on(ProcessoAdministrativoDTOParam.class);

        getForm().add(new InputField<String>(path(proxy.getNumeroProcesso())));
        getForm().add(new InputField<String>(path(proxy.getAutuado())));
        getForm().add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        getForm().add(new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getVigilanciaPessoa())));
        getForm().add(pnlChoicePeriod = new PnlChoicePeriod(path(proxy.getPeriodoCadastro())));
        pnlChoicePeriod.setOutputMarkupPlaceholderTag(true);
        pnlChoicePeriod.setDefaultOutro();
        getForm().add(getDropDownSituacao(path(proxy.getSituacao())));

        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getTipo()), ProcessoAdministrativo.Tipo.values(), true, "Todos", false, false, false));

        getForm().add(new AbstractAjaxLink("btnPendencias") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                addModal(target, dlgPendenciasProcesso = new DlgPendenciasProcessoAdministrativo(newModalId()) {

                    @Override
                    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        table.clearSelection();
                        table.update(target);
                    }
                });
                dlgPendenciasProcesso.show(target);
            }
        });

        getForm().add(table = new SelectionPageableTable("table", getColumns(), getDataProvider(), 10){
            @Override
            protected Item newRowItem(String id, int index, IModel model) {
                return new CustomColorSelectionTableRow(id, index, model, this) {
                    @Override
                    public TableColorEnum getColor() {
                        ProcessoAdministrativo processoAdministrativo = ((ProcessoAdministrativo) getRowObject());
                        if (ProcessoAdministrativo.Situacao.CONCLUIDO.value().equals(processoAdministrativo.getSituacao())) {
                            return TableColorEnum.VERDE_LIGHT;
                        }

                        if(!isEnabledMovimentarProcessoAdministrativo(null,processoAdministrativo)) {
                            return TableColorEnum.VERMELHA_LIGHT;
                        }

                        return TableColorEnum.PADRAO;
                    }
                };
            }
        });
        table.addSelectionAction(new ISelectionAction<ProcessoAdministrativo>() {
            @Override
            public void onSelection(AjaxRequestTarget target, ProcessoAdministrativo object) {
                selectionActionTable(target, object);
            }
        });
        table.populate();

        getForm().add(btnProcurar = new ProcurarButton("btnProcurar", table) {
            @Override
            public ProcessoAdministrativoDTOParam getParam() {
                ProcessoAdministrativoDTOParam param = (ProcessoAdministrativoDTOParam) getForm().getModel().getObject();
                param.setExibirSomentesProcessosFiscal(exibirSomentesProcessosFiscal);
                return param;
            }
        });

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar();
            }
        }.setDefaultFormProcessing(false).setVisible(classeVoltar != null));

        getForm().add(new AbstractAjaxButton("btnConsultaPenalidade") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ConsultaAutoPenalidadePage page = new ConsultaAutoPenalidadePage();
                page.setClassVoltar(ConsultaProcessoAdministrativoPage.class);
                setResponsePage(page);
            }
            @Override
            public boolean isVisible() {
                return new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaAutoPenalidadePage.class.getName());
            }
        }.setDefaultFormProcessing(false));

        getForm().add(new AbstractAjaxButton("btnConsultaInfracao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PageParameters parameters = new PageParameters();
                parameters.add("returnClass", ConsultaProcessoAdministrativoPage.class.getName());
                parameters.add("exibirOutrosFiscais", RepositoryComponentDefault.SIM_LONG);
                ConsultaAutoInfracaoPage consultaAutoInfracaoPage = new ConsultaAutoInfracaoPage(parameters);
                setResponsePage(consultaAutoInfracaoPage);
            }
            @Override
            public boolean isVisible() {
                return new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaAutoInfracaoPage.class.getName());
            }
        }.setDefaultFormProcessing(false));

        getForm().add(new AbstractAjaxButton("btnConsultaMulta") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PageParameters parameters = new PageParameters();
                parameters.add("returnClass", ConsultaProcessoAdministrativoPage.class.getName());
                parameters.add("exibirOutrosFiscais", RepositoryComponentDefault.SIM_LONG);
                ConsultaAutoMultaPage page = new ConsultaAutoMultaPage(parameters);
                setResponsePage(page);
            }
            @Override
            public boolean isVisible() {
                return new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaAutoMultaPage.class.getName());
            }
        }.setDefaultFormProcessing(false));

        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());

        add(getForm());

        btnProcurar.procurar();
    }


    private DropDown<Long> getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<>(id);
            dropDownSituacao.addAjaxUpdateValue();
            dropDownSituacao.setOutputMarkupPlaceholderTag(true);
            dropDownSituacao.addChoice(null, BundleManager.getString("todas"));

            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.PENDENTE.value(),ProcessoAdministrativo.Situacao.PENDENTE.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.EM_ANALISE.value(), ProcessoAdministrativo.Situacao.EM_ANALISE.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_RECURSO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_NOTIFICACAO_1_RECURSO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_NOTIFICACAO_1_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.EM_ANALISE_1_RECURSO.value(), ProcessoAdministrativo.Situacao.EM_ANALISE_1_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO_1_RECURSO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO_1_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.EM_ANALISE_2_RECURSO.value(), ProcessoAdministrativo.Situacao.EM_ANALISE_2_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO_2_RECURSO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_DECISAO_2_RECURSO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.AGUARDANDO_ARQUIVAMENTO.value(), ProcessoAdministrativo.Situacao.AGUARDANDO_ARQUIVAMENTO.descricao());
            dropDownSituacao.addChoice(ProcessoAdministrativo.Situacao.CONCLUIDO.value(), ProcessoAdministrativo.Situacao.CONCLUIDO.descricao());
        }
        return dropDownSituacao;
    }


    private void selectionActionTable(AjaxRequestTarget target, ProcessoAdministrativo object) {
        if(isEnabledMovimentarProcessoAdministrativo(target, object)) {
            initDlgAcoesProcessoAdministrativo(target, object);
        }
        table.clearSelection(target);
    }

    private void voltar() {
        setResponsePage(classeVoltar);
    }

    private Form<ProcessoAdministrativoDTOParam> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new ProcessoAdministrativoDTOParam()));
        }
        return form;
    }


    private List<ISortableColumn<ProcessoAdministrativo>> getColumns() {
        List columns = new ArrayList<>();
        ProcessoAdministrativo proxy = on(ProcessoAdministrativo.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("numeroProcesso"), proxy.getNumeroProcesso(), proxy.getNumeroProcessoFormatado()));
        columns.add(createSortableColumn(bundle("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("tipo"), proxy.getTipo(), proxy.getDescricaoTipo()));
        columns.add(createColumn(bundle("numeroDocumento"), proxy.getNumeroTipoProcessoFormatado()));
        columns.add(createSortableColumn(bundle("dataInicio"), proxy.getDataInicio()));
        columns.add(createSortableColumn(bundle("autuado"), proxy.getAutuado()));
        columns.add(createSortableColumn(BundleManager.getString("situacaoFinanceira"), proxy.getSituacaoFinanceira(), proxy.getDescricaoSituacaoFinanceira()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getSituacao(), proxy.getDescricaoSituacao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ProcessoAdministrativo>() {
            @Override
            public void customizeColumn(ProcessoAdministrativo rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ProcessoAdministrativo>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoAdministrativo modelObject) throws ValidacaoException, DAOException {
                        responsePage(modelObject);
                    }
                }).setVisible(isActionPermitted(Permissions.CONSULTAR));
                addAction(ActionType.IMPRIMIR, rowObject, new IModelAction<ProcessoAdministrativo>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoAdministrativo modelObject) throws ValidacaoException, DAOException {
                        responseImpression(target, modelObject);
                    }
                }).setVisible(isActionPermitted(Permissions.IMPRIMIR));

                addAction(ActionType.ANEXO, rowObject, new IModelAction<ProcessoAdministrativo>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoAdministrativo modelObject) throws ValidacaoException, DAOException {
                        PnlRequerimentoVigilanciaAnexoDTO dto = montarDtoAnexo(modelObject);
                        addModal(target, dlgRequerimentoVigilanciaAnexo = new DlgRequerimentoVigilanciaAnexo<ProcessoAdministrativo>(newModalId(), dto, true) {
                            public void onConfirmar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList, String descricaoOcorrencia) throws ValidacaoException, DAOException {
                                ProcessoAdministrativo processoAdministrativo = getObject();
                                BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(processoAdministrativo, requerimentoVigilanciaAnexoDTOList, requerimentoVigilanciaAnexoDTOExcluidoList, descricaoOcorrencia, true);
                                table.update(target);
                            }
                        });
                        dlgRequerimentoVigilanciaAnexo.show(target, modelObject);
                    }
                }).setIcon(Icon.CLIP).setTitleBundleKey("anexarDocumentos").setVisible(isActionPermitted(Permissions.ANEXAR));

                addAction(ActionType.CONJUNTO, rowObject, new IModelAction<ProcessoAdministrativo>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoAdministrativo modelObject) throws ValidacaoException, DAOException {
                        if(isEnabledMovimentarProcessoAdministrativo(target, modelObject)) {
                            initDlgAcoesProcessoAdministrativo(target, modelObject);
                        }
                        table.clearSelection(target);
                    }
                }).setIcon(Icon.ROUND_PLUS).setTitleBundleKey("maisAcoes");
            }
        };
    }

    private PnlRequerimentoVigilanciaAnexoDTO montarDtoAnexo(ProcessoAdministrativo modelObject) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(modelObject);

        List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);
            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
        PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
        dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
        return dtoPnlAnexo;
    }

    private void responseImpression(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) {
        try {
            info(target, "Impressão enviada para processamento e em alguns instantes você receberá uma mensagem com o arquivo gerado");
            BOFactoryWicket.getBO(VigilanciaReportFacade.class).enviarImpressaoDocumentosProcessoAdministrativo(processoAdministrativo);
        } catch (ValidacaoException e) {
            warn(target, e.getMessage());
        } catch (Exception e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }

    private void responsePage(ProcessoAdministrativo modelObject) {
        setResponsePage(new DetalhesProcessoAdministrativoPage(modelObject));
    }

    private void initDlgAcoesProcessoAdministrativo(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) {
        if(dlgAcoesProcessoAdministrativo == null) {
            addModal(target, dlgAcoesProcessoAdministrativo = new DlgAcoesProcessoAdministrativo(newModalId()) {
                @Override
                public void onConsultar(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    responsePage(processoAdministrativo);
                }

                @Override
                public void onEmAnalise(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).analisarProcessoAdministrativo(processoAdministrativo);
                    atualizarTabela(target);
                    if (classeVoltar != null) {
                        setResponsePage(classeVoltar);
                    }
                }

                @Override
                public void onReverterSituacao(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    actionReverterProcessoAdministrativo(processoAdministrativo);
                    atualizarTabela(target);
                }

                @Override
                public void onDataRecebimento(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    actionDataRecebimento(target, processoAdministrativo);
                }

                @Override
                public void onLancarParecer(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    CadastroParecerProcessoAdministrativoPage page = new CadastroParecerProcessoAdministrativoPage(processoAdministrativo, getPageParameters());
                    setResponsePage(page);
                }

                @Override
                public void onLancarDecisao(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    Page page = new CadastroDecisaoProcessoAdministrativoPage(processoAdministrativo, getPageParameters());
                    setResponsePage(page);
                }

                @Override
                public void onGerarPenalidade(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    CadastroAutoPenalidadePage cadastroAutoPenalidadePage = new CadastroAutoPenalidadePage() {
                        @Override
                        public Class getResponsePage() {
                            return ConsultaProcessoAdministrativoPage.class;
                        }
                    };
                    cadastroAutoPenalidadePage.instanceFromProcessoAdministrativo(target, processoAdministrativo);
                    setResponsePage(cadastroAutoPenalidadePage);
                }

                @Override
                public void onSolicitarDocumento(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    initDlgDocumentoProcessoAdministrativo(target, processoAdministrativo);
                }

                @Override
                public void onGerarCertidao(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    initDlgGerarCertidaoProcessoAdministrativo(target, processoAdministrativo);
                }

                @Override
                public void onFinalizar(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
                    actionFinalizarProcessoAdministrativo(target, processoAdministrativo);
                }

                @Override
                public void onVoltar(AjaxRequestTarget target) {
                    atualizarTabela(target);
                }
            });
        }
        DlgProcessoAdministrativoDTO dto = new DlgProcessoAdministrativoDTO();
        dto.setProcessoAdministrativo(processoAdministrativo);
        dto.setPermissaoAnalisar(isActionPermitted(Permissions.ANALISAR));
        dto.setPermissaoReverter(isActionPermitted(Permissions.ENCAMINHAR));
        dto.setPermissaoConsultar(isActionPermitted(Permissions.CONSULTAR));
        dto.setPermissaoParecer(isActionPermitted(Permissions.PARECER));
        dto.setPermissaoDecisao(isActionPermitted(Permissions.DECISAO));
        dto.setPermissaoSolicitarDocumento(isActionPermitted(Permissions.PEDIDO_DOCUMENTO));
        dto.setPermissaoAnexarDocumento(isActionPermitted(Permissions.ANEXAR));
        dto.setPermissaoFinalizar(isActionPermitted(Permissions.CONFIRMAR));
        dto.setPermissaoAutoPenalidade(isActionPermitted(Permissions.AUTO_PENALIDADE));
        dto.setPermissaoDataRecebimento(isActionPermitted(Permissions.RECEBER));
        dlgAcoesProcessoAdministrativo.show(target, dto);
    }

    private void actionReverterProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
        BOFactory.getBO(VigilanciaFacade.class).reverterProcessoAdministrativo(processoAdministrativo,true, true);
    }

    private void actionFinalizarProcessoAdministrativo(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) {
        VigilanciaFinanceiro vigilanciaFinanceiro = null;
        AutoPenalidade autoPenalidade = AutosHelper.getAutoPenalidade(processoAdministrativo);
        if(autoPenalidade != null && autoPenalidade.getCodigo() != null) {
            vigilanciaFinanceiro = VigilanciaHelper.getVigilanciaFinanceiro(autoPenalidade);
        } else if(processoAdministrativo.getAutoMulta() != null && processoAdministrativo.getAutoMulta().getCodigo() != null) {
            vigilanciaFinanceiro = VigilanciaHelper.getVigilanciaFinanceiro(processoAdministrativo.getAutoMulta());
        }
        String descricaoDialog;
        Long financeiro = null;
        if(vigilanciaFinanceiro  != null && vigilanciaFinanceiro.getCodigo() != null) {
            descricaoDialog = VigilanciaHelper.validacaoBoletosPendentes(vigilanciaFinanceiro);
            descricaoDialog = descricaoDialog.concat(" Deseja arquivar o processo administrativo e o registro do boleto?");
            financeiro = vigilanciaFinanceiro.getCodigo();
        } else {
            descricaoDialog = BundleManager.getString("desejaArquivarProcessoAdministrativo");
        }

        final Long finalFinanceiro = financeiro;
        addModal(target, dlgConfirmacaoFinalizar = new DlgConfirmacaoSimNao<ProcessoAdministrativo>(newModalId(), descricaoDialog) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(VigilanciaFacade.class).finalizarProcessoAdministrativo(getObject(), false, finalFinanceiro);
                atualizarTabela(target);
            }
        });
        dlgConfirmacaoFinalizar.show(target);
        dlgConfirmacaoFinalizar.setObject(processoAdministrativo);
    }

    private void actionDataRecebimento(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) {
        addModal(target, dlgReceberDecisao = new DlgReceberDecisaoProcessoAdministrativo(newModalId(), processoAdministrativo) {
            @Override
            public void onRecebimento(AjaxRequestTarget target, Date dataRecebimento, ProcessoAdministrativo processoAdministrativo) throws DAOException, ValidacaoException {
                actionRecebeProcessoAdministrativo(dataRecebimento, processoAdministrativo);
                atualizarTabela(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                atualizarTabela(target);
            }
        });
        dlgReceberDecisao.show(target);
    }

    private void actionRecebeProcessoAdministrativo(Date dataRecebimento, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException {
        if (dataRecebimento == null) {
            throw new ValidacaoException(bundle("msgInformeData"));
        }
        if(ProcessoAdministrativo.Tipo.AUTO_MULTA.value().equals(processoAdministrativo.getTipo())) {
            processoAdministrativo.setAutoMulta(loadAutoMulta(processoAdministrativo));
            if(processoAdministrativo.getAutoMulta().getDataRecebimento() == null) {
                processoAdministrativo.getAutoMulta().setDataRecebimento(dataRecebimento);
            }
            Date dataPrazoRecurso = AutosHelper.getDataPrazoRecurso(dataRecebimento, processoAdministrativo);
            if (dataPrazoRecurso != null) {
                if (processoAdministrativo.getPrazoSegundaInstancia() == null) {
                    processoAdministrativo.setPrazoSegundaInstancia(dataPrazoRecurso);
                } else if (processoAdministrativo.getPrazoTerceiraInstancia() == null) {
                    processoAdministrativo.setPrazoTerceiraInstancia(dataPrazoRecurso);
                }
                BOFactoryWicket.save(processoAdministrativo.getAutoMulta());
            }
        } else if(ProcessoAdministrativo.Tipo.AUTO_INFRACAO.value().equals(processoAdministrativo.getTipo())) {
            processoAdministrativo.setAutoInfracao(loadAutoInfracao(processoAdministrativo));
            if(processoAdministrativo.getAutoInfracao().getDataRecebimento() == null) {
                processoAdministrativo.getAutoInfracao().setDataRecebimento(dataRecebimento);
            }
            Date dataPrazoRecurso = AutosHelper.getDataPrazoRecurso(dataRecebimento, processoAdministrativo);
            if (processoAdministrativo.getPrazoTerceiraInstancia() == null) {
                processoAdministrativo.setPrazoTerceiraInstancia(dataPrazoRecurso);
            }
            BOFactoryWicket.save(processoAdministrativo.getAutoInfracao());
        }
        BOFactoryWicket.save(processoAdministrativo);
    }

    private AutoInfracao loadAutoInfracao(ProcessoAdministrativo processoAdministrativo) {
        return LoadManager.getInstance(AutoInfracao.class)
                .addProperties(new HQLProperties(AutoInfracao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoInfracao.PROP_CODIGO),processoAdministrativo.getAutoInfracao().getCodigo()))
                .setMaxResults(1).start().getVO();
    }

    private AutoMulta loadAutoMulta(ProcessoAdministrativo processoAdministrativo) {
        return LoadManager.getInstance(AutoMulta.class)
                .addProperties(new HQLProperties(AutoMulta.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoMulta.PROP_CODIGO),processoAdministrativo.getAutoMulta().getCodigo()))
                .setMaxResults(1).start().getVO();
    }

    private boolean isEnabledMovimentarProcessoAdministrativo(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) {
        if(ProcessoAdministrativo.Tipo.AUTO_INFRACAO.value().equals(processoAdministrativo.getTipo())) {
            if(AutoInfracao.Status.AGUARDANDO_RECEBIMENTO.value().equals(processoAdministrativo.getAutoInfracao().getSituacao())) {
                if(target != null) {
                    warn(target, bundle("autoInfracaoXNaoFoiRecebidoPeloContribuinte", processoAdministrativo.getAutoInfracao().getNumeroFormatado()));
                    atualizarTabela(target);
                }
                return false;
            }
            boolean emPeriodoRecurso = AutosHelper.emPeriodoRecurso(processoAdministrativo.getAutoInfracao());
            if(emPeriodoRecurso) {
                if(!AutosHelper.existsDefesaPreviaEnviada(processoAdministrativo)) {
                    if(target != null) {
                        warn(target, bundle("autoXEstaPeriodoDefesaAindaNaoFoiRegistradoRecursoContribuinte", "De Infração Nº " + processoAdministrativo.getAutoInfracao().getNumeroFormatado()));
                        atualizarTabela(target);
                    }
                    return false;
                }
            }
        } else if(ProcessoAdministrativo.Tipo.AUTO_MULTA.value().equals(processoAdministrativo.getTipo())) {
            boolean emPeriodoRecurso = AutosHelper.emPeriodoRecurso(processoAdministrativo.getAutoMulta());
            if(emPeriodoRecurso) {
                if (!AutosHelper.existsDefesaPreviaEnviada(processoAdministrativo)) {
                    if(target  != null) {
                        warn(target, bundle("autoXEstaPeriodoDefesaAindaNaoFoiRegistradoRecursoContribuinte", "De Multa Nº " + processoAdministrativo.getAutoMulta().getNumeroFormatado()));
                        atualizarTabela(target);
                    }
                    return false;
                }
            }
        }
        return true;
    }

    private void atualizarTabela(AjaxRequestTarget target) {
        table.clearSelection(target);
        table.populate(target);
    }

    private void initDlgDocumentoProcessoAdministrativo(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) {
        if (dlgOcorrenciaProcessoAdministrativo == null) {
            addModal(target, dlgOcorrenciaProcessoAdministrativo = new DlgSolicitacaoDocumentoProcessoAdministrativo(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, CadastroProcessoAdministrativoOcorrenciaDTO dto) throws ValidacaoException, DAOException {
                    dto.setTipo(ProcessoAdministrativoOcorrencia.Tipo.SOLICITACAO_DOCUMENTO.value());
                    BOFactoryWicket.getBO(VigilanciaFacade.class).solicitarDocumentoProcessoAdministrativo(dto);
                    atualizarTabela(target);
                    getSession().getFeedbackMessages().info(this, bundle("documentoSolicitadoComSucesso"));
                    ConsultaProcessoAdministrativoPage.this.updateNotificationPanel(target);
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    atualizarTabela(target);
                }
            });
        }
        List<Profissional> profissionalList = null;
        if(ProcessoAdministrativo.Tipo.AUTO_INFRACAO.value().equals(processoAdministrativo.getTipo())) {
            List<FiscalAutoInfracao> fiscaisFromInfracao = AutosHelper.getFiscaisFromInfracao(processoAdministrativo.getAutoInfracao());
            if (CollectionUtils.isNotNullEmpty(fiscaisFromInfracao)) {
                profissionalList = Lambda.extract(fiscaisFromInfracao, Lambda.on(FiscalAutoInfracao.class).getProfissional());
            }
            dlgOcorrenciaProcessoAdministrativo.show(target, processoAdministrativo, profissionalList);
        } else if(ProcessoAdministrativo.Tipo.AUTO_MULTA.value().equals(processoAdministrativo.getTipo())) {
            List<AutoMultaFiscal> fiscaisFromMulta = AutosHelper.getFiscaisFromMulta(processoAdministrativo.getAutoMulta());
            if (CollectionUtils.isNotNullEmpty(fiscaisFromMulta)) {
                profissionalList = Lambda.extract(fiscaisFromMulta, Lambda.on(AutoMultaFiscal.class).getProfissional());
            }
        } else if(ProcessoAdministrativo.Tipo.SOLICITACAO_JURIDICA.value().equals(processoAdministrativo.getTipo())){
            try {
                profissionalList = VigilanciaHelper.getFiscaisRequerimentoList(processoAdministrativo.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia());
            } catch (DAOException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        dlgOcorrenciaProcessoAdministrativo.show(target, processoAdministrativo, profissionalList);
    }

    private void initDlgGerarCertidaoProcessoAdministrativo(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) {
        if (dlgTextoCertidaoProcessoAdministrativo == null) {
            addModal(target, dlgTextoCertidaoProcessoAdministrativo = new DlgMotivoArea<ProcessoAdministrativo>(newModalId(), bundle("texto"), bundle("descricao")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, ProcessoAdministrativo object) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).gerarCertidaoProcessoAdministrativo(object, motivo);
                    atualizarTabela(target);
                    getSession().getFeedbackMessages().info(this, bundle("despachoAnexadoProcessoAdministrativo"));
                    ConsultaProcessoAdministrativoPage.this.updateNotificationPanel(target);
                }
            });
        }
        dlgTextoCertidaoProcessoAdministrativo.setObject(processoAdministrativo);
        dlgTextoCertidaoProcessoAdministrativo.show(target);
    }


    private QueryPagerProvider getDataProvider() {
        if (dataProvider == null) {
            dataProvider = new QueryPagerProvider<ProcessoAdministrativo, ProcessoAdministrativoDTOParam>() {
                @Override
                public DataPagingResult executeQueryPager(DataPaging<ProcessoAdministrativoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                    if (getSort() != null) {
                        getForm().getModel().getObject().setAscending(getSort().isAscending());
                        getForm().getModel().getObject().setPropSort(getSort().getProperty());
                    }
                    return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarProcessoAdministrativo(dataPaging);
                }
            };
        }
        return dataProvider;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaProcessosAdministrativos");
    }

}
