package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacaoestrutura.autocomplete.AutoCompleteConsultaLocalizacaoEstrutura;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.tipodocumento.autocomplete.AutoCompleteConsultaTipoDocumento;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioMovimentacaoEstoqueParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 28
 */
@Private

public class RelatorioMovimentacaoEstoquePage extends RelatorioPage<RelatorioMovimentacaoEstoqueParam> {

    private DropDown cbxExibeObservacao;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("lstEmpresa")
                .setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA))
                .setOperadorValor(true)
                .setMultiplaSelecao(true));

        form.add(new AutoCompleteConsultaEmpresa("empresaDestino"));
        form.add(autoCompleteConsultaProduto = (AutoCompleteConsultaProduto) new AutoCompleteConsultaProduto("lstProduto").setOperadorValor(true).setMultiplaSelecao(true));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new AutoCompleteConsultaTipoDocumento("lstTipoDocumento").setOperadorValor(true).setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaDeposito("lstDeposito").setOperadorValor(true).setMultiplaSelecao(true));
        form.add(new InputField<String>("grupoEstoque"));
        form.add(new InputField<String>("numeroDocumento"));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getDropDownTipoRelatorio());
        form.add(cbxExibeObservacao = DropDownUtil.getNaoSimDropDown("exibeObservacao"));
        cbxExibeObservacao.setEnabled(false);
        form.add(new AutoCompleteConsultaLocalizacaoEstrutura("localizacaoEstrutura").setExibirApenasVisivelSim(true));
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));
    }

    private DropDown getDropDownTipoRelatorio() {
        final DropDown dropDown = new DropDown("tipoRelatorio");

        dropDown.addChoice(Bundle.getStringApplication("rotulo_resumido"), BundleManager.getString("resumido"));
        dropDown.addChoice(Bundle.getStringApplication("rotulo_detalhado"), BundleManager.getString("detalhado"));

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                cbxExibeObservacao.setEnabled(dropDown.getModelObject().equals(Bundle.getStringApplication("rotulo_detalhado")));
                cbxExibeObservacao.limpar(target);
                target.add(cbxExibeObservacao);
            }
        });

        return dropDown;
    }

    @Override
    public Class<RelatorioMovimentacaoEstoqueParam> getDTOParamClass() {
        return RelatorioMovimentacaoEstoqueParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioMovimentacaoEstoqueParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioMovimentacaoEstoque(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioMovimentacaoEstoque");
    }

}
