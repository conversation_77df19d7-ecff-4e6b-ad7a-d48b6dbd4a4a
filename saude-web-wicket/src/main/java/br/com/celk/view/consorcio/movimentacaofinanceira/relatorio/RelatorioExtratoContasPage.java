/*
 * Copyright 2012 joao.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package br.com.celk.view.consorcio.movimentacaofinanceira.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioExtratoContasDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Arrays;

import br.com.ksisolucoes.vo.consorcio.SubContaAno;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;


/**
 *
 * <AUTHOR>
 * Programa - 128
 */
@Private

public class RelatorioExtratoContasPage extends RelatorioPage<RelatorioExtratoContasDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Boolean controlaSaldoPorAno;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(getContainerAno());
        form.add(new AutoCompleteConsultaTipoConta("tipoConta").setValidarVisivelConsorciado(true));
    }

    private WebMarkupContainer getContainerAno() {
        WebMarkupContainer containerAno = new WebMarkupContainer("containerAno");
        containerAno.add(DropDownUtil.getAnoDropDown("ano", true, false, SubContaAno.ANO_INICIAL, false));
        containerAno.setVisible(controlaSaldoPorAno());
        return containerAno;
    }

    private boolean controlaSaldoPorAno() {
        if (controlaSaldoPorAno == null) {
            try {
                controlaSaldoPorAno =  RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("controlaSaldoPorAno"));
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage());
            }
        }

        return controlaSaldoPorAno;
    }

    @Override
    public void customDTOParam(RelatorioExtratoContasDTOParam param) {
        if (controlaSaldoPorAno()) {
            param.setAno((long) DataUtil.getAno());
        }
        param.setControlaSaldoPorAno(controlaSaldoPorAno());
    }

    @Override
    public Class<RelatorioExtratoContasDTOParam> getDTOParamClass() {
        return RelatorioExtratoContasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioExtratoContasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioExtratoContas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("extratoContas");
    }
    
}
