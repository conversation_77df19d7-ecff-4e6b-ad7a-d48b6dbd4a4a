package br.com.celk.view.consorcio.pagamentoguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoRecibosMunicipioDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;

import java.util.Arrays;


/**
 * <AUTHOR>
 * Programa - 868
 */
@Private

public class RelatorioResumoRecibosMunicipioPage extends RelatorioPage<RelatorioResumoRecibosMunicipioDTOParam> {

    private PnlDatePeriod periodo;
    private MesAnoField txtMesAno;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(txtMesAno = new MesAnoField("competencia"));
        form.add(periodo = new PnlDatePeriod("periodo"));
        form.add(DropDownUtil.getSimNaoLongDropDown("apenasRecibosGuias"));
    }

    @Override
    public void antesGerarRelatorio(AjaxRequestTarget target) throws ValidacaoException {
        if (txtMesAno.getComponentValue() != null) {
            if (periodo.getModelObject() != null) {
                throw new ValidacaoException(Bundle.getStringApplication("msgInformeApenasUmdosFiltrosDeData"));
            }
        }
    }

    @Override
    public Class<RelatorioResumoRecibosMunicipioDTOParam> getDTOParamClass() {
        return RelatorioResumoRecibosMunicipioDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoRecibosMunicipioDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioResumoRecibosMunicipio(param);
    }


    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoRecibosMunicipio");
    }

}
