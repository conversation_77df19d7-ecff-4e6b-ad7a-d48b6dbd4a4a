package br.com.celk.view.vigilancia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioAlvarasDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 519
 */
@Private
public class RelatorioAlvarasPage extends RelatorioPage<RelatorioAlvarasDTOParam> {

    private DropDown dropDownTipoRelatorio;
    private DropDown dropDownFA;
    private DropDown dropDownTipoAlvara;

    @Override
    public void init(Form<RelatorioAlvarasDTOParam> form) {
        RelatorioAlvarasDTOParam proxy = on(RelatorioAlvarasDTOParam.class);

        form.add(new PnlAtividadeEstabelecimento(path(proxy.getAtividade())));
        form.add(new AutoCompleteConsultaSetorVigilancia(path(proxy.getSetorVigilancia())));
        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(DropDownUtil.getSimNaoDropDown(path(proxy.getVencidos()), true, bundle("ambos")));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), RelatorioAlvarasDTOParam.Situacao.values(), true, bundle("todos")));
        form.add(dropDownFA = DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioAlvarasDTOParam.FormaApresentacao.values()));
        form.add(new InputField<String>(path(proxy.getBairro())));
        form.add(dropDownTipoAlvara = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAlvara()), RelatorioAlvarasDTOParam.TipoAlvara.values(), true, false, true));
        form.add(dropDownTipoRelatorio = DropDownUtil.getIEnumDropDown(path(proxy.getTipoRelatorio()), RelatorioAlvarasDTOParam.TipoRelatorio.values()));
        dropDownTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (RelatorioAlvarasDTOParam.TipoRelatorio.RESUMIDO.value().equals(dropDownTipoRelatorio.getComponentValue())) {
                    dropDownFA.setEnabled(false);
                    dropDownTipoAlvara.setEnabled(false);
                } else {
                    dropDownFA.setEnabled(true);
                    dropDownTipoAlvara.setEnabled(true);
                }
                if (target != null) {
                    target.add(dropDownFA);
                    target.add(dropDownTipoAlvara);
                }
            }
        });

    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioAlvaras");
    }

    @Override
    public Class<RelatorioAlvarasDTOParam> getDTOParamClass() {
        return RelatorioAlvarasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioAlvarasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioAlvaras(param);
    }

}
