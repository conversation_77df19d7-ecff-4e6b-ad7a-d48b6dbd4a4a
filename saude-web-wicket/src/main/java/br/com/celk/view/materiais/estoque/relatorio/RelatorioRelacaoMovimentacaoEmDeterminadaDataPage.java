package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.empresa.pnl.PnlConsultaEmpresa;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.basico.localizacao.pnl.PnlConsultaLocalizacao;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoMovimentacaoEmDeterminadaDataDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 57
 */
@Private
public class RelatorioRelacaoMovimentacaoEmDeterminadaDataPage extends RelatorioPage<RelatorioRelacaoMovimentacaoEmDeterminadaDataDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaLocalizacao autoCompleteConsultaLocalizacao;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown dropDownTipoPreco;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(autoCompleteConsultaLocalizacao = new AutoCompleteConsultaLocalizacao("localizacao"));
        form.add(new RequiredDateChooser("dataEstoqueFisico"));
        form.add(getDropDownOrdenacao());
        form.add(getDropDownTipoPreco());
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));

        dropDownTipoPreco.setComponentValue(RepositoryComponentDefault.TipoPreco.PRECO_MEDIO);
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoMovimentacaoEmDeterminadaDataDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoMovimentacaoEmDeterminadaDataDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioRelacaoMovimentacaoEmDeterminadaData(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("movimentacaoDeterminadaData");
    }

    private DropDown getDropDownTipoPreco() {
        if (dropDownTipoPreco == null) {
            dropDownTipoPreco = DropDownUtil.getEnumDropDown("tipoPreco", RepositoryComponentDefault.TipoPreco.values());
            dropDownTipoPreco.add(new Tooltip().setText("msgTooltipTipoPreco"));
        }
        return dropDownTipoPreco;
    }

    public DropDown getDropDownOrdenacao() {
        DropDown dropDown = new DropDown("ordenacao");
        dropDown.addChoice(Produto.PROP_DESCRICAO, BundleManager.getString("descricao"));
        dropDown.addChoice(Produto.PROP_CODIGO, BundleManager.getString("codigo"));

        return dropDown;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }
}
