package br.com.celk.view.vigilancia.rotinas.roteiroinspecao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 * Programa - 618
 */
@Private
public class ConsultaRegistroRoteiroInspecaoPage extends ConsultaPage<RegistroInspecao, List<BuilderQueryCustom.QueryParameter>> {

    private String inspecionado;
    private Estabelecimento estabelecimento;
    private VigilanciaPessoa vigilanciaPessoa;
    private AtividadeEstabelecimento atividadeEstabelecimento;
    private Profissional profissional;
    private Long status;

    public ConsultaRegistroRoteiroInspecaoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaEstabelecimento("estabelecimento"));
        form.add(new AutoCompleteConsultaVigilanciaPessoa("vigilanciaPessoa"));
        form.add(new PnlAtividadeEstabelecimento("atividadeEstabelecimento"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(DropDownUtil.getIEnumDropDown("status", RegistroInspecao.Status.values(), true, bundle("ambos")));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        RegistroInspecao proxy = on(RegistroInspecao.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("codigo"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createColumn(bundle("inspecionado"), proxy.getInspecionado()));
        columns.add(createSortableColumn(bundle("protocolo"), proxy.getRequerimentoVigilancia().getProtocolo(), proxy.getRequerimentoVigilancia().getProtocoloFormatado()));
        columns.add(createSortableColumn(bundle("solicitacao"), proxy.getRequerimentoVigilancia().getTipoDocumento(), proxy.getRequerimentoVigilancia().getDescricaoTipoDocumento()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        columns.add(createSortableColumn(bundle("dataInspecao"), proxy.getDataInspecao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<RegistroInspecao>() {
            @Override
            public void customizeColumn(RegistroInspecao rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<RegistroInspecao>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroInspecao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroRoteiroInspecaoPage(modelObject, false));
                    }
                }).setEnabled(RegistroInspecao.Status.PENDENTE.value().equals(rowObject.getStatus()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<RegistroInspecao>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroInspecao modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().update(target);
                    }
                }).setEnabled(RegistroInspecao.Status.PENDENTE.value().equals(rowObject.getStatus()));

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<RegistroInspecao>() {
                    @Override
                    public DataReport action(RegistroInspecao modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRoteiroInspecao(modelObject);
                    }
                }).setEnabled(RegistroInspecao.Status.PENDENTE.value().equals(rowObject.getStatus()));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<RegistroInspecao>() {
                    @Override
                    public void action(AjaxRequestTarget target, RegistroInspecao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroRoteiroInspecaoPage(modelObject, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public String[] getProperties() {
                RegistroInspecao proxy = on(RegistroInspecao.class);
                return VOUtils.mergeProperties(
                        new HQLProperties(RegistroInspecao.class).getProperties(),
                        new HQLProperties(RequerimentoVigilancia.class, RegistroInspecao.PROP_REQUERIMENTO_VIGILANCIA).getProperties(),
                        new HQLProperties(Estabelecimento.class, RegistroInspecao.PROP_ESTABELECIMENTO).getProperties(),
                        new HQLProperties(VigilanciaPessoa.class, RegistroInspecao.PROP_VIGILANCIA_PESSOA).getProperties(),
                        new String[] {
                                path(proxy.getAtividadeEstabelecimento().getDescricao()),
                        }
                );
            }

            @Override
            public Class getClassConsulta() {
                return RegistroInspecao.class;
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(RegistroInspecao.PROP_DATA_CADASTRO, false);
            }

            @Override
            public List getInterceptors() {
                return Arrays.asList(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        if (profissional != null) {
                            HQLHelper subquery = hql.getNewInstanceSubQuery();
                            subquery.addToSelect("1");
                            subquery.addToFrom("RegistroInspecaoFiscais rif");
                            subquery.addToWhereWhithAnd("rif.profissional.codigo = ", profissional.getCodigo());
                            subquery.addToWhereWhithAnd("rif.registroInspecao.codigo = " + alias + ".codigo");
                            hql.addToWhereWhithAnd("EXISTS(" + subquery.getQuery() + ")");
                        }
                    }
                });
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroInspecao.PROP_VIGILANCIA_PESSOA, vigilanciaPessoa));
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroInspecao.PROP_ESTABELECIMENTO, estabelecimento));
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroInspecao.PROP_ATIVIDADE_ESTABELECIMENTO, atividadeEstabelecimento));
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroInspecao.PROP_STATUS, status));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRegistroRoteiroInspecaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRegistroRoteiroInspecao");
    }
}
