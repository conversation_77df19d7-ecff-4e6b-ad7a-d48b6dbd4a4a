package br.com.celk.view.patrimonio.relatorios;

import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.celk.view.patrimonio.bemgrupo.autocomplete.AutoCompleteConsultaBemGrupo;
import br.com.celk.view.patrimonio.setor.autocomplete.AutoCompleteConsultaSetor;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.patrimonio.interfaces.dto.RelacaoPatrimoniosDTOParam;
import br.com.ksisolucoes.report.patrimonio.interfaces.facade.PatrimonioReportFacade;
import br.com.ksisolucoes.vo.patrimonio.BemPatrimonio;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 * Programa - 384
 */
public class RelacaoPatrimoniosPage extends RelatorioPage<RelacaoPatrimoniosDTOParam> {

    private PnlChoicePeriod pnlChoicePeriod;
    private DropDown dropDownStatus;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("empresa")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaBemGrupo("bemGrupo"));
        form.add(new AutoCompleteConsultaCentroCusto("centroCusto"));
        form.add(new AutoCompleteConsultaSetor("setor"));
        form.add(getDropDownStatus());
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelacaoPatrimoniosDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getNaoSimBooleanDropDown("visualizarValor"));
        form.add(pnlChoicePeriod = new PnlChoicePeriod("periodo"));
        pnlChoicePeriod.setDefaultOutro();
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelacaoPatrimoniosDTOParam.Ordenacao.values()));
    }

    private DropDown getDropDownStatus() {
        dropDownStatus = new DropDown("status");

        dropDownStatus.addChoice(BemPatrimonio.Status.PROP_STATUS_ATIVO.value(), BemPatrimonio.Status.PROP_STATUS_ATIVO.descricao());
        dropDownStatus.addChoice(BemPatrimonio.Status.PROP_STATUS_BAIXADO.value(), BemPatrimonio.Status.PROP_STATUS_BAIXADO.descricao());
        dropDownStatus.addChoice(BemPatrimonio.Status.PROP_STATUS_CANCELADO.value(), BemPatrimonio.Status.PROP_STATUS_CANCELADO.descricao());
        dropDownStatus.addChoice(null, bundle("todos"));
        return dropDownStatus;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoPatrimonios");
    }

    @Override
    public Class<RelacaoPatrimoniosDTOParam> getDTOParamClass() {
        return RelacaoPatrimoniosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelacaoPatrimoniosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(PatrimonioReportFacade.class).relatorioDetalhamentoLicitacoes(param);
    }

}
