package br.com.celk.view.consorcio.consorcioguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.consorcioprocedimento.autocomplete.AutoCompleteConsorcioProcedimento;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoProcedimentosDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.util.Arrays;


/**
 *
 * <AUTHOR>
 * Programa - 127
 */
@Private

public class RelatorioDetalhamentoProcedimentosPage extends RelatorioPage<RelatorioDetalhamentoProcedimentosDTOParam> {
    
    private AutoCompleteConsorcioProcedimento autoCompleteConsorcioProcedimento;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsorcioProcedimento = new AutoCompleteConsorcioProcedimento("consorcioProcedimento"));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaCidade("cidade"));
        form.add(new AutoCompleteConsultaTipoConta("tipoConta"));
//        form.add(new AutoCompleteConsultaUsuarioCadsus("paciente"));
        form.add(new UpperField("nomePaciente"));
        form.add(DropDownUtil.getEnumDropDown("tipoData", RelatorioDetalhamentoProcedimentosDTOParam.TipoData.values()));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(new CheckBox("situacaoAberta"));
        form.add(new CheckBox("situacaoCancelada"));
        form.add(new CheckBox("situacaoPaga"));
        form.add(new CheckBox("situacaoUtilizada"));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioDetalhamentoProcedimentosDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getIEnumDropDown("tipoOrdenacao", RelatorioDetalhamentoProcedimentosDTOParam.TipoOrdenacao.values(), false));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioDetalhamentoProcedimentosDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("modeloImpressao", RelatorioDetalhamentoProcedimentosDTOParam.ModeloImpressao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoResumo", RelatorioDetalhamentoProcedimentosDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getSimNaoDropDown("separarPrestadores"));
    }
    
    @Override
    public Class<RelatorioDetalhamentoProcedimentosDTOParam> getDTOParamClass() {
        return RelatorioDetalhamentoProcedimentosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioDetalhamentoProcedimentosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioDetalhamentoProcedimentos(param);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhamentoProcedimentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsorcioProcedimento;
    }

}
