package br.com.celk.view.vigilancia.escalaplantao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.EscalaPlantao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> S. Schmoeller
 * Programa - 959
 */
@Private
public class ConsultaEscalaPlantaoPage extends ConsultaPage<EscalaPlantao, List<BuilderQueryCustom.QueryParameter>> {

    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DatePeriod periodo;
    private Profissional profissional;

    public ConsultaEscalaPlantaoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional"));
        form.add(new PnlDatePeriod("periodo"));

    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        EscalaPlantao proxy = on(EscalaPlantao.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("diaInicio"), proxy.getDescricaoDiaInicio()));
        columns.add(createSortableColumn(BundleManager.getString("dataInicio"), proxy.getDataFormatadaInicio()));
        columns.add(createSortableColumn(BundleManager.getString("diaFim"), proxy.getDescricaoDiaFim()));
        columns.add(createSortableColumn(BundleManager.getString("dataFim"), proxy.getDataFormatadaFim()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<EscalaPlantao>() {
            @Override
            public void customizeColumn(final EscalaPlantao rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<EscalaPlantao>() {
                    @Override
                    public void action(AjaxRequestTarget target, EscalaPlantao modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEscalaPlantaoPage(rowObject, false, true));

                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EscalaPlantao>() {
                    @Override
                    public void action(AjaxRequestTarget target, EscalaPlantao modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().update(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return EscalaPlantao.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(EscalaPlantao.PROP_DESCRICAO_DIA_INICIO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (profissional != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EscalaPlantao.PROP_PROFISSIONAL), profissional));
        }

        if (periodo != null) {
            if (periodo.getDataInicial() != null) {
                parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EscalaPlantao.PROP_DATA_INICIAL), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, periodo.getDataInicial()));
            }
            if (periodo.getDataFinal() != null) {
                parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EscalaPlantao.PROP_DATA_FINAL), BuilderQueryCustom.QueryParameter.MENOR_IGUAL, periodo.getDataFinal()));
            }
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroEscalaPlantaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaEscalaPlantao");
    }
}
