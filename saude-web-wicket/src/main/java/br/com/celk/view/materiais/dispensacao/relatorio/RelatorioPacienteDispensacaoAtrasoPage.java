package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioPacienteDispensacaoAtrasoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Arrays;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 * Programa - 176
 */
@Private

public class RelatorioPacienteDispensacaoAtrasoPage extends RelatorioPage<RelatorioPacienteDispensacaoAtrasoDTOParam> {
        
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresas")
                .setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_EXTERNO))
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProduto("produtos")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaUsuarioCadsus("pacientes")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioPacienteDispensacaoAtrasoDTOParam.FormaApresentacao.values()));

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioPacienteDispensacaoAtrasoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioPacienteDispensacaoAtrasoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioPacienteDispensacaoAtraso(param,true);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("dispensacaoUsoContinuoAberto");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
