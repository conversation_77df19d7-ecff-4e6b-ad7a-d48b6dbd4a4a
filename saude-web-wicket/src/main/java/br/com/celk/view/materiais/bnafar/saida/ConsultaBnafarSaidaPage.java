package br.com.celk.view.materiais.bnafar.saida;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.table.selection.DTOSelection;
import br.com.celk.component.table.selection.MultiSelectionPageableTable;
import br.com.celk.materiais.bnafar.consultaIntegracao.saida.ConsultaIntegracaoBnafarSaidaDTO;
import br.com.celk.materiais.bnafar.consultaIntegracao.saida.ConsultaIntegracaoBnafarSaidaDTOParam;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.dao.paginacao.DataPagingResultImpl;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Marques
 * Programa - 1091
 */
@Private
public class ConsultaBnafarSaidaPage extends ConsultaPage<ConsultaIntegracaoBnafarSaidaDTO, ConsultaIntegracaoBnafarSaidaDTOParam> {

    private ConsultaIntegracaoBnafarSaidaDTOParam param = new ConsultaIntegracaoBnafarSaidaDTOParam();

    private Empresa empresa;
    private Produto produto;
    private DatePeriod dataSaida;
    private Long statusRegistro;
    private String numeroDocumento;

    private AbstractAjaxButton reenviar;
    private DlgConfirmacaoSimNao dlgMensagemConfirmacao;

    public ConsultaBnafarSaidaPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("empresa", new PropertyModel(this, "empresa")));
        form.add(new AutoCompleteConsultaProduto("produto", new PropertyModel(this, "produto")));
        form.add(new InputField("numeroDocumento", new PropertyModel(this, "numeroDocumento")));
        form.add(getEnumStatusRegistros());
        form.add(new PnlDatePeriod("dataSaida", new PropertyModel(this, "dataSaida")));

        getControls().add(reenviar = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                reprocessarItensSelecionados(getIntegracoesSelecionadas());
                getPageableTable().update(target);
            }
        });
        reenviar.add(new AttributeModifier("class", "btn-orange"));
        reenviar.add(new AttributeModifier("value", bundle("reenviar")));

        AbstractAjaxButton reenviarTodosComErro = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmarReenvio(target);
                getPageableTable().update(target);
            }
        };
        reenviarTodosComErro.add(new AttributeModifier("class", "btn-blue"));
        reenviarTodosComErro.add(new AttributeModifier("value", bundle("reenviarTodosComErro")));
        reenviarTodosComErro.add(new AttributeModifier("style", "margin-left: 5px;"));
        getControls().add(reenviarTodosComErro);
        getLinkNovo().setVisible(false);
        add(form);
    }

    private void confirmarReenvio(AjaxRequestTarget target) throws ValidacaoException {
        if (isProcessando()) {
            throw new ValidacaoException("Já existe um processo de reenvio de erros em execução. Aguarde a conclusão para novo reenvio.");
        }
        if (dlgMensagemConfirmacao == null) {
            addModal(target, dlgMensagemConfirmacao = new DlgConfirmacaoSimNao(newModalId(), bundle("msgConfirmacaoReenvioErrosBnafarX", BnafarHelper.TipoSincronizacao.SAIDA.descricao())) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    reprocessarTodosItensComErro(target);
                }
            });
        }
        dlgMensagemConfirmacao.show(target);
    }

    private void reprocessarTodosItensComErro(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        info(target, "Processamento iniciado! Em alguns instantes você receberá uma mensagem com mais informações.");
        BOFactory.getBO(MaterialBasicoFacade.class).processarReenvioErrosBnafar(BnafarHelper.TipoSincronizacao.SAIDA);
    }

    private boolean isProcessando() {
        return LoadManager.getInstance(AsyncProcess.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AsyncProcess.STATUS_AGUARDANDO_PROCESSAMENTO, AsyncProcess.STATUS_PROCESSANDO)))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_TIPO, AsyncProcess.TIPO_PROCESSO))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_DATA_REGISTRO, BuilderQueryCustom.QueryParameter.MAIOR, Data.removeDias(DataUtil.getDataAtual(), 1)))
                .addParameter(new QueryCustom.QueryCustomParameter(AsyncProcess.PROP_NOME_PROCESSO, BnafarHelper.getDescricaoProcessoAssincrono(BnafarHelper.TipoSincronizacao.SAIDA)))
                .start().exists();
    }

    private DropDown getEnumStatusRegistros() {

        DropDown dropDown = new DropDown("statusRegistro", new PropertyModel(this, "statusRegistro"));

        dropDown.addChoice(null, BundleManager.getString("todos"));
        dropDown.addChoice(BnafarHelper.StatusRegistro.ENVIADO.value(), BnafarHelper.StatusRegistro.ENVIADO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.PROCESSADO.value(), BnafarHelper.StatusRegistro.PROCESSADO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.ERRO.value(), BnafarHelper.StatusRegistro.ERRO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.ERRO_SEM_REENVIO.value(), BnafarHelper.StatusRegistro.ERRO_SEM_REENVIO.descricao());
        dropDown.addChoice(BnafarHelper.StatusRegistro.CANCELADO.value(), BnafarHelper.StatusRegistro.CANCELADO.descricao());

        return dropDown;
    }

    @Override
    public List getColumns(List<IColumn> columns) {

        ConsultaIntegracaoBnafarSaidaDTO proxy = on(ConsultaIntegracaoBnafarSaidaDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(BundleManager.getString("dataSaida"), proxy.getDataSaida()));
        columns.add(createColumn(BundleManager.getString("numeroDocumento"), proxy.getNumeroDocumento()));
        columns.add(createColumn(BundleManager.getString("estabelecimento"), proxy.getEmpresa().getDescricao()));
        columns.add(createColumn(BundleManager.getString("produto"), proxy.getProduto().getDescricao()));
        columns.add(createColumn(BundleManager.getString("lote"), proxy.getBnafarSaidaElo().getBnafarSaidaIntegracao().getCodigo()));
        columns.add(createColumn(BundleManager.getString("ultimoEnvio"), proxy.getBnafarSaida().getDataUltimoEnvio()));
        columns.add(createColumn(BundleManager.getString("situacao"), proxy.getDescricaoSituacao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ConsultaIntegracaoBnafarSaidaDTO>() {
            @Override
            public void customizeColumn(ConsultaIntegracaoBnafarSaidaDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarSaidaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarSaidaDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesBnafarSaidaPage(modelObject.getBnafarSaida(), true));
                    }
                });
                addAction(ActionType.REENVIAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarSaidaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarSaidaDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactory.getBO(MaterialBasicoFacade.class).reenviarBnafarSaida(modelObject.getBnafarSaidaElo().getBnafarSaidaIntegracao().getCodigo());
                        getPageableTable().update(target);
                    }
                }).setVisible(rowObject.getBnafarSaida().getStatusRegistro().equals(BnafarHelper.StatusRegistro.ERRO.value()));
                addAction(ActionType.CANCELAR, rowObject, new IModelAction<ConsultaIntegracaoBnafarSaidaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaIntegracaoBnafarSaidaDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactory.getBO(MaterialBasicoFacade.class).alterarStatusBnafarSaida(modelObject.getBnafarSaida().getCodigo(), modelObject.getBnafarSaidaElo().getCodigo(), BnafarHelper.StatusRegistro.CANCELADO.value());
                        getPageableTable().update(target);
                    }
                }).setVisible(rowObject.getBnafarSaida().getStatusRegistro().equals(BnafarHelper.StatusRegistro.ERRO.value()));
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<ConsultaIntegracaoBnafarSaidaDTO, ConsultaIntegracaoBnafarSaidaDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaIntegracaoBnafarSaidaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                DataPagingResult<ConsultaIntegracaoBnafarSaidaDTO> result = BOFactoryWicket.getBO(MaterialBasicoFacade.class).queryConsultaIntegracaoBnafarSaida(dataPaging);
                return getDataPagingSelectedDTO(result);
            }
        };
    }

    static private DataPagingResult getDataPagingSelectedDTO(DataPagingResult<ConsultaIntegracaoBnafarSaidaDTO> result) {
        List<ConsultaIntegracaoBnafarSaidaDTO> resultList = result.getList();
        List<DTOSelection<ConsultaIntegracaoBnafarSaidaDTO>> resultSelectList = new ArrayList<>();
        if (resultList != null) {
            for (ConsultaIntegracaoBnafarSaidaDTO dto : resultList) {
                resultSelectList.add(new DTOSelection<>(false, dto));
            }
        }
        DataPagingResult<DTOSelection<ConsultaIntegracaoBnafarSaidaDTO>> resultSelection = new DataPagingResultImpl<>(resultSelectList, result.getAmountResults());
        return resultSelection;
    }

    private List<ConsultaIntegracaoBnafarSaidaDTO> getIntegracoesSelecionadas() {
        return ((MultiSelectionPageableTable) getPageableTable()).getSelectedObjects();
    }

    @Override
    public ConsultaIntegracaoBnafarSaidaDTOParam getParameters() {

        ConsultaIntegracaoBnafarSaidaDTOParam param = new ConsultaIntegracaoBnafarSaidaDTOParam();

        param.setEmpresa(empresa);
        param.setProduto(produto);
        param.setDataSaida(dataSaida);
        param.setStatusRegistro(statusRegistro);

        return param;
    }

    @Override
    public PageableTable newPageableTable(String tableId, List<IColumn> columns, IPagerProvider pagerProvider, int rowsPerPage) {
        return new MultiSelectionPageableTable(tableId, columns, pagerProvider);
    }

    private void reprocessarItensSelecionados(List<ConsultaIntegracaoBnafarSaidaDTO> bnafarSaidaList) throws DAOException, ValidacaoException {
        if (CollectionUtils.isEmpty(bnafarSaidaList)) return;

        ArrayList<Long> codigosReenvio = new ArrayList<>();
        for (ConsultaIntegracaoBnafarSaidaDTO modelObject : bnafarSaidaList) {
            if (modelObject.getBnafarSaidaElo() != null && modelObject.getBnafarSaidaElo().getBnafarSaidaIntegracao() != null) {
                codigosReenvio.add(modelObject.getBnafarSaidaElo().getBnafarSaidaIntegracao().getCodigo());
            }
        }
        if (CollectionUtils.isEmpty(codigosReenvio)) return;

        BOFactory.getBO(MaterialBasicoFacade.class).reenviarBnafarSaida(codigosReenvio);
    }

    @Override
    public Class getCadastroPage() {
        return this.getClass();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consulta_bnafar_saida");
    }
}