package br.com.celk.view.vigilancia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.checkbox.CheckBoxSimNao;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoEstabelecimentosSemRTDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 * Programa - 758
 */
@Private
public class RelatorioRelacaoEstabelecimentosSemRTPage extends RelatorioPage<RelatorioRelacaoEstabelecimentosSemRTDTOParam> {

    @Override
    public void init(Form<RelatorioRelacaoEstabelecimentosSemRTDTOParam> form) {
        RelatorioRelacaoEstabelecimentosSemRTDTOParam proxy = on(RelatorioRelacaoEstabelecimentosSemRTDTOParam.class);

        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(new PnlAtividadeEstabelecimento(path(proxy.getAtividadeEstabelecimento())));
        form.add(new AutoCompleteConsultaSetorVigilancia(path(proxy.getSetorVigilancia())));
        form.add(new CheckBoxSimNao(path(proxy.getVenceuPrazo())));
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoEstabelecimentosSemRT");
    }

    @Override
    public Class<RelatorioRelacaoEstabelecimentosSemRTDTOParam> getDTOParamClass() {
        return RelatorioRelacaoEstabelecimentosSemRTDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoEstabelecimentosSemRTDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRelacaoEstabelecimentosSemRT(param);
    }

}
