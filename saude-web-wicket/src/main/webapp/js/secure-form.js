/**
 * Utilitários para formulários seguros
 * Minimiza exposição de dados sensíveis em requisições HTTP
 */
var SecureForm = {
    
    /**
     * Ofusca dados sensíveis antes do envio
     */
    obfuscateFormData: function(formElement) {
        var inputs = formElement.querySelectorAll('input[data-sensitive="true"]');
        var obfuscatedData = {};
        
        inputs.forEach(function(input) {
            if (input.value && input.value.trim() !== '') {
                // Cria hash simples dos dados (não é criptografia real, apenas ofuscação)
                obfuscatedData[input.name] = SecureForm.simpleHash(input.value);
                // Limpa o valor original do campo
                input.setAttribute('data-original-value', input.value);
                input.value = '';
            }
        });
        
        return obfuscatedData;
    },
    
    /**
     * Restaura dados após envio (se necessário)
     */
    restoreFormData: function(formElement) {
        var inputs = formElement.querySelectorAll('input[data-sensitive="true"]');
        
        inputs.forEach(function(input) {
            var originalValue = input.getAttribute('data-original-value');
            if (originalValue) {
                input.value = originalValue;
                input.removeAttribute('data-original-value');
            }
        });
    },
    
    /**
     * Hash simples para ofuscação (não é segurança real)
     */
    simpleHash: function(str) {
        var hash = 0;
        if (str.length === 0) return hash;
        
        for (var i = 0; i < str.length; i++) {
            var char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        
        return Math.abs(hash).toString(36);
    },
    
    /**
     * Limpa campos sensíveis após um delay
     */
    clearSensitiveFields: function(formElement, delay) {
        delay = delay || 1000; // 1 segundo por padrão
        
        setTimeout(function() {
            var inputs = formElement.querySelectorAll('input[data-sensitive="true"]');
            inputs.forEach(function(input) {
                input.value = '';
                input.removeAttribute('data-original-value');
            });
        }, delay);
    },
    
    /**
     * Desabilita autocomplete e histórico do navegador
     */
    disableBrowserFeatures: function(formElement) {
        // Desabilita autocomplete no formulário
        formElement.setAttribute('autocomplete', 'off');
        
        // Desabilita autocomplete em campos sensíveis
        var inputs = formElement.querySelectorAll('input[data-sensitive="true"]');
        inputs.forEach(function(input) {
            input.setAttribute('autocomplete', 'off');
            input.setAttribute('spellcheck', 'false');
            
            // Previne que o navegador salve o valor
            input.addEventListener('input', function() {
                // Força limpeza do histórico de input
                setTimeout(function() {
                    if (input.value === '') {
                        input.removeAttribute('value');
                    }
                }, 100);
            });
        });
    },
    
    /**
     * Intercepta submissão do formulário para aplicar segurança
     */
    secureFormSubmission: function(formElement, callback) {
        formElement.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Ofusca dados antes do envio
            var obfuscatedData = SecureForm.obfuscateFormData(formElement);
            
            // Executa callback com dados ofuscados
            if (callback && typeof callback === 'function') {
                callback(obfuscatedData);
            }
            
            // Limpa campos após envio
            SecureForm.clearSensitiveFields(formElement, 500);
            
            return false;
        });
    },
    
    /**
     * Inicializa proteções de segurança em um formulário
     */
    initSecureForm: function(formSelector) {
        var form = document.querySelector(formSelector);
        if (!form) return;
        
        // Aplica todas as proteções
        SecureForm.disableBrowserFeatures(form);
        
        // Limpa campos quando a página é descarregada
        window.addEventListener('beforeunload', function() {
            SecureForm.clearSensitiveFields(form, 0);
        });
        
        // Limpa campos quando a aba perde o foco
        window.addEventListener('blur', function() {
            SecureForm.clearSensitiveFields(form, 2000);
        });
    },
    
    /**
     * Monitora tentativas de inspecionar elementos
     */
    addAntiInspectionMeasures: function() {
        // Desabilita menu de contexto em campos sensíveis
        document.addEventListener('contextmenu', function(e) {
            if (e.target.getAttribute('data-sensitive') === 'true') {
                e.preventDefault();
                return false;
            }
        });
        
        // Detecta abertura do DevTools (método básico)
        var devtools = {
            open: false,
            orientation: null
        };
        
        setInterval(function() {
            if (window.outerHeight - window.innerHeight > 200 || 
                window.outerWidth - window.innerWidth > 200) {
                if (!devtools.open) {
                    devtools.open = true;
                    // Limpa todos os campos sensíveis se DevTools for aberto
                    var sensitiveInputs = document.querySelectorAll('input[data-sensitive="true"]');
                    sensitiveInputs.forEach(function(input) {
                        input.value = '';
                    });
                }
            } else {
                devtools.open = false;
            }
        }, 500);
    }
};

// Inicialização automática quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Inicializa proteções em formulários de recuperação de senha
    SecureForm.initSecureForm('form[id*="recuperar"], form[id*="Recuperar"]');
    SecureForm.addAntiInspectionMeasures();
});

// Exporta para uso global
window.SecureForm = SecureForm;
