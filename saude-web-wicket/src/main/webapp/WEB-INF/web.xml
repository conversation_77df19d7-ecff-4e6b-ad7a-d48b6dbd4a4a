<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
	version="3.0">

    <display-name>saude-web-wicket</display-name>

	<!--
		There are three means to configure Wickets configuration mode and they 
		are tested in the order given.
		
		1) A system property: -Dwicket.configuration 
		2) servlet specific <init-param> 
		3) context specific <context-param>

  		The value might be either "development" (reloading when templates change) or 
  		"deployment". If no configuration is found, "development" is the default. -->
<!--    <servlet-mapping>
        <servlet-name>javax.ws.rs.core.Application</servlet-name>
        <url-pattern>/rest/*</url-pattern>
    </servlet-mapping>-->

    <servlet>
        <servlet-name>default</servlet-name>
        <servlet-class>io.undertow.servlet.handlers.DefaultServlet</servlet-class>
        <init-param>
            <param-name>default-allowed</param-name>
            <param-value>true</param-value>
        </init-param>
        <init-param>
             <param-name>allowed-extensions</param-name>
             <param-value>jar</param-value>
        </init-param>
        <init-param>
             <param-name>disallowed-extensions</param-name>
             <param-value>exe,bat,sh</param-value>
        </init-param>
    </servlet>

    <!-- Filtros de Segurança XSS -->
    <filter>
        <filter-name>URLParameterValidationFilter</filter-name>
        <filter-class>br.com.celk.security.URLParameterValidationFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>URLParameterValidationFilter</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
    </filter-mapping>

    <!-- Filtro de Segurança para Dados Sensíveis -->
    <filter>
        <filter-name>SensitiveDataRequestFilter</filter-name>
        <filter-class>br.com.celk.security.SensitiveDataRequestFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>SensitiveDataRequestFilter</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
    </filter-mapping>

    <filter>
        <filter-name>wicket.saude-web-wicket</filter-name>
        <filter-class>org.apache.wicket.protocol.http.WicketFilter</filter-class>
        <init-param>
            <param-name>applicationClassName</param-name>
            <param-value>br.com.celk.system.Application</param-value>
        </init-param>
        <init-param>
            <param-name>ignorePaths</param-name>
            <param-value>rest/</param-value>
        </init-param>
    </filter>
        
    <filter-mapping>
        <filter-name>wicket.saude-web-wicket</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    
    <!--<distributable/>-->

    <context-param>
        <param-name>configuration</param-name>
        <!--<param-value>development</param-value>-->
        <param-value>deployment</param-value>
    </context-param>

    <!--security-constraint>
        <web-resource-collection>
            <web-resource-name>HTTPS Page</web-resource-name>
            <url-pattern>/*</url-pattern>
        </web-resource-collection>
        <user-data-constraint>
            <transport-guarantee>CONFIDENTIAL</transport-guarantee>
        </user-data-constraint>
    </security-constraint-->
</web-app>
