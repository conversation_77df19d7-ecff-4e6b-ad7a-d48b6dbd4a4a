# Melhorias de Segurança - Recuperação de Senha

## Problemas Identificados e Soluções Implementadas

### 1. User Enumeration via Discrepancies in Error Messages

**Problema**: O sistema retornava mensagens diferentes quando um usuário existia vs quando não existia, permitindo que atacantes enumerassem usuários válidos.

**Solução Implementada**:
- Todas as mensagens de erro agora retornam a mesma mensagem genérica: "Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha."
- Implementado em:
  - `ReiniciarSenha.java` (linha 79)
  - `RedefinirSenhaVigilancia.java` (linha 49)
  - `ReenviarEmailVigilancia.java` (linha 45)

### 2. Timing Attacks Prevention

**Problema**: Diferenças no tempo de resposta poderiam revelar se um usuário existe ou não.

**Solução Implementada**:
- Adicionado delay artificial aleatório (500ms-1500ms) quando usuário não existe
- Método `simulateProcessingDelay()` implementado em todas as classes de comando
- Simula o tempo de processamento real para mascarar diferenças de timing

### 3. Exposição de Dados Sensíveis na URL e Requisições HTTP

**Problema**: Parâmetros do formulário ficavam visíveis na requisição HTTP, expondo dados como login e CPF no navegador e logs.

**Solução Implementada**:
- **Tokenização de Dados**: Criado `SecurePasswordRecoveryService` que gera tokens temporários criptografados
- **Criptografia AES**: Dados sensíveis são criptografados antes do armazenamento temporário
- **Tokens de Uso Único**: Cada token expira em 5 minutos e só pode ser usado uma vez
- **Validação de IP**: Token só funciona para o mesmo IP que o criou
- **Limpeza Automática**: Campos são limpos automaticamente após submissão
- **JavaScript de Segurança**: Proteções no lado cliente para limpar dados
- **Filtro de Requisições**: `SensitiveDataRequestFilter` intercepta e mascara dados sensíveis
- **Headers de Segurança**: Previne cache e adiciona proteções contra XSS

### 4. Rate Limiting

**Problema**: Ausência de controle de tentativas permitia ataques de força bruta.

**Solução Implementada**:
- Criado `RateLimitingService.java` com controle por IP
- Máximo de 5 tentativas por IP em 15 minutos
- Mensagem informativa sobre tempo restante para nova tentativa
- Integrado em `PnlRecuperarSenha.java`

### 5. Logs de Segurança Melhorados

**Problema**: Logs expunham dados sensíveis e não forneciam auditoria adequada.

**Solução Implementada**:
- Criado `SecurityAuditLogger.java` para logs seguros
- Ofuscação de dados sensíveis (login, CPF) nos logs
- Logs de auditoria separados para análise de segurança
- Não exposição de informações sobre existência de usuários nos logs públicos

## Arquivos Modificados

### Classes de Comando (Backend)
1. `saude-command/src/main/java/br/com/ksisolucoes/bo/controle/usuario/ReiniciarSenha.java`
2. `saude-command/src/main/java/br/com/ksisolucoes/bo/vigilancia/externo/RedefinirSenhaVigilancia.java`
3. `saude-command/src/main/java/br/com/ksisolucoes/bo/vigilancia/externo/ReenviarEmailVigilancia.java`

### Classes de Interface (Frontend)
1. `saude-web-wicket/src/main/java/br/com/celk/view/login/PnlRecuperarSenha.java`
2. `saude-web-wicket/src/main/java/br/com/celk/view/vigilancia/externo/view/login/VigilanciaEsqueceuSenhaPage.java`

### Novas Classes de Segurança
1. `saude-web-wicket/src/main/java/br/com/celk/security/RateLimitingService.java`
2. `saude-web-wicket/src/main/java/br/com/celk/security/SecurityAuditLogger.java`
3. `saude-web-wicket/src/main/java/br/com/celk/security/SecurePasswordRecoveryService.java`
4. `saude-web-wicket/src/main/java/br/com/celk/security/SensitiveDataRequestFilter.java`

### Novos Recursos Frontend
1. `saude-web-wicket/src/main/webapp/js/secure-form.js`
2. HTML atualizado com atributos de segurança (`data-sensitive`, `autocomplete="off"`, etc.)

## Benefícios de Segurança

### Prevenção de User Enumeration
- Atacantes não conseguem mais determinar se um usuário existe
- Mensagens consistentes independente do cenário
- Tempo de resposta consistente

### Proteção contra Ataques de Força Bruta
- Rate limiting por IP impede tentativas excessivas
- Janela de tempo configurável (15 minutos)
- Bloqueio temporário após 5 tentativas

### Proteção de Dados Sensíveis
- **Tokenização**: Dados sensíveis são substituídos por tokens criptografados
- **Criptografia AES**: Proteção forte dos dados temporários
- **Limpeza Automática**: Campos são limpos após inatividade ou perda de foco
- **Filtro de Requisições**: Intercepta e mascara dados em requisições HTTP
- **JavaScript de Segurança**: Proteções no lado cliente contra inspeção
- **Headers de Segurança**: Previne cache e adiciona proteções XSS/CSRF
- **Logs Seguros**: Ofuscação adequada de dados pessoais nos logs

### Auditoria de Segurança
- Logs detalhados para análise de segurança
- Rastreamento de tentativas suspeitas
- Alertas para atividade anômala

## Configurações Recomendadas

### Configuração do Filtro de Segurança (web.xml)
```xml
<!-- Filtro para interceptar dados sensíveis -->
<filter>
    <filter-name>SensitiveDataRequestFilter</filter-name>
    <filter-class>br.com.celk.security.SensitiveDataRequestFilter</filter-class>
</filter>
<filter-mapping>
    <filter-name>SensitiveDataRequestFilter</filter-name>
    <url-pattern>/*</url-pattern>
</filter-mapping>
```

### Configuração do Wicket Application
```java
// No método init() da sua WicketApplication
@Override
public void init() {
    super.init();

    // Registra listener de segurança
    SensitiveDataRequestFilter.WicketSecurityListener.register(this);

    // Outras configurações...
}
```

### Rate Limiting
- **Máximo de tentativas**: 5 por IP
- **Janela de tempo**: 15 minutos
- **Limpeza de cache**: A cada 30 minutos

### Tokenização de Dados
- **Algoritmo**: AES-128 para criptografia
- **Expiração de Token**: 5 minutos
- **Uso Único**: Cada token só pode ser usado uma vez
- **Validação de IP**: Token vinculado ao IP de origem

### Logs de Auditoria
- **Nível de log**: INFO para operações normais, WARN para falhas, ERROR para atividade suspeita
- **Retenção**: Manter logs de segurança por pelo menos 90 dias
- **Monitoramento**: Alertas automáticos para múltiplas tentativas do mesmo IP
- **Ofuscação**: Dados sensíveis são mascarados nos logs (ex: `j***o` ao invés de `joao`)

## Testes Recomendados

### Testes de Segurança
1. **User Enumeration**: Verificar se mensagens são idênticas para usuários existentes e inexistentes
2. **Timing Attacks**: Medir tempos de resposta para diferentes cenários
3. **Rate Limiting**: Testar bloqueio após 5 tentativas
4. **Data Exposure**: Verificar se dados sensíveis não aparecem em URLs ou logs
5. **Tokenização**: Verificar se tokens são únicos e expiram corretamente
6. **Criptografia**: Testar se dados são criptografados adequadamente
7. **Filtro de Requisições**: Verificar se dados são mascarados nas requisições HTTP
8. **JavaScript de Segurança**: Testar limpeza automática de campos
9. **Headers de Segurança**: Verificar se headers apropriados são adicionados

### Testes Funcionais
1. **Recuperação Normal**: Usuário válido com email
2. **Usuário Inexistente**: Comportamento correto
3. **Usuário sem Email**: Mensagem apropriada
4. **Múltiplas Tentativas**: Rate limiting funcionando

## Monitoramento Contínuo

### Métricas de Segurança
- Número de tentativas de recuperação por hora/dia
- IPs com múltiplas tentativas
- Taxa de sucesso vs falha
- Tempo médio de resposta

### Alertas Recomendados
- Mais de 10 tentativas do mesmo IP em 1 hora
- Picos anômalos de tentativas de recuperação
- Falhas consecutivas de envio de email
- Tentativas fora do horário comercial (se aplicável)

## Considerações Futuras

### Melhorias Adicionais
1. **CAPTCHA**: Implementar reCAPTCHA após algumas tentativas
2. **Geolocalização**: Alertas para tentativas de países suspeitos
3. **Device Fingerprinting**: Rastreamento mais sofisticado de dispositivos
4. **Machine Learning**: Detecção de padrões anômalos automatizada

### Integração com WAF
- Configurar Web Application Firewall para bloquear IPs suspeitos
- Regras específicas para endpoints de recuperação de senha
- Rate limiting adicional no nível de infraestrutura
