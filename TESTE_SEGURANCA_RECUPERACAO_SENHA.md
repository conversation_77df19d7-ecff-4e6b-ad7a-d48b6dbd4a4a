# Testes de Segurança - Recuperação de Senha

## Como Verificar se as Melhorias de Segurança Estão Funcionando

### 1. Teste de User Enumeration

**Objetivo**: Verificar se não é possível determinar se um usuário existe ou não.

**Passos**:
1. Acesse a página de recuperação de senha
2. Digite um usuário que **EXISTE** no sistema
3. Anote a mensagem retornada e o tempo de resposta
4. Digite um usuário que **NÃO EXISTE** no sistema  
5. Anote a mensagem retornada e o tempo de resposta

**Resultado Esperado**:
- Ambas as situações devem retornar a mesma mensagem: *"Se os dados informados estiverem corretos, você receberá um email com instruções para recuperação da senha."*
- O tempo de resposta deve ser similar (diferença máxima de 1-2 segundos devido ao delay artificial)

### 2. Teste de Exposição de Dados na Requisição

**Objetivo**: Verificar se dados sensíveis não aparecem na requisição HTTP.

**Passos**:
1. Abra o navegador e pressione F12 para abrir as ferramentas de desenvolvedor
2. Vá para a aba "Network" (Rede)
3. Acesse a página de recuperação de senha
4. Digite um login/CPF no formulário
5. Clique em "Recuperar"
6. Examine as requisições HTTP na aba Network

**Resultado Esperado**:
- Os dados originais (login/CPF) **NÃO** devem aparecer em texto claro nas requisições
- Deve aparecer apenas tokens criptografados ou dados mascarados
- Exemplo do que **NÃO** deve aparecer: `loginRecuperar=joao123&cpfRecuperar=12345678901`
- Exemplo do que **DEVE** aparecer: `token=a1b2c3d4e5f6...` (token criptografado)

### 3. Teste de Rate Limiting

**Objetivo**: Verificar se o sistema bloqueia tentativas excessivas.

**Passos**:
1. Faça 5 tentativas consecutivas de recuperação de senha
2. Tente fazer uma 6ª tentativa
3. Observe a mensagem retornada

**Resultado Esperado**:
- Após 5 tentativas, deve aparecer mensagem: *"Muitas tentativas de recuperação de senha. Tente novamente em X minutos."*
- O sistema deve bloquear novas tentativas por 15 minutos

### 4. Teste de Limpeza Automática de Campos

**Objetivo**: Verificar se os campos são limpos automaticamente.

**Passos**:
1. Digite dados nos campos de login/CPF
2. Aguarde 30 segundos sem interagir com a página
3. Observe se os campos foram limpos
4. Alternativamente, mude para outra aba do navegador por 5 segundos e volte

**Resultado Esperado**:
- Campos devem ser limpos automaticamente após inatividade
- Campos devem ser limpos quando a aba perde o foco

### 5. Teste de Headers de Segurança

**Objetivo**: Verificar se headers de segurança são adicionados.

**Passos**:
1. Abra as ferramentas de desenvolvedor (F12)
2. Vá para a aba "Network" (Rede)
3. Acesse a página de recuperação de senha
4. Clique na requisição da página
5. Examine os headers de resposta

**Resultado Esperado**:
- Deve conter os headers:
  - `Cache-Control: no-cache, no-store, must-revalidate, private`
  - `X-Content-Type-Options: nosniff`
  - `X-Frame-Options: DENY`
  - `Referrer-Policy: no-referrer`

### 6. Teste de Logs de Auditoria

**Objetivo**: Verificar se os logs não expõem dados sensíveis.

**Passos**:
1. Faça uma tentativa de recuperação de senha
2. Examine os logs da aplicação
3. Procure por entradas relacionadas à recuperação de senha

**Resultado Esperado**:
- Logs **NÃO** devem conter dados sensíveis em texto claro
- Exemplo do que **NÃO** deve aparecer: `Password recovery for user: joao123`
- Exemplo do que **DEVE** aparecer: `Password recovery for user: j***3`

### 7. Teste de Tokenização

**Objetivo**: Verificar se os tokens funcionam corretamente.

**Passos**:
1. Faça uma tentativa de recuperação de senha
2. Tente usar o mesmo token novamente (se possível interceptar)
3. Aguarde 5 minutos e tente usar um token antigo

**Resultado Esperado**:
- Tokens devem ser de uso único
- Tokens devem expirar em 5 minutos
- Tentativas de reutilização devem falhar

## Ferramentas Recomendadas para Teste

### Ferramentas do Navegador
- **Chrome DevTools**: F12 → Network tab
- **Firefox Developer Tools**: F12 → Network tab
- **Burp Suite Community**: Para análise avançada de requisições HTTP

### Ferramentas de Linha de Comando
```bash
# Teste de headers com curl
curl -I https://seusite.com/login

# Teste de múltiplas requisições
for i in {1..6}; do
  curl -X POST https://seusite.com/recuperar-senha \
    -d "login=teste$i" \
    -w "Tentativa $i: %{http_code} - Tempo: %{time_total}s\n"
done
```

### Scripts de Teste Automatizado
```javascript
// Teste de limpeza automática de campos (Console do navegador)
setTimeout(() => {
  const inputs = document.querySelectorAll('input[data-sensitive="true"]');
  inputs.forEach(input => {
    console.log(`Campo ${input.name}: "${input.value}"`);
  });
}, 31000); // Após 31 segundos
```

## Checklist de Verificação

- [ ] Mensagens idênticas para usuário existente/inexistente
- [ ] Dados sensíveis não aparecem em requisições HTTP
- [ ] Rate limiting funciona após 5 tentativas
- [ ] Campos são limpos automaticamente
- [ ] Headers de segurança estão presentes
- [ ] Logs não expõem dados sensíveis
- [ ] Tokens são únicos e expiram
- [ ] Tempo de resposta similar para diferentes cenários
- [ ] JavaScript de segurança está ativo
- [ ] Filtro de requisições está mascarando dados

## Problemas Comuns e Soluções

### Problema: Dados ainda aparecem na requisição
**Solução**: Verificar se o `SecurePasswordRecoveryService` está sendo usado corretamente

### Problema: Rate limiting não funciona
**Solução**: Verificar se o `RateLimitingService` está sendo chamado no botão de recuperação

### Problema: Campos não são limpos automaticamente
**Solução**: Verificar se o JavaScript está carregando e se os atributos `data-sensitive="true"` estão nos campos

### Problema: Headers de segurança ausentes
**Solução**: Verificar se o `SensitiveDataRequestFilter` está configurado no web.xml

### Problema: Logs ainda expõem dados
**Solução**: Verificar se os métodos de ofuscação estão sendo chamados antes de logar

## Relatório de Teste

Após executar todos os testes, documente:

1. **Data do teste**: ___________
2. **Versão testada**: ___________
3. **Testes que passaram**: ___/10
4. **Problemas encontrados**: ___________
5. **Ações corretivas necessárias**: ___________

## Testes de Regressão

Execute estes testes sempre que:
- Modificar código relacionado à recuperação de senha
- Atualizar bibliotecas de segurança
- Fazer deploy em novo ambiente
- Receber relatórios de vulnerabilidade
