#!/bin/bash

# Script de validação da implementação de segurança
# Verifica se todos os arquivos foram criados e configurados corretamente

echo "=== Validação da Implementação de Segurança - Recuperação de Senha ==="
echo ""

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Contadores
TOTAL_CHECKS=0
PASSED_CHECKS=0

# Função para verificar arquivo
check_file() {
    local file_path=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file_path" ]; then
        echo -e "${GREEN}✓${NC} $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}✗${NC} $description - ARQUIVO NÃO ENCONTRADO: $file_path"
        return 1
    fi
}

# Função para verificar conteúdo em arquivo
check_content() {
    local file_path=$1
    local search_pattern=$2
    local description=$3
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file_path" ] && grep -q "$search_pattern" "$file_path"; then
        echo -e "${GREEN}✓${NC} $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}✗${NC} $description - CONTEÚDO NÃO ENCONTRADO"
        return 1
    fi
}

echo "1. Verificando Classes de Segurança..."
echo "----------------------------------------"

# Classes principais de segurança
check_file "saude-web-wicket/src/main/java/br/com/celk/security/RateLimitingService.java" \
    "RateLimitingService - Controle de tentativas por IP"

check_file "saude-web-wicket/src/main/java/br/com/celk/security/SecurePasswordRecoveryService.java" \
    "SecurePasswordRecoveryService - Tokenização e criptografia"

check_file "saude-web-wicket/src/main/java/br/com/celk/security/SensitiveDataRequestFilter.java" \
    "SensitiveDataRequestFilter - Filtro de requisições HTTP"

check_file "saude-web-wicket/src/main/java/br/com/celk/security/SecurityAuditLogger.java" \
    "SecurityAuditLogger - Logs seguros de auditoria"

check_file "saude-web-wicket/src/main/java/br/com/celk/security/SecurityMaintenanceScheduler.java" \
    "SecurityMaintenanceScheduler - Limpeza automática de caches"

echo ""
echo "2. Verificando Classes de Comando Modificadas..."
echo "------------------------------------------------"

# Classes de comando modificadas
check_content "saude-command/src/main/java/br/com/ksisolucoes/bo/controle/usuario/ReiniciarSenha.java" \
    "simulateProcessingDelay" \
    "ReiniciarSenha - Delay para prevenir timing attacks"

check_content "saude-command/src/main/java/br/com/ksisolucoes/bo/vigilancia/externo/RedefinirSenhaVigilancia.java" \
    "simulateProcessingDelay" \
    "RedefinirSenhaVigilancia - Delay para prevenir timing attacks"

check_content "saude-command/src/main/java/br/com/ksisolucoes/bo/vigilancia/externo/ReenviarEmailVigilancia.java" \
    "simulateProcessingDelay" \
    "ReenviarEmailVigilancia - Delay para prevenir timing attacks"

echo ""
echo "3. Verificando Interfaces Web Modificadas..."
echo "--------------------------------------------"

# Interfaces web modificadas
check_content "saude-web-wicket/src/main/java/br/com/celk/view/login/PnlRecuperarSenha.java" \
    "RateLimitingService" \
    "PnlRecuperarSenha - Integração com rate limiting"

check_content "saude-web-wicket/src/main/java/br/com/celk/view/login/PnlRecuperarSenha.java" \
    "SecurePasswordRecoveryService" \
    "PnlRecuperarSenha - Integração com tokenização"

check_content "saude-web-wicket/src/main/java/br/com/celk/view/vigilancia/externo/view/login/VigilanciaEsqueceuSenhaPage.java" \
    "limparCamposFormulario" \
    "VigilanciaEsqueceuSenhaPage - Limpeza de campos"

echo ""
echo "4. Verificando Configurações..."
echo "-------------------------------"

# Configurações
check_content "saude-web-wicket/src/main/webapp/WEB-INF/web.xml" \
    "SensitiveDataRequestFilter" \
    "web.xml - Filtro de segurança configurado"

check_content "saude-web-wicket/src/main/java/br/com/celk/system/Application.java" \
    "WicketSecurityListener.register" \
    "Application.java - Listener de segurança registrado"

echo ""
echo "5. Verificando Recursos Frontend..."
echo "-----------------------------------"

# Recursos frontend
check_file "saude-web-wicket/src/main/webapp/js/secure-form.js" \
    "JavaScript de segurança para formulários"

check_content "saude-web-wicket/src/main/java/br/com/celk/view/login/PnlRecuperarSenha.html" \
    "data-sensitive" \
    "HTML - Atributos de segurança nos campos"

check_content "saude-web-wicket/src/main/java/br/com/celk/view/login/PnlRecuperarSenha.html" \
    "autocomplete=\"off\"" \
    "HTML - Desabilitação de autocomplete"

echo ""
echo "6. Verificando Testes..."
echo "------------------------"

# Testes
check_file "saude-web-wicket/src/test/java/br/com/celk/security/SecurePasswordRecoveryTest.java" \
    "Testes unitários de segurança"

echo ""
echo "7. Verificando Documentação..."
echo "------------------------------"

# Documentação
check_file "SECURITY_IMPROVEMENTS.md" \
    "Documentação das melhorias de segurança"

check_file "TESTE_SEGURANCA_RECUPERACAO_SENHA.md" \
    "Guia de testes de segurança"

echo ""
echo "8. Verificando Mensagens de Segurança..."
echo "----------------------------------------"

# Verifica se mensagens genéricas foram implementadas
check_content "saude-command/src/main/java/br/com/ksisolucoes/bo/controle/usuario/ReiniciarSenha.java" \
    "Se os dados informados estiverem corretos" \
    "ReiniciarSenha - Mensagem genérica implementada"

check_content "saude-command/src/main/java/br/com/ksisolucoes/bo/vigilancia/externo/RedefinirSenhaVigilancia.java" \
    "Se os dados informados estiverem corretos" \
    "RedefinirSenhaVigilancia - Mensagem genérica implementada"

echo ""
echo "=== RESUMO DA VALIDAÇÃO ==="
echo "Total de verificações: $TOTAL_CHECKS"
echo "Verificações aprovadas: $PASSED_CHECKS"
echo "Verificações falharam: $((TOTAL_CHECKS - PASSED_CHECKS))"

if [ $PASSED_CHECKS -eq $TOTAL_CHECKS ]; then
    echo -e "${GREEN}✓ TODAS AS VERIFICAÇÕES PASSARAM!${NC}"
    echo ""
    echo "A implementação de segurança está completa e pronta para uso."
    echo ""
    echo "Próximos passos:"
    echo "1. Execute os testes unitários: mvn test -Dtest=SecurePasswordRecoveryTest"
    echo "2. Faça deploy da aplicação"
    echo "3. Execute os testes manuais conforme TESTE_SEGURANCA_RECUPERACAO_SENHA.md"
    echo "4. Monitore os logs de segurança"
    exit 0
else
    echo -e "${RED}✗ ALGUMAS VERIFICAÇÕES FALHARAM!${NC}"
    echo ""
    echo "Por favor, verifique os arquivos marcados como faltando e"
    echo "complete a implementação antes de fazer o deploy."
    exit 1
fi
