package br.com.celk.component.behavior;

import br.com.celk.system.javascript.JScript;
import br.com.celk.util.CollectionUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.behavior.AbstractAjaxBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.request.handler.resource.ResourceStreamRequestHandler;
import org.apache.wicket.request.resource.ContentDisposition;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AjaxPreviewBlank extends AbstractAjaxBehavior {

    private boolean addAntiCache;
    private List<ResourceFile> lstResourcesFile;

    public AjaxPreviewBlank() {
        this(true);
    }

    public AjaxPreviewBlank(boolean addAntiCache) {
        super();
        this.addAntiCache = addAntiCache;
        lstResourcesFile = new ArrayList();
    }

    /**
     * Call this method to initiate the download.
     */
    public void initiate(AjaxRequestTarget target, String fileName, IResourceStream iResourceStream) {
        lstResourcesFile.add(new ResourceFile(fileName, iResourceStream));

        String url = getCallbackUrl().toString();

        if (addAntiCache) {
            url = url + (url.contains("?") ? "&" : "?");
            url = url + "antiCache=" + System.currentTimeMillis();
        }

        // the timeout is needed to let Wicket release the channel
        target.appendJavaScript("setTimeout(\"window.open('" + url + "', '_blank')\", 100);");
    }

    public void initiatePdfBase64(AjaxRequestTarget target, String base64) {
        target.appendJavaScript(JScript.openPdfBase64(base64));
    }

    public void initiateJsonBase64(AjaxRequestTarget target, String base64) {
        target.appendJavaScript(JScript.openJsonBase64(base64));
    }

    public void initiateXmlBase64(AjaxRequestTarget target, String base64) {
        target.appendJavaScript(JScript.openXmlBase64(base64));
    }

    public void initiateDownloadPdfBase64(AjaxRequestTarget target, String base64) {
        target.appendJavaScript(JScript.openDownloadPdfBase64(base64));
    }

    public void initiateJpegBase64(AjaxRequestTarget target, String base64) {
        target.appendJavaScript(JScript.openJpegBase64(base64));
    }

    /**
     * Call this method to initiate the download.
     */
    public void initiate(IHeaderResponse response, String fileName, IResourceStream iResourceStream) {
        lstResourcesFile.add(new ResourceFile(fileName, iResourceStream));

        String url = getCallbackUrl().toString();

        if (addAntiCache) {
            url = url + (url.contains("?") ? "&" : "?");
            url = url + "antiCache=" + System.currentTimeMillis();
        }

        // the timeout is needed to let Wicket release the channel
        response.render(OnDomReadyHeaderItem.forScript("setTimeout(\"window.open('" + url + "', '_blank')\", 100);"));
    }

    @Override
    public void onRequest() {
        if (CollectionUtils.isNotNullEmpty(lstResourcesFile)) {
            ResourceFile rf = lstResourcesFile.remove(lstResourcesFile.size() - 1);
            getComponent().getRequestCycle().scheduleRequestHandlerAfterCurrent(rf.getRequestHandler());
        }
    }

    private class ResourceFile implements Serializable {
        private String fileName;
        private IResourceStream iResourceStream;

        public ResourceFile(String fileName, IResourceStream iResourceStream) {
            this.fileName = fileName;
            this.iResourceStream = iResourceStream;
        }

        public ResourceStreamRequestHandler getRequestHandler() {
            ResourceStreamRequestHandler handler = new ResourceStreamRequestHandler(iResourceStream, fileName);
            handler.setContentDisposition(ContentDisposition.INLINE);
            return handler;
        }
    }
}
