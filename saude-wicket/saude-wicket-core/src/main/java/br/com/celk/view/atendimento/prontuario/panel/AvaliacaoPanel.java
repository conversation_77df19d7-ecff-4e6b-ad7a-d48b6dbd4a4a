package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.temp.behavior.TempBehavior;
import br.com.celk.component.temp.behavior.TempFormBehavior;
import br.com.celk.component.temp.interfaces.TempHelper;
import br.com.celk.component.temp.interfaces.impl.DefaultTempStoreStrategy;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoPrimarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class AvaliacaoPanel extends ProntuarioCadastroPanel{

    private Form<AtendimentoPrimario> form;
    private List<AtendimentoPrimario> historico;
    private DropDown<Long> cbxTipoGlicemia;
    private DropDown<Long> cbxPrioridade;
//    private MsDropDown<Long> cbxPrioridade;
    private DropDown<String> cbxAlergico;
    private InputArea txaDescricaoAlergia;
    private Label lblAvaliacaoGlicemia;
    private Table tblHistoricoAvaliacoes;
    private InputField txtDataAvaliacao;
    private String vacina;
    private InputField txtVacina;

    private AttributeModifier modifierVermelho = new AttributeModifier("style", "color: rgb(255, 102, 102);");
    private InputArea txaObservacao;
    private DropDown<Long> dropDownAtendimentoRN;
    private DropDown<Long> dropDownIndicadorAcidente;

    public AvaliacaoPanel(String id) {
        super(id, BundleManager.getString("avaliacao"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        getVacinaEmDiaPaciente();
        form = new Form("form", new CompoundPropertyModel(carregarAtendimentoPrimario()));

        AtendimentoPrimario proxy = on(AtendimentoPrimario.class);

        form.add(txtDataAvaliacao = new InputField(path(proxy.getDataAvaliacao())));
        txtDataAvaliacao.add(new TempBehavior());
        form.add(txtVacina = new InputField<String>("vacinaEmDia",new PropertyModel(this, "vacina")));
        txtVacina.setEnabled(false);
        form.add(new DoubleField(path(proxy.getPeso())).setMDec(3).setVMax(300D).add(new TempBehavior()));
        form.add(new DoubleField(path(proxy.getAltura())).setMDec(1).add(new TempBehavior()));
        form.add(new DoubleField(path(proxy.getPerimetroCefalico())).add(new TempBehavior()));
        form.add(new DoubleField(path(proxy.getPerimetroPanturrilha())).setMDec(1).add(new TempBehavior()));
        form.add(new LongField(path(proxy.getCintura())).add(new TempBehavior()));
        form.add(new DoubleField(path(proxy.getTemperatura())).add(new TempBehavior()));
        form.add(new LongField(path(proxy.getGlicemia())).add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarGlicemia(target);
                new TempHelper().save(target, cbxTipoGlicemia);
            }
        }).add(new TempBehavior()));
        cbxTipoGlicemia =getCbxTipoGlicemia(path(proxy.getGlicemiaTipo()), false);
        form.add(cbxTipoGlicemia.add(new TempBehavior()));
        form.add(getCbxGlicemiaForaFaixa(path(proxy.getGlicemiaForaFaixa())).add(new TempBehaviorV2()));
        form.add(getCbxTipoGlicemia(path(proxy.getGlicemiaForaFaixaTipo()), true).add(new TempBehavior()).add(new Tooltip().setText("msgGlicemiaForaFaixa")));
        form.add(lblAvaliacaoGlicemia = new Label("lblAvaliacaoGlicemia"));
        form.add(new LongField(path(proxy.getFrequenciaCardiaca())).add(new TempBehavior()));
        form.add(new LongField(path(proxy.getPressaoArterialSistolica())).setLabel(new Model(bundle("pas"))).add(new TempBehavior()));
        form.add(new LongField(path(proxy.getPressaoArterialDiastolica())).setLabel(new Model(bundle("pad"))).add(new TempBehavior()));
        form.add(new DoubleField(path(proxy.getTemperaturaRetal())).add(new TempBehavior()));
        form.add(new LongField(path(proxy.getFrequenciaRespiratoria())).add(new TempBehavior()));
//        form.add(new LongField(path(proxy.getFrequenciaCardiacaFetal())).add(new TempBehavior()));
        form.add(new LongField(path(proxy.getDiurese())).add(new TempBehavior()));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoDiurese()), AtendimentoPrimario.TipoDiurese.values(), true).add(new TempBehavior()));
        form.add(new LongField(path(proxy.getSaturacaoOxigenio())).add(new TempBehavior()));
        form.add(new InputField(path(proxy.getEvacuacao())).add(new TempBehavior()));
        form.add(getCbxPrioridade(path(proxy.getPrioridade())).add(new TempBehavior()));
        form.add(cbxAlergico = (DropDown<String>) DropDownUtil.getNaoSimDropDown(path(proxy.getFlagAlergico())).add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarAlergia(target);
                new TempHelper().save(target, txaDescricaoAlergia);
            }
        }).add(new TempBehavior()));
        form.add(txaDescricaoAlergia = new InputArea(path(proxy.getDescricaoAlergia())));

        txaDescricaoAlergia.add(new TempBehavior());

        form.add(txaObservacao = new InputArea(path(proxy.getObservacao())));

        txaObservacao.add(new TempBehavior());

        form.add(tblHistoricoAvaliacoes = new Table("tblHistoricoAvaliacoes", getColumnsHistoricoAvaliacoes(), getCollectionProviderHistoricoAvaliacoes()));
        tblHistoricoAvaliacoes.populate();
        tblHistoricoAvaliacoes.setScrollY("272px");
        tblHistoricoAvaliacoes.setScrollX("768px");

        add(form);

        dropDownAtendimentoRN = new DropDown<Long>(path(proxy.getAtendimentoRN()));
        dropDownAtendimentoRN.addChoice(RepositoryComponentDefault.NAO_LONG, "Não");
        dropDownAtendimentoRN.addChoice(RepositoryComponentDefault.SIM_LONG, "Sim");
        form.add(dropDownAtendimentoRN.add(new TempBehavior()));
        form.add(dropDownIndicadorAcidente = (DropDown<Long>) DropDownUtil.getIEnumDropDown(path(proxy.getIndicadorAcidente()), AtendimentoInformacao.IndicadorAcidente.values()));
        form.add(dropDownIndicadorAcidente.add(new TempBehavior()));

        UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addProperty(UsuarioCadsusDado.PROP_GESTANTE)
                .addProperty(UsuarioCadsusDado.PROP_PESO_NASCER)
                .addProperty(UsuarioCadsusDado.PROP_DESCRICAO_ALERGICO)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, getAtendimento().getUsuarioCadsus().getCodigo()))
                .start().getVO();
        if (usuarioCadsusDado != null && usuarioCadsusDado.getDescricaoAlergico() != null) {
            cbxAlergico.setComponentValue(RepositoryComponentDefault.SIM);
            txaDescricaoAlergia.setComponentValue(usuarioCadsusDado.getDescricaoAlergico());
        }
        if (form.getModelObject().getFlagAlergico()==null) {
            cbxAlergico.setModelObject(RepositoryComponentDefault.NAO);
            new TempHelper().save(cbxAlergico, RepositoryComponentDefault.NAO);
        }

        form.add(new TempFormBehavior(new DefaultTempStoreStrategy(getAtendimento(), getIdentificador().toString(), AtendimentoPrimario.class)));

        if(form.getModelObject().getDataAvaliacao()==null){
            Date dataAtual = DataUtil.getDataAtual();

            txtDataAvaliacao.setModelObject(dataAtual);
            new TempHelper().save(txtDataAvaliacao, Data.formatarDataHora(dataAtual));
        }

        if (form.getModelObject().getPrioridade()==null) {
            cbxPrioridade.setModelObject(Atendimento.PRIORIDADE_NORMAL);
            new TempHelper().save(cbxPrioridade, Atendimento.PRIORIDADE_NORMAL.toString());
        }

        if (form.getModelObject().getAtendimentoRN()==null) {
            dropDownAtendimentoRN.setModelObject(RepositoryComponentDefault.NAO_LONG);
            new TempHelper().save(dropDownAtendimentoRN, RepositoryComponentDefault.NAO_LONG.toString());
        } else {
            new TempHelper().save(dropDownAtendimentoRN, dropDownAtendimentoRN.getModelObject().toString());
        }
        if (form.getModelObject().getIndicadorAcidente()==null) {
            dropDownIndicadorAcidente.setModelObject(AtendimentoInformacao.IndicadorAcidente.NAO_ACIDENTE.value());
            new TempHelper().save(dropDownIndicadorAcidente, AtendimentoInformacao.IndicadorAcidente.NAO_ACIDENTE.value().toString());
        } else {
            new TempHelper().save(dropDownIndicadorAcidente, dropDownIndicadorAcidente.getModelObject().toString());
        }

        lblAvaliacaoGlicemia.setOutputMarkupId(true);
        avaliarAlergia(null);
        avaliarGlicemia(null);
    }

    private void getVacinaEmDiaPaciente() {
        boolean isVacinaEmDia = false;
        try {
            isVacinaEmDia = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).isVacinaEmDia(getAtendimento().getUsuarioCadsus());
            if (isVacinaEmDia) {
                vacina = Bundle.getStringApplication("rotulo_sim");
            } else {
                vacina = Bundle.getStringApplication("rotulo_nao");
            }
        } catch (DAOException ex) {
            Logger.getLogger(AvaliacaoPanel.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(AvaliacaoPanel.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private DropDown<String> getCbxGlicemiaForaFaixa(String proxy) {
        DropDown<String> cbx= new DropDown<>(proxy);
        cbx.addChoice(bundle("lo"),bundle("lo"));
        cbx.addChoice(bundle("hi"),bundle("hi"));
        return cbx;
    }

    private DropDown<Long> getCbxTipoGlicemia(String id, boolean foraFaixa){
        DropDown<Long> cbxTipo = new DropDown<Long>(id);
        cbxTipo.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_JEJUM, bundle("emJejum"));
        cbxTipo.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_POS_PRANDIAL, bundle("posPrandial"));
        cbxTipo.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_PRE_PRANDIAL, bundle("prePrandial"));
        cbxTipo.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_NAO_ESPECIFICADO, bundle("naoEspecificado"));
        if(!foraFaixa) {
            cbxTipo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    avaliarGlicemia(target);
                }
            });
        }
        return cbxTipo;
    }

    private DropDown<Long> getCbxPrioridade(String id){
        if (cbxPrioridade==null) {
            cbxPrioridade = new DropDown<Long>(id);

            cbxPrioridade.addChoice(Atendimento.PRIORIDADE_NORMAL, bundle("nao"));
            cbxPrioridade.addChoice(Atendimento.PRIORIDADE_URGENTE, bundle("sim"));

        }
        return cbxPrioridade;
    }

//    private MsDropDown<Long> getCbxPrioridade(String id){
//        if (cbxPrioridade==null) {
//            cbxPrioridade = new MsDropDown<Long>(id);
//     
//            cbxPrioridade.addChoice(new MsItem<Long>(Atendimento.PRIORIDADE_ELETIVO, bundle("nao"), urlFor(new SharedResourceReference(Resources.class, "images/css/icons32/ball-blue.png"), null).toString()));
//            cbxPrioridade.addChoice(new MsItem<Long>(Atendimento.PRIORIDADE_URGENTE, bundle("sim"), urlFor(new SharedResourceReference(Resources.class, "images/css/icons32/ball-red.png"), null).toString()));
//            
//            cbxPrioridade.add(new AjaxFormComponentUpdatingBehavior("onchange") {
//                @Override
//                protected void onUpdate(AjaxRequestTarget target) {
//                    target.appendJavaScript("alert('"+cbxPrioridade.getModelObject()+"')");
//                }
//            });
//                    
//        }
//        return cbxPrioridade;
//    }

    private List<IColumn> getColumnsHistoricoAvaliacoes(){
        List<IColumn> columns = new ArrayList<IColumn>();

        AtendimentoPrimario proxy = on(AtendimentoPrimario.class);

        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getAtendimento().getDataAtendimento())));
        columns.add(new DoubleColumn(bundle("pesoKg"), path(proxy.getPeso())).setCasasDecimais(3));
        columns.add(createColumn(bundle("temperatura"), proxy.getTemperatura()));
        columns.add(createColumn(bundle("pa"), proxy.getPressaoArterial()));
        columns.add(new DoubleColumn(bundle("alturaCm"), path(proxy.getAltura())).setCasasDecimais(1));
        columns.add(createColumn(bundle("frequenciaCardiacaAbrev"), proxy.getFrequenciaCardiaca()));
        columns.add(createColumn(bundle("saturacaoOxigenioAbrev"), proxy.getSaturacaoOxigenio()));
        columns.add(createColumn(bundle("glicemia"), proxy.getGlicemiaFormatada()));
        columns.add(createColumn(bundle("glicemiaForaFaixa"), proxy.getGlicemiaForaFaixaFormatada()));
        columns.add(new DoubleColumn(bundle("perimetroPanturrilhaCm"), path(proxy.getPerimetroPanturrilha())).setCasasDecimais(1));

        return columns;
    }

    private ICollectionProvider getCollectionProviderHistoricoAvaliacoes(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return historico;
            }
        };
    }

    private void avaliarAlergia(AjaxRequestTarget target){
        boolean enable = RepositoryComponentDefault.SIM.equals(form.getModelObject().getFlagAlergico());
        txaDescricaoAlergia.setEnabled(enable);
        if (target!=null) {
            if (!enable) {
                txaDescricaoAlergia.limpar(target);
            } else {
                target.add(txaDescricaoAlergia);
            }
        }
    }

    private void avaliarGlicemia(AjaxRequestTarget target){
        try{
            IParameterModuleContainer ipmc = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE);
            Long tipoGlicemia = form.getModelObject().getGlicemiaTipo();
            Long glicemiaInformada = form.getModelObject().getGlicemia();

            if (AtendimentoPrimario.TIPO_GLICEMIA_JEJUM.equals(tipoGlicemia)) {
                Long glicemiaPosJejumInicial = ipmc.getParametro("GlicemiaPosJejumInicial");
                Long glicemiaPosJejumFinal = ipmc.getParametro("GlicemiaPosJejumFinal");
                atualizaLabelGlicemia(glicemiaPosJejumInicial, glicemiaPosJejumFinal, glicemiaInformada);

            } else if (AtendimentoPrimario.TIPO_GLICEMIA_PRE_PRANDIAL.equals(tipoGlicemia)) {
                Long glicemiaPosPrandialInicial = ipmc.getParametro("GlicemiaPrePrandialInicial");
                Long glicemiaPosPrandialFinal = ipmc.getParametro("GlicemiaPrePrandialFinal");
                atualizaLabelGlicemia(glicemiaPosPrandialInicial, glicemiaPosPrandialFinal, glicemiaInformada);

            } else if (AtendimentoPrimario.TIPO_GLICEMIA_POS_PRANDIAL.equals(tipoGlicemia)) {
                Long glicemiaPosPrandialInicial = ipmc.getParametro("GlicemiaPosPrandialInicial");
                Long glicemiaPosPrandialFinal = ipmc.getParametro("GlicemiaPosPrandialFinal");
                atualizaLabelGlicemia(glicemiaPosPrandialInicial, glicemiaPosPrandialFinal, glicemiaInformada);

            } else if (AtendimentoPrimario.TIPO_GLICEMIA_NAO_ESPECIFICADO.equals(tipoGlicemia)) {
                atualizaLabelGlicemia(null, null, glicemiaInformada);

            } else {
                atualizaLabelGlicemia(null, null, null);
            }

            boolean enable = !Coalesce.asLong(form.getModelObject().getGlicemia()).equals(0L);
            cbxTipoGlicemia.setEnabled(enable);
            if (target!=null) {
                target.add(lblAvaliacaoGlicemia);
                target.add(cbxTipoGlicemia);
                if (!enable) {
                    cbxTipoGlicemia.limpar(target);
                }
            }
        } catch (DAOException ex){
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void atualizaLabelGlicemia(Long glicemiaInicial, Long glicemiaFinal, Long glicemiaInformada) {
        if (Coalesce.asLong(glicemiaInformada) == 0L) {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return "";
                }
            });
            setLabelNormal();
        } else if (glicemiaInicial == null || glicemiaFinal == null) {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return bundle("desconhecido");
                }
            });
            setLabelNormal();
        } else if (glicemiaInformada >= glicemiaInicial && glicemiaInformada <= glicemiaFinal) {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return bundle("normal");
                }
            });
            setLabelNormal();
        } else {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return bundle("foraNormalidade");
                }
            });
            setLabelVermelho();
        }
    }

    private void setLabelNormal(){
        if (lblAvaliacaoGlicemia.getBehaviors().contains(modifierVermelho)) {
            lblAvaliacaoGlicemia.remove(modifierVermelho);
        }
    }

    private void setLabelVermelho(){
        if (!lblAvaliacaoGlicemia.getBehaviors().contains(modifierVermelho)) {
            lblAvaliacaoGlicemia.add(modifierVermelho);
        }
    }

    private AtendimentoPrimario carregarAtendimentoPrimario(){
        AtendimentoPrimario ap = new AtendimentoPrimario();
        try {
            AtendimentoPrimarioDTO dto = BOFactoryWicket.getBO(AtendimentoFacade.class).carregarDadosAtendimentoPrimario(getAtendimento().getUsuarioCadsus().getCodigo(), getAtendimento());
            historico = dto.getAtendimentoPrimarioList();

            if(dto.getAtendimentoInformacao() != null){
                ap.setAtendimentoRN(dto.getAtendimentoInformacao().getAtendimentoRN());
                ap.setIndicadorAcidente(dto.getAtendimentoInformacao().getIndicadorAcidente());
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        return ap;
    }

}
