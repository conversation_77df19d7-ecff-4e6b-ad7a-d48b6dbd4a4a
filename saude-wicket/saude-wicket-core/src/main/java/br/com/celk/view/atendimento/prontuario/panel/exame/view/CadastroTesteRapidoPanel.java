package br.com.celk.view.atendimento.prontuario.panel.exame.view;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.atendimento.prontuario.panel.TesteRapidoPanel;
import br.com.celk.view.atendimento.prontuario.panel.exame.dialog.DlgCadastroTesteRapidoConjunto;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.utils.PermissoesWebUtil;
import br.com.ksisolucoes.agendamento.dto.TesteRapidoDTO;
import br.com.ksisolucoes.agendamento.exame.ExameHelper;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoTesteRapidoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapido;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoConjunto;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado;
import br.com.ksisolucoes.vo.prontuario.basico.TipoTesteRapido;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoTipoTabela;
import ch.lambdaj.Lambda;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.DropDownChoice;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.vo.prontuario.basico.TesteRapido.*;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class CadastroTesteRapidoPanel extends ProntuarioCadastroPanel {

    private Form<TesteRapido> form;
    private Form formTipoTesteRapido;

    private WebMarkupContainer containerOne;
    private WebMarkupContainer containerTwo;
    private WebMarkupContainer containerThree;
    private WebMarkupContainer containerFour;
    private WebMarkupContainer containerFive;
    private WebMarkupContainer containerSix;
    private WebMarkupContainer containerSeven;
    private WebMarkupContainer containerPrimeirosSintomas;
    private WebMarkupContainer containerTuberculose;
    private WebMarkupContainer containerAmostra;

    private DropDown<Long> dropDownPermiteContato;
    private DropDown<Long> dropDownTipoContato;
    private DropDown<Long> dropDownApenasComProprio;
    private DropDown<Long> dropDownComoFicouSabendoServico;
    private DropDown dropDownTipoTeste;

    private InputField txtTipoContatoOutro;
    private InputField txtFalarCom;
    private InputField txtComoFicouSabendoOutro;

    private DlgImpressaoObject<List<TesteRapidoRealizado>> dlgConfirmacaoImpressao;
    private DlgCadastroTesteRapidoConjunto dlgCadastroTesteRapidoConjunto;

    private TipoTesteRapido tipoTesteRapidoAdicionar;
    private TesteRapidoRealizado testeRapidoRealizado;
    private TesteRapidoConjunto testeRapidoConjunto;

    private UsuarioCadsusEsus usuarioCadsusEsus;

    private Table tblTipoTesteRapido;
    private List<TesteRapidoDTO> tipoTesteRapidoList = new ArrayList<>();

    private final String CSS_FILE = "CadastroTesteRapidoPanel.css";
    private DropDown dropDownNomeConjuntoDiagnostico;
    private AbstractAjaxLink btnCadTesteRapidoConjunto;
    private DisabledInputField txtFabricante;
    private DisabledInputField txtMetodo;
    private DateChooser dateChooserdataPrimeirosSintomas;
    private Date dataPrimeirosSintomas;
    private boolean readOnly;
    private DropDown dropDownTipoAmostra;
    private Long tipoAmostra;
    private DropDown<Long> dropDownGestante;
    private InputField cd4;

    private Long tipoTeste;
    private Long gestanteOunao;



    public CadastroTesteRapidoPanel(String id, TesteRapidoRealizado testeRapidoRealizado, Long tipoTeste) {
        this(id, testeRapidoRealizado, tipoTeste, false);
    }

    public CadastroTesteRapidoPanel(String id, TesteRapidoRealizado testeRapidoRealizado, Long tipoTeste, boolean readOnly) {
        super(id, bundle("testeRapido"));
        this.testeRapidoRealizado = testeRapidoRealizado;
        this.tipoTesteRapidoList = carregarTipoTesteCadastrados();
        this.tipoTeste = tipoTeste;
        this.readOnly = readOnly;
    }

    public CadastroTesteRapidoPanel(String id, Long tipoTeste) {
        this(id);
        this.tipoTeste = tipoTeste;
        this.readOnly = false;
    }

    public CadastroTesteRapidoPanel(String id) {
        super(id,bundle("testeRapido"));
    }

    private void createContainers(TesteRapido proxy) {
            createContainerOne(proxy, tipoTeste);
            createContainerTwo(proxy, tipoTeste);
            createContainerThree(proxy, tipoTeste);
            createContainerFour(proxy, tipoTeste);
            createContainerFive(proxy, tipoTeste);
            createContainerSix(proxy, tipoTeste);
            createFormTipoTesteRapido();
            createContainerTuberculos(proxy);
    }

    private void createContainerOne(TesteRapido proxy, Long tipoTeste) {
        containerOne.setVisible(!tipoTeste.equals(TIPO_TESTE_COVID_MAIS_INFLUENZA_AB) &&!tipoTeste.equals(TIPO_TESTE_COVID_19) && !tipoTeste.equals(TIPO_TESTE_TUBERCULOSE) && !tipoTeste.equals(TIPO_TESTE_DENGUE) && !tipoTeste.equals(TIPO_TESTE_INFLUENZA) && !tipoTeste.equals(TIPO_TESTE_HANSENIASE) && !tipoTeste.equals(TIPO_TESTE_HIV_SIFILIS) && !tipoTeste.equals(TIPO_TESTE_AIDS_AVANCADO));
        containerOne.setEnabled(!readOnly);
        /* INICIO - containerOne */
        containerOne.add(dropDownPermiteContato = DropDownUtil.getSimNaoLongDropDown(path(proxy.getPermiteContato())));
        dropDownPermiteContato.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                disabledAbordagemConsentida(target);
            }
        });

        containerOne.add(dropDownTipoContato = DropDownUtil.getIEnumDropDown(path(proxy.getTipoContato()), TesteRapido.TipoContato.values(), true, false));
        dropDownTipoContato.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                disabledTipoContato(target);
            }
        });
        containerOne.add(txtTipoContatoOutro = new InputField<String>(path(proxy.getTipoContatoOutro())));

        containerOne.add(dropDownApenasComProprio = DropDownUtil.getSimNaoLongDropDown(path(proxy.getApenasComProprio()), true, false));
        dropDownApenasComProprio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                disabledApenasComProprio(target);
            }
        });
        containerOne.add(txtFalarCom = new InputField<String>(path(proxy.getFalarCom())));
        /* FIM - containerOne */
    }

    private void createContainerTwo(TesteRapido proxy, Long tipoTeste) {
        containerTwo.setVisible(!tipoTeste.equals(TIPO_TESTE_COVID_MAIS_INFLUENZA_AB) &&!tipoTeste.equals(TIPO_TESTE_COVID_19) && !tipoTeste.equals(TIPO_TESTE_TUBERCULOSE) && !tipoTeste.equals(TIPO_TESTE_DENGUE) && !tipoTeste.equals(TIPO_TESTE_INFLUENZA) && !tipoTeste.equals(TIPO_TESTE_HANSENIASE) && !tipoTeste.equals(TIPO_TESTE_HIV_SIFILIS) && !tipoTeste.equals(TIPO_TESTE_AIDS_AVANCADO));
        containerOne.setEnabled(!readOnly);
        containerTwo.add(new CheckBoxLongValue(path(proxy.getTipoExposicaoRelacaoSexual())));
        containerTwo.add(new CheckBoxLongValue(path(proxy.getTipoExposicaoUsuarioDrogasInjetaveis())));
        containerTwo.add(new CheckBoxLongValue(path(proxy.getTipoExposicaoUsuarioDrogas())));
        containerTwo.add(new CheckBoxLongValue(path(proxy.getTipoExposicaoTransmitido())));
        containerTwo.add(new CheckBoxLongValue(path(proxy.getTipoExposicaoHemofilico())));
        containerTwo.add(new CheckBoxLongValue(path(proxy.getTipoExposicaoSemRisco())));
        containerTwo.add(new InputField<String>(path(proxy.getTipoExposicaoOutro())));
    }

    private void createContainerThree(TesteRapido proxy, Long tipoTeste) {
        containerThree.setVisible(!tipoTeste.equals(TIPO_TESTE_COVID_MAIS_INFLUENZA_AB) &&!tipoTeste.equals(TIPO_TESTE_COVID_19) && !tipoTeste.equals(TIPO_TESTE_TUBERCULOSE) && !tipoTeste.equals(TIPO_TESTE_DENGUE) && !tipoTeste.equals(TIPO_TESTE_INFLUENZA) && !tipoTeste.equals(TIPO_TESTE_HANSENIASE) && !tipoTeste.equals(TIPO_TESTE_HIV_SIFILIS) && !tipoTeste.equals(TIPO_TESTE_AIDS_AVANCADO));
        containerThree.setEnabled(!readOnly);
        containerThree.add(new CheckBoxLongValue(path(proxy.getRecorteUsuarioDrogas())));
        containerThree.add(new CheckBoxLongValue(path(proxy.getRecorteUsuarioDrogasInjetaveis())));
        containerThree.add(new CheckBoxLongValue(path(proxy.getRecorteProfissionalSexo())));
        containerThree.add(new CheckBoxLongValue(path(proxy.getRecorteSexoHomem())));
        containerThree.add(new InputField<String>(path(proxy.getRecorteOutro())));
    }

    private void createContainerFour(TesteRapido proxy, Long tipoTeste) {
        containerFour.setVisible(!tipoTeste.equals(TIPO_TESTE_COVID_MAIS_INFLUENZA_AB) && !tipoTeste.equals(TIPO_TESTE_COVID_19) && !tipoTeste.equals(TIPO_TESTE_TUBERCULOSE) && !tipoTeste.equals(TIPO_TESTE_DENGUE) && !tipoTeste.equals(TIPO_TESTE_INFLUENZA) && !tipoTeste.equals(TIPO_TESTE_HANSENIASE) && !tipoTeste.equals(TIPO_TESTE_HIV_SIFILIS) && !tipoTeste.equals(TIPO_TESTE_AIDS_AVANCADO));
        containerFour.setEnabled(!readOnly);
        containerFour.add(new CheckBoxLongValue(path(proxy.getUsoDrogaNunca())));
        containerFour.add(new CheckBoxLongValue(path(proxy.getUsoDrogaBebeFrequencia())));
        containerFour.add(new CheckBoxLongValue(path(proxy.getUsoDrogaInjetavel())));
        containerFour.add(new CheckBoxLongValue(path(proxy.getUsoDrogaOutras())));
        containerFour.add(new CheckBoxLongValue(path(proxy.getUsoDrogaParceiro())));

    }

    private void createContainerFive(TesteRapido proxy, Long tipoTeste) {
        containerFive.setVisible(!tipoTeste.equals(TIPO_TESTE_COVID_MAIS_INFLUENZA_AB) && !tipoTeste.equals(TIPO_TESTE_COVID_19) && !tipoTeste.equals(TIPO_TESTE_TUBERCULOSE) && !tipoTeste.equals(TIPO_TESTE_DENGUE) && !tipoTeste.equals(TIPO_TESTE_INFLUENZA) && !tipoTeste.equals(TIPO_TESTE_HANSENIASE) && !tipoTeste.equals(TIPO_TESTE_HIV_SIFILIS) && !tipoTeste.equals(TIPO_TESTE_AIDS_AVANCADO));
        containerFive.setEnabled(!readOnly);

        if (usuarioCadsusEsus != null && usuarioCadsusEsus.getUsuarioCadsus() != null) {
            if (UsuarioCadsus.SEXO_FEMININO.equals(usuarioCadsusEsus.getUsuarioCadsus().getSexo())) {
                dropDownGestante = DropDownUtil.getSimNaoLongDropDown(path(proxy.getGestante()), false, false);
                dropDownGestante.setRequired(true);
            } else {
                dropDownGestante = DropDownUtil.getSimNaoLongDropDown(path(proxy.getGestante()), true, false);
                dropDownGestante.setEnabled(false);
            }
        } else {
            dropDownGestante = DropDownUtil.getSimNaoLongDropDown(path(proxy.getGestante()), true, false);
        }
        containerFive.add(dropDownGestante);
        containerFive.add(DropDownUtil.getIEnumDropDown(path(proxy.getOrientacaoSexual()), TesteRapido.OrientacaoSexual.values(), true, false));

    }

    private void createContainerSix(TesteRapido proxy, Long tipoTeste) {
        containerSix.setVisible(!tipoTeste.equals(TIPO_TESTE_COVID_MAIS_INFLUENZA_AB) &&!tipoTeste.equals(TIPO_TESTE_COVID_19) && !tipoTeste.equals(TIPO_TESTE_TUBERCULOSE) && !tipoTeste.equals(TIPO_TESTE_DENGUE) && !tipoTeste.equals(TIPO_TESTE_INFLUENZA) && !tipoTeste.equals(TIPO_TESTE_HANSENIASE) && !tipoTeste.equals(TIPO_TESTE_HIV_SIFILIS) && !tipoTeste.equals(TIPO_TESTE_AIDS_AVANCADO));
        containerSix.setEnabled(!readOnly);

        DropDownChoice<Long> dropDownGestante = DropDownUtil.getSimNaoLongDropDown(path(proxy.getGestante()));
        dropDownGestante.setOutputMarkupId(true);
        containerSix.add(dropDownGestante);

        dropDownGestante.add(new AjaxFormComponentUpdatingBehavior("change") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                verificarObrigatoriedadeCampos(target);
            }
        });

        containerSix.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getPrimeiroTesteHiv()), true, false).setOutputMarkupId(true));

        dropDownComoFicouSabendoServico = DropDownUtil.getIEnumDropDown(path(proxy.getComoFicouSabendoServico()), TesteRapido.SabendoServico.values(), true, false);
        dropDownComoFicouSabendoServico.setOutputMarkupId(true);
        dropDownComoFicouSabendoServico.add(new AjaxFormComponentUpdatingBehavior("change") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                disabledSabendoServico(target);
            }
        });
        containerSix.add(dropDownComoFicouSabendoServico);

        txtComoFicouSabendoOutro = new InputField<String>(path(proxy.getComoFicouSabendoOutro()));
        txtComoFicouSabendoOutro.setOutputMarkupId(true);
        containerSix.add(txtComoFicouSabendoOutro);

        containerSix.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoParceiro()), TesteRapido.TipoParceiro.values(), true, false)
                .setLabel(new Model<String>(bundle("tipoParceiroUlt12Meses", this)))
                .setOutputMarkupId(true));

        containerSix.add(DropDownUtil.getIEnumDropDown(path(proxy.getNumeroParceiro()), TesteRapido.NumeroParceiro.values(), true, false)
                .setLabel(new Model<String>(bundle("numeroParceirosSexuaisUlt12Meses", this)))
                .setOutputMarkupId(true));

        containerSix.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getTeveDst()), true, false)
                .setLabel(new Model<String>(bundle("teveAlgumaDstUlt12Meses", this)))
                .setOutputMarkupId(true));

        containerSix.add(DropDownUtil.getIEnumDropDown(path(proxy.getUsoCamisinhaParceiroFixo()), TesteRapido.CamisinhaParceiroFixo.values(), true, false)
                .setLabel(new Model<String>(bundle("usoCamisinhaRelacoesParceiroFixo", this)))
                .setOutputMarkupId(true));

        containerSix.add(DropDownUtil.getIEnumDropDown(path(proxy.getUsoCamisinhaParceiroEventual()), TesteRapido.CamisinhaParceiroEventual.values(), true, false)
                .setLabel(new Model<String>(bundle("usoCamisinhaRelacoesParceiroEventual", this)))
                .setOutputMarkupId(true));
    }

    private void verificarObrigatoriedadeCampos(AjaxRequestTarget target) {
        DropDownChoice<Long> dropDownGestante = (DropDownChoice<Long>) containerSix.get("gestanteDropdown");
        Long valorGestante = dropDownGestante.getModelObject();
        boolean isGestante = valorGestante != null && valorGestante.equals(1L);

        for (Component component : containerSix) {
            if (component instanceof FormComponent) {
                FormComponent<?> formComponent = (FormComponent<?>) component;
                formComponent.setRequired(isGestante);
            }
        }
        target.add(containerSix);
    }

    private void createFormTipoTesteRapido() {
        containerSeven.setEnabled(!readOnly);
        formTipoTesteRapido = new Form("formTipoTesteRapido", new CompoundPropertyModel(this));

        containerSeven.add(getDropDownTipoTeste());
        containerSeven.add(getDropDownNomeConjuntoDiagnostico());
        containerSeven.add(txtFabricante = new DisabledInputField("testeRapidoConjunto.fabricante"));
        containerSeven.add(getDropDownTipoAmostra());
        containerSeven.add(txtMetodo = new DisabledInputField("testeRapidoConjunto.metodo"));
        containerPrimeirosSintomas.add(dateChooserdataPrimeirosSintomas = new DateChooser("dataPrimeirosSintomas", new PropertyModel(this, "dataPrimeirosSintomas") ));
        containerPrimeirosSintomas.setVisible(TIPO_TESTE_COVID_MAIS_INFLUENZA_AB.equals(tipoTeste)||TIPO_TESTE_COVID_19.equals(tipoTeste) || TIPO_TESTE_DENGUE.equals(tipoTeste) || TIPO_TESTE_INFLUENZA.equals(tipoTeste) || TIPO_TESTE_HANSENIASE.equals(tipoTeste));
        containerSeven.add(containerPrimeirosSintomas);


        containerSeven.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarTipoDeTeste(target);
            }
        });


        containerSeven.add(btnCadTesteRapidoConjunto = new AbstractAjaxLink("btnNomeConjuntoDiagnostico") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                if (dlgCadastroTesteRapidoConjunto == null) {
                    WindowUtil.addModal(target, this, dlgCadastroTesteRapidoConjunto = new DlgCadastroTesteRapidoConjunto(WindowUtil.newModalId(this)) {
                        @Override
                        public void onSalvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                            updateNomeConjuntoDiagnostico(target);
                        }
                    });
                }
                dlgCadastroTesteRapidoConjunto.show(target);
            }
        });
        try {
            controlarBtnCadTesteRapidoConjunto();
        } catch (DAOException | ValidacaoException e) {
            Loggable.log.error(e);
        }
        formTipoTesteRapido.add(tblTipoTesteRapido = new Table("tblTipoTesteRapido", getColumns(), getCollectionProvider()));
        tblTipoTesteRapido.populate();

        formTipoTesteRapido.add(containerSeven);

    }

    private void controlarBtnCadTesteRapidoConjunto() throws DAOException, ValidacaoException {
        String caminhoPaginaTesteRapidoConjunto = buscarPrograma();
        boolean permissaoPagina = new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().getUsuario(), caminhoPaginaTesteRapidoConjunto);

        btnCadTesteRapidoConjunto.setEnabled(permissaoPagina);
    }

    private String buscarPrograma() {
        final Long idPaginaTesteRapidoConjunto = 1180L;
        ProgramaWeb programaWeb = LoadManager.getInstance(ProgramaWeb.class)
                .setId(idPaginaTesteRapidoConjunto)
                .addProperties(new HQLProperties(ProgramaWeb.class).getProperties())
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CAMINHO_PAGINA))
                .start().getVO();
        return programaWeb.getProgramaPaginaPrincipal().getCaminhoPagina();
    }

    private void createContainerTuberculos(TesteRapido proxy) {
        containerTuberculose.setVisible(TIPO_TESTE_TUBERCULOSE.equals(tipoTeste));
        containerTuberculose.add(new CheckBoxLongValue(path(proxy.getTbPulmonarExtrapulmonar())).setEnabled(!readOnly));
        containerTuberculose.add(new InputField<String>(path(proxy.getCd4())).setEnabled(!readOnly));
    }

    private void initContainers() {
        containerAmostra = new WebMarkupContainer("containerAmostra");
        containerTuberculose = new WebMarkupContainer("containerTuberculose");
        containerPrimeirosSintomas = new WebMarkupContainer("containerPrimeirosSintomas");
        containerOne = new WebMarkupContainer("containerOne");
        containerTwo = new WebMarkupContainer("containerTwo");
        containerThree = new WebMarkupContainer("containerThree");
        containerFour = new WebMarkupContainer("containerFour");
        containerFive = new WebMarkupContainer("containerFive");
        containerSix = new WebMarkupContainer("containerSix");
        containerSeven = new WebMarkupContainer("containerSeven");
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        initContainers();
        carregarUsuarioCadsusEsus();
        TesteRapido proxy = on(TesteRapido.class);

        createContainers(proxy);

        getForm().add(containerOne);
        getForm().add(containerTwo);
        getForm().add(containerThree);
        getForm().add(containerFour);
        getForm().add(containerFive);
        getForm().add(containerSix);
        getForm().add(containerTuberculose);
        getForm().add(formTipoTesteRapido);

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                TesteRapidoPanel testeRapidoPanel = new TesteRapidoPanel(getProntuarioController().panelId());
                getProntuarioController().changePanel(target, testeRapidoPanel);
            }
        }.setDefaultFormProcessing(false));

        getForm().add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        }.setVisible(!readOnly));

        add(getForm());
    }

    private void adicionarTipoDeTeste(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        TipoTesteRapido tipoTesteRapido = (TipoTesteRapido) dropDownTipoTeste.getComponentValue();
        adicionaTipoExame(tipoTesteRapido);
        tblTipoTesteRapido.update(target);
        dropDownTipoTeste.limpar(target);
        dropDownNomeConjuntoDiagnostico.limpar(target);
        txtFabricante.limpar(target);
        txtMetodo.limpar(target);
        dateChooserdataPrimeirosSintomas.limpar(target);
        dropDownTipoAmostra.limpar(target);
        tipoTesteRapidoAdicionar = null;
    }

    private void adicionaTipoExame(TipoTesteRapido tipoTesteRapido) throws ValidacaoException, DAOException {
        validarTesteRapidoAdicionado(tipoTesteRapido);

        boolean found = false;
        for (TesteRapidoDTO ttr : tipoTesteRapidoList) {
            if (ttr.getTipoTesteRapido().getCodigo().equals(tipoTesteRapidoAdicionar.getCodigo())) {
                found = true;
                break;
            }
        }

        if (!found) {
            TesteRapidoDTO dto = new TesteRapidoDTO();
            dto.setTipoTesteRapido(tipoTesteRapidoAdicionar);
            dto.setTesteRapidoConjunto(testeRapidoConjunto);
            dto.setDataPrimeirosSintomas(dataPrimeirosSintomas);
            dto.setTipoAmostra(tipoAmostra);
            tipoTesteRapidoList.add(dto);
        } else {
            throw new ValidacaoException(bundle("tipoExameJaAdicionado"));
        }
    }

    private void validarTesteRapidoAdicionado(TipoTesteRapido tipoTesteRapido) throws ValidacaoException, DAOException {
        if (tipoTesteRapidoAdicionar == null) {
            throw new ValidacaoException(bundle("selecioneTipoExame"));
        } else if (isAmostraVisible(tipoTesteRapido) && tipoAmostra == null) {
            throw new ValidacaoException(bundle("selecioneTipoAmostra"));
        }

        ExameHelper.validarTesteRapido(tipoTesteRapidoAdicionar, getAtendimento());
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        TesteRapidoDTO proxy = on(TesteRapidoDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("descricao"), proxy.getTipoTesteRapido().getDescricao()));
        columns.add(createColumn(bundle("tipoTeste"), proxy.getTipoTesteRapido().getDescricaoTipoTeste()));
        columns.add(createColumn(bundle("nomeConjuntoDiagnostico"), proxy.getTesteRapidoConjunto().getNomeConjunto()));
        // containerPrimeirosSintomas.setVisible(TIPO_TESTE_COVID_19.equals(tipoTeste) || TIPO_TESTE_DENGUE.equals(tipoTeste) || TIPO_TESTE_INFLUENZA.equals(tipoTeste) || TIPO_TESTE_HANSENIASE.equals(tipoTeste));
        if (TIPO_TESTE_COVID_19.equals(tipoTeste) ) {
            columns.add(createColumn(bundle("dataPrimeirosSintomas"), proxy.getDataPrimeirosSintomas()));
        }

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TesteRapidoDTO>() {

            @Override
            public void customizeColumn(TesteRapidoDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<TesteRapidoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, TesteRapidoDTO modelObject) throws ValidacaoException, DAOException {
                        removerTipoTesteRapido(target, modelObject);
                    }
                });
            }

        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return tipoTesteRapidoList;
            }
        };
    }

    private void removerTipoTesteRapido(AjaxRequestTarget target, TesteRapidoDTO modelObject) {
        for (int i = 0; i < tipoTesteRapidoList.size(); i++) {
            if (tipoTesteRapidoList.get(i).getTipoTesteRapido().getCodigo().equals(modelObject.getTipoTesteRapido().getCodigo())) {
                tipoTesteRapidoList.remove(i);
                break;
            }
        }
        tblTipoTesteRapido.update(target);
    }

    private DropDown getDropDownNomeConjuntoDiagnostico() {
        if (dropDownNomeConjuntoDiagnostico == null) {
            if (this.getTesteRapidoConjunto() == null) {
                this.setTesteRapidoConjunto(new TesteRapidoConjunto());
            }

            dropDownNomeConjuntoDiagnostico = new DropDown("testeRapidoConjunto") {
                @Override
                public void limpar(AjaxRequestTarget target) {
                    super.limpar(target);
                    dropDownNomeConjuntoDiagnostico.removeAllChoices();
                    dropDownNomeConjuntoDiagnostico.addChoice(null, "Selecione um Tipo de Exame");
                    txtFabricante.limpar(target);
                    txtMetodo.limpar(target);
                    dateChooserdataPrimeirosSintomas.limpar(target);
                }
            };

            dropDownNomeConjuntoDiagnostico.addChoice(null, "Selecione um Tipo de Exame");

            dropDownNomeConjuntoDiagnostico.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget art) {
                    if (dropDownNomeConjuntoDiagnostico.getComponentValue() != null) {
                        txtFabricante.setComponentValue(testeRapidoConjunto.getFabricante());
                        txtMetodo.setComponentValue(testeRapidoConjunto.getMetodo());
                    } else {
                        txtFabricante.limpar(art);
                        txtMetodo.limpar(art);
                    }

                    art.add(txtFabricante);
                    art.add(txtMetodo);
                    art.add(formTipoTesteRapido);
                }
            });
        }

        return dropDownNomeConjuntoDiagnostico;
    }

    private DropDown getDropDownTipoTeste() {
        if (dropDownTipoTeste == null) {
            dropDownTipoTeste = new DropDown("tipoTesteRapidoAdicionar");

            dropDownTipoTeste.addChoice(null, bundle("selecione"));
            List<TipoTesteRapido> lst = LoadManager.getInstance(TipoTesteRapido.class)
                    .addProperties(new HQLProperties(TipoTesteRapido.class).getProperties())
                    .addProperties(new HQLProperties(Procedimento.class, TipoTesteRapido.PROP_PROCEDIMENTO).getProperties())
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoTesteRapido.PROP_CODIGO))
                    .start().getList();

            for (TipoTesteRapido ttr : lst) {
                if (TIPO_TESTE_COVID_19.equals(tipoTeste)) {
                    if (TipoTesteRapido.TipoTeste.COVID_19.value().equals(ttr.getTipoTeste())) {
                        dropDownTipoTeste.addChoice(ttr, ttr.getDescricao());
                    }
                } else if (TIPO_TESTE_AIDS_AVANCADO.equals(tipoTeste)) {
                    if (TipoTesteRapido.TipoTeste.AIDS_AVANCADO.value().equals(ttr.getTipoTeste())) {
                        dropDownTipoTeste.addChoice(ttr, ttr.getDescricao());
                    }
                } else {
                    dropDownTipoTeste.addChoice(ttr, ttr.getDescricao());
                }
            }

            dropDownTipoTeste.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget art) {
                    updateNomeConjuntoDiagnostico(art);
                    TipoTesteRapido tipoTesteRapido = (TipoTesteRapido) dropDownTipoTeste.getComponentValue();
                    containerAmostra.setVisible(isAmostraVisible(tipoTesteRapido));
                    art.add(formTipoTesteRapido);
                }
            });
        }
        return dropDownTipoTeste;
    }

    private WebMarkupContainer getDropDownTipoAmostra() {
        if (dropDownTipoAmostra == null) {
            dropDownTipoAmostra = DropDownUtil.getIEnumDropDown("tipoAmostra", TesteRapidoRealizado.TipoAmostra.values(), true, "Selecione");
            containerAmostra.setVisible(false);
            containerAmostra.add(dropDownTipoAmostra);
        }
        return containerAmostra;
    }

    private void updateNomeConjuntoDiagnostico(AjaxRequestTarget target) {
        if (dropDownTipoTeste.getComponentValue() != null) {
            List<TesteRapidoConjunto> testesRapidoConjunto = LoadManager.getInstance(TesteRapidoConjunto.class)
                    .addProperties(new HQLProperties(TesteRapidoConjunto.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TesteRapidoConjunto.PROP_TESTE_RAPIDO_TIPO, TipoTesteRapido.PROP_CODIGO),
                                    ((TipoTesteRapido) dropDownTipoTeste.getComponentValue()).getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(
                            new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(TesteRapidoConjunto.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, TesteRapidoConjunto.STATUS_INATIVO))),
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(TesteRapidoConjunto.PROP_STATUS, BuilderQueryCustom.QueryParameter.IS_NULL))))))
                    .addSorter(new QueryCustom.QueryCustomSorter(TesteRapidoConjunto.PROP_CODIGO))
                    .start().getList();

            dropDownNomeConjuntoDiagnostico.limpar(target);
            for (TesteRapidoConjunto testeRapidoConjuntoDiag : testesRapidoConjunto) {
                dropDownNomeConjuntoDiagnostico.addChoice(null, bundle("selecione"));
                dropDownNomeConjuntoDiagnostico.addChoice(testeRapidoConjuntoDiag, testeRapidoConjuntoDiag.getNomeConjunto());
            }
            if (testesRapidoConjunto.size() == 1) {
                dropDownNomeConjuntoDiagnostico.setComponentValue(testesRapidoConjunto.get(0));
            }

            target.add(dropDownTipoTeste, dropDownNomeConjuntoDiagnostico);
        }
    }

    private boolean isAmostraVisible(TipoTesteRapido tipoTesteRapidoValue) {
        Long tipoTesteRapido = null;
        List<Long> tiposTesteComAmostra = new ArrayList<>();
        tiposTesteComAmostra.add((Long) TipoTesteRapido.TipoTeste.HIV.value());
        tiposTesteComAmostra.add((Long) TipoTesteRapido.TipoTeste.HEPATITE_B.value());
        tiposTesteComAmostra.add((Long) TipoTesteRapido.TipoTeste.HEPATITE_C.value());
        tiposTesteComAmostra.add((Long) TipoTesteRapido.TipoTeste.SIFILIS.value());
        tiposTesteComAmostra.add((Long) TipoTesteRapido.TipoTeste.TB_LAM.value());
        tiposTesteComAmostra.add((Long) TipoTesteRapido.TipoTeste.AIDS_AVANCADO.value());
        if (tipoTesteRapidoValue != null) {
            tipoTesteRapido = tipoTesteRapidoValue.getTipoTeste();
        }

        return tiposTesteComAmostra.contains(tipoTesteRapido);
    }

    private Form<TesteRapido> getForm() {
        if (this.form == null) {
            TesteRapido testeRapido;
            if (this.testeRapidoRealizado == null) {
                testeRapido = new TesteRapido();
                if (usuarioCadsusEsus != null) {
                    testeRapido.setGestante(usuarioCadsusEsus.getEstaGestante());
                    //testeRapido.setOrientacaoSexual(usuarioCadsusEsus.getOrientacaoSexual());
                }
            } else {
                testeRapido = this.testeRapidoRealizado.getTesteRapido();
            }

            this.form = new Form<TesteRapido>("form", new CompoundPropertyModel<TesteRapido>(testeRapido));
        }

        return this.form;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        List<TesteRapidoRealizado> testeRapidoRealizadoSalvoList = saveTestesRapidosRealizados(getForm().getModel().getObject());
        initDialogImpressao(target);
        dlgConfirmacaoImpressao.show(target, testeRapidoRealizadoSalvoList);
    }

    private List<TesteRapidoRealizado> saveTestesRapidosRealizados(TesteRapido testeRapidoSalvar) throws ValidacaoException, DAOException {
        if (CollectionUtils.isEmpty(tipoTesteRapidoList)) {
            throw new ValidacaoException(bundle("selecionePeloMenosUmTipoExame"));
        }

        if (testeRapidoSalvar.getCodigo() == null) {
            testeRapidoSalvar.setAtendimento(getAtendimento());
        }

        if (TIPO_TESTE_AIDS_AVANCADO.equals(tipoTeste)) {
            testeRapidoSalvar.setFlagAidsAvancado(RepositoryComponentDefault.SIM_LONG);
        }

        List<TesteRapidoRealizado> testeRapidoRealizadoList = new ArrayList<>();

        for (TesteRapidoDTO ttr : tipoTesteRapidoList) {
            TesteRapidoRealizado testeRapidoRealizadoSalvar = new TesteRapidoRealizado();
            testeRapidoRealizadoSalvar.setTesteRapido(testeRapidoSalvar);
            testeRapidoRealizadoSalvar.setTipoTesteRapido(ttr.getTipoTesteRapido());
            testeRapidoRealizadoSalvar.setStatus(TesteRapidoRealizado.Status.PENDENTE.value());
            testeRapidoRealizadoSalvar.setTesteRapidoConjunto(ttr.getTesteRapidoConjunto());
            testeRapidoRealizadoSalvar.setDataPrimeirosSintomas(ttr.getDataPrimeirosSintomas());
            testeRapidoRealizadoSalvar.setTipoAmostra(ttr.getTipoAmostra());
            if (RepositoryComponentDefault.NAO.equals(testeRapidoRealizadoSalvar.getTipoTesteRapido().getProcedimento().getFlagFaturavel())) {
                Procedimento procedimentoFaturavel = getProcedimentoFaturavel(testeRapidoRealizadoSalvar.getTipoTesteRapido().getProcedimento());
                if (procedimentoFaturavel != null) {
                    testeRapidoRealizadoSalvar.setProcedimento(procedimentoFaturavel);
                } else {
                    throw new ValidacaoException(bundle("msgProcedimentoNaoConfigurado"));
                }
            } else {
                testeRapidoRealizadoSalvar.setProcedimento(testeRapidoRealizadoSalvar.getTipoTesteRapido().getProcedimento());
            }

            testeRapidoRealizadoList.add(testeRapidoRealizadoSalvar);
        }

        return BOFactoryWicket.getBO(AtendimentoFacade.class).salvarTesteRapido(usuarioCadsusEsus, testeRapidoRealizadoList);
    }

    private Procedimento getProcedimentoFaturavel(Procedimento procedimento) {
        ProcedimentoTipoTabela procedimentoTipoTabela = LoadManager.getInstance(ProcedimentoTipoTabela.class)
                .addProperties(new HQLProperties(Procedimento.class, ProcedimentoTipoTabela.PROP_PROCEDIMENTO_FATURAVEL).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ProcedimentoTipoTabela.PROP_PROCEDIMENTO, procedimento))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoTipoTabela.PROP_PROCEDIMENTO_FATURAVEL, Procedimento.PROP_TIPO_TABELA_PROCEDIMENTO), getAtendimento().getConvenio().getTipoTabelaProcedimento()))
                .start().getVO();

        if (procedimentoTipoTabela != null && procedimentoTipoTabela.getProcedimentoFaturavel() != null) {
            return procedimentoTipoTabela.getProcedimentoFaturavel();
        }

        return null;
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<List<TesteRapidoRealizado>>(getProntuarioController().newWindowId(), bundle("exameCadastradoComSucessoDesejaImprimir", this)) {
                @Override
                public DataReport getDataReport(List<TesteRapidoRealizado> testeRapidoRealizado) throws ReportException {
                    ImpressaoTesteRapidoDTOParam param = new ImpressaoTesteRapidoDTOParam();
                    param.setCodigoTesteRapidoRealizados(Lambda.extract(testeRapidoRealizado, on(TesteRapidoRealizado.class).getCodigo()));
                    param.setImprimeSomenteFicha(true);

                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoTesteRapido(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, List<TesteRapidoRealizado> testeRapidoRealizado) throws ValidacaoException, DAOException {
                    getProntuarioController().changePanel(target, new TesteRapidoPanel(getProntuarioController().panelId()));
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }

    private void disabledAbordagemConsentida(AjaxRequestTarget target) {
        if (dropDownPermiteContato.getComponentValue() == null || RepositoryComponentDefault.SIM_LONG.equals(dropDownPermiteContato.getComponentValue())) {
            dropDownTipoContato.setEnabled(true);
        } else {
            dropDownTipoContato.setEnabled(false);
            dropDownApenasComProprio.setEnabled(false);
            txtTipoContatoOutro.setEnabled(false);
            txtFalarCom.setEnabled(false);
        }

        if (target != null) {
            dropDownTipoContato.limpar(target);
            dropDownApenasComProprio.limpar(target);
            txtTipoContatoOutro.limpar(target);
            txtFalarCom.limpar(target);
        }
    }

    private void disabledTipoContato(AjaxRequestTarget target) {
        if (TesteRapido.TipoContato.OUTRO.value().equals(dropDownTipoContato.getComponentValue())) {
            txtTipoContatoOutro.setEnabled(true);
            dropDownApenasComProprio.setEnabled(false);
        } else if (TesteRapido.TipoContato.TELEFONE.value().equals(dropDownTipoContato.getComponentValue())) {
            dropDownApenasComProprio.setEnabled(true);
            txtTipoContatoOutro.setEnabled(false);
        } else {
            txtTipoContatoOutro.setEnabled(false);
            txtFalarCom.setEnabled(false);
            dropDownApenasComProprio.setEnabled(false);
        }

        if (target != null) {
            txtTipoContatoOutro.limpar(target);
            txtFalarCom.limpar(target);
            dropDownApenasComProprio.limpar(target);
        }
    }

    private void disabledApenasComProprio(AjaxRequestTarget target) {
        txtFalarCom.setEnabled(dropDownApenasComProprio.getComponentValue() != null && !RepositoryComponentDefault.SIM_LONG.equals(dropDownApenasComProprio.getComponentValue()));

        if (target != null) {
            txtFalarCom.limpar(target);
        }
    }

    private void disabledSabendoServico(AjaxRequestTarget target) {
        txtComoFicouSabendoOutro.setEnabled(TesteRapido.SabendoServico.OUTRO.value().equals(dropDownComoFicouSabendoServico.getComponentValue()));

        if (target != null) {
            txtComoFicouSabendoOutro.limpar(target);
        }
    }

    private void carregarUsuarioCadsusEsus() {
        UsuarioCadsusEsus proxy = on(UsuarioCadsusEsus.class);

        usuarioCadsusEsus = LoadManager.getInstance(UsuarioCadsusEsus.class)
                .addProperties(new HQLProperties(UsuarioCadsusEsus.class).getProperties())
                .addProperty(VOUtils.montarPath(UsuarioCadsusEsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(UsuarioCadsusEsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(UsuarioCadsusEsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO))
                .addProperty(VOUtils.montarPath(UsuarioCadsusEsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CELULAR))
                .addProperty(VOUtils.montarPath(UsuarioCadsusEsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_TELEFONE))
                .addProperty(VOUtils.montarPath(UsuarioCadsusEsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_EMAIL))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus()), getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getSexo()), getAtendimento().getUsuarioCadsus().getSexo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getTelefone()), getAtendimento().getUsuarioCadsus().getTelefone()))
                .start().getVO();
    }

    protected List<TesteRapidoDTO> carregarTipoTesteCadastrados() {
        List<TesteRapidoDTO> listaTiposTesteRapido = new ArrayList<>();
        if (testeRapidoRealizado != null && testeRapidoRealizado.getCodigo() != null) {
            List<TesteRapidoRealizado> list = getTestesRapidosRealizados();

            for (TesteRapidoRealizado trr : list) {
                TesteRapidoDTO dto = new TesteRapidoDTO();
                dto.setTipoTesteRapido(trr.getTipoTesteRapido());
                dto.setTesteRapidoConjunto(trr.getTesteRapidoConjunto());
                dto.setTipoTesteRapido(trr.getTipoTesteRapido());
                dto.setDataPrimeirosSintomas(trr.getDataPrimeirosSintomas());
                dto.setTipoAmostra(trr.getTipoAmostra());
                listaTiposTesteRapido.add(dto);
            }
        }
        return listaTiposTesteRapido.isEmpty() ? tipoTesteRapidoList : listaTiposTesteRapido;
    }

    private List<TesteRapidoRealizado> getTestesRapidosRealizados() {
        TesteRapidoRealizado proxy = Lambda.on(TesteRapidoRealizado.class);
        return LoadManager.getInstance(TesteRapidoRealizado.class)
                        .addProperties(new HQLProperties(TesteRapidoRealizado.class).getProperties())
                        .addProperties(new HQLProperties(TipoTesteRapido.class, path(proxy.getTipoTesteRapido())).getProperties())
                        .addProperty(path(proxy.getTipoAmostra()))
                        .addProperties(new HQLProperties(TesteRapido.class, path(proxy.getTesteRapido())).getProperties())
                        .addProperties(new HQLProperties(TesteRapidoConjunto.class, path(proxy.getTesteRapidoConjunto())).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTesteRapido()), testeRapidoRealizado.getTesteRapido()))
                        .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                        .start().getList();
    }

    public TesteRapidoConjunto getTesteRapidoConjunto() {
        return testeRapidoConjunto;
    }

    public void setTesteRapidoConjunto(TesteRapidoConjunto testeRapidoConjunto) {
        this.testeRapidoConjunto = testeRapidoConjunto;
    }
}
