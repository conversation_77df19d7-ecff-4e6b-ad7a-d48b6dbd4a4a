package br.com.celk.view.atendimento.prontuario.panel.exame.view;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.agendamento.ValidarFilaEsperaNoAtendimento;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoObject;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dialog.DlgJustificativaObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.laboratorio.exames.dto.ProcedimentoHelper;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.AtendimentoUtil;
import br.com.celk.view.atendimento.prontuario.nodes.SolicitacaoExamesNode;
import br.com.celk.view.atendimento.prontuario.panel.MarcacaoAgendamentoExameBpaiPanel;
import br.com.celk.view.atendimento.prontuario.panel.RegistroEspecializadoPanel;
import br.com.celk.view.atendimento.prontuario.panel.SoapPanel;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoExamesPanel;
import br.com.celk.view.atendimento.prontuario.panel.encaminhamento.dialog.DlgUnidadeResponsavelSolicitacaoAgendamento;
import br.com.celk.view.atendimento.prontuario.panel.exame.AutorizacaoExameSusPanel;
import br.com.celk.view.atendimento.prontuario.panel.exame.dialog.DlgConcluirExameRequisicao;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.ksisolucoes.agendamento.exame.ExameHelper;
import br.com.ksisolucoes.agendamento.exame.dto.*;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.exame.interfaces.dto.SolicitacaoExameDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.HQLConvertKeyToProperties;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.geral.interfaces.dto.LoadInterceptorNotExistsExameExameBpaApac;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.solicitacaoagendamento.ValidacaoDadosPacienteAtendimento.validaDadosPaciente;
import static br.com.celk.system.javascript.JScript.*;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class ExameBpaiViewPanel extends ProntuarioCadastroPanel {

    public static final long VALOR_GENERICO_PARA_CONSTRUTOR = 0L;
    public static final int TAMANHO_LIMITE = 512;
    private Form<ExameBpaiCadastroAprovacaoDTO> form;
    private Form<ExameProcedimentoDTO> formExameProcedimento;
    private AutoCompleteConsultaExameProcedimento autoCompleteConsultaExameProcedimento;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private Table tblExamesProcedimento;
    private DropDown<String> cbxUrgente;
    private InputArea<String> txaMotivo;
    private LongField txtQuantidade;
    private WebMarkupContainer containerUrgente;
    private DlgImpressaoObject<Long> dlgConfirmacaoImpressao;
    private Empresa empresa;
    private WebMarkupContainer containerJustificativa;
    private WebMarkupContainer containerResumoAnamnesee;
    private Profissional profissional;
    private DlgJustificativaObject dlgJustificativaObject;
    private DlgConfirmacaoObject dlgConfirmacaoObject;
    private final TipoExame tipoExame;
    private AutoCompleteConsultaCid autoCompleteConsultaCidPrinc;

    private WebMarkupContainer containerSolicitarPrioridade;
    private WebMarkupContainer containerDadosRegulacao;
    private DropDown<Long> dropDownPrioridade;
    private String tipoControleRegulacao;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissionalDesejado;
    private InputField txtNomeProfissionalDesejado;

    private DropDown dropDownEnviarRegulacao;
    private InputArea txaDescricaoEnviarRegulacao;
    private InputArea txaResumoAnamnese;
    private InputArea txaJustificativa;
    private WebMarkupContainer containerSolicitacaoProfissional;
    private WebMarkupContainer containerNaoColocarListaEsperaSus;
    private CheckBoxLongValue checkNaoColocarListaEsperaSus;

    private CheckBoxLongValue checkDom;
    private CheckBoxLongValue checkSeg;
    private CheckBoxLongValue checkTer;
    private CheckBoxLongValue checkQua;
    private CheckBoxLongValue checkQui;
    private CheckBoxLongValue checkSex;
    private CheckBoxLongValue checkSab;
    private InputField InputMelhorHora;
    private CheckBoxLongValue checkTodos;
    private Form containerObservacaoUrgencia;

    private SoapDTO.ContainerTelaSoap containerTelaSoap;
    private NodoAtendimentoWeb nodoAtendimentoWeb;
    private DlgUnidadeResponsavelSolicitacaoAgendamento dlgUnidadeResponsavelSolicitacaoAgendamento;
    private final boolean edicaoCadastro;
    private List<ExameProcedimentoDTO> examesEmAberto;
    private Boolean obrigatorioCidRequisicaoExame;

    private Deque<Long> codigosExames;

    public ExameBpaiViewPanel(String id, TipoExame tipoExame, boolean edicaoCadastro) {
        super(id, bundle("laudoBpaI"));
        this.tipoExame = tipoExame;
        this.edicaoCadastro = edicaoCadastro;
    }

    public ExameBpaiViewPanel(String id, TipoExame tipoExame, SoapDTO.ContainerTelaSoap containerTelaSoap, boolean edicaoCadastro) {
        super(id, bundle("laudoBpaI"));
        this.tipoExame = tipoExame;
        this.containerTelaSoap = containerTelaSoap;
        this.edicaoCadastro = edicaoCadastro;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        ExameBpaiCadastroAprovacaoDTO dto = getForm().getModelObject();
        if (dto != null && dto.getExameBpai() != null && dto.getExameBpai().getExame() != null) {
            profissional = dto.getExameBpai().getExame().getProfissional();
        }
        empresa = getAtendimento().getEmpresa();
        try {
            examesEmAberto = getExamesEmAberto();
        } catch (ValidacaoException | DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        formExameProcedimento = new Form("formExameProcedimento", new CompoundPropertyModel<ExameProcedimentoDTO>(new ExameProcedimentoDTO()));
        ExameProcedimentoDTO exameProcedimentoDTOProxy = on(ExameProcedimentoDTO.class);

        formExameProcedimento.add(autoCompleteConsultaProfissional =
                (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional
                        ("profissional",
                             new PropertyModel<Profissional>(this, "profissional"), true)
                .setPeriodoEmpresa(true)
                .setCodigoEmpresa(empresa.getCodigo())
                .setLabel(new Model(bundle("profissional"))));
        if (carregarPermissaoNoInformarProfissional() != null) {
            autoCompleteConsultaProfissional.setGrupoAtendimentoCbo(nodoAtendimentoWeb.getGrupoAtendimentoCbo());
        } else {
            profissional = getAtendimento().getProfissional();
            autoCompleteConsultaProfissional.setEnabled(false);
        }

        autoCompleteConsultaProfissional.getModel().setObject(profissional);

        formExameProcedimento.add(autoCompleteConsultaExameProcedimento = new AutoCompleteConsultaExameProcedimento(path(exameProcedimentoDTOProxy.getExameProcedimento())));
        autoCompleteConsultaExameProcedimento.setTipoExame(tipoExame);
        autoCompleteConsultaExameProcedimento.setSexo(getAtendimento().getUsuarioCadsus().getSexo());
        autoCompleteConsultaExameProcedimento.setIdadeEmMese(getAtendimento().getUsuarioCadsus().getIdadeEmMeses());
        formExameProcedimento.add(txtQuantidade = new LongField(path(exameProcedimentoDTOProxy.getQuantidade())));
        if (txtQuantidade.getComponentValue() == null || txtQuantidade.getComponentValue() == 0L) {
            txtQuantidade.setComponentValue(1L);
        }
        formExameProcedimento.add(new AbstractAjaxButton("btnAdicionarProcedimento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarRegra(target, formExameProcedimento.getModelObject());
                autoCompleteConsultaExameProcedimento.focus(target);
            }
        });

        formExameProcedimento.add(tblExamesProcedimento = new Table("tblExamesProcedimento", getColumns(), getCollectionProvider()));
        tblExamesProcedimento.populate();

        getForm().add(formExameProcedimento);

        ExameBpaiCadastroAprovacaoDTO exameBpaiCadastroAprovacaoDTOProxy = on(ExameBpaiCadastroAprovacaoDTO.class);
        getForm().add(new InputField(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getDescricaoDiagnostico())));
        getForm().add(autoCompleteConsultaCidPrinc = new AutoCompleteConsultaCid(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getCidPrincipal())));
        autoCompleteConsultaCidPrinc.getTxtDescricao().setLabel(new Model("cidPrincipal"));

        if (AtendimentoUtil.isCidObrigatorioRequisicaoExame(obrigatorioCidRequisicaoExame, getAtendimento())) {
            autoCompleteConsultaCidPrinc.setRequired(true);
            autoCompleteConsultaCidPrinc.getTxtDescricao().setRequired(true);
            autoCompleteConsultaCidPrinc.getTxtDescricao().addRequiredClass();
        }

        getForm().add(new AutoCompleteConsultaCid(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getCidSecundario())));
        getForm().add(new AutoCompleteConsultaCid(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getCidCausa())));
        getForm().add(containerResumoAnamnesee = new WebMarkupContainer("containerResumoAnamnesee"));
        containerResumoAnamnesee.add(txaResumoAnamnese = new InputArea(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getResumoAnamnese())));
        if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
            txaResumoAnamnese.addRequiredClass();
        }
        getForm().add(containerJustificativa = new WebMarkupContainer("containerJustificativa"));
        containerJustificativa.add(txaJustificativa = new InputArea(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getJustificativaProcedimento())));

        getForm().add(containerUrgente = new WebMarkupContainer("containerUrgente"));
        containerUrgente.setOutputMarkupId(true);
        containerUrgente.add(cbxUrgente = DropDownUtil.getNaoSimDropDown(path(exameBpaiCadastroAprovacaoDTOProxy.getExameCadastroAprovacaoDTO().getUrgente())));
        containerUrgente.add(txaMotivo = new InputArea<String>(path(exameBpaiCadastroAprovacaoDTOProxy.getExameCadastroAprovacaoDTO().getMotivo())));

        cbxUrgente.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                JScript.toggleFieldset(target, containerUrgente);
                if (RepositoryComponentDefault.NAO.equals(cbxUrgente.getComponentValue())) {
                    txaMotivo.limpar(target);
                }
            }
        });

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
            }

            @Override
            public boolean isVisible() {
                return SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO.equals(containerTelaSoap);
            }
        }.setDefaultFormProcessing(false));

        AbstractAjaxButton btnAvancar;
        getForm().add(btnAvancar = new AbstractAjaxButton("btnAvancar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarLaudo();
                DefaultProntuarioPanel panel;
                if (carregarPermissaoNoInformarProfissional() != null) {
                    panel = new AutorizacaoExameSusPanel(getProntuarioController().panelId(), tipoExame, (Profissional) autoCompleteConsultaProfissional.getComponentValue(), containerTelaSoap);
                } else {
                    panel = new AutorizacaoExameSusPanel(getProntuarioController().panelId(), tipoExame, containerTelaSoap);
                }
                getProntuarioController().changePanel(target, panel);
            }
        });

        getForm().add(containerSolicitacaoProfissional = new WebMarkupContainer("containerSolicitacaoProfissional"));
        containerSolicitacaoProfissional.setOutputMarkupPlaceholderTag(true);
        containerSolicitacaoProfissional.add(dropDownEnviarRegulacao = getDropDownEnviarRegulacao(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getFlagEnviarRegulacao())));
        containerSolicitacaoProfissional.add(txaDescricaoEnviarRegulacao = new InputArea(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getDescricaoEnviarRegulacao())));

        dropDownEnviarRegulacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean enable = getForm().getModel().getObject().getExameBpai().getFlagEnviarRegulacao().equals(RepositoryComponentDefault.SIM_LONG);
                if (enable) {
                    target.add(txaDescricaoEnviarRegulacao);
                } else {
                    txaDescricaoEnviarRegulacao.limpar(target);
                }
                toggleFieldset(target, containerSolicitacaoProfissional);
            }
        });

        getForm().add(containerSolicitarPrioridade = new WebMarkupContainer("containerSolicitarPrioridade"));
        containerSolicitarPrioridade.setOutputMarkupId(true);
        containerSolicitarPrioridade.setOutputMarkupPlaceholderTag(true);
        containerSolicitarPrioridade.add(dropDownPrioridade = DropDownUtil.getNaoSimLongDropDown(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getSolicitarPrioridade())));
        dropDownPrioridade.addAjaxUpdateValue();
        dropDownPrioridade.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModelObject().getExameBpai().getSolicitarPrioridade())) {
                    target.appendJavaScript(JScript.showFieldset(containerObservacaoUrgencia));
                } else {
                    ComponentUtils.limparContainer(containerObservacaoUrgencia, target);
                    target.appendJavaScript(JScript.hideFieldset(containerObservacaoUrgencia));
                }
            }
        });

        getForm().add(containerDadosRegulacao = new WebMarkupContainer("containerDadosRegulacao"));
        containerDadosRegulacao.add(autoCompleteConsultaProfissionalDesejado = new AutoCompleteConsultaProfissional(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getProfissionalDesejado())));
        containerDadosRegulacao.add(txtNomeProfissionalDesejado = new InputField(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getNomeProfissionalDesejado())));

        autoCompleteConsultaProfissionalDesejado.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                txtNomeProfissionalDesejado.setComponentValue(object.getNome());
                txtNomeProfissionalDesejado.setEnabled(false);
                target.add(txtNomeProfissionalDesejado);
            }
        });
        autoCompleteConsultaProfissionalDesejado.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                txtNomeProfissionalDesejado.limpar(target);
                txtNomeProfissionalDesejado.setEnabled(true);
                target.add(txtNomeProfissionalDesejado);
            }
        });

        containerDadosRegulacao.setOutputMarkupId(true);
        containerDadosRegulacao.setOutputMarkupPlaceholderTag(true);
        containerDadosRegulacao.add(checkSeg = new CheckBoxLongValue(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getMelhorDiaSeg())));
        containerDadosRegulacao.add(checkDom = new CheckBoxLongValue(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getMelhorDiaDom())));
        containerDadosRegulacao.add(checkTer = new CheckBoxLongValue(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getMelhorDiaTer())));
        containerDadosRegulacao.add(checkQua = new CheckBoxLongValue(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getMelhorDiaQua())));
        containerDadosRegulacao.add(checkQui = new CheckBoxLongValue(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getMelhorDiaQui())));
        containerDadosRegulacao.add(checkSex = new CheckBoxLongValue(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getMelhorDiaSex())));
        containerDadosRegulacao.add(checkSab = new CheckBoxLongValue(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getMelhorDiaSab())));
        containerDadosRegulacao.add(checkTodos = new CheckBoxLongValue(path(exameBpaiCadastroAprovacaoDTOProxy.getCheckTodos())));
        containerDadosRegulacao.add(InputMelhorHora = new InputField(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getMelhorHora())));
        checkTodos.addAjaxUpdateValue();
        checkTodos.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                Long value = checkTodos.getComponentValue();

                checkDom.setComponentValue(value);
                checkSeg.setComponentValue(value);
                checkTer.setComponentValue(value);
                checkQua.setComponentValue(value);
                checkQui.setComponentValue(value);
                checkSex.setComponentValue(value);
                checkSab.setComponentValue(value);

                target.add(checkDom);
                target.add(checkSeg);
                target.add(checkTer);
                target.add(checkQua);
                target.add(checkQui);
                target.add(checkSex);
                target.add(checkSab);
            }
        });

        containerUrgente.setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(getTipoControleRegulacao()));
        containerSolicitarPrioridade.setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao()));
        containerDadosRegulacao.setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao()));
        containerSolicitacaoProfissional.setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())
                && RepositoryComponentDefault.SIM.equals(tipoExame.getAgendado()));

        getForm().add(containerObservacaoUrgencia = new Form("containerObservacaoUrgencia"));
        containerObservacaoUrgencia.setOutputMarkupId(true);
        containerObservacaoUrgencia.add(new InputArea(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getObservacaoUrgente())).setLabel(Model.of(bundle("justificativaUrgencia"))));
        if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao())) {
            containerObservacaoUrgencia.add(new Label("labelObservacao", BundleManager.getString("justificativaPrioridade")));
        } else if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(getTipoControleRegulacao())) {
            containerObservacaoUrgencia.add(new Label("labelObservacao", BundleManager.getString("justificativaUrgencia")));
        } else if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
            containerObservacaoUrgencia.add(new Label("labelObservacao", ""));
        }

        AbstractAjaxButton btnSalvar;
        form.add(btnSalvar = new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {

                if ((txaResumoAnamnese.getValue() != null && txaResumoAnamnese.getValue().length() > TAMANHO_LIMITE)
                        || (txaJustificativa.getValue() != null && txaJustificativa.getValue().length() > TAMANHO_LIMITE)) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_execedeu_limite_anamnese_justificativaProcedimento"));
                }

                codigosExames = new ArrayDeque<>(salvarLaudo());
                initDialogImpressao(target);
                dlgConfirmacaoImpressao.show(target, VALOR_GENERICO_PARA_CONSTRUTOR);
            }

        });

        getForm().add(containerNaoColocarListaEsperaSus = new WebMarkupContainer("containerNaoColocarListaEsperaSus"));
        containerNaoColocarListaEsperaSus.setOutputMarkupId(true);
        containerNaoColocarListaEsperaSus.add(checkNaoColocarListaEsperaSus = new CheckBoxLongValue(path(exameBpaiCadastroAprovacaoDTOProxy.getExameBpai().getExame().getNaoColocarListaEsperaSus())));

        checkNaoColocarListaEsperaSus.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean edicao = form.getModel().getObject().getExameBpai() != null && form.getModel().getObject().getExameBpai().getExame() != null
                        && form.getModel().getObject().getExameBpai().getExame().getCodigo() != null
                        && form.getModel().getObject().getExameBpai().getExame().getNaoColocarListaEsperaSus() != null;
                regrasControleRegulacaoSolicitacaoProfissional(target, edicao, tipoExame.getTipoProcedimento());
            }
        });

        add(form);

        if (tipoExameAutorizacao()) {
            btnSalvar.setVisible(false);
        } else {
            btnAvancar.setVisible(false);
        }

        regrasControleRegulacaoSolicitacaoProfissional(null, getForm().getModel().getObject() != null
                && getForm().getModel().getObject().getExameBpai() != null && getForm().getModel().getObject().getExameBpai().getCodigo() != null, tipoExame.getTipoProcedimento());

    }

    private boolean tipoExameAutorizacao() {
        boolean autorizaPorTipoExameUnidade = LoadManager.getInstance(TipoExameUnidade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoExameUnidade.PROP_EMPRESA), getAtendimento().getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoExameUnidade.PROP_TIPO_EXAME), this.tipoExame))
                .exists();
        if (autorizaPorTipoExameUnidade) {
            return RepositoryComponentDefault.SIM_LONG.equals(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getFlagAutorizaExame());
        }
        return autorizaPorTipoExameUnidade;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ExameProcedimentoDTO proxy = on(ExameProcedimentoDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("procedimento", this), proxy.getExameProcedimento().getProcedimento().getCodigoFormatado()));
        columns.add(createColumn(bundle("exame", this), proxy.getExameProcedimento().getDescricaoVO()));
        columns.add(createColumn(bundle("tipoExame", this), proxy.getExameProcedimento().getTipoExame().getDescricao()));
        columns.add(createColumn(bundle("quantidade", this), proxy.getQuantidade()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ExameProcedimentoDTO>() {
            @Override
            public void customizeColumn(ExameProcedimentoDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ExameProcedimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ExameProcedimentoDTO modelObject) throws ValidacaoException, DAOException {
                        removerExameProcedimento(target, modelObject);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModelObject().getExameCadastroAprovacaoDTO().getExameProcedimentoDTOs();
            }
        };
    }

    private void validarFormExameProcedimento(ExameProcedimentoDTO exameDTO) throws ValidacaoException {
        if (profissional == null) {
            throw new ValidacaoException(bundle("informeProfissional", this));
        } else if (exameDTO.getExameProcedimento() == null) {
            throw new ValidacaoException(bundle("informeProcedimento", this));
        } else if (exameDTO.getQuantidade() == null || exameDTO.getQuantidade() <= 0) {
            throw new ValidacaoException(bundle("quantidadeInvalida", this));
        } else if (!getForm().getModelObject().getExameCadastroAprovacaoDTO().getExameProcedimentoDTOs().isEmpty()) {
            if (exameDTO.getExameProcedimento().getTipoExame() == null ||
                    !exameDTO.getExameProcedimento()
                            .getTipoExame().equals(
                                    getForm().getModelObject().getExameCadastroAprovacaoDTO()
                                            .getExameProcedimentoDTOs().get(0).getExameProcedimento().getTipoExame()
                            )) {
                throw new ValidacaoException(bundle("msg_somente_exames_mesmo_tipo_exame", this));
            } else if (exameDTO.getExameProcedimento().getProcedimento() == null) {
                throw new ValidacaoException(bundle("msg_exame_sem_procedimento_definido", this));
            }
        }
    }

    private boolean validarExameAdicionado(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        ExameProcedimentoDTO dto = formExameProcedimento.getModelObject();

        validarFormExameProcedimento(dto);

        Empresa empresa = ApplicationSession.get().getSessaoAplicacao().getEmpresa();
        TipoAtendimento tipoAtendimento = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento();
        ProcedimentoHelper.validaQuantidadeMaxima(dto, empresa, tipoAtendimento);

        dto.setValidarCotaExameUrgente(true);
        List<ExameProcedimentoDTO> exameProcedimentoDtos = getForm().getModelObject()
                .getExameCadastroAprovacaoDTO().getExameProcedimentoDTOs();

        List<ExameRequisicao> examesPendentes = getExamesPendentes();
        // Valida exames pendentes
        if (examesPendentes.size() > 0) {
            for (ExameRequisicao ep : examesPendentes) {
                if (ep.getExameProcedimento().getCodigo().equals(dto.getExameProcedimento().getCodigo())) {
                    if (validaExamePendente(ep, dto.getExameProcedimento())) {
                        viewDlgConcluirExameRequisicao(target, ep);
                        return false;
                    } else {
                        ep.setStatus(ExameRequisicao.Status.CONCLUIDO.value());
                        ep.setDataResultado(DataUtil.getDataAtual());
                        ep.setDescricaoResultado("Realizado");
                        salvarExameRequisicao(ep);
                    }
                }
            }
        }

        if (checarExisteMesmoExameEmAberto(dto)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_solicitacao_pendente_exame"));
        }

        if (checarExameJaAdicionado(exameProcedimentoDtos, dto)) {
            throw new ValidacaoException(bundle("procedimentoJaAdicionado", this));
        }

        return true;
    }

    private void adicionarExameProcedimento(AjaxRequestTarget target) {
        ExameProcedimentoDTO dto = formExameProcedimento.getModelObject();

        getForm().getModelObject().getExameCadastroAprovacaoDTO().getExameProcedimentoDTOs().add(dto);

        configurarObrigatoriedadeCid(target, dto);
        limparExameProcedimento(target);
        tblExamesProcedimento.update(target);
    }

    private void salvarExameRequisicao(ExameRequisicao modelObject) throws DAOException, ValidacaoException {
        ExameRequisicao proxy = Lambda.on(ExameRequisicao.class);
        LoadManager loadManager = LoadManager.getInstance(ExameRequisicao.class);
        loadManager.addProperties(new HQLProperties(ExameRequisicao.class).getProperties())
                .addProperties(new HQLProperties(ExameProcedimento.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO)).getProperties());
        addFilterStatusEqualsAbertoOrIsNull(proxy, loadManager);
        List<ExameRequisicao> list = loadManager.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getAtendimento().getCodigo()), QueryCustom.QueryCustomParameter.DIFERENTE, getAtendimento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getAtendimento().getUsuarioCadsus().getCodigo()), getAtendimento().getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExameProcedimento().getCodigo()), modelObject.getExameProcedimento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getTipoExame().getTipo()), QueryCustom.QueryCustomParameter.DIFERENTE, RepositoryComponentDefault.Tipo.LACEN.value()))
                .addInterceptor(new LoadInterceptorNotExistsExameExameBpaApac(LoadInterceptorNotExistsExameExameBpaApac.Tipo.EXAME_REQUISICAO))
                .start().getList();
        for (ExameRequisicao requisicao : list) {
            requisicao.setStatus(modelObject.getStatus());
            requisicao.setDataResultado(modelObject.getDataResultado());
            requisicao.setDescricaoResultado(modelObject.getDescricaoResultado());
            BOFactory.save(requisicao);
        }

    }

    private boolean checarExisteMesmoExameEmAberto(ExameProcedimentoDTO exameDTO) {
        for (ExameProcedimentoDTO sed : examesEmAberto) {
            if (sed.getExameProcedimento().getCodigo() != null && exameDTO.getExameProcedimento() != null) {
                if (sed.getExameProcedimento().getCodigo().equals(exameDTO.getExameProcedimento().getCodigo())) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checarExameJaAdicionado(List<ExameProcedimentoDTO> exameProcedimentoDtos, ExameProcedimentoDTO exameDTO) {
        for (ExameProcedimentoDTO exameProcedimentoDto : exameProcedimentoDtos) {
            if (exameProcedimentoDto.getExameProcedimento().equals(exameDTO.getExameProcedimento())) {
                return true;
            }
        }
        return false;
    }

    private void removerExameProcedimento(AjaxRequestTarget target, ExameProcedimentoDTO modelObject) {
        for (int i = 0; i < getForm().getModelObject().getExameCadastroAprovacaoDTO().getExameProcedimentoDTOs().size(); i++) {
            if (getForm().getModelObject().getExameCadastroAprovacaoDTO().getExameProcedimentoDTOs().get(i) == modelObject) {
                getForm().getModelObject().getExameCadastroAprovacaoDTO().getExameProcedimentoDTOs().remove(i);
                break;
            }
        }
        removerObrigatoriedadeCid(target);
        tblExamesProcedimento.update(target);
    }

    private void configurarObrigatoriedadeCid(AjaxRequestTarget target, ExameProcedimentoDTO modelObject) {
        ProcedimentoCompetencia procedimentoCompetencia = null;
        try {
            procedimentoCompetencia = LoadManager.getInstance(ProcedimentoCompetencia.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO), modelObject.getExameProcedimento().getProcedimento().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO)))
                    .start().getVO();

        } catch (ValidacaoException ex) {
            Logger.getLogger(ExameSusPanel.class.getName()).log(Level.SEVERE, null, ex);
        }
        boolean existsProcedCid = LoadManager.getInstance(ProcedimentoCid.class)
                .addParameter(new QueryCustom.QueryCustomParameter(new HQLConvertKeyToProperties(VOUtils.montarPath(ProcedimentoCid.PROP_ID, ProcedimentoCidPK.PROP_PROCEDIMENTO_COMPETENCIA), procedimentoCompetencia)))
                .exists();

        autoCompleteConsultaCidPrinc.limpar(target);
        if (existsProcedCid) {
            autoCompleteConsultaCidPrinc.setRequired(true);
            autoCompleteConsultaCidPrinc.getTxtDescricao().addRequiredClass();
            autoCompleteConsultaCidPrinc.setProcedimentoCompetencia(procedimentoCompetencia);
            modelObject.setCidObrigatorio(true);
        } else {
            modelObject.setCidObrigatorio(false);
        }
        target.add(autoCompleteConsultaCidPrinc);
    }

    private void removerObrigatoriedadeCid(AjaxRequestTarget target) {
        List<ExameProcedimentoDTO> exameProcedimentoDTOs = getForm().getModelObject().getExameCadastroAprovacaoDTO().getExameProcedimentoDTOs();
        List<ExameProcedimentoDTO> cidObrigatorio = Lambda.select(exameProcedimentoDTOs, Lambda.having(Lambda.on(ExameProcedimentoDTO.class).isCidObrigatorio(), Matchers.equalTo(true)));
        if (CollectionUtils.isAllEmpty(cidObrigatorio) || cidObrigatorio.size() > 0) {
            autoCompleteConsultaCidPrinc.setRequired(false);
            autoCompleteConsultaCidPrinc.setProcedimentoCompetencia(null);
            autoCompleteConsultaCidPrinc.getTxtDescricao().removeRequiredClass();
        }
        target.add(autoCompleteConsultaCidPrinc);
    }

    private void limparExameProcedimento(AjaxRequestTarget target) {
        formExameProcedimento.setModelObject(new ExameProcedimentoDTO());
        autoCompleteConsultaExameProcedimento.setEnabled(true);
        autoCompleteConsultaExameProcedimento.limpar(target);
        autoCompleteConsultaExameProcedimento.focus(target);
        txtQuantidade.setComponentValue(1L);
        target.add(txtQuantidade);
    }

    private List<Long> salvarLaudo() throws DAOException, ValidacaoException {
        ExameBpaiCadastroAprovacaoDTO dto = getForm().getModelObject();

        if (dto.getExameCadastroAprovacaoDTO().getDataSolicitacao() == null) {
            dto.getExameCadastroAprovacaoDTO().setDataSolicitacao(DataUtil.getDataAtual());
        }
        dto.getExameCadastroAprovacaoDTO().setCodigoProfissional(profissional.getCodigo());
        dto.getExameCadastroAprovacaoDTO().setAtendimento(getAtendimento());
        dto.getExameCadastroAprovacaoDTO().setTipoExame(tipoExame);
        dto.getExameCadastroAprovacaoDTO().setCodigoUnidade(empresa.getCodigo());
        dto.getExameCadastroAprovacaoDTO().setNomeProfissional(profissional.getNome());
        dto.getExameCadastroAprovacaoDTO().setCodigoPaciente(getAtendimento().getUsuarioCadsus().getCodigo());
        dto.getExameCadastroAprovacaoDTO().setNomePaciente(getAtendimento().getUsuarioCadsus().getNomeSocial());
        dto.getExameCadastroAprovacaoDTO().setTipoConvenio(TipoExame.CONVENIO_SUS);

        validaDadosPaciente(getAtendimento().getUsuarioCadsus());
        return BOFactoryWicket.getBO(ExameFacade.class).cadastrarSolicitacoesExameBpais(dto);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        if (RepositoryComponentDefault.SIM.equals(form.getModelObject().getExameCadastroAprovacaoDTO().getUrgente())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerUrgente)));
        }
        if (StringUtils.trimToNull(form.getModelObject().getExameBpai().getJustificativaProcedimento()) != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerJustificativa)));
        }
        if (StringUtils.trimToNull(form.getModelObject().getExameBpai().getResumoAnamnese()) != null
                || RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerResumoAnamnesee)));
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(form.getModelObject().getExameBpai().getFlagEnviarRegulacao())) {
            response.render(OnLoadHeaderItem.forScript(toggleFieldset(containerSolicitacaoProfissional)));
        }

        if (form.getModelObject().getExameBpai().getExame() != null && RepositoryComponentDefault.SIM_LONG.equals(form.getModelObject().getExameBpai().getExame().getNaoColocarListaEsperaSus())) {
            response.render(OnLoadHeaderItem.forScript(toggleFieldset(containerNaoColocarListaEsperaSus)));
        }
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaExameProcedimento.getTxtDescricao().getTextField())));
    }

    private DropDown<Long> getDropDownEnviarRegulacao(String id) {
        DropDown dropDown = new DropDown(id);
        dropDown.addChoice(RepositoryComponentDefault.NAO_LONG, WicketMethods.bundle("nao", this));
        dropDown.addChoice(RepositoryComponentDefault.SIM_LONG, WicketMethods.bundle("sim", this));

        return dropDown;
    }

    public void setDto(ExameBpaiCadastroAprovacaoDTO exameBpaiCadastroAprovacaoDTO) {
        getForm().setModelObject(exameBpaiCadastroAprovacaoDTO);
    }

    private Form<ExameBpaiCadastroAprovacaoDTO> getForm() {
        if (this.form == null) {
            this.form = new Form<>("form", new CompoundPropertyModel<>(new ExameBpaiCadastroAprovacaoDTO()));
            this.form.getModelObject().setExameBpai(new ExameBpai());
            this.form.getModelObject().setExameCadastroAprovacaoDTO(new ExameCadastroAprovacaoDTO());
            this.form.getModelObject().getExameCadastroAprovacaoDTO().setExameProcedimentoDTOs(new ArrayList<>());
        }
        return this.form;
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<Long>(getProntuarioController().newWindowId(), BundleManager.getString("desejaImprimirLaudo")) {
                @Override
                public DataReport getDataReport(Long object) throws ReportException {
                    Long codigoExame = codigosExames.pop();
                    codigosExames.addLast(codigoExame);
                    return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioImpressaoExameBpai(codigoExame);
                }

                @Override
                public void afterClose(AjaxRequestTarget target, Long object) throws ValidacaoException, DAOException {
                    if(codigosExames.size() == 1) {
                        onFecharImpressao(target, codigosExames.getFirst());
                    } else {
                        onFecharImpressao(target, null);
                    }
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }

    private void onFecharImpressao(AjaxRequestTarget target, Long codigoExame) throws DAOException, ValidacaoException {
        ExameBpai exameBpai = null;
        boolean realizarAgendamento = true;
        SolicitacaoAgendamento solicitacaoAgendamento = null;
        if (codigoExame != null) {
            ExameBpai proxyExame = on(ExameBpai.class);
            exameBpai = LoadManager.getInstance(ExameBpai.class)
                    .addProperty(path(proxyExame.getCodigo()))
                    .addProperty(path(proxyExame.getFlagEnviarRegulacao()))
                    .addProperty(path(proxyExame.getExame().getCodigo()))
                    .addProperty(path(proxyExame.getExame().getNaoColocarListaEsperaSus()))
                    .addProperty(path(proxyExame.getExame().getTipoExame().getTipoProcedimento().getCodigo()))
                    .addProperty(path(proxyExame.getExame().getTipoExame().getTipoProcedimento().getFlagAgendarNoAtendimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxyExame.getExame().getCodigo()), codigoExame))
                    .start().getVO();
        }

        if (exameBpai != null && exameBpai.getCodigo() != null) {
            IMTableSolicitacaoAgendamentoToExameBpai imTable = LoadManager.getInstance(IMTableSolicitacaoAgendamentoToExameBpai.class)
                    .addProperty(IMTableSolicitacaoAgendamentoToExameBpai.PROP_CODIGO)
                    .addProperties(new HQLProperties(SolicitacaoAgendamento.class, IMTableSolicitacaoAgendamentoToExameBpai.PROP_SOLICITACAO_AGENDAMENTO).getProperties())
                    .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(IMTableSolicitacaoAgendamentoToExameBpai.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS)).getProperties())
                    .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(IMTableSolicitacaoAgendamentoToMamografia.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_UNIDADE_ORIGEM)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(IMTableSolicitacaoAgendamentoToExameBpai.PROP_EXAME_BPAI, exameBpai))
                    .start().getVO();

            if (imTable != null && imTable.getCodigo() != null) {
                solicitacaoAgendamento = imTable.getSolicitacaoAgendamento();
            }

            TipoProcedimento tipoProcedimento = null;
            if (exameBpai.getExame() != null && exameBpai.getExame().getTipoExame() != null) {
                tipoProcedimento = exameBpai.getExame().getTipoExame().getTipoProcedimento();
            }

            if (RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(exameBpai.getFlagEnviarRegulacao(), RepositoryComponentDefault.NAO_LONG))
                    && realizarAgendamento && RepositoryComponentDefault.NAO_LONG.equals(exameBpai.getExame().getNaoColocarListaEsperaSus())
                    && AtendimentoHelper.isPermiteAgendarNoAtendimento(tipoProcedimento)) {
                AgendaGradeAtendimentoDTOParam param = new AgendaGradeAtendimentoDTOParam();
                param.setApenasAgendasComVagas(true);
                param.setEmpresaOrigem(getAtendimento().getEmpresa());
                param.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
                param.setTipoProcedimento(tipoProcedimento);
                param.setTipoAgendaList(Arrays.asList(TipoProcedimento.TipoAgenda.DIARIO, TipoProcedimento.TipoAgenda.HORARIO));
                param.setTipoAtendimentoAgendaList(Arrays.asList(TipoAtendimentoAgenda.TIPO_CONSULTA, TipoAtendimentoAgenda.TIPO_INTERNA));
                param.setProfissionalVagaInterna(getAtendimento().getProfissional());

                List<AgendaGradeAtendimentoDTO> agendasDisponiveisProximidade = consultarVagasDisponiveisProximidade(param);

                if (solicitacaoAgendamento != null && AtendimentoHelper.obrigatorioAgendamentoExamePadrao(exameBpai.getExame(), tipoProcedimento)) {

                    param.setSomenteVagaInterna(this.validaFilaEsperaAtendimento(solicitacaoAgendamento, getAtendimento()));

                    Empresa empresaResponsavelSolicitacaoAgendamento = AgendamentoHelper.empresaResponsavelSolicitacaoAgendamento(getAtendimento().getUsuarioCadsus().getCodigo());
                    if(!edicaoCadastro && RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()) && empresaResponsavelSolicitacaoAgendamento == null){
                        EmpresaResponsavelSolicitacaoAgendamentoDTO empresaResponsavelSolicitacaoAgendamentoDTO = new EmpresaResponsavelSolicitacaoAgendamentoDTO();
                        empresaResponsavelSolicitacaoAgendamentoDTO.setParam(param);
                        empresaResponsavelSolicitacaoAgendamentoDTO.setAgendasDisponiveis(agendasDisponiveisProximidade);
                        empresaResponsavelSolicitacaoAgendamentoDTO.setSolicitacaoAgendamento(solicitacaoAgendamento);

                        initDlgUnidadeResponsavelSolicitacaoAgendamento(target, empresaResponsavelSolicitacaoAgendamentoDTO, tipoProcedimento);
                        return;
                    } else {
                        if (empresaResponsavelSolicitacaoAgendamento != null) {
                            solicitacaoAgendamento.setEmpresa(empresaResponsavelSolicitacaoAgendamento);
                            BOFactoryWicket.save(solicitacaoAgendamento);
                        }

                        onAgendar(target, agendasDisponiveisProximidade, param, tipoProcedimento);
                        return;
                    }
                }
            } else if (solicitacaoAgendamento != null && RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
                Empresa empresaResponsavelSolicitacaoAgendamento = AgendamentoHelper.empresaResponsavelSolicitacaoAgendamento(getAtendimento().getUsuarioCadsus().getCodigo());
                if (!edicaoCadastro && RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()) && empresaResponsavelSolicitacaoAgendamento == null) {
                    EmpresaResponsavelSolicitacaoAgendamentoDTO empresaResponsavelSolicitacaoAgendamentoDTO = new EmpresaResponsavelSolicitacaoAgendamentoDTO();
                    empresaResponsavelSolicitacaoAgendamentoDTO.setSolicitacaoAgendamento(solicitacaoAgendamento);

                    initDlgUnidadeResponsavelSolicitacaoAgendamento(target, empresaResponsavelSolicitacaoAgendamentoDTO, tipoProcedimento);
                    return;
                } else {
                    if (empresaResponsavelSolicitacaoAgendamento != null) {
                        solicitacaoAgendamento.setEmpresa(empresaResponsavelSolicitacaoAgendamento);
                        BOFactoryWicket.save(solicitacaoAgendamento);
                    }
                }
            }
        }
        if (containerTelaSoap != null && RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())) {
            getProntuarioController().changePanel(target, new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), containerTelaSoap));
        } else if (containerTelaSoap != null && RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())) {
            getProntuarioController().changePanel(target, new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), containerTelaSoap));
        } else {
            getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap));
        }
    }

    private void onAgendar(AjaxRequestTarget target, List<AgendaGradeAtendimentoDTO> agendasDisponiveisProximidade, AgendaGradeAtendimentoDTOParam param, TipoProcedimento tipoProcedimento) throws ValidacaoException, DAOException {
        SolicitacaoAgendamento solicitacaoAgendamento = LoadManager.getInstance(SolicitacaoAgendamento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_ATENDIMENTO_ORIGEM, getAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, tipoProcedimento))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, SolicitacaoAgendamento.STATUS_CANCELADO))
                .addSorter(new QueryCustom.QueryCustomSorter(SolicitacaoAgendamento.PROP_DATA_CADASTRO))
                .setMaxResults(1).start().getVO();
        param.setSolicitacaoAgendamento(solicitacaoAgendamento);

        getProntuarioController().changePanel(target, new MarcacaoAgendamentoExameBpaiPanel(getProntuarioController().panelId(), agendasDisponiveisProximidade, param, solicitacaoAgendamento) {
            @Override
            public void retornarPanelConsulta(AjaxRequestTarget target) {
                if (containerTelaSoap != null && RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())) {
                    getProntuarioController().changePanel(target, new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), containerTelaSoap));
                } else if (containerTelaSoap != null && RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())) {
                    getProntuarioController().changePanel(target, new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), containerTelaSoap));
                } else {
                    getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap));
                }
            }
        });
    }

    private List<AgendaGradeAtendimentoDTO> consultarVagasDisponiveisProximidade(AgendaGradeAtendimentoDTOParam param) throws DAOException, ValidacaoException {
        List<AgendaGradeAtendimentoDTO> agendasDisponiveis = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarVagasDisponiveisAgendaExame(param);
        List<AgendaGradeAtendimentoDTO> agendasDisponiveisProximidade = BOFactoryWicket.getBO(AgendamentoFacade.class).validarAgendasProximidadeSolicitanteExecutante(getAtendimento().getEmpresa(), agendasDisponiveis);

        return agendasDisponiveisProximidade;
    }

    private void initDlgUnidadeResponsavelSolicitacaoAgendamento(AjaxRequestTarget target, EmpresaResponsavelSolicitacaoAgendamentoDTO empresaResponsavelSolicitacaoAgendamentoDTO, final TipoProcedimento tipoProcedimento) {
        if (dlgUnidadeResponsavelSolicitacaoAgendamento == null) {
            dlgUnidadeResponsavelSolicitacaoAgendamento = new DlgUnidadeResponsavelSolicitacaoAgendamento(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, EmpresaResponsavelSolicitacaoAgendamentoDTO dto) throws ValidacaoException, DAOException {
                    dto.getSolicitacaoAgendamento().setEmpresa(dto.getEmpresa());
                    BOFactoryWicket.save(dto.getSolicitacaoAgendamento());

                    if (CollectionUtils.isNotNullEmpty(dto.getAgendasDisponiveis())) {
                        onAgendar(target, dto.getAgendasDisponiveis(), dto.getParam(), tipoProcedimento);
                    } else {
                        if (containerTelaSoap != null && RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())) {
                            getProntuarioController().changePanel(target, new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), containerTelaSoap));
                        } else if (containerTelaSoap != null && RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())) {
                            getProntuarioController().changePanel(target, new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), containerTelaSoap));
                        } else {
                            getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap));
                        }
                    }
                }
            };
            getProntuarioController().addWindow(target, dlgUnidadeResponsavelSolicitacaoAgendamento);
        }
        dlgUnidadeResponsavelSolicitacaoAgendamento.show(target, empresaResponsavelSolicitacaoAgendamentoDTO);
    }

    public void validarRegra(AjaxRequestTarget target, ExameProcedimentoDTO dto) throws ValidacaoException, DAOException {
        ExameProcedimento ep = ExameHelper.validarRegrasExameProcedimento(dto.getExameProcedimento(), getAtendimento().getUsuarioCadsus());
        if (ep != null) {
            Long regra = ep.getTipoRegra() != null ? ep.getTipoRegra() : ep.getTipoExame().getTipoRegra();
            if (ExameProcedimento.TipoRegra.EMITIR_AVISO.value().equals(regra)) {
                if (dlgConfirmacaoObject == null) {
                    dlgConfirmacaoObject = new DlgConfirmacaoObject<ExameProcedimentoDTO>(getProntuarioController().newWindowId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, ExameProcedimentoDTO modelObject) throws ValidacaoException, DAOException {
                            if(validarExameAdicionado(target)) {
                                adicionarExameProcedimento(target);
                            }
                        }
                    };
                }
                getProntuarioController().addWindow(target, dlgConfirmacaoObject);
                Long quantidadeDias = ep.getQuantidadeDias() == null ? ep.getTipoExame().getQuantidadeDias() : ep.getQuantidadeDias();
                dlgConfirmacaoObject.setMessage(target, bundle("msgTempoLimiteExameDias", quantidadeDias));
                dlgConfirmacaoObject.show(target, dto);
            } else if (ExameProcedimento.TipoRegra.EXIGIR_JUSTIFICATIVA.value().equals(regra)) {
                if (dlgJustificativaObject == null) {
                    dlgJustificativaObject = new DlgJustificativaObject<ExameProcedimentoDTO>(getProntuarioController().newWindowId(), bundle("justificativa")) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, String motivo, ExameProcedimentoDTO object) throws ValidacaoException, DAOException {
                            object.setJustificativa(motivo);
                            if(validarExameAdicionado(target)) {
                                adicionarExameProcedimento(target);
                            }
                        }
                    };
                }
                getProntuarioController().addWindow(target, dlgJustificativaObject);
                dlgJustificativaObject.setObject(dto);
                dlgJustificativaObject.show(target);
            } else if (ExameProcedimento.TipoRegra.NEGAR.value().equals(regra)) {
                Long quantidadeDias = ep.getQuantidadeDias() == null ? ep.getTipoExame().getQuantidadeDias() : ep.getQuantidadeDias();
                throw new ValidacaoException(bundle("msgTempoLimiteExameDias", quantidadeDias));
            }
        } else {
            if(validarExameAdicionado(target)) {
                adicionarExameProcedimento(target);
            }
        }
    }

    public NodoAtendimentoWeb carregarPermissaoNoInformarProfissional() {
        if (nodoAtendimentoWeb == null) {
            NodoAtendimentoWeb proxy = on(NodoAtendimentoWeb.class);

            nodoAtendimentoWeb = LoadManager.getInstance(NodoAtendimentoWeb.class)
                    .addProperties(new HQLProperties(NodoAtendimentoWeb.class).getProperties())
                    .addProperties(new HQLProperties(GrupoAtendimentoCbo.class, path(proxy.getGrupoAtendimentoCbo())).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTipoAtendimento()), getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getClasseNodo()), SolicitacaoExamesNode.class.getName()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getInformarProfissional()), RepositoryComponentDefault.SIM_LONG))
                    .start().getVO();
        }

        return nodoAtendimentoWeb;
    }

    public String getTipoControleRegulacao() {
        if (tipoControleRegulacao == null) {
            try {
                tipoControleRegulacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }

    private void regrasControleRegulacaoSolicitacaoProfissional(AjaxRequestTarget target, boolean edicaoCadastro, TipoProcedimento tipoProcedimento) {
        if (target != null) {
            dropDownEnviarRegulacao.limpar(target);
            txaDescricaoEnviarRegulacao.limpar(target);
        }

        if (form.getModelObject().getExameBpai() != null && form.getModelObject().getExameBpai().getExame() != null
                && RepositoryComponentDefault.SIM_LONG.equals(form.getModelObject().getExameBpai().getExame().getNaoColocarListaEsperaSus())) {
            if (containerSolicitacaoProfissional.isVisible()) {
                containerSolicitacaoProfissional.setVisible(false);

                if (target != null) {
                    ComponentUtils.limparContainer(containerSolicitacaoProfissional, target);
                }
            }

            return;
        } else if (!containerSolicitacaoProfissional.isVisible()) {
            containerSolicitacaoProfissional.setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()));
            if (target != null) {
                target.add(containerSolicitacaoProfissional);
            }
        }

        if (tipoProcedimento != null && tipoProcedimento.getCodigo() != null && tipoProcedimento.getRegulado() == null) {
            tipoProcedimento = LoadManager.getInstance(TipoProcedimento.class)
                    .addProperty(TipoProcedimento.PROP_CODIGO)
                    .addProperty(TipoProcedimento.PROP_REGULADO)
                    .setId(tipoProcedimento.getCodigo())
                    .start().getVO();
        }

        if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
            if (edicaoCadastro) {
                if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getExameBpai().getFlagEnviarRegulacao())
                        || tipoProcedimento != null && RepositoryComponentDefault.SIM.equals(tipoProcedimento.getRegulado())) {
                    dropDownEnviarRegulacao.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    if (tipoProcedimento != null && RepositoryComponentDefault.SIM.equals(tipoProcedimento.getRegulado())) {
                        dropDownEnviarRegulacao.setEnabled(false);
                    }

                    if (target != null) {
                        showFieldset(target, containerSolicitacaoProfissional);
                    }
                }
            } else if (tipoProcedimento != null && RepositoryComponentDefault.SIM.equals(tipoProcedimento.getRegulado())) {
                dropDownEnviarRegulacao.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                dropDownEnviarRegulacao.setEnabled(false);

                if (target != null) {
                    showFieldset(target, containerSolicitacaoProfissional);
                }
            } else {
                dropDownEnviarRegulacao.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                dropDownEnviarRegulacao.setEnabled(true);

                if (target != null) {
                    hideFieldset(target, containerSolicitacaoProfissional);
                }
            }
        }

        if (target != null) {
            target.add(dropDownEnviarRegulacao);
            target.add(txaDescricaoEnviarRegulacao);
        }
    }

    private boolean validaFilaEsperaAtendimento(SolicitacaoAgendamento solicitacaoAgendamento, Atendimento atendimento) throws DAOException {
        return new ValidarFilaEsperaNoAtendimento().setSolicitacaoAgendamento(solicitacaoAgendamento)
                .setAtendimento(atendimento)
                .setFlagRetorno(null)
                .validar();
    }

    private List<ExameProcedimentoDTO> getExamesEmAberto() throws ValidacaoException, DAOException {
        List<ExameProcedimentoDTO> abertos = new LinkedList<>();
        List<SolicitacaoExameDTO> examesAtendimento = BOFactoryWicket.getBO(ExameFacade.class).consultarExamesAdicionadoAtendimento(getAtendimento());

        for (SolicitacaoExameDTO se : examesAtendimento) {
            ExameCadastroAprovacaoDTO exameCadastroAprovacaoDTO = BOFactoryWicket
                                                                    .getBO(ExameFacade.class)
                                                                    .carregarExame(se.getExame().getCodigo());
            abertos.addAll(exameCadastroAprovacaoDTO.getExameProcedimentoDTOs());
        }

        return abertos;
    }

    private boolean validaExamePendente(ExameRequisicao exameRequisicao, ExameProcedimento exameProcedimento) {
        int diasValidacaoExamePendente = exameProcedimento.getDiasValidacaoExamePendente() == null ? 0
                : exameProcedimento.getDiasValidacaoExamePendente().intValue();
        if (diasValidacaoExamePendente == 0) {
            diasValidacaoExamePendente = tipoExame.getDiasValidacaoExamePendente() == null ? 0
                    : tipoExame.getDiasValidacaoExamePendente().intValue();
        }
        if (diasValidacaoExamePendente > 0) {
            return exameRequisicao.getExame().getDataSolicitacao() != null
                    && exameRequisicao.getExame()
                    .getDataSolicitacao().after(Data.removeDias(DataUtil.getDataAtual(), diasValidacaoExamePendente));
        } else {
            return true;
        }
    }

    private List<ExameRequisicao> getExamesPendentes() {
        ExameRequisicao proxy = Lambda.on(ExameRequisicao.class);
        LoadManager loadManager = LoadManager.getInstance(ExameRequisicao.class);
        loadManager.addProperties(new HQLProperties(ExameRequisicao.class).getProperties())
                .addProperties(new HQLProperties(ExameProcedimento.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO)).getProperties())
                .addProperties(new HQLProperties(Exame.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME)).getProperties());
        addFilterStatusEqualsAbertoOrIsNull(proxy, loadManager);
        return loadManager.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getAtendimento().getCodigo()), QueryCustom.QueryCustomParameter.DIFERENTE, getAtendimento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getAtendimento().getUsuarioCadsus().getCodigo()), getAtendimento().getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getTipoExame().getTipo()), QueryCustom.QueryCustomParameter.DIFERENTE, RepositoryComponentDefault.Tipo.LACEN.value()))
                .addInterceptor(new LoadInterceptorNotExistsExameExameBpaApac(LoadInterceptorNotExistsExameExameBpaApac.Tipo.EXAME_REQUISICAO))
                .start().getList();
    }

    private void addFilterStatusEqualsAbertoOrIsNull(ExameRequisicao proxy, LoadManager loadManager) {
        loadManager.addParameter(new QueryCustom.QueryCustomParameter(
                new BuilderQueryCustom.QueryGroupAnd(
                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IGUAL, ExameRequisicao.Status.ABERTO.value()))),
                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IS_NULL))))));
    }

    private void viewDlgConcluirExameRequisicao(AjaxRequestTarget target, ExameRequisicao examesPendentes) {
        DlgConcluirExameRequisicao dlgConcluirExameRequisicao;
        getProntuarioController().addWindow(target, dlgConcluirExameRequisicao = new DlgConcluirExameRequisicao(getProntuarioController().newWindowId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ExameRequisicao exameRequisicao) throws ValidacaoException, DAOException {
                salvarExameRequisicao(exameRequisicao);
                close(target);
                if(validarExameAdicionado(target)) {
                    adicionarExameProcedimento(target);
                }
            }
        });
        dlgConcluirExameRequisicao.show(target, examesPendentes);
    }

}
