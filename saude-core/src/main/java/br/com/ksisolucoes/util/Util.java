package br.com.ksisolucoes.util;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.commons.lang.StringUtils;

import javax.swing.text.MaskFormatter;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.util.regex.Pattern;

/*
 * Util.java
 *
 * Created on 17 de Maro de 2003, 14:34
 */

/**
 * <AUTHOR>
 */
public final class Util {

    public static void validarIdentificadorUsuario(String identificador) throws ValidacaoException {
        if (Coalesce.asString(identificador).trim().length() < 3) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_identificacao_minimo_X_digitos", "3"));
        }
    }

    public static void validarSenha(String senhaOriginal) throws ValidacaoException {
        if (Coalesce.asString(senhaOriginal).trim().length() < 8) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_senha_minimo_oito_caracteres"));
        }

        if (senhaOriginal.replaceAll("[^a-zA-Z]", "").trim().length() == 0) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_senha_letras_numeros"));
        }

        if (senhaOriginal.replaceAll("[^0-9]", "").trim().length() == 0) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_senha_letras_numeros"));
        }

        if (senhaOriginal.replaceAll("[^A-Z]", "").trim().length() == 0 || senhaOriginal.replaceAll("[^a-z]", "").trim().length() == 0) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_senha_mai_min"));
        }
    }

    /**
     * Verifica se a senha atende aos seguintes critérios:
     * - Mínimo de 8 caracteres
     * - Pelo menos uma letra maiúscula
     * - Pelo menos uma letra minúscula
     * - Pelo menos um número
     *
     * @param senha A senha a ser validada
     * @return true se for válida, false caso contrário
     */
    public static boolean isSenhaValida(String senha) {
        if (senha == null) return false;

        return senha.matches("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$");
    }

    public static String criptografarSenha(String senhaOriginal) {
        return criptografarSenha(senhaOriginal, null);
    }

    public static String criptografarSenha(String senhaOriginal, String method) {
        try {
            MessageDigest algorithm;
            if (method == null) {
                algorithm = MessageDigest.getInstance("SHA-256");
            } else {
                algorithm = MessageDigest.getInstance(method);
            }
            byte[] messageDigest = algorithm.digest(senhaOriginal.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                hexString.append(String.format("%02X", 0xFF & b));
            }

            return hexString.toString();
        } catch (UnsupportedEncodingException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (NoSuchAlgorithmException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return "";
    }

    /**
     * Retorna a descricao do produto formatada no seguinte formato:<br>
     * ( codigo ) descricao
     *
     * @return <code>String</code> - se algum argumento for null ser retornado
     * null
     */
    public static String getDescricaoFormatado(String codigo, String descricao) {
        if (codigo == null || descricao == null) {
            return null;
        } else {
            return Bundle.getStringApplication("format_identificador_descricao",
                    StringUtils.trimToEmpty(codigo), StringUtils.trimToEmpty(descricao));
        }
    }

    /**
     * Retorna a descricao do produto formatada no seguinte formato:<br>
     * ( codigo ) descricao
     *
     * @return <code>String</code> - se algum argumento for null ser retornado
     * null
     */
    public static String getDescricaoFormatado(Long codigo, String descricao) {
        if (codigo == null || descricao == null) {
            return null;
        } else {
            return Bundle.getStringApplication("format_identificador_descricao",
                    StringUtils.trimToEmpty(codigo.toString()), StringUtils.trimToEmpty(descricao));
        }

    }

    public static String getDescricaoFormatadoInvertido(String descricao, Long codigo) {
        if (codigo == null || descricao == null) {
            return null;
        } else {
            return Bundle.getStringApplication("format_identificador_descricao_invertido",
                    StringUtils.trimToEmpty(StringUtils.trimToEmpty(descricao)), codigo.toString());
        }
    }

    public static String getTelefoneFormatado(String ddd, String telefone) {
        if (!Coalesce.asString(ddd, "").trim().isEmpty()) {
            return "(" + Coalesce.asString(ddd, "") + ") " + Coalesce.asString(telefone, "");
        } else {
            return Coalesce.asString(telefone, "");
        }
    }

    public static String getTelefoneFormatado(Long ddd, Long telefone) {
        return getTelefoneFormatado(Coalesce.asString(ddd, ""), Coalesce.asString(telefone, ""));
    }

    public static String getTelefoneFormatado(Long ddd, String telefone) {
        return getTelefoneFormatado(Coalesce.asString(ddd, ""), Coalesce.asString(telefone, ""));
    }

    public static String getTelefoneFormatado(String telefoneComDdd) {
        telefoneComDdd = Coalesce.asString(telefoneComDdd).replaceAll("[^0-9]", "");
        if (telefoneComDdd.trim().length() > 2) {
            try {
                MaskFormatter m;

                if (telefoneComDdd.length() == 11) {
                    m = new MaskFormatter("(##) #####-####");
                } else {
                    m = new MaskFormatter("(##) ####-####");
                }

                m.setValueContainsLiteralCharacters(false);
                return m.valueToString(telefoneComDdd);
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return "";
    }

    public static String getRemoteClient() {
        try {
            Class forName = Class.forName("br.com.celk.system.util.WebPropertiesRetriever");
            if (forName != null) {
                Object newInstance = forName.newInstance();
                Method method = forName.getMethod("getClientIp");
                return (String) method.invoke(newInstance);
            }
        } catch (NoSuchMethodException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ClassNotFoundException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (Exception ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return "";
    }

    public static boolean isWeb() {
        try {
            Class forName = Class.forName("br.com.celk.system.util.WebPropertiesRetriever");
            if (forName != null) {
                Object newInstance = forName.newInstance();
                Method method = forName.getMethod("getClientIp");
                return method.invoke(newInstance) != null;
            }
        } catch (NoSuchMethodException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ClassNotFoundException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (Exception ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return false;
    }

    public static String getClientEnvironment() {
        if (isWeb()) {
            return Bundle.getStringApplication("rotulo_web");
        }
        return Bundle.getStringApplication("rotulo_desktop");
    }

    public static String getCnpjFormatado(String cnpj) {
        if (cnpj != null && !cnpj.trim().equals("")) {
            cnpj = StringUtilKsi.getDigits(Coalesce.asString(cnpj));
            try {
                MaskFormatter m = new MaskFormatter("##.###.###/####-##");
                m.setValueContainsLiteralCharacters(false);
                return m.valueToString(cnpj);
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return "";
    }

    public static String getCpfFormatado(String cpf) {
        if (cpf != null && !cpf.trim().equals("")) {
            cpf = StringUtilKsi.getDigits(Coalesce.asString(cpf));
            try {
                MaskFormatter m = new MaskFormatter("###.###.###-##");
                m.setValueContainsLiteralCharacters(false);
                return m.valueToString(cpf);
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return "";
    }

    public static String getCNSFormatado(String cns) {
        if (cns != null && !cns.trim().equals("")) {
            cns = StringUtilKsi.getDigits(Coalesce.asString(cns));
            try {
                MaskFormatter m = new MaskFormatter("###.###.###.###.###");
                m.setValueContainsLiteralCharacters(false);
                return m.valueToString(cns);
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return "";
    }

    public static String getPlacaFormatado(String placa) {
        if (placa != null && !placa.trim().equals("")) {
            try {
                MaskFormatter m = new MaskFormatter("AAA-####");
                m.setValueContainsLiteralCharacters(false);
                return m.valueToString(placa);
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return "";
    }

    public static String getCepFormatado(String cep) {
        if (cep != null && cep.trim().length() != 0) {
            try {
                MaskFormatter m = new MaskFormatter("#####-###");
                m.setValueContainsLiteralCharacters(false);
                return m.valueToString(cep);
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return "";
    }

    public static boolean isValidCellphone(String cellPhone) {
        String regex = "^(?:[14689][1-9]|2[12478]|3[1234578]|5[1345]|7[134579])9[1-9][0-9]{3}[0-9]{4}$";
        Pattern p = Pattern.compile(regex);

        return p.matcher(cellPhone).matches();
    }
}
