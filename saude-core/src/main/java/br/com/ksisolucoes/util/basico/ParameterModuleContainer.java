package br.com.ksisolucoes.util.basico;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.parametrogem.IModulos;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.parametrogem.NivelParametroGem;
import br.com.ksisolucoes.util.parametrogem.ParametroGemHelper;
import br.com.ksisolucoes.util.profissional.ProfissionalUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.ParametroGem;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.Collection;
import java.util.List;


/**
 *
 * <AUTHOR>
 */
public class ParameterModuleContainer implements Serializable, IParameterModuleContainer {

    private Collection<ParametroGem> parametroGems;
    //filtros para o cache
    private IModulos modulo;

    public ParameterModuleContainer(IModulos modulo) {
        this.modulo = modulo;
    }

    public IParameterModuleContainer build() throws DAOException {
        try {
            this.parametroGems = new ProcessParametroGem(modulo).process().getParametros();
        } catch (SGKException ex) {
            throw new DAOException("Erro ao carregar ParametroGem", ex);
        }

        return this;                    
    }

    public <T> T getParametro(Long codigoEmpresa, Long codigoUsuario, List<TabelaCbo> tabelaCbos, Long codigoTipoProcedimento, String parametro) {
        ParametroGem parametroGem = null;
        //POR USUÁRIO
        if (parametroGem == null && codigoUsuario != null) {
            for (ParametroGem parametroGem_ : parametroGems) {
                if (parametroGem_.getId().getParametro().equals(parametro)
                        && NivelParametroGem.USUARIO.getNivel().equals(parametroGem_.getId().getNivel())) {
                    if (!codigoUsuario.toString().equals(parametroGem_.getId().getIdentificador())) {
                        continue;
                    }
                    if (parametroGem_.getValor() != null && parametroGem_.getValor().trim().length() > 0) {
                        parametroGem = parametroGem_;
                        break;
                    } else {
                        break;
                    }
                }
            }
        }
        //POR CBO
        if (parametroGem == null && codigoEmpresa != null) {
            List<String> cbosProfissional = Lambda.extract(tabelaCbos, Lambda.on(TabelaCbo.class).getCbo());

            for (ParametroGem parametroGem_ : parametroGems) {
                if (parametroGem_.getId().getParametro().equals(parametro)
                        && NivelParametroGem.CBO.getNivel().equals(parametroGem_.getId().getNivel())) {
                    if (!cbosProfissional.contains(parametroGem_.getId().getIdentificador())) {
                        continue;
                    }
                    if (parametroGem_.getValor() != null && parametroGem_.getValor().trim().length() > 0) {
                        parametroGem = parametroGem_;
                        break;
                    } else {
                        break;
                    }
                }
            }
        }
        //POR UNIDADE
        if (parametroGem == null && codigoEmpresa != null) {
            for (ParametroGem parametroGem_ : parametroGems) {
                if (parametroGem_.getId().getParametro().equals(parametro)
                        && NivelParametroGem.EMPRESA.getNivel().equals(parametroGem_.getId().getNivel())) {
                    if (!codigoEmpresa.toString().equals(parametroGem_.getId().getIdentificador())) {
                        continue;
                    }
                    if (parametroGem_.getValor() != null && parametroGem_.getValor().trim().length() > 0) {
                        parametroGem = parametroGem_;
                        break;
                    } else {
                        break;
                    }
                }
            }
        }
        //POR TIPO DE PROCEDIMENTO
        if (parametroGem == null && codigoTipoProcedimento != null) {
            for (ParametroGem parametroGem_ : parametroGems) {
                if (parametroGem_.getId().getParametro().equals(parametro)
                        && NivelParametroGem.TIPO_PROCEDIMENTO.getNivel().equals(parametroGem_.getId().getNivel())) {
                    if (!codigoTipoProcedimento.toString().equals(parametroGem_.getId().getIdentificador())) {
                        continue;
                    }
                    if (parametroGem_.getValor() != null && parametroGem_.getValor().trim().length() > 0) {
                        parametroGem = parametroGem_;
                        break;
                    } else {
                        break;
                    }
                }
            }
        }
        //GERAL
        if (parametroGem == null) {
            for (ParametroGem parametroGem_ : parametroGems) {
                if (parametroGem_.getId().getParametro().equals(parametro)
                        && NivelParametroGem.GERAL.getNivel().equals(parametroGem_.getId().getNivel())) {
                        parametroGem = parametroGem_;
                        break;
                }
            }
        }
        if (parametroGem == null) {
            throw new ValidacaoRuntimeException("Parametro "+parametro+" inexistente!");
        } else {
            Object value = ParametroGemHelper.getValueTyped(parametroGem.getType(),parametroGem.getValor());
            if (value == null && parametroGem.isObrigatorio()) {
                //� obrigatorio a defini��o do parametro X 
                throw new ValidacaoRuntimeException(Bundle.getStringApplication("msg_e_obrigatorio_a_definicao_do_parametro_x",parametro));
            }
            return (T) value;
        }
    }

    public <T> T getParametro(Long codigoEmpresa, Long codigoUsuario, String parametro) {
        return (T) getParametro(codigoEmpresa, codigoUsuario, ProfissionalUtils.getCbosUsuario(codigoEmpresa, codigoUsuario), null, parametro);
    }

    public <T> T getParametro(Long codigoEmpresa, Long codigoUsuario, Long codigoTipoProcedimento, String parametro) {
        return (T) getParametro(codigoEmpresa, codigoUsuario, ProfissionalUtils.getCbosUsuario(codigoEmpresa, codigoUsuario), codigoTipoProcedimento, parametro);
    }

    public <T> T getParametro(String parametro) {
        if(SessaoAplicacaoImp.getInstance() != null){
            Long codigoEmpresa = SessaoAplicacaoImp.getInstance().getCodigoEmpresa();
            Long codigoUsuario = SessaoAplicacaoImp.getInstance().getCodigoUsuario();
            List<TabelaCbo> tabelaCbos = SessaoAplicacaoImp.getInstance().getTabelaCbos();
            return (T) getParametro(codigoEmpresa, codigoUsuario, tabelaCbos, null, parametro);
        }else{
            return (T) getParametro(null, null, null, null, parametro);
        }
    }

    public static void main(String[] args) {
        try {
            Class clazz = Class.forName("br.com.ksisolucoes.util.basico.ParameterModuleContainer");
            Constructor constructor = clazz.getConstructors()[0];
            IParameterModuleContainer iParameterModuleContainer = (IParameterModuleContainer) constructor.newInstance(Modulos.MATERIAIS);
            iParameterModuleContainer.build();
        } catch (Exception e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }

}
