<?xml version="1.0"?>
<estrutura>

    <!--<PERSON><PERSON><PERSON>
        P - Parametro
        E - Empresa
        U - Usuarios
    -->

    <!-- <PERSON><PERSON><PERSON>
        GERAL                      1
        CONTROLE                   4
        AUDITORIA                 22
        MATERIAIS       	      12
        VACINAS  	              18
        INTEGRAÇÃO  	      25
        AGENDAMENTO 	      26
        UNIDADE DE SAÚDE	      24
        CONSÓRCIO   	      27
        VIGILANCIA                28 - DEPRECIADO
        LABORATÓRIO               29
        MOBILE                    30
        PORTAL                    31
        INTEGRACAO INOVAMFRI      32
        VIGILANCIA SANITARIA      33
        FROTA                     34

            <parametro nome="retornoBorderoGerarMovimentacaoFinanceira" tipo="java.lang.String"
                observacao="Na leitura do arquivo de retorno de cobranca contas a receber gerar registro na movimentação financeira"
                grupo="Bordero Contas a Receber" defaultValue="S">
                <checkValues value="S" rotulo="rotulo_sim" />
                <checkValues value="N" rotulo="rotulo_nao" />
            </parametro>
        -->
    <!--Agendamento-->
    <modulo nome="26">
        <parametro nome="DefineQuemRecebeNotificaçãoAgendamento" tipo="java.lang.Long"
                   grupo="Regulação"
                   defaultValue="0"
                   observacao="Define quem recebe e da prosseguimento a notificação de um agendamento."
        >
            <checkValues value="0" rotulo="padrao"/>
            <checkValues value="1" rotulo="unidade_responsavel"/>
            <checkValues value="2" rotulo="regulacao"/>
        </parametro>

        <parametro nome="DiasLimiteparaRetornoConsultaEspecialista" tipo="java.lang.Long" grupo="Regulação" defaultValue="0"
                   observacao="Define quantos dias anteriores à data atual o sistema deve considerar para aprovar uma consulta de retorno ou interconsulta com um especialista."
        >
        </parametro>

        <parametro nome="consomeCotaOutraUnidadeAutorizadora" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Ativa o consumo de cota de outra unidade autorizadora deixando de consumir a cota da unidade solicitante pela tela 318 nó Exames."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="UtilizaProcessoRegulaçãoMR2paraAIHs" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Quando ligado envia AIHs cujo status são 'Mudança de procedimento' ou 'Critério de gravidade' ou 'Negada pela unidade' ou cota direta' com situação 'autorizadas' para aprovação de um segundo médico regulador."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="consomeCotaDaUnidade" tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao="Unidade autorizadora que terá a cota conforme o parâmetro consomeCotaOutraUnidadeAutorizadora."
                   obrigatorio="false"
        >
        </parametro>

        <parametro nome="exibirColunaDataPrevistaFilaPublica" tipo="java.lang.String" grupo="Lista Pública" defaultValue="S"
                   observacao="Define se será exibido a coluna Data Prevista na fila de Espera Pública."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="HabilitaPesquisaCPFListaPublica" tipo="java.lang.String" grupo="Lista Pública" defaultValue="S"
                   observacao="Define se será habilitado o filtro de CPF na tela da Lista Pública."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ListaAIHVinculadaAtendimento" tipo="java.lang.String" defaultValue="S" grupo="AIH"
                   observacao="Mostra na listagem da tela 293 e da tela 1034 os laudos de AIHs inserido pelo médico pelo atendimento da tela 266."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ListaAIHVinculadaAtendimentoDataInicio" tipo="java.util.Date" grupo="AIH"
            observacao="Define a partir de qual data irão ser apresentadas as AIHs que foram cadastradas pela tela 266 (atendimento) na tela 1034."
        />

        <parametro nome="DataSolicitacaoAIHUtilizaDataAtual" tipo="java.lang.String" grupo="AIH" defaultValue="N"
                   observacao="Define se na tela 293 o sistema apresentara o campo Data da Solicitação com data atual."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="GeraNumeracaoAIH" tipo="java.lang.String" grupo="AIH" defaultValue="AT"
                   observacao="Define em qual processo será gerado a numeração da autorização de AIH."
        >
            <checkValues value="AP" rotulo="rotulo_alta_paciente"/>
            <checkValues value="AT" rotulo="rotulo_autorizacao_aih"/>
        </parametro>

        <parametro nome="DiasRetroativoDataSolicitacaoAIH" tipo="java.lang.Long" grupo="AIH"
                   defaultValue="0"
                   observacao="Define os dias limite para data retroativa do cadastros da AIH pela tela 293."
        />


        <parametro nome="ModeloImpressaoComprovante" tipo="java.lang.String" defaultValue="A5"
                   observacao="Define o tipo do modelo que será impresso o comprovante de agendamento.">
            <checkValues value="A5" rotulo="rotulo_a5"/>
            <checkValues value="A4" rotulo="rotulo_a4"/>
            <checkValues value="T" rotulo="rotulo_termica"/>
        </parametro>

        <parametro nome="diasRetroativoDataSolicitacaoAgendamento" tipo="java.lang.Long" grupo="Solicitação Agendamento"
                   defaultValue="999"
                   observacao="Define os dias limite para data retroativa da solicitação de agendamento."
        />

        <parametro nome="habilitaCampoDataDesejada" tipo="java.lang.String" grupo="Solicitação Agendamento"
                   defaultValue="S"
                   observacao="Quando a opção em 'SIM' habilita o campo 'Data Desejada' nas telas 611 e 346."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="UtilizaControleAPACSolicitacaoAgendamento" tipo="java.lang.String" grupo="Solicitação Agendamento"
                   defaultValue="N"
                   observacao="Define se na tela 611 o sistema apresentará os campos da APAC quando o procedimento estiver configurado como APAC na tela 161."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="UtilizaValidaçãoTratamentoAPACs" tipo="java.lang.String" grupo="Solicitação Agendamento"
                   defaultValue="N"
                   observacao="Quando em 'SIM', aplica validações no cadastro da APAC, impedindo a continuidade se as regras de 'PRIMEIRO ATENDIMENTO' e 'SEGUIMENTO' não forem atendidas."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="PermiteUsarCampoTextoLivreNomeProfissional" tipo="java.lang.String" grupo="Solicitação Agendamento"
                   defaultValue="S"
                   observacao="Quando a opção em 'SIM' habilita o campo 'Nome do Profissional' (texto livre) nas telas 611 e 346."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="habilitaClassificacaoRiscoTela611e346" tipo="java.lang.String" grupo="Solicitação Agendamento"
                   defaultValue="N"
                   observacao="Quando ativo habilita na tela 611 e 346 o campo Classificação de risco."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ValidaTipoDeLeitoNoAgendamentoDaAIH" tipo="java.lang.String"
                   defaultValue="N"
                   grupo="AIH"
                   observacao="Valida se no processo de autorização da AIH pela tela 1034 vai ser mostrados todos os leitos disponíveis independente do tipo."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="HabilitaSinalizacaoAIHsistemaTerceiro" tipo="java.lang.String"
                   defaultValue="N"
                   grupo="AIH"
                   observacao="Quando ligado, habilita no processo de regulação da AIH funcionalidade para médico orientar a AIH para ser cadastrado em sistema terceiro."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="GerarNumeracaoAPAC" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se o sistema utilizará a geração da numeração da APAC no momento da confirmação de presença do paciente."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ValidaFilaEsperaNoAtendimento" tipo="java.lang.String" grupo="Solicitação Agendamento"
                   defaultValue="S"
                   observacao="Define se será validado ou não se existem pacientes em fila de espera no momento da solicitação de agendamento.  Quando definido como SIM, não será possível agendar por dentro do atendimento, caso existam pacientes em fila de espera para o respectivo Tipo de Procedimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="diasRemanejarAgendamentoRetroativo" tipo="java.lang.Long"
                   defaultValue="0"
                   observacao="Define com até quantos dias retroativos à data atual pode ser realizado o remanejamento de agendamentos que já passaram da data sem o atendimento realizado."
        />

        <parametro nome="ordenacaoListaEspera" tipo="java.lang.Long" defaultValue="0" grupo="Ordenação"
                   observacao="Define o segundo critério de ordenação da fila de espera."
        >
            <checkValues value="0" rotulo="rotulo_data_solicitacao"/>
            <checkValues value="1" rotulo="rotulo_data_de_cadastro"/>
        </parametro>

        <parametro nome="DiasLimiteCotasUnidade" tipo="java.lang.Long"
                   observacao="Define até quantos dias antes da data da agenda a validação das cotas por unidade deve ser
            considerada, após a data limite qualquer unidade poderá utilizar as vagas disponíveis"
                   defaultValue="0"
        />

        <parametro nome="validaDistribuicaoCotaAgenda" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se o sistema aplicará o controle de cota física quando o Tipo de Procedimento está setado como Controla Cota = SIM e o estabelecimento não possui nenhum registro de distribuição de cota na tela 489."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="quantidadeMaximaSolicitacoesAgendamentoLote" tipo="java.lang.Long"
                   observacao="Define a quantidade máxima de solicitações que serão processadas a cada lote de agendamento (feitos pela tela 951)."
                   defaultValue="500"
        />

        <parametro nome="DiasLimiteAgendamento" tipo="java.lang.Long"
                   observacao="Define até quantos dias antes da data da agenda o processo de agendamento pode
            realizar a marcação automática de horários"
                   defaultValue="0"
        />

        <parametro nome="diasLimiteConfirmacaoPrevia" tipo="java.lang.Long"
                   observacao="Define até quantos dias antes da data do agendamento a Unidade poderá registrar a confirmação de presença do agendamento."
                   defaultValue="0"
        />

        <parametro nome="DiasLiberacaoReservaTecnica" tipo="java.lang.Long"
                   observacao="Define até quantos dias antes da data da agenda o processo de agendamento pode
            realizar a marcação automática de horários para consultas com tipo de atendimento Reserva Técnica"
                   defaultValue="0"
        />

        <parametro nome="DiasReaproveitamentoAgendaRetorno" tipo="java.lang.Long"
                   observacao="Dentro do intervalo de dias indicado as vagas de retorno existentes serão usadas como vagas de consultas e não restarão vagas ociosas"
                   defaultValue="0"
        />

        <parametro nome="PercentualVagasPrioridadeAlta" tipo="java.lang.Double"
                   observacao="Define qual o percentual de vagas das agendas que serão alocadas para marcação automática de consultas
            de pessoas que tem prioridade (Ídosos, gestantes ...)."
                   defaultValue="0"
        />

        <parametro nome="diasMaximoCancelamentoAgendamento" tipo="java.lang.Integer" defaultValue="0"
                   observacao="Define até quantos dias anteriores a data do agendamento o cancelamento pode ser realizado."
        />

        <parametro nome="diasLimiteConfirmacao" tipo="java.lang.Integer" defaultValue="0"
                   observacao="Define até quantos dias após a data de agendamento as Unidades não informatizadas poderão confirmar presença dos pacientes."
        />

        <parametro nome="observacaoComprovanteTFD" tipo="java.lang.String" grupo="TFD"
                   observacao="Observação que será impressa nos Comprovantes de Recebimento do TFD."
        />

        <parametro nome="agendarRetroativo" tipo="java.lang.String" grupo="TFD" defaultValue="N"
                   observacao="Permitir que seja inserido e salvo data menor que a data atual."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="obrigatorioTodosCamposLaudo" tipo="java.lang.String" grupo="TFD" defaultValue="N"
                   observacao="Obriga o preenchimento de todos os campos do Laudo TFD"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="utilizaRegulacaoTFD" tipo="java.lang.String" grupo="TFD" defaultValue="N"
                   observacao="Define se as solicitações de agendamento originadas do TFD serão analisadas no processo de regulação."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="utilizaAnonimizacaoCPFRoteiroViagem" tipo="java.lang.String" grupo="TFD" defaultValue="N"
                   observacao="Quando ativado oculta parte da informação do CPF na impressão do roteiro de viagem tela 421."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="diasCalculoMediaEsperadaExames" tipo="java.lang.Long" defaultValue="0"
                   observacao="(Definir)"
        />

        <parametro nome="diasValidadeExame" tipo="java.lang.Long" defaultValue="0"
                   observacao="(Definir)"
        />

        <parametro nome="diasLimiteParaDevolucaoDasAIHs" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define os dias limite que a AIH ficará aguardando aprovação na tela 1034 até ser devolvida para a unidade de origem."
        />

        <parametro nome="integracaoAquarela" tipo="java.lang.String" grupo="Integração Aquarela" defaultValue="N"
                   observacao="Define integracao com aquarela"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="urlPrescritor" tipo="java.lang.String" grupo="Integração Aquarela"
                   observacao="url para requisicao de recomendacao de datas"
        />

        <parametro nome="urlStatusConsulta" tipo="java.lang.String" grupo="Integração Aquarela"
                   observacao="url para informar status da consulta"
        />

        <parametro nome="loginAquarela" tipo="java.lang.String" grupo="Integração Aquarela"
                   observacao="login para integracao com a aquarela"
        />

        <parametro nome="senhaAquarela" tipo="java.lang.String" grupo="Integração Aquarela"
                   observacao="senha para integracao com a aquarela"
        />

        <parametro nome="tamanhoMinimoCelular" tipo="java.lang.Long" grupo="Celular"
                   observacao="Tamanho mínimo para o numero de celular que será utilizado no envio de SMS"
        />

        <parametro nome="tamanhoMaximoCelular" tipo="java.lang.Long" grupo="Celular"
                   observacao="Tamanho máximo para o numero de celular que será utilizado no envio de SMS"
        />

        <parametro nome="diasLimiteEnvioSMS" tipo="java.lang.Long" grupo="SMS" obrigatorio="true"
                   observacao="Campo para definir o dia Limite que será enviado SMS para o paciente quando realizar o agendamento"
        />

        <parametro nome="emailAvisoErro" tipo="java.lang.String" grupo="SMS"
                   observacao="Lista de Email para onde será enviado no caso de erro de comunicação no serviço de SMS, no caso de mais de um email separar a lista por vírgula"
        />

        <parametro nome="diasLimiteEnvioViagem" tipo="java.lang.Long" grupo="SMS"
                   defaultValue="1"
                   observacao="Campo para definir o dia Limite que será enviado a mensagem para o paciente quando realizar o agendamento da viagem"
        />

        <parametro nome="mensagemSMS" tipo="java.lang.String" grupo="SMS" obrigatorio="true"
                   observacao="Campo para descrever mensagem que será enviada para o paciente quando for realizado um agendamento, sendo permitido no máximo 155 caracteres.
            Na criação das mensagens, são aceitos os seguintes caracteres: Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos, Caracteres acentuados: À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ), Espaço, ! exclamação, &quot; aspas, # sustenido, %, ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?, @, _ underline.
            Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS.
            Variáveis das informações que podem ser incluidas na mensagem: $data$, $hora$, $local_agendamento$, $tipo_procedimento$, $nome_paciente$, $nome_completo_paciente$, $chave_validacao$, $telefone_local_agendamento$, $url_comprovante$."
        />

        <parametro nome="mensagemSMSAgendamentoViagem" tipo="java.lang.String" grupo="SMS" obrigatorio="true" defaultValue="$nome$, $local_embarque$, $data$, $hora$"
                   observacao="Campo para descrever mensagem que será enviada para o paciente quando for realizado um agendamento uma viagem sendo permitido no máximo 155 caracteres.
            Na criação das mensagens, são aceitos os seguintes caracteres: Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos, Caracteres acentuados: À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ), Espaço, ! exclamação, &quot; aspas, # sustenido, %, ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?, @, _ underline.
            Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS.
                Variáveis das informações que podem ser incluidas na mensagem: $nome$, $local_embarque$, $data$, $hora$."
        />

        <parametro nome="mensagemAvisoCancelamentoRoteiro"
                   tipo="java.lang.String" grupo="SMS"
                   obrigatorio="true"
                   defaultValue="Ola $nome_paciente$, seu angendamento de viagem do dia: $data_viagem$ as $hora_viagem$ foi cancelado. Em breve informaremos nova data."
                   observacao="Campo para descrever mensagem que será enviada para o paciente quando a viagem agendada for cancelada, sendo permitido no máximo 155 caracteres, quando SMS.
                   Na criação das mensagens, são aceitos os seguintes caracteres: Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos, Caracteres acentuados: À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ), Espaço, ! exclamação, aspas, # sustenido, %, ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?, @, _ underline.
                   Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS. Variáveis das informações que podem ser incluidas na mensagem: $nome_paciente$, $data_viagem$ e $hora_viagem$."
        />

        <parametro nome="urlServicoSMS" tipo="java.lang.String" grupo="SMS" obrigatorio="true"
                   observacao="URL do webservice da API de envio de SMS"
        />

        <parametro nome="chaveServicoSMS" tipo="java.lang.String" grupo="SMS" obrigatorio="true"
                   observacao="Chave de acesso da API de envio de SMS"
        />

        <parametro nome="MensagemAvisoExamesconcluídos" tipo="java.lang.String" grupo="SMS"
                   defaultValue="Sr.(a) $nome_paciente$, o resultado do seu exame realizado em $data$ já está disponível. Em caso de dúvidas, procure sua Unidade de Saúde."
        />

        <parametro nome="diasReaviso" tipo="java.lang.Integer" grupo="SMS" obrigatorio="true"
                   observacao="Define a quantidade de dias antes da data agendada para enviar o SMS de reaviso no caso do paciente não ter pego ainda o processo."
        />

        <parametro nome="diasMinimoAgendamento" tipo="java.lang.Integer" grupo="SMS:Confirmacao" obrigatorio="true"
                   observacao="Define a quantidade de dias mínimo de agendamento para poder enviar o SMS de Confirmação"
        />

        <parametro nome="diasConfirmacao" tipo="java.lang.Integer" grupo="SMS:Confirmacao" obrigatorio="true"
                   observacao="Define a quantidade de dias antes da data agendada para enviar o SMS de Confirmação"
        />

        <parametro nome="diasAvisoAgendamentoLocal" tipo="java.lang.Integer" grupo="SMS:Agendamento Local"
                   obrigatorio="true"
                   observacao="Define a quantidade de dias antes da data agendada para enviar o SMS de Aviso"
        />

        <parametro nome="mensagemConfirmacao" tipo="java.lang.String" grupo="SMS:Confirmacao" obrigatorio="true"
                   observacao="Campo para descrever mensagem que será enviada para o paciente para confirmação do agendamento, sendo permitido no máximo 155 caracteres.
            Na criação das mensagens, são aceitos os seguintes caracteres: Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos, Caracteres acentuados: À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ), Espaço, ! exclamação, &quot; aspas, # sustenido, %, ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?, @, _ underline.
            Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS.
            Variáveis das informações que podem ser incluidas na mensagem: $data$, $hora$, $local_agendamento$, $tipo_procedimento$, $nome_paciente$, $nome_completo_paciente$."
        />

        <parametro nome="mensagemReaviso" tipo="java.lang.String" grupo="SMS:Confirmacao" obrigatorio="true"
                   observacao="Campo para descrever mensagem que será enviada para reavisar o paciente sobre o agendamento, sendo permitido no máximo 155 caracteres.
            Na criação das mensagens, são aceitos os seguintes caracteres: Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos, Caracteres acentuados: À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ), Espaço, ! exclamação, &quot; aspas, # sustenido, %, ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?, @, _ underline.
            Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS.
            Variáveis das informações que podem ser incluidas na mensagem: $data$, $hora$, $local_agendamento$, $tipo_procedimento$, $nome_paciente$, $nome_completo_paciente$."
        />

        <parametro nome="respostaNaoComparecimento" tipo="java.lang.String" grupo="SMS" obrigatorio="true"
                   observacao="Define qual vai ser a reposta que será identificada pelo sistema para cancelar o agendamento do paciente"
        />

        <parametro nome="mensagemProcessamentoRetorno" tipo="java.lang.String" grupo="SMS" obrigatorio="true"
                   observacao="Campo para descrever mensagem que será enviada para o paciente para confirmação do processamento da resposta do retorno, sendo permitido no máximo 155 caracteres.
            Na criação das mensagens, são aceitos os seguintes caracteres: Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos, Caracteres acentuados: À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ),
            Espaço, ! exclamação, &quot; aspas, # sustenido, %, ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?,
            @, _ underline Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS Variáveis das informações que podem
            ser incluidas na mensagem: $data, $hora, $local_agendamento, $tipo_procedimento, $nome_paciente"
        />

        <parametro nome="mensagemAviso" tipo="java.lang.String" grupo="SMS:Agendamento Local" obrigatorio="true"
                   observacao=" Campo para descrever mensagem que será enviada para o paciente para lembrar do agendamento, sendo permitido no máximo 155 caracteres.
            Na criação das mensagens, são aceitos os seguintes caracteres: Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos,
            Caracteres acentuados: À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ), Espaço, ! exclamação, &quot; aspas, # sustenido, %,
            ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?, @, _ underline
            Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS Variáveis das informações
            que podem ser incluidas na mensagem: $data$, $hora$, $local_agendamento$, $tipo_procedimento$, $nome_paciente$, $nome_completo_paciente$."
        />

        <parametro nome="observacaoComprovanteViagem" tipo="java.lang.String" grupo="TFD"
                   observacao="Observação que será impressa no comprovante de viagem ao confirmar a entrega do TFD ao paciente"
        />

        <parametro nome="procedimentoDeslocamentoPaciente"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento" grupo="TFD:BPA"
                   observacao="Procedimento que vai ser utilizado na geração do BPA para os pacientes que utilizarão o transporte do TFD"
                   obrigatorio="true"
        />

        <parametro nome="procedimentoDeslocamentoAcompanhante"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento" grupo="TFD:BPA"
                   observacao="Procedimento que vai ser utilizado na geração do BPA para os acompanhantes dos pacientes que utilizarão o transporte do TFD"
                   obrigatorio="true"
        />

        <parametro nome="valorDivisaoDistancia" tipo="java.lang.Long" grupo="TFD:BPA"
                   observacao="Valor utilizado para calcular a quantidade do procedimento na geração do BPA com base na distância da cidade destino da viagem"
                   obrigatorio="true"
        />

        <parametro nome="observacaoFichaAgendamento" tipo="java.lang.String"
                   observacao="Observação que vai ser impressa na impressão da ficha de agendamento que é enviado para as unidades"
        />

        <parametro nome="MensagemPadraoComprovanteAgendamento" tipo="java.lang.String"
                   observacao="Mensagem para ser apresentada no campo observação do comprovante de agendamento caso não possua no cadastro da agenda (Tela 341) uma observação especifica."
        />

        <parametro nome="aprovaAgendaCadastro" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se vai ser feito aprovação da agenda após o cadastro ou será aprovado direto no cadastro."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="obrigatorioOrigemDaSolicitacao" tipo="java.lang.String" defaultValue="N"
                   observacao="Define a obrigatoriedade no preenchimento do campo Origem da Solicitação na Solicitação de Agendamento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="CentralAgendamento" tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao="Define a unidade que é a central e agendamentos do município."
                   obrigatorio="true"
        />

        <parametro nome="obrigatorioInformacoesAdicionaisDoPaciente" tipo="java.lang.String" defaultValue="N"
                   observacao="Define a obrigatoriedade dos dados de CNS, CPF, CEP e celular no cadastro do cliente na tela de Solicitação de agendamento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ControlaEnvioSolicitacoes" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se vai ser feito o controle dos lotes de envio das solicitações entre a unidade e a central de agendamento"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="profissionalPadraoGeracaoBpaViagensTfd" tipo="br.com.ksisolucoes.vo.cadsus.Profissional"
                   grupo="TFD:BPA:Profissional"
                   observacao="Profissional padrão para a geração do BPA das viagens do TFD"
                   obrigatorio="true"
        />

        <!--        <parametro nome="CboPadraoGeracaoBpaViagensTfd" tipo="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo" grupo="TFD:BPA:CBO"
                           observacao="CBO padrão para a geração do BPA das viagens do TFD"
                           obrigatorio="true"
        />-->

        <parametro nome="UnidadeFaturamentoTfdGeracaoBpaViagensTfd" tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   grupo="TFD:BPA:Unidade"
                   observacao="Unidade onde é feito o faturamento TFD, para a geração do BPA das viagens do TFD"
                   obrigatorio="true"
        />

        <parametro nome="UsuarioResponsavelAgendamentoSolicitacao" tipo="br.com.ksisolucoes.vo.controle.Usuario"
                   grupo="SMS" obrigatorio="false"
                   observacao="Usuário que vai receber as mensagens do processo de envio do SMS dos agendamentos"
        />

        <parametro nome="UsuarioResponsavelAnaliseRespostasUsuarios" tipo="br.com.ksisolucoes.vo.controle.Usuario"
                   grupo="SMS" obrigatorio="false"
                   observacao="Usuário que vai receber/analisar as mensagens de respostas dos pacientes"
        />

        <parametro nome="mensagemAvisoCancelamento" tipo="java.lang.String" grupo="SMS:Cancelamento Agendamento"
                   obrigatorio="true"
                   observacao="Campo para descrever mensagem que será enviada para o paciente para informar que o agendamento foi cancelado, sendo permitido no máximo 155 caracteres.
            Na criação das mensagens, são aceitos os seguintes caracteres: Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos,
            Caracteres acentuados: À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ), Espaço, ! exclamação, &quot; aspas, # sustenido, %,
            ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?, @, _ underline
            Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS. Variáveis das informações
            que podem ser incluidas na mensagem: $data$, $hora$, $local_agendamento$, $tipo_procedimento$, $nome_paciente$, $nome_completo_paciente$."
        />

        <parametro nome="mensagemAvisoRemanejamento" tipo="java.lang.String" grupo="SMS:Remanejamento"
                   obrigatorio="true"
                   observacao="Campo para descrever mensagem que será enviada para o paciente para avisar que seu agendamento foi remanejado, sendo permitido no máximo 155 caracteres.
            Na criação das mensagens, são aceitos os seguintes caracteres: Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos,
            Caracteres acentuados: À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ), Espaço, ! exclamação, &quot; aspas, # sustenido, %,
            ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?, @, _ underline
            Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS. Variáveis das informações
            que podem ser incluidas na mensagem: $data$, $hora$, $local_agendamento$, $tipo_procedimento$, $nome_paciente$, $nome_completo_paciente$, $data_anterior$, $hora_anterior$, $local_agendamento_anterior$."
        />

        <parametro nome="habilitaPaginaPublicaListaEspera" tipo="java.lang.String" grupo="Lista Pública" defaultValue="N"
                   observacao="Define se será possível acessar a lista de espera externa (${URL}/fila-espera)"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="habilitaPaginaPublicaNoModelo" tipo="java.lang.String" grupo="Lista Pública" defaultValue="P"
                   observacao="Define qual modelo de lista pública será mostrando em URL/lista-publica"
        >
            <checkValues value="P" rotulo="rotulo_modelo_padrao"/>
            <checkValues value="NP" rotulo="rotulo_modelo_nova_petropolis"/>
        </parametro>

        <parametro nome="utilizarFiltroExameListaPublica" tipo="java.lang.Long" grupo="Lista Pública" defaultValue="1"
                   observacao="Define se será utilizado o filtro de exames na lista pública">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="exibirColunaTempoMediodeEspera" tipo="java.lang.String" grupo="Lista Pública" defaultValue="N"
                   observacao="Define se será exibido a coluna 'Estimativa de Espera' na lista pública"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="quantidadedeAgendamentoConsideradaparaoCalculodeMediadeEspera" tipo="java.lang.Long" grupo="Lista Pública" defaultValue="10"
                   observacao="Quantidade de agendamento à considerar para o cálculo da média de tempo de espera na fila pública"

        />

        <parametro nome="SepararFilaSolicitacao" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se as solicitações serão separadas em fila de regulação e fila de espera.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibirAbaListaAgendamentosListaPublica" tipo="java.lang.String" grupo="Lista Pública" defaultValue="S"
                   observacao="Define se será exibido a aba de lista de agendamentos na tela da Lista Pública."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibirAbaListaDevolvidosListaPublica" tipo="java.lang.String" grupo="Lista Pública" defaultValue="S"
                   observacao="Define se será exibido a aba de devolvidos na tela da Lista Pública."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibirAbaListaAtendidosListaPublica" tipo="java.lang.String" grupo="Lista Pública" defaultValue="S"
                   observacao="Define se será exibido a aba de atendidos na tela da Lista Pública."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="mediaDataPrevistaFilaEsperaPublica" tipo="java.lang.Long" grupo="Lista Pública" defaultValue="3"
                   observacao="Define os meses em que será realizado o calculo da média do tempo de espera."
        >
            <checkValues value="1" rotulo="1"/>
            <checkValues value="2" rotulo="2"/>
            <checkValues value="3" rotulo="3"/>
            <checkValues value="4" rotulo="4"/>
            <checkValues value="5" rotulo="5"/>
            <checkValues value="6" rotulo="6"/>
            <checkValues value="7" rotulo="7"/>
            <checkValues value="8" rotulo="8"/>
            <checkValues value="9" rotulo="9"/>
            <checkValues value="10" rotulo="10"/>
            <checkValues value="11" rotulo="11"/>
            <checkValues value="12" rotulo="12"/>
        </parametro>

        <parametro nome="OrdenarFaixaEtaria"
                   tipo="java.lang.String"
                   grupo="Ordenação"
                   defaultValue="N"
                   observacao="Define se a lista de espera vai incluir na ordenação a prioridade configurada na faixa etária."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="consideraNumeraçãoAuxiliarNaPosicao"
                   tipo="java.lang.String"
                   grupo="Ordenação"
                   defaultValue="N"
                   observacao="Define se a numeração auxiliar será critério para cálculo da posição da fila de espera."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="FaixaEtaria"
                   tipo="br.com.ksisolucoes.vo.basico.FaixaEtaria"
                   grupo="Ordenação"
                   observacao="Define qual a faixa etária que será utilizada na ordenação"
        />

        <parametro nome="DiasParaNotificacaoNaoComparecimento"
                   tipo="java.lang.Integer"
                   grupo="Notificações"
                   defaultValue="90"
                   observacao="Número de dias para considerar os agendamentos que o paciente não compareceu, para gerar no processo de notificação"
        />

        <parametro nome="numeroViasImpressaoLaudoTFD" tipo="java.lang.Long" grupo="TFD" obrigatorio="true"
                   defaultValue="2"
                   observacao="Define quantidade de vias a serem emitidas na impressão do Laudo TFD do atendimento.">

            <checkValues value="1" rotulo="1"/>
            <checkValues value="2" rotulo="2"/>
        </parametro>

        <parametro nome="exibeObservacaoSolicitacaoAgendamento" tipo="java.lang.Long" obrigatorio="true"
                   defaultValue="0"
                   observacao="Define se será exibido a observação da solicitação na tela de consulta.">

            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permiteInformarUrgenteEncaminhamentoEspecialista" tipo="java.lang.Long" obrigatorio="true"
                   defaultValue="1"
                   observacao="Define se o campo prioridade vai estar habilitado no cadastro do encaminhamento especialista dentro do atendimento.">

            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="tipoControleRegulação" tipo="java.lang.String" grupo="Regulação" defaultValue="T"
                   observacao="Define o tipo de controle que será utilizado para os processos de encaminhamento e regulação: TIPO PROCEDIMENTO: No momento da solicitação, será possível definir a prioridade da solicitação: Eletivo, Brevidade ou Urgente. SOLICITAÇÃO DE PRIORIDADE: No momento da solicitação, não será possível definir a prioridade, apenas solicitar para a regulação uma avaliação de prioridade, que poderá ser atendida ou não pelo regulador. SOLICITAÇÃO DO PROFISSIONAL: No momento da solicitação, não será possível definir a prioridade, apenas indicar se a solicitação deverá ir para a fila cronológica ou para a avaliação do regulador.">
            <checkValues value="T" rotulo="rotulo_tipo_procedimento"/>
            <checkValues value="S" rotulo="rotulo_solicitacao_prioridade"/>
            <checkValues value="P" rotulo="rotulo_solicitacao_profissional"/>
        </parametro>

        <parametro nome="PermiteSelecionarFilaEspera"
                   tipo="java.lang.String"
                   grupo="Regulação"
                   defaultValue="N"
                   observacao="Quando ligado permite que o gestor selecione para qual fila de espera o sistema deve enviar a solicitação. Tela 346 ou 777."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>


        <parametro nome="DefineQuemRecebeNotificaçãoDevolução" tipo="java.lang.String" grupo="Regulação" defaultValue="unidadeResponsavel"
                   observacao="Define quem recebe a notificação da devolução de uma solicitação."
        >
            <checkValues value="unidadeResponsavel" rotulo="rotulo_unidade_responsavel"/>
            <checkValues value="origemSolicitacao" rotulo="rotulo_origem_solicitacao"/>
        </parametro>

        <parametro nome="utilizaResetManualFilaEspera" tipo="java.lang.Long" grupo="Regulação" defaultValue="0"
                   observacao="Define se a Fila de Espera é resetada manualmente pela tela de Agendamento da Lista de Espera por Lote (951).">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ativaRegraPrioridadeConsumoVaga" tipo="java.lang.Long" grupo="Regulação" defaultValue="0"
                   observacao="Define a prioridade do consumo de vagas no agendamento Manual e Automático.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="habilitaClassificaçãoRiscoEncaminhamentoEspecialista" tipo="java.lang.Long" grupo="Regulação" defaultValue="0"
                   observacao="Quando habilitado vai substituir o campo prioridade no nó Encaminhamento Especialista pela Classificação de Risco.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permiteInformarUnidadeExecutante" tipo="java.lang.Long" grupo="Regulação" defaultValue="1"
                   observacao="“Define se será possível selecionar a unidade executante para o agendamento nas telas 346 e 777">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="justificarMudançaPosicaoFilaEsperaPublica" tipo="java.lang.Long" defaultValue="0" grupo="Lista Pública"
                   observacao="Define se será obrigatório informar uma justificativa sempre que for executada uma ação que possa alterar a posição de um paciente na fila de espera pública. Caso o valor seja “Sim”, será obrigatório o preenchimento do campo “Justificativa” na avaliação de prioridade da tela 350, na alteração de posição da fila na tela 690 e na alteração de classificação de risco das telas 350 e 777">

            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="MensagemFilaEsperaPublica" tipo="java.lang.String" defaultValue="" grupo="Lista Pública"
                   observacao="Campo para descrever a mensagem que será mostrada na lista de espera pública.">
        </parametro>

        <parametro nome="tempoReservaVagaTipoProcedimento" tipo="java.lang.Long" defaultValue="5" grupo="Regulação"
                   observacao="Define o tempo limite em minutos que a vaga permanecerá reservada para o usuário, na tela de Resumo Contato para Agendamento (825), a partir da sua confirmação."
        />

        <parametro nome="defineDiasBloqueio" tipo="java.lang.Integer" grupo="Regulação" defaultValue="0"
                   observacao="Define a quantidade de dias em que a solicitação de agendamento permanecerá bloqueada na tela Resumo Contato para Agendamento."
        />

        <parametro nome="diasLimiteAgendamentoListasEspera" tipo="java.lang.Long" grupo="Regulação" defaultValue="0"
                   obrigatorio="true"
                   observacao="Define a partir de quantos dias úteis após a data atual o sistema exibirá as vagas para agendamento nas telas 346 e 777."
        />

        <parametro nome="diasLimiteCancelamentoSolicitacoesDevolvidas" tipo="java.lang.Long" grupo="Regulação"
                   observacao="Define a partir de quantos dias após a data da devolução as solicitações devolvidas serão canceladas (expiradas), conforme a habilitação do processo Cancelar Solicitações de Agendamentos devolvidas."
        />

        <parametro nome="diasLimiteParaDevolucaoDasSolicitacoes" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define os dias limite que a solicitação ficará aguardando agendamento na tela 346 ou 777 até ser devolvida para unidade origem."
        />

        <parametro nome="defineDiasCalculoVagasDisponiveis" tipo="java.lang.Integer" grupo="Regulação" defaultValue="0"
                   observacao="Define a quantidade de dias corridos para gerar o período (a partir da data atual) utilizado para o cálculo de vagas disponíveis, na tela de Resumo Contato para Agendamento. (Zero = Dia Atual)"
        />

        <parametro nome="defineDiasCalculoVagasRetornadas" tipo="java.lang.Integer" grupo="Regulação" defaultValue="0"
                   observacao="Define a quantidade de dias corridos para gerar o período (a partir da data atual) utilizado para o cálculo de vagas retornadas, na tela de Resumo Contato para Agendamento. (Zero = Default 365 Dias)"
        />

        <parametro nome="utilizaPrioridadeUnidadeOrigemESolicitante" tipo="java.lang.Long" grupo="Regulação" defaultValue="0"
                   observacao="Define se irá utilizar prioridade para Unidade de Origem da Solicitação, caso a mesma seja informada na solicitação, e subsequente validando se a unidade solicitante também executa os procedimentos.">

            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="utilizaNovoServicoRegulacao" tipo="java.lang.Long" grupo="Regulação" obrigatorio="true" defaultValue="0"
                   observacao="Informa se irá utilizar o novo serviço de processamento de fila de agendamento.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="utilizaConfigProximidadevsPrestadorvsProcedimento" tipo="java.lang.String" grupo="Regulação" defaultValue="N"
                   observacao="Define se no agendamento em lote verificará se para o grupo de procedimento será utilizado a configuração da Proximidade Solicitante vs Executante (954).">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="controlaCotaFinanceiraPPI" tipo="java.lang.String" grupo="Regulação" defaultValue="N"
                   observacao="Define se o agendamento deve controlar a cota dos municípios pactuados na PPI."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="controlaCotaFinanceiraPPIDataInicio" grupo="Regulação" tipo="java.util.Date"
                   observacao="Os estornos de valores para a PPI passarão a considerar os agendamentos corridos a partir dessa data."
        />

        <parametro nome="controlaCotaFinanceiraPPITetoMaximoAntecipacao" grupo="Regulação" tipo="java.lang.Double" defaultValue="100"
                   observacao="Define o teto máximo em porcentagem (%) que a antecipação de saldo pode retirar do saldo total das PPIs da mesma microrregião." />

        <parametro nome="controlaCotaFinanceiraPPIValidaProcedimento611" tipo="java.lang.String" grupo="Regulação" defaultValue="N"
                   observacao="Na tela 611, no cadastro de uma solicitação de agendamento, aplica filtro no campo Exames e Exames Padrão para os procedimentos pactuados com a unidade responsável tela 155."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="controlaCotaFinanceiraPPInocadastrotela611e346" tipo="java.lang.String" grupo="Regulação" defaultValue="N"
                   observacao="Quando ligado, valida a PPI para Unidades responsáveis que seja secretaria, no cadastro da solicitação, pela telas 346 e 611."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="URLwqr" tipo="java.lang.String" grupo="Regulação" obrigatorio="true"
                   observacao="Define URL para comunicação do Celk Saúde com o Wait Queue Regulator."
        />

        <parametro nome="limiteSolicitacaoAgendamento" tipo="java.lang.Integer" defaultValue="0"
                   observacao="Define o limite de solicitações de agendamento que devem ser exibidas na tela de Contato para Agendamento."
        />

        <parametro nome="questionarSolicitacaoAgendamentoUnidadeDiferentePaciente" tipo="java.lang.Long"
                   defaultValue="0" grupo="Solicitação Agendamento"
                   observacao="Define se o usuário será questionado ao agendar uma solicitação de agendamento de retorno e a unidade logada for diferente da unidade do paciente.">

            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="informaProcedimentoCiapManual" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se as descrições do Procedimento e do CIAP serão informadas manualmente na impressão da Relação dos Agendamentos (200)."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="permitirAgendamentoRetroativo" tipo="java.lang.Long" defaultValue="1"
                   observacao="Permite que seja agendado para um horário retroativo ao horário atual.">

            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="validaInformaçãoCIDdatainicio" tipo="java.util.Date"
                   observacao="Data considerada para validar o CID no cadastro ou edição de uma solicitação de agendamento pelas telas 611, 350 e 346."

        />


        <parametro nome="horasLimiteAcolhimentoExterno" tipo="java.lang.Long" defaultValue="0" obrigatorio="false"
                   observacao="Define em horas, o tempo limite (dias) da consulta da agenda do serviço de acolhimento externo"/>

        <parametro nome="horasPeriodoAcolhimentoExterno" tipo="java.lang.Long" defaultValue="0" obrigatorio="false"
                   observacao="Define a quantidade de horas que será cosiderada para iniciar a contagem do tempo limite (dias) da consulta da agenda do serviço de acolhimento externo"/>

        <parametro nome="horasLimiteVerificaçãoOcorrênciasAIH" grupo="AIH" tipo="java.lang.Long" defaultValue="24"
                   observacao="Define a quantidade de horas retroativas que a consulta vai considerar para apresentar as AIH na tela de Ocorrências Recentes."/>

        <parametro nome="DataSolicitacaoAgendamentoUtilizaDataAtual" tipo="java.lang.String" grupo="Solicitação Agendamento" defaultValue="N"
                   observacao="Define se na tela 346 e 611 o sistema apresentará o campo Data da Solicitação com a data atual"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="permiteConfirmacaoPosterior" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se na tela 318, no nó Confirmação de Presença, a data de autorização pode ser informada com uma data anterior a data atual"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="FPOPrestadorDiasAvisoVecimento" tipo="java.lang.Long" defaultValue="0" obrigatorio="false"
            observacao="Define a quantidade de dias antecedentes ao fim do contrato que o usuário deve ser notificado." />

        <parametro nome="UtilizaPriorizaçãoDeMunicipesNaRegulação" tipo="java.lang.Long" grupo="Regulação" defaultValue="0"
                   observacao="Quando ativado, libera na tela 951 uma flag para o usuário indicar nos parâmetros enviados que deseja processar apenas solicitações de munícipes."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="AvisoInclusãoFilaDeEspera" tipo="java.lang.String"
                   observacao="Campo para descrever mensagem que será enviada para o paciente quando uma solicitação de agendamento foi cadastrado para a fila de espera,
                   sendo permitido no máximo 155 caracteres, quando SMS. Na criação das mensagens, são aceitos os seguintes caracteres:
                   Caracteres de a até z ( maiúsculas e minúsculas ), Caracteres numéricos, Caracteres acentuados:
                   À, Á, Â, Ã, Ç, É, Ê, Í, Ó, Ô, Õ, Ú ( maiúsculas e minúsculas ), Espaço, ! exclamação, aspas, # sustenido, %, ' apóstrofo, (, ), * asterisco, +, , vírgula, - traço, . ponto, / barra, : dois pontos, ; ponto e vígula, =, ?, @, _ underline.
                   Caracteres com acentuação serão aceitos, mas a acentuação será removida posteriormente pela API de envio de SMS. Variáveis das informações que podem ser incluidas na mensagem: $nome_paciente$, $tipo_procedimento$, $dataCadastro$, $dataSolicitacao$, $urlMunicipio$"
                   grupo="SMS"
                   defaultValue="Olá, $nome_paciente$, informamos que você foi incluído na fila de espera para $tipo_procedimento$, no dia $dataCadastro$. Para acompanhamento da sua solicitação, acesse o link: $urlMunicipio$/lista-publica"/>

        <parametro nome="utilizaTabelaCustomizadaParaInformacaoDaClinica" tipo="java.lang.String" grupo="AIH" defaultValue="N"
                   observacao="Quanto ativado faz com que as opções do campo 'Clínica', informação obrigatória em um cadastro de nova AIH, apresente informações customizadas (Clinica + especialidade)."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ValidaDadosClinicosnoCadastrodaSolicitação" tipo="java.lang.String" grupo="Solicitação Agendamento" defaultValue="N"
                   observacao="Quando habilitado, o campo 'Dados clinicos' será de preenchimento obrigatório quando cadastrado pelas tela 346 e 611."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>
    </modulo>

    <!--AUDITORIA-->
    <modulo nome="22">

        <parametro nome="Auditoria de Guia de Procedimento" tipo="java.lang.String" defaultValue="N"
                   observacao="Análise do auditor para conferência de prioridade e de procedimento após o cadastro da guia">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="Permite agendamento sem auditoria" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite agendamento de guia de procedimento sem auditoria">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>
    </modulo>

    <!--Materiais-->
    <modulo nome="12">

        <parametro nome="habilitaVencimentoInsulinaFrascoEAmpola42Dias" tipo="java.lang.String" defaultValue="N"
                   observacao="Ajusta a regra de cálculo de próxima retirada de insulina frasco no sistema, considerando a validade do frasco após aberto (42 dias), e não apenas a posologia diária, garantindo que o sistema permita nova retirada conforme a prática clínica.."
                   grupo="Dispensação"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="emailInconsistenciaMovimentoEstoque" tipo="java.lang.String" grupo="Estoque"
                   defaultValue="<EMAIL>"
                   observacao="Define o endereço de email que deve ser utilizado para notificar sobre inconsistências do movimento de estoque."
        />

        <parametro nome="HabilitaCalculoEstoque"
                   tipo="java.lang.String"
                   grupo="Estoque"
                   defaultValue="N"
                   observacao="Habilita a geração do cálculo de estoque pela tela Materiais / Estoque / Lançamentos / Cálculo de estoque."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="EmiteAlertaSemDomicilio"
                   tipo="java.lang.String"
                   grupo="Dispensação"
                   defaultValue="N"
                   observacao="Define se no momento da dispensa será emitido alerta caso paciente não esteja vinculado à um domicílio."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="imprimirComprovanteEmprestimo"
                   tipo="java.lang.String"
                   grupo="Estoque"
                   defaultValue="N"
                   observacao="Permite ao usuário imprimir o comprovante de empréstimos (Tela 467) independente da situação do empréstimo."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="RegistrarAtendimento"
                   tipo="java.lang.String"
                   grupo="Dispensação"
                   defaultValue="N"
                   observacao="Habilita a opção de realizar o registro de um atendimento através da dispensação quando o profissional estiver habilitado, ou seja, o CBO do profisisonal do usuário logado esta configurado nos Grupos do cadastro do Tipo Atendimento (229) configurado no parametroGEM.TipoAtendimentoDispensação."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ApresentaColunaCódigoMaterial"
                   tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Define se na lista de consulta de estoque de materiais pública será apresentada a coluna com o código do medicamento/material."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="SinalizacaoCoresSaldoEstoque"
                   tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Sinalização por cores nas linhas correspondentes aos produtos próximos ao vencimento e vencidos."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="TempoProdutoProximoVencimento" tipo="java.lang.Integer"
                   defaultValue="30"
                   observacao="Estabelecer o período de tempo para considerar um produto como próximo ao vencimento"
        />

        <parametro nome="TipoAtendimentoDispensacao" grupo="Dispensação"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
                   observacao="Tipo de Atendimento que será utilizado para geração do atendimento na dispensação. Deve possuir cadastro do relacionamento Natureza Procura x Tipo Atendimento."
        />
        <parametro nome="AtualizaPaginaImpressaoPrescricao" grupo="Dispensação"
                   tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Permite atualização da pagina Consulta Impressão Prescrisão"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ObrigaResponsavelMenorIdade" grupo="Dispensação"
                   tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Definirá se o campo Responsável será apresentado para preenchimento dentro da dispens"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>


        <parametro nome="ValidarEstoquePedidoAlmoxarifado"
                   tipo="java.lang.String"
                   grupo="Pedido"
                   defaultValue="S"
                   observacao="Flag utilizada no pedido de almoxarifado para possibilitar o cadastro de produtos que não possuem estoque no almoxarifado selecionado"
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="tipoResumoImpressaoSeparacaoPedido"
                   tipo="java.lang.String"
                   grupo="Pedido"
                   defaultValue="S"
                   observacao="Define o tipo de resumo da impressão da separação do pedido."
        >
            <checkValues value="S" rotulo="rotulo_subgrupo"/>
            <checkValues value="G" rotulo="rotulo_grupo"/>
        </parametro>

        <parametro grupo="Recebimento"
                   nome="PermiteConfirmarNotaEntradaAoSalvar"
                   tipo="java.lang.Long"
                   defaultValue="0"
                   observacao="Define se será permitido confirmar a nota fiscal de entrada ao salvar o cadastro da mesma."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ImprimirValorPedido"
                   tipo="java.lang.String"
                   grupo="Pedido"
                   defaultValue="N"
                   observacao="Flag utilizada para exibir o valor do pedido na impressão do pedido transferência"
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="EnvioPedidoSeparacao"
                   tipo="java.lang.String"
                   grupo="Pedido"
                   defaultValue="N"
                   observacao="Flag utilizada para realizar o envio do pedido de transferência pelas telas de cadastro e separação do pedido de transferência"
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="AdicionarProximoLoteVencerSeparacaoPedido"
                   tipo="java.lang.String"
                   grupo="Pedido"
                   defaultValue="N"
                   observacao="Define se na tela de Separação do Pedido de Transferência (130) será adicionado automaticamente o(s) lote(s) mais próximo(s) a vencer(em) do produto."
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="PermiteGerarPedidoSeparacao"
                   tipo="java.lang.String"
                   grupo="Pedido"
                   defaultValue="N"
                   observacao="Permitir que na tela de Separação do Pedido de Transferência (130) seja criado um novo Pedido com os produtos/quantidades faltantes ao Pedido em Separação."
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ImprimirPedidoSeparacao"
                   tipo="java.lang.String"
                   grupo="Pedido"
                   defaultValue="N"
                   observacao="Flag utilizada para realizar a impressão do pedido de embarque na separação do pedido de transferência"
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="PeriodoConsumo" tipo="java.lang.Long" grupo="Pedido" obrigatorio="true" defaultValue="1"
                   observacao="Período utilizado para Realizar o Cálculo do Consumo do Produto no Pedido de Transferência.">
            <checkValues value="1" rotulo="rotulo_mes_anterior"/>
            <checkValues value="2" rotulo="rotulo_ultimos_trinta_dias"/>
            <checkValues value="3" rotulo="rotulo_ultimos_noventa_dias"/>
        </parametro>

        <parametro nome="chave" tipo="java.lang.String" grupo="Pedido:Integração Ambulatorial"
                   defaultValue=""
                   observacao="Chave da integração com o sistema Hospital envio e recebimento dos pedidos de Almoxarifado."
        />

        <parametro nome="id" tipo="java.lang.String" grupo="Pedido:Integração Ambulatorial"
                   defaultValue=""
                   observacao="Id da integração com o sistema Hospital envio e recebimento dos pedidos de Almoxarifado."
        />

        <parametro nome="url" tipo="java.lang.String" grupo="Pedido:Integração Ambulatorial"
                   defaultValue=""
                   observacao="URL do servidor do sistema Hospital."
        />

        <parametro nome="obrigatorioInformarDataValidadeCadastroProdutosSolicitados"
                   tipo="java.lang.String"
                   grupo="Dispensação"
                   defaultValue="N"
                   observacao="Define se será obrigatório informar a data de validade do produto no Cadastro de Solicitação de Produtos (520)."
                   obrigatorio="false"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ValidaSaldoDispensacaoPrescricao"
                   tipo="java.lang.String"
                   grupo="Dispensação"
                   defaultValue="N"
                   observacao="Define se a dispensação das prescrições internas, usada nos hospitais, deve validar o saldo da quantidade que foi prescrita no receituário."
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="validaDispensacaoPacienteProvisorio"
                   tipo="java.lang.String"
                   grupo="Dispensação"
                   defaultValue="S"
                   observacao="Define se irá validar se o cadastro do paciente é provisório ao dispensar. Se for provisório, pode dispensar apenas um dia, para dispensar em outro dia se deve completar o cadastro."
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="tipoDocumentoTransferenciaEntrada" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="(Definir)"
                   obrigatorio="true"
        />

        <parametro nome="informaCodigoTCEProdutos" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se no Cadastro de Medicamentos será permitido informar o campo Código do TCE (Tribunal de Contas do Estado)."
                   obrigatorio="false"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ManterMedicamentoContínuo" tipo="java.lang.String" defaultValue="N"
                   observacao="impede a remoção indevida de medicamentos contínuos ao excluir uma receita clonada"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>


        <parametro nome="tipoDocumentoTransferenciaSaida" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="(Definir)"
                   obrigatorio="true"
        />

        <parametro grupo="Dispensação" nome="imprimeDispensacao" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se imprime a dispensação logo após a dispensação"
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="validadeReceitaUsoContinuo" tipo="java.lang.Long" grupo="Dispensação"
                   defaultValue="180"
                   observacao="Validades em dias das Receitas de Uso Continuo"
        />

        <parametro nome="validadeReceitaBasica" tipo="java.lang.Long" grupo="Dispensação"
                   defaultValue="30"
                   observacao="Validades em dias das Receitas Básicas"
        />

        <parametro nome="validadeReceitaBranca" tipo="java.lang.Long" grupo="Dispensação"
                   defaultValue="30"
                   observacao="Validades em dias das Receitas Brancas"
        />

        <parametro nome="validadeReceitaAzul" tipo="java.lang.Long" grupo="Dispensação"
                   defaultValue="30"
                   observacao="Validades em dias das Receitas Azuis"
        />

        <parametro nome="validadeReceitaAmarela" tipo="java.lang.Long" grupo="Dispensação"
                   defaultValue="30"
                   observacao="Validades em dias das Receitas Amarelas"
        />

        <parametro nome="validadeReceitaBrancaC3" tipo="java.lang.Long" grupo="Dispensação"
                   defaultValue="20"
                   observacao="Validades em dias das Receitas Brancas C3"
        />

        <parametro nome="diasToleranciaParaDispensacao" tipo="java.lang.Long" grupo="Dispensação"
                   defaultValue="5"
                   observacao="Número de dias em que o sistema vai liberar antes do prazo da próxima dispensação do medicamento"
        />

        <parametro nome="diasDataDispensacao" tipo="java.lang.Long" grupo="Dispensação"
                   defaultValue="0"
                   observacao="Define o número de dias retroativo que pode ser informado na data da dispensação"
        />

        <parametro nome="permiteDispensarSemDataLimite" tipo="java.lang.Long" grupo="Dispensação"
                   observacao="De acordo com a PORTARIA SES Nº 208/2020, este parâmetro permite dispensar receitas do Tipo Azul e do Tipo Amarela sem prazo final."
                   defaultValue="0">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ProcedimentoFaturadoDispensa" tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   grupo="Dispensação"
                   observacao="Procedimento que será Faturado na Geração do BPA para cada dispensação realizada."
                   obrigatorio="false"
        />

        <parametro nome="FormaApresentacaoImpressaoPedido" tipo="java.lang.String" defaultValue="G" grupo="Pedido"
                   observacao="Forma de Apresentação que deve sair na impressão do processo de separação dos pedidos."
        >
            <checkValues value="G" rotulo="rotulo_geral"/>
            <checkValues value="F" rotulo="rotulo_fornecedor"/>
        </parametro>

        <parametro grupo="Dispensação" nome="tipoDocumentoEntradaDevolucaoMedicamentosPaciente"
                   tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Tipo de Documento para movimento de estoque de Entrada por devolução de medicamentos não utilizados pelo paciente"
                   obrigatorio="true"
        />

        <parametro
                grupo="Dispensação"
                nome="LimiteParaDispensarPrescricoesAtendimentoFechado"
                observacao="Limite(em horas) para dispensar prescrições de um atendimento fechado."
                defaultValue="0"
                tipo="java.lang.Long"
        />

        <parametro grupo="Recebimento" nome="percentualDiferenca" tipo="java.lang.Long"
                   observacao="Percentual de divergência entre o preço que esta sendo informado e o da última entrada para prevenir digitação de preços errados. Zero não faz a validação."
                   defaultValue="0"
        />

        <parametro grupo="Recebimento" nome="entradaNotaFiscalParcial" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se será possível realizar a entrada parcial dos itens na confirmação da Entrada de Materiais/Medicamentos."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro grupo="Recebimento" nome="tipoDocumentoEntradaNFe"
                   tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Indica o tipo de documento que será sugerido ao importar o XML de uma Nota Fiscal eletrônica."
        />

        <parametro grupo="Dispensação" nome="PermiteCadastroPaciente" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se vai ser possível fazer cadastro simplificado do paciente na hora da dispensação."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro grupo="Dispensação" nome="ImprimirDispensacaoImpessoraTermica" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se o Comprovante da Dispensação de Medicamentos será impresso em uma impressora do tipo térmica."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro grupo="Dispensação:Devolução" nome="TipoConsulta" tipo="java.lang.Long" defaultValue="0"
                   observacao=" Tipo da consulta a ser feita para listar a dispensações para a devolução, o tipo por atendimento a dispensação deve estar relacionado a um atendimento, que é utilizado nos hospitais."
        >
            <checkValues value="0" rotulo="rotulo_atendimento"/>
            <checkValues value="1" rotulo="rotulo_data_dispensacao"/>
        </parametro>

        <parametro grupo="Dispensação:Devolução" nome="diasDevolução" tipo="java.lang.Long"
                   observacao=" Número de dias após a dispensação que será permitido fazer a devolução."
                   defaultValue="1"
        />

        <parametro nome="diferencaMaximaSaldoInicial" tipo="java.lang.Double" grupo="Estoque"
                   defaultValue="0"
                   observacao="Define um valor máximo na diferença entre os saldo anterior e saldo inicial entre os meses que será permitido no Resumo do Saldo do Estoque"
        />

        <parametro nome="numeroContagemConfirmacaoInventario" tipo="java.lang.Long" defaultValue="1" grupo="Estoque"
                   observacao="Define o número total de contagem e recontagem no lançamento do inventário, onde na última contagem assumirá o valor do mesmo."
        >
            <checkValues value="1" rotulo="rotulo_uma_vez_descritivo"/>
            <checkValues value="2" rotulo="rotulo_duas_vezes_descritivo"/>
            <checkValues value="3" rotulo="rotulo_tres_vezes_descritivo"/>
        </parametro>

        <parametro nome="tipoImpressoraEtiqueta" tipo="java.lang.Long" defaultValue="1" grupo="Estoque"
                   observacao="Define o tipo de impressora que irá usar para imprimir as etiquetas"
        >
            <checkValues value="0" rotulo="rotulo_argox"/>
            <checkValues value="1" rotulo="rotulo_zebra3318"/>
            <checkValues value="2" rotulo="rotulo_zebra315"/>
            <checkValues value="3" rotulo="rotulo_zebra3321"/>
            <checkValues value="4" rotulo="rotulo_elgin3434"/>
            <checkValues value="5" rotulo="rotulo_elgin3321"/>
        </parametro>

        <parametro nome="tamanhoImpressaoRotulo" tipo="java.lang.Long" defaultValue="0" grupo="Estoque"
                   observacao="Define o tipo de impressora que irá usar para imprimir os rótulos."
        >
            <checkValues value="0" rotulo="rotulo_34x34x3"/>
        </parametro>

        <parametro nome="dataProximaDispensacaoMenosDiasTolerancia" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se será exibida, na tela Dispensação de Medicamentos/Materiais (44), a data da próxima dispensação subtraída do número de dias do parâmetro GEM diasToleranciaParaDispensacao. Obs: apenas visualmente.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="dispensarQuantidadeIgualSolicitadaEmProdutosSolicitados" tipo="java.lang.Long" defaultValue="3"
                   grupo="Dispensação"
                   observacao="Define se será permitido dispensar na tela Dispensação de Produtos Solicitados (522), apenas a quantidade igual à solicitada no Cadastro de Solicitação de Produtos (520).">
            <checkValues value="0" rotulo="rotulo_abaixo"/>
            <checkValues value="1" rotulo="rotulo_acima"/>
            <checkValues value="2" rotulo="rotulo_igual"/>
            <checkValues value="3" rotulo="rotulo_sem_controle"/>
        </parametro>

        <!-- Grupo Hórus -->

        <parametro nome="enderecoEnvioHorus" tipo="java.lang.String" grupo="Hórus"
                   defaultValue="http://horusws.saude.gov.br/horus-ws-service/HorusWSService/HorusWS?wsdl"
                   observacao="Endereço a ser enviado"/>

        <parametro nome="usuarioSCPA" tipo="java.lang.String" grupo="Hórus"
                   observacao="Usuário conforme cadastrado no SCPA."/>

        <parametro nome="senhaSCPA" tipo="java.lang.String" grupo="Hórus"
                   observacao="Senha conforme cadastrado no SCPA."/>

        <parametro nome="usuarioResponsavelAvisoHorus" tipo="br.com.ksisolucoes.vo.controle.Usuario" grupo="Hórus"
                   obrigatorio="true"
                   observacao="Usuário que vai receber as mensagens no caso de erro na validação do xml da sincronização com o Hórus"
        />

        <parametro nome="tipoDocumentoPedidoTransferenciaEntrada"
                   tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento" grupo="Pedido"
                   observacao="Tipo de documento de entrada que será utilizado no pedido de transferência."
                   obrigatorio="true"
        />

        <parametro nome="tipoDocumentoPedidoTransferenciaSaida"
                   tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento" grupo="Pedido"
                   observacao="Tipo de documento de saída que será utilizado no pedido de transferência."
                   obrigatorio="true"
        />

        <parametro nome="tipoDocumentoEstornoPedido" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   grupo="Pedido"
                   observacao="Tipo de documento que será utilizado no estorno do pedido."
                   obrigatorio="true"
        />

        <!-- Grupo Recebimento -->
        <parametro nome="tipoDocumentoRecebimento" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   grupo="Recebimento"
                   observacao="Tipo de documento que será utilizado na Importação do Pedido do Consórcio"
                   obrigatorio="false"
        />

        <parametro nome="serieRecebimento" tipo="br.com.ksisolucoes.vo.financeiro.Serie" grupo="Recebimento"
                   observacao="Número da Série que será utilizada na importação do Pedido do Consórcio"
                   obrigatorio="false"
        />

        <parametro nome="liberaTodosCamposAssinatura"
                   tipo="java.lang.String"
                   grupo="Dispensação"
                   defaultValue="N"
                   observacao="Libera os campos de assinatura: Conferido por e Entregue por. No relatorio Entrega Pedido Transfêrencia."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="utilizaLocalizacaoEstoque"
                   tipo="java.lang.String"
                   grupo="Dispensação"
                   defaultValue="N"
                   observacao="Define se será utilizado o controle de localização de estoque na dispensação de medicamentos/materiais."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="localizacaoEstruturaPadrao" tipo="br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura"
                   grupo="Dispensação"
                   observacao="Define a localização/estrutura padrão utilizada no controle de localização de estoque."
                   obrigatorio="true"
        />
        <parametro nome="buscaCodigoPacienteDispensacaoMedicamentos" tipo="java.lang.String" defaultValue="N"
                   grupo="Dispensação"
                   observacao="Define se será visível o campo de pesquisa por código/referência do paciente na página Dispensação de Medicamentos/Materiais (44)."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="DiasLimiteParaEstornoNotas" tipo="java.lang.Long" grupo="Recebimento"
                   defaultValue="7"
                   observacao="Define o número de dias em que o sistema permitirá realizar o estorno de notas após a data de cadastro"
                   obrigatorio="true"
        />

        <parametro nome="tipoMovimentacaoEstornoPedidoAlmoxarifado"
                   tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao" grupo="Recebimento"
                   observacao="Define o tipo de movimentação padrão para o estorno de pedidos feitos para o almoxarifado"
                   obrigatorio="true"
        />

        <parametro nome="TipoDocumentoEstornoNotas" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   grupo="Recebimento"
                   observacao="Tipo de documento que será utilizado no Estorno de Notas"
                   obrigatorio="true"
        />

        <parametro nome="sugerirPrimeiroLoteVencerSaida" tipo="java.lang.String" defaultValue="N" grupo="Estoque"
                   observacao="Sugere o primeiro lote a vencer nas telas/dialog de saída de medicamento/material."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="sugerirPrimeiroLoteVencerDispensacao" tipo="java.lang.String" defaultValue="N"
                   grupo="Dispensação"
                   observacao="Sugere o primeiro lote a vencer na tela de Dispensação de Medicamentos/Materiais."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="utilizaAutorizacaoFornecimentoDescritivo"
                   tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se será permitido gerar a autorização de fornecimento para controle da quantidade do pregão na ordem de compra."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="utilizarLeitoraCodigoBarrasProduto"
                   tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se será utilizado leitor de código de barras para a movimentação de produtos."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="validarSituacaoCodigoBarrasProduto"
                   tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Define ser no processo de leitura da etiqueta ira validar se a mesma já foi utilizada ou não."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="sugerirQuantidadeNaAF"
                   tipo="java.lang.Long"
                   defaultValue="0"
                   observacao="Define se ao cadastrar uma nova Autorização de Fornecimento na tela Ordem de Compra (353), sistema fará a sugestão automática dos itens que devem ser solicitados, sugerindo inclusive a quantidade a ser solicitada (quantidade sugerida é igual ao consumo dos últimos 3 meses, menos o estoque físico)."
        >
            <checkValues value="1" rotulo="rotulo_sim"/>
            <checkValues value="0" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="bloquearPrecoUnitarioValorTotalEntradaMateriaisMedicamentos"
                   tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se será bloqueado o Preço Unitário e o Valor Total na Entrada de Materiais/Medicamentos com Autorização de Fornecimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibirTodoHistoricoDispensacaoPaciente"
                   tipo="java.lang.String"
                   grupo="Dispensação"
                   defaultValue="N"
                   observacao="Define se irá exibir todo o histórico de dispensação do paciente na tela Dispensação de Medicamentos/Materiais (44)."
                   obrigatorio="true"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <!-- Integração Smar -->
        <parametro nome="integrarSmar" grupo="Smar"
                   obrigatorio="true"
                   tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se o sistema irá integrar com o Smar."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>


        <parametro nome="urlSmarProducao" tipo="java.lang.String" grupo="Smar"
                   obrigatorio="true"
                   observacao="Define a URL de produção para o serviço de integração com a Smar."
        />

        <parametro nome="urlSmarHomologacao" tipo="java.lang.String" grupo="Smar"
                   obrigatorio="true"
                   observacao="Define a URL de homologação para o serviço de integração com a Smar."
        />

        <parametro nome="urlSmartUtilizada"
                   tipo="java.lang.String"
                   grupo="Smar"
                   defaultValue="H"
                   observacao="Define a URL que será utilizada no serviço de integração com a Smar."
                   obrigatorio="true"
        >
            <checkValues value="H" rotulo="rotulo_homologacao"/>
            <checkValues value="P" rotulo="rotulo_producao"/>
        </parametro>

        <parametro nome="chaveSmar" tipo="java.lang.String" grupo="Smar"
                   obrigatorio="true"
                   observacao="Define a chave para o serviço de integração com a Smar."
        />

        <parametro nome="apiKeySmar" tipo="java.lang.String" grupo="Smar"
                   obrigatorio="true"
                   observacao="Define a api key para o serviço de integração com a Smar."
        />

        <parametro nome="dataInicioIntegraçãoSmar" grupo="Smar" tipo="java.util.Date"
                   observacao="Parametrizar início da integração do novo sistema Smar a partir dessa data."
                   defaultValue="Fri March 01 00:00:00 BRT 2024"
        />

        <parametro nome="usuarioSmar" tipo="java.lang.String" grupo="Smar"
                   obrigatorio="true"
                   observacao="Define o usuário para o serviço de integração com a Smar."
        />

        <!-- Fim Integração Smar -->

        <parametro nome="urlBranetProducao" tipo="java.lang.String" grupo="Branet:Integração Almoxarifado"
                   obrigatorio="true"
                   observacao="Define a URL de produção para o serviço de integração com a Branet."
        />

        <parametro nome="urlBranetHomologacao" tipo="java.lang.String" grupo="Branet:Integração Almoxarifado"
                   obrigatorio="true"
                   observacao="Define a URL de homologação para o serviço de integração com a Branet."
        />

        <parametro nome="chaveAssinaturaBranet" tipo="java.lang.String" grupo="Branet:Integração Almoxarifado"
                   obrigatorio="true"
                   observacao="Define a chave de assinatura para o serviço de integração com a Branet."
        />

        <parametro nome="chaveIntegracaoBranet" tipo="java.lang.String" grupo="Branet:Integração Almoxarifado"
                   obrigatorio="true"
                   observacao="Define a chave de integração para o serviço de integração com a Branet."
        />

        <parametro nome="urlBranetUtilizada"
                   tipo="java.lang.String"
                   grupo="Branet:Integração Almoxarifado"
                   defaultValue="H"
                   observacao="Define a URL que será utilizada no serviço de integração com a Branet."
                   obrigatorio="true"
        >
            <checkValues value="H" rotulo="rotulo_homologacao"/>
            <checkValues value="P" rotulo="rotulo_producao"/>
        </parametro>

        <parametro nome="habilitarIntegracaoBranet" tipo="java.lang.String" grupo="Branet:Integração Almoxarifado"
                   obrigatorio="true"
                   observacao="Define se serviço de integração com a Branet estará habilitado." defaultValue="N"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="habilitarIntegracaoBranetSolicitante" tipo="java.lang.String" grupo="Branet:Integração Almoxarifado"
                   obrigatorio="true"
                   observacao="Define se o serviço de integração com a Branet estará habilitado para receber pedidos criados na Branet." defaultValue="N"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="validarIdNomeIntegracaoBranet" tipo="java.lang.String" grupo="Branet:Integração Almoxarifado"
                   obrigatorio="true"
                   observacao="Quando a integração com a Branet estiver habilitada, define se irá validar o campo de ID e NOME para realizar a integração com a Branet."
                   defaultValue="N"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="habilitarConsultaPorCentroDeCusto" tipo="java.lang.String" grupo="Branet:Integração Almoxarifado"
                   obrigatorio="true"
                   observacao="Define se o solicitante, caso for sim, será o centro de custo, caso for não, define o solicitante sendo a unidade." defaultValue="N"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="habilitarAprovacaoPedidoTransf" tipo="java.lang.String"
                   observacao="Define se os pedidos da tela Pedido Transferência, precisarão de aprovação prévia."
                   defaultValue="N"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="limitePedidosBranet"
                   tipo="java.lang.String"
                   grupo="Branet:Integração Almoxarifado"
                   defaultValue="N"
                   observacao="Define se os produtos solicitados para a Branet terão quantidades limitadas pela unidade."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>


        <!-- BNAFAR -->

        <parametro nome="dataInicioIntegração" grupo="BNAFAR" tipo="java.util.Date"
                   observacao="Parametrizar início da integração do novo sistema BNAFAR a partir dessa data."
                   defaultValue="Wed May 31 00:00:00 BRT 2023"

        />

        <!-- Integracao OBM -->
        <parametro nome="urlObmVmp" grupo="Integração OBM" tipo="java.lang.String"
                   observacao="Define a URL de acesso a api da OBM"
        />
        <parametro nome="apiToken" grupo="Integração OBM" tipo="java.lang.String"
                   observacao="Define o token de autenticação a api da OBM"
        />
        <!-- Fim Integracao OBM -->
    </modulo>

    <!--Unidade Saude-->
    <modulo nome="24">

        <parametro nome="chamarUtilizando" tipo="java.lang.String"
                   defaultValue="PACIENTE" grupo="Painel"
                   observacao="Define como o paciente será chamado pelo painel, pelo nome ou senha."
        >
            <checkValues value="PACIENTE" rotulo="rotulo_gem_paciente"/>
            <checkValues value="SENHA" rotulo="rotulo_gem_senha"/>
        </parametro>

        <parametro nome="avisoAtendimentoRecenteOutraUnidade" tipo="java.lang.Long" defaultValue="0"
                   observacao="permite habilitar a opção de exibir alerta informando que houveram atendimentos de pacientes em outras unidades">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permiteCadastroLMEporNaoMedico" grupo="LME" tipo="java.lang.Long" defaultValue="0"
                   observacao="Permite cadastrar o Laudo de Medicamentos Especiais por profissional não médico. Não valida Anamnese, permite definir a unidade e o profissional do laudo.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="obrigaInformarCondicoesMoradiaCadastroDomiciliarTerritorial" tipo="java.lang.String"
                   defaultValue="N" grupo="CDS"
                   observacao="Define se alguns campos do grupo Condições de Moradia da página Cadastro Domiciliar e Territorial (396) serão obrigatórios."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="LimiteRegistrarVisitaDomiciliar" tipo="java.lang.Long" grupo="CDS"
                   observacao="Define o limite de dias em que uma visita domiciliar poderá ser registrada."
        />

        <parametro nome="TempoConfirmacaoEncaminhamentoInternacao" tipo="java.lang.Long" defaultValue="24"
                   grupo="Recepção"
                   observacao="Define o tempo em horas que os pacientes encaminhados para internação aparecerão na lista de confirmação da recepção."
        />

        <parametro nome="CarregarCidadeCadPacienteAutomaticamente" tipo="java.lang.String" defaultValue="S"
                   grupo="Recepção"
                   observacao="Define se o campo cidade do cadastro do paciente será carregado automaticamente com a cidade da unidade logada."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ConfirmacaoExameAposConfirmacaoPresenca" tipo="java.lang.String" defaultValue="N"
                   grupo="Recepção"
                   observacao="Confirmação do exame logo após a confirmação de presença, sem gerar atendimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ValidarAtendimentoAbertoMarcacao" tipo="java.lang.String" defaultValue="N" grupo="Recepção"
                   observacao="Define se irá validar se o paciente possui atendimento em aberto ao gerar um novo atendimento por meio da ação Marcar do nó Consultas da Recepção."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="obrigatorioInformarChaveValidacaoConfirmacaoPresenca" tipo="java.lang.String" defaultValue="S"
                   grupo="Recepção" obrigatorio="true"
                   observacao="Define se será obrigatório informar a chave de validação ao confirmar a presença de um paciente na Recepção."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="NatProcuraAgendamentoUnidade" grupo="Recepção"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura"
                   obrigatorio="true"
                   observacao="Natureza de Procura que será utilizada para a geração do atendimento na recepção. Para o agendamento de consultas da unidade"
        />

        <parametro nome="NatProcuraMarcacaoUnidade" grupo="Recepção"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura"
                   obrigatorio="true"
                   observacao="Natureza de Procura que será utilizada para a geração do atendimento na recepção. Para marcação de consultas da unidade"
        />

        <parametro nome="NatProcuraInternacao" grupo="Recepção"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura"
                   obrigatorio="true"
                   observacao="Natureza de Procura que será utilizada para a geração do atendimento na recepção. Para Internação"
        />

        <parametro nome="NatProcuraExame" grupo="Recepção"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura"
                   obrigatorio="true"
                   observacao="Natureza Procura que será utilizada para a geração do atendimento na recepção do nó exames"
        />

        <parametro nome="FiltroConfirmacaoPresenca" tipo="java.lang.Long" defaultValue="0" grupo="Recepção"
                   observacao="Classificação do Tipo de Procedimento que deve listar na confirmação do Agendamento na Recepção"
        >
            <checkValues value="0" rotulo="rotulo_ambos"/>
            <checkValues value="1" rotulo="rotulo_consulta"/>
            <checkValues value="2" rotulo="rotulo_exames"/>
        </parametro>

        <parametro nome="SelecionarConvenio" grupo="Recepção" tipo="java.lang.String" defaultValue="N"
                   observacao=" Define se o Convênio será Selecionado na Marcação/Agendamento de uma Consulta e na Confirmação do Agendamento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="modeloAgendamentoRecepcao" grupo="Recepção" tipo="java.lang.Long" defaultValue="1"
                   observacao="Define o modelo de agendamento na recepção. Quando for por CBO, será possível filtrar as agendas por profissional e/ou CBO e fazer a troca do tipo do procedimento da agenda ao realizar o agendamento. Quando for por Tipo de Atendimento, será necessário selecionar o tipo de atendimento ao selecionar o paciente, não sendo possível fazer a troca do tipo do procedimento da agenda ao realizar o agendamento. "
        >
            <checkValues value="0" rotulo="rotulo_cbo"/>
            <checkValues value="1" rotulo="rotulo_tipo_atendimento"/>
        </parametro>

        <parametro nome="tipoAgendaRecepcao" grupo="Recepção" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define quais tipos de agenda serão exibidas para agendamento na recepção. Quando for Compartilhada, serão exibidas apenas as agendas com tipo de agenda compartilhada. Quando for Local, serão exibidas apenas as agendas com tipo de agenda local. Quando for Ambas, serão exibidas as agendas independente do tipo de agenda, ou seja, serão exibidas as compartilhadas e locais."
        >
            <checkValues value="0" rotulo="rotulo_ambas"/>
            <checkValues value="1" rotulo="rotulo_compartilhada"/>
            <checkValues value="2" rotulo="rotulo_local"/>
        </parametro>

        <parametro nome="utilizaProtocoloSEPSE" tipo="java.lang.String" defaultValue="N"
                   grupo="Recepção"
                   observacao="Quando ativado apresenta no resultado do Score News uma pergunta 'Deseja dar início ao protocolo SEPSE'. O nó do 'Protocolo SEPSE' deve ser adicionado em todos os Tipos de atendimento da unidade que vai utilizar o protocolo"
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="cdNatProcuraReceita" grupo="Receituario"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura"
                   observacao="Natureza de Procura que será utilizado para geração do atendimento na emissão das receitas da lista de medicamento do paciente, deve possuir cadastro do relacionamento Natureza Procura x Tipo Atendimento"
        />

        <parametro nome="tamanhoImpressaoReceitaBasica" grupo="Receituario" tipo="java.lang.String" defaultValue="A4"
                   observacao="Define se a receita básica vai ser impressa em uma folha ou em meia folha (2 vias)."
        >
            <checkValues value="A4" rotulo="rotulo_a4"/>
            <checkValues value="A5" rotulo="rotulo_a5"/>
        </parametro>

        <parametro nome="cdTpAtendimentoReceita" grupo="Receituario"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
                   observacao="Tipo de Atendimento que será utilizado para geração do atendimento na emissão das receitas da lista de medicamento do paciente, deve possuir cadastro do relacionamento Natureza Procura x Tipo Atendimento"
        />

        <parametro nome="mostrarEstoqueAlmoxarifado" grupo="Receituario" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite mostrar estoque de unidades do tipo almoxarifado na consulta de medicamentos do nó receituário"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibeValidadeReceita" grupo="Receituario" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será exibido no rodapé da receita o descritivo com os dias de validade da receita."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibirFormularioAquisicao" grupo="Receituario" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será exibido o Formulário de Aquisição para medicamentos não padronizados no nó receituário"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibeDadosPacienteIdentificacaoCompradorReceitaB" grupo="Receituario" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Exibe na impressão da Receita B (Branca) os dados do paciente na Identificação do Comprador."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibeDadosPacienteIdentificadoMenor18anos" grupo="Receituario" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Exibe na impressão da Receita B (Branca) os dados do paciente na Identificação do Comprador, para menores de 18."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="utilizaPrescricaoSugerida" grupo="Receituario" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se o botão Prescrições Sugeridas será exibido ou não no NÓ RECEITUÁRIOS"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="separaReceituarioAutomaticamente" grupo="Receituario" tipo="java.lang.String" defaultValue="N"
                   observacao="Ao selecionar 'SIM', Ao gerar um receituário no NÓ RECEITUÁRIO, dentro do atendimento, os medicamentos com diferentes tipos de receita sempre serão impressos separadamente, cada um no seu respectivo Tipo de Receita."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="identificarMedicamentosNaoConstaSecretaria" grupo="Receituario" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se será identificado na receita os medicamentos que não constam na secretaria do município"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="permiteAuditoriaMedicamento" grupo="Receituario" tipo="java.lang.String" defaultValue="N"
                   observacao="Insere no nó prescrição_interna _enfermagem processo para farmacêutico auditar o medicamentoe habilitar a adiministração do mesmo."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="autoriza_exame_unidade_externa" grupo="Exames" tipo="java.lang.String" defaultValue="S"
                   observacao="Parametro deve ser definido a nível de usuário para que no cadastro de exame/aprovação a pesquisa da unidade mostre apenas as unidades externas, sendo que este usuário trabalha na SMS e realiza a autorização dos exames da PPI dos municípios pactuados."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="faturarExameConfirmado" grupo="Exames" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se, ao solicitar um exame e encaminhar para o próximo atendimento, os exames confirmados através do nó CONFIRMAÇÃO DE EXAMES serão faturados.">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="UnidadeSolicitaExames/Procedimento" grupo="Exames" tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Este parâmetro define os estabelecimentos que poderão solicitar novos Exames/Procedimentos no Nó Exames/Procedimentos.">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="observacaoGeral" grupo="Exames" tipo="java.lang.String"/>

        <parametro nome="observacaoAutorizado" grupo="Exames" tipo="java.lang.String"/>

        <parametro nome="diaPrevistoRetornoRequisicaoExame" grupo="Exames" tipo="java.lang.Long"
                   observacao="Dias utilizado para calculo da previsão de retorno dos exames coletados e enviados para fora do município."
        />

        <parametro nome="diasAguardandoAmarelo" grupo="Exames" tipo="java.lang.Long"
                   observacao="Número de dias aguardando retorno do exame enviado ao laboratório que a linha vai ser pintada de amarelo no acompanhamento do exame."
        />

        <parametro nome="diasAguardandoVermelho" grupo="Exames" tipo="java.lang.Long"
                   observacao="Número de dias aguardando retorno do exame enviado ao laboratório que a linha vai ser pintada de vermelho no acompanhamento do exame. Este dia deve ser superior ao Dia Aguardando Amarelo"
        />

        <parametro nome="tamanhoImpressao" grupo="Exames" tipo="java.lang.String" defaultValue="A4"
                   observacao="Define se a impressão da solicitação do exame vai sair em uma folha ou em meia folha."
        >
            <checkValues value="A4" rotulo="rotulo_a4"/>
            <checkValues value="A5" rotulo="rotulo_a5"/>
        </parametro>

        <parametro nome="HabilitaDataDesejadaEmSolicitacaoDeExamesProcedimento" grupo="Exames" tipo="java.lang.Long" defaultValue="0"
                   observacao="Quando em 'Sim', habilita a data desejada durante o cadastro da solicitação pelo atendimento da tela 266.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="TipoAtendimentoConsultaProntuario" grupo="Prontuario"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
                   observacao="Define qual o tipo de atendimento que será utilizado na tela de consulta do prontuário."
                   obrigatorio="true"
        />

        <parametro nome="NaturezaTipoAtendimentoUrgenciaAtencaoBasica" grupo="Prontuario"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento"
                   obrigatorio="true"
                   observacao="Define qual o Natureza/Tipo que será utilizado na página Atendimento Atenção Básica (924) para geração do atendimento de urgência."
        />

        <parametro nome="NaturezaTipoAgendamentoAtencaoBasica" grupo="Prontuario"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento"
                   obrigatorio="true"
                   observacao="Define qual o Natureza/Tipo que será utilizado na página Atendimento Atenção Básica (924) para geração de agendamento."
        />

        <parametro
                nome="MarcaDaguaSolicitacaoExame"
                grupo="Prontuario"
                tipo="java.lang.String"
                observacao="Demonstra a marca d’água na Solicitação de Exames."
                obrigatorio="true"
                defaultValue="S"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ValidadePrescricaoInterna" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Define a validade da prescrição interna (em horas)."
        />
        <parametro nome="complementoSolicBpaiLaudoAmbulatorialObrigatorio" tipo="java.lang.String" defaultValue="N"
                   grupo="Prontuario"
                   observacao="Define a obrigatoriedade do campo Complemento do nó SOLICITACAO_BPA_I."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ObrigatorioCidRequisicaoExame" tipo="java.lang.String" defaultValue="N"
                   grupo="Prontuario"
                   observacao="Define se o campo CID é de preenchimento obrigatório ao solicitar um exame dentro do NÓ SOLICITACAO_EXAME_UNIDADE dentro do atendimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ValidadePrescricaoADEP" grupo="Prontuario" tipo="java.lang.Long" defaultValue="24"
                   observacao="Define a validade em horas de uma ADEP"
        />

        <parametro nome="permiteEdicaoDataHoraEvolucao" grupo="Prontuario" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se será possível editar a data e hora da evolução no atendimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exigePesoAltura" grupo="Prontuario" tipo="java.lang.String" defaultValue="N"
                   observacao="Quando ativado (valor SIM), o sistema exigirá o preenchimento dos campos de peso e altura do paciente nos nós AVALIACAO e AVALIACAO_UNIDADE do atendimento (tela 266). O sistema deve impedir a finalização do atendimento caso os campos estejam em branco."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="permiteInformarCodigoPcacr" grupo="Prontuario" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se o botão para informar o código do PCACR será apresentado nos nós AVALIACAO_EMERGENCIA_UPA e AVALIACAO_UNIDADE do atendimento (tela 266)."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="urlIntegracaoPcacr" tipo="java.lang.String" grupo="Integração PCACR"
                   obrigatorio="false"
                   observacao="URL utilizado para a integração do sistema PCACR."
        />

        <parametro nome="loginIntegracaoPcacr" tipo="java.lang.String" grupo="Integração PCACR"
                   obrigatorio="false"
                   observacao="Login utilizado para a integração do sistema PCACR."
        />

        <parametro nome="senhaIntegracaoPcacr" tipo="java.lang.String" grupo="Integração PCACR"
                   obrigatorio="false"
                   observacao="Senha utilizada para a integração do sistema PCACR."
        />



        <parametro nome="exibeSinaisClinicosObservadosCovid19" grupo="Prontuario" tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Indica se as opções sobre os sinais clínicos observados serão ou não exibidas na ficha de triagem do Covid-19."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibeDataPrimeirosSintomasCovid19" grupo="Prontuario" tipo="java.lang.String" defaultValue="S"
                   observacao="Indica se o campo Data dos primeiros sintomas será ou não exibido na ficha de triagem do Covid-19."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibeOutrasInformacoesProfissionalSaudeFichaCovid19" grupo="Prontuario" tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Indica se as informações adicionais referentes a profissionais de saúde serão ou não exibidas na ficha de triagem do Covid-19."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibeDadosExposicaoCovid19" grupo="Prontuario" tipo="java.lang.String" defaultValue="S"
                   observacao=" Indica se as opções sobre os dados de exposição e viagens serão ou não exibidas na ficha de triagem do Covid-19."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="valorMinimoSuspeitoCovid19" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Valor de pontuação mínima para que caso seja considerado suspeita de COVID-19."
                   defaultValue="4"
                   obrigatorio="true"
        />

        <parametro nome="nomeNoCovid19" grupo="Prontuario" tipo="java.lang.String"
                   observacao="Define o nome que será exibido no nó de triagem do Covid-19."
                   defaultValue="Triagem Covid-19"
                   obrigatorio="true"
        />

        <parametro nome="informacaoHistoricoEvolucaoProntuario" grupo="Prontuario" tipo="java.lang.String"
                   defaultValue="tipoAtendimento"
                   observacao="Define se qual informação será mostrada na tabela histórico do nó EVOLUCAO_PRONTUARIO."
        >
            <checkValues value="tipoAtendimento" rotulo="rotulo_tipo_atendimento"/>
            <checkValues value="profissional" rotulo="rotulo_profissional"/>
        </parametro>

        <parametro nome="destacarPacientesPorIdade" grupo="Prontuario" tipo="java.lang.String" defaultValue="N"
                   observacao="Destacar os pacientes com idade superior a 60 anos ou inferior a 2 anos na lista do atendimento"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="laudo" grupo="Prontuario" tipo="java.lang.String" defaultValue="B"
                   observacao="Define o tipo de laudo (BPAI ou Ambulatorial) que será utilizado no atendimento"
        >
            <checkValues value="B" rotulo="rotulo_laudo_bpai"/>
            <checkValues value="A" rotulo="rotulo_laudo_ambulatorial"/>
        </parametro>

        <parametro nome="EditarFonte" grupo="Prontuario:Editor Evolução" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será possível editar a fonte no editor da evolução."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="EditarFormatacaoTexto" grupo="Prontuario:Editor Evolução" tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Define se será possível formatar o texto (negrito, itálico, sublinhado, riscado e alinhamentos) no editor da evolução."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="EditarCor" grupo="Prontuario:Editor Evolução" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se será possível editar a cor da fonte no editor da evolução."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="EditarTamanho" grupo="Prontuario:Editor Evolução" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se será possível editar o tamanho da fonte no editor da evolução."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="EditarEstiloParagrafo" grupo="Prontuario:Editor Evolução" tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Define se será possível editar o estilo do parágrafo no editor da evolução."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="CabecalhoPadraoRelatorio" grupo="Relatório" tipo="java.lang.String"
                   observacao="(Definir)"
        />

        <parametro nome="CabecalhoAdicional1" grupo="Relatório" tipo="java.lang.String"
                   observacao="(Definir)"
        />

        <parametro nome="CabecalhoAdicional2" grupo="Relatório" tipo="java.lang.String"
                   observacao="(Definir)"
        />

        <parametro nome="ExibirLaudoCabecalhoAdicional1" grupo="Relatório" tipo="java.lang.String" defaultValue="S"
                   observacao="Ocultar/exibir o cabeçalho adicional 1 do relatório Laudo TFD. (utilizado apenas neste relatório, parâmetros já existentes devem ser mantidos)."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="SobreecreverLaudoCabecalhoAdicional2" grupo="Relatório" tipo="java.lang.String"
                   observacao="Sobreescreve o cabeçalho adicional 2 do relatório Laudo TFD. (utilizado apenas neste relatório, parâmetros já existentes devem ser mantidos)."
        />

        <parametro nome="SobreecreverLaudoUnidadeAtendimento" grupo="Relatório" tipo="java.lang.String"
                   observacao="Sobreescreve a unidade de atendimento no cabeçalho do relatório Laudo TFD. (utilizado apenas neste relatório, parâmetros já existentes devem ser mantidos)."
        />

        <parametro nome="CabecalhoDiretorTecnico" grupo="Relatório" tipo="java.lang.String"
                   observacao="Espaço reservado para a descrição do diretor técnico, impressa no cabeçalho padrão dos relatórios."
        />

        <parametro nome="CabecalhoLacen" grupo="Relatório" tipo="java.lang.String"
                   defaultValue="Estado de Santa Catarina"
                   observacao="Espaço reservado para a descrição do logo do relatorio Lacen configuração 1"
        />

        <parametro nome="CabecalhoLacen2" grupo="Relatório" tipo="java.lang.String"
                   defaultValue="Secretaria de Estado da Saúde"
                   observacao="Espaço reservado para a descrição do logo do relatorio Lacen configuração 2"
        />

        <parametro nome="CabecalhoLacen3" grupo="Relatório" tipo="java.lang.String"
                   defaultValue="Superintendência de Vigilância em Saúde"
                   observacao="Espaço reservado para a descrição do logo do relatorio Lacen configuração 3"
        />

        <parametro nome="CabecalhoLacen4" grupo="Relatório" tipo="java.lang.String"
                   defaultValue="Laboratório Central de Saúde Publica"
                   observacao="Espaço reservado para a descrição do logo do relatorio Lacen configuração 4"
        />


        <parametro nome="QtdadeProcedimentoSolicitados" grupo="APAC" tipo="java.lang.Long"
                   observacao="Número máximo de procedimento que pode ser cadastrado por APAC"
                   obrigatorio="true"
        />

        <parametro nome="obrigatorioInformarCIDPrincipalLaudoApac" grupo="APAC" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será obrigatório informar o campo CID Principal do Laudo APAC."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="GlicemiaPosPrandialInicial" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Valor inicial que será utilizado na avaliação da glicemia considerada normal para o tipo Pós-Prandial (Avaliação da glicose no sangue 1 a 2 horas após a alimentação)."
                   obrigatorio="true"
        />

        <parametro nome="GlicemiaPosPrandialFinal" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Valor final que será utilizado na avaliação da glicemia considerada normal para o tipo Pós-Prandial (Avaliação da glicose no sangue 1 a 2 horas após a alimentação)."
                   obrigatorio="true"
        />

        <parametro nome="GlicemiaPosJejumInicial" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Valor inicial que será utilizado na avaliação da glicemia considerada normal para o tipo em jejum."
                   obrigatorio="true"
        />

        <parametro nome="GlicemiaPrePrandialFinal" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Valor final que será utilizado na avaliação da glicemia considerada normal para o tipo Pré-Prandial."
                   obrigatorio="true"
        />

        <parametro nome="GlicemiaPrePrandialInicial" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Valor inicial que será utilizado na avaliação da glicemia considerada normal para o tipo Pré-Prandial."
                   obrigatorio="true"
        />

        <parametro nome="GlicemiaPosJejumFinal" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Valor final que será utilizado na avaliação da glicemia considerada normal para o tipo em jejum."
                   obrigatorio="true"
        />

        <parametro nome="tempoReabrirAtendimento" grupo="Prontuario" tipo="java.lang.Integer"
                   observacao="Tempo em minutos para que seja possível reabrir um atendimento finalizado."
                   defaultValue="60"
                   obrigatorio="true"
        />

        <parametro nome="idadeLimiteAtendimentoPediatria" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Define a idade limite para determinar o CBO de atendimento pediátrico."
        />

        <parametro nome="exibirAtendimentosAntigosAberto" tipo="java.lang.String" defaultValue="N" grupo="Prontuario"
                   observacao="Exibe os atendimentos retroativos que estão com a situação aguardando."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="obrigatorioInformarCidRegistroEspecializado" tipo="java.lang.String" defaultValue="N"
                   grupo="Prontuario"
                   observacao="Define a obrigatoriedade do preenchimento do CID no NÓ REGISTRO_ESPECIALIZADO dentro do atendimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="obrigatorioInformarCondutaCovid" tipo="java.lang.String" defaultValue="N" grupo="Prontuario"
                   observacao="Define a obrigatoriedade do preenchimento do campo conduta para COVID-19 durante o atendimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>
        <parametro nome="DadosAdicionais_CID" tipo="java.lang.String" defaultValue="N"
                   grupo="Prontuario"
                   observacao="Define o popup que será apresentado ao salvar um atendimento que tenha sido informado um CID com registro de agravo."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ProcedimentoAntropometrico" grupo="Prontuario:Procedimento"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Procedimento utilizado na avaliação do atendimento para faturamento quando informar Peso ou Altura ou Cintura."
                   obrigatorio="true"
        />

        <parametro nome="TipoDocumentoSaidaProduto" grupo="Prontuario:Procedimento"
                   tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Tipo de documento que será utilizado na baixa dos insumos informados para o procedimento."
                   obrigatorio="true"
        />

        <parametro nome="validaProcedimentoAtendimento" tipo="java.lang.String" defaultValue="S"
                   grupo="Prontuario:Procedimento" obrigatorio="true"
                   observacao="O parametro dos procedimentos deverá ser inserido, para validar a obrigatoriedade dos procedimentos no atendimento"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="diaInicioCompetencia" grupo="Exames" tipo="java.lang.Long"
                   observacao="Dia que vai iniciar a competência para controle na autorização dos exames, o valor deve estar entre 1 a 28."
                   defaultValue="1"
        />

        <parametro nome="ProcedimentoVisitaDomiciliar" grupo="BPA"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Procedimento a ser utilizado na geração do BPA para as visitas domiciliares informadas no ficha SSA2 do SIAB."
        />

        <parametro nome="CBOVisitaDomiciliar" grupo="BPA" tipo="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                   observacao="CBO a ser utilizado na geração do BPA para as visitas domiciliares informadas no ficha SSA2 do SIAB. "
        />

        <parametro nome="ProcedimentoAdministracaoMedicamento" grupo="BPA"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Procedimento a ser usado na geração do BPA para administração de medicamentos."
        />

        <parametro nome="CBOAdministracaoMedicamento" grupo="BPA"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                   observacao="CBO a ser usado na geração do BPA para administração de medicamentos."
        />

        <parametro nome="CBODispensacaoProcedimento" grupo="BPA"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                   observacao="CBO a ser usado na geração do BPA para dispensação de medicamentos."
                   obrigatorio="true"
        />

        <parametro nome="ProcedimentoAplicacao" grupo="BPA:Vacina"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Procedimento que será utilizado na geração do BPA da aplicação da Vacina."
                   obrigatorio="true"
        />

        <parametro nome="CboAplicacao" grupo="BPA:Vacina" tipo="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                   observacao="CBO que será utilizado na geração do BPA da aplicação da Vacina."
                   obrigatorio="true"
        />

        <parametro nome="gerarSiscan" grupo="BPA:SISCAN" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se será permitido gerar o SISCAN (Sistema de Informação do Câncer) através da tela Geração do BPA (287)."
        >
            <checkValues value="1" rotulo="rotulo_sim"/>
            <checkValues value="0" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="procedimentoFaturadoVisitaDenuncia"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento" grupo="BPA:Vigilância"
                   observacao="Procedimento que vai ser utilizado na geração do BPA para denuncias da Vigilância"
                   obrigatorio="true"
        />

        <parametro nome="ClassificacaoRiscoPadrao" grupo="Prontuario"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco"
                   observacao="Define a Classificação de Risco padrão que será utilizada na geração do atendimento."
                   obrigatorio="false"
        />

        <parametro nome="CalculoTempo" grupo="Prontuario" tipo="java.lang.String"
                   observacao="Tipo do cálculo do Tempo de permanencia do paciente feito na lista de atendimento."
                   defaultValue="U"
        >
            <checkValues value="C" rotulo="rotulo_chegada"/>
            <checkValues value="U" rotulo="rotulo_ultimo_atendimento"/>
        </parametro>

        <parametro nome="TempoAtualizacaoListaAtendimento" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Define qual o intervalo de tempo em segundos para a atualização automática da lista de pacientes (Mínimo 120 segundos)."
                   defaultValue="0"
        />

        <parametro nome="LimiteListaAtendimento" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Define a quantidade de registros que lista vai ser limitada/carregada, caso não for preenchido será ignorado, valor minimo 14"
                   defaultValue="0"
        />


        <parametro nome="minimoCaracteresJustificativaAPEF" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="No nó APEF, obrigar que a Justificativa tenha no mínimo o tanto de caracteres definido no parâmetro."
                   defaultValue="0"
        />

        <parametro nome="ColunasConsultaAtendimeto" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Define quais a colunas que devem aparecer na lista de pacientes do prontuário, como base em modelos pré-definidos."
                   defaultValue="1"
        >
            <checkValues value="1" rotulo="rotulo_unidade_saude"/>
            <checkValues value="2" rotulo="rotulo_emergencia"/>
            <checkValues value="3" rotulo="rotulo_internacao"/>
        </parametro>

        <parametro nome="defineCidUtilizadoTelaAtendimento" grupo="Prontuario"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                   observacao="Define o CID que será utilizado no campo CID (nó Evolução) da tela de atendimento"
        />

        <parametro nome="defineTipoAtendimentoUtilizadoTelaAtendimento" grupo="Prontuario"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoAtendimento"
                   observacao="Define o Tipo de Atendimento que será utilizado no campo Tipo de Atendimento (nó Evolução) da tela de atendimento"
        />

        <parametro nome="defineCondutaUtilizadoTelaAtendimento" grupo="Prontuario"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.Conduta"
                   observacao="Define a Conduta que será utilizada no campo Conduta (nó Evolução) da tela de atendimento"
        />

        <parametro nome="PBFIdadeCrianca" grupo="Bolsa Família" tipo="java.lang.Integer"
                   observacao="Idade máxima da criança para ser considerada no programa bolsa família."
                   defaultValue="7" obrigatorio="true"
        />

        <parametro nome="PBFIdadeFinalMulheres" grupo="Bolsa Família" tipo="java.lang.Integer"
                   observacao="Idade final das mulheres para serem consideradas no programa bolsa família."
                   defaultValue="44" obrigatorio="true"
        />

        <parametro nome="PBFIdadeInicialMulheres" grupo="Bolsa Família" tipo="java.lang.Integer"
                   observacao="Idade inicial das mulheres para serem consideradas no programa bolsa família."
                   defaultValue="14" obrigatorio="true"
        />

        <parametro nome="idadeParaValidarAlimentacao" grupo="SISVAN" tipo="java.lang.Integer"
                   observacao="Idade máxima em meses para fazer a validação do tipo da alimentação no SISVAN."
                   defaultValue="24" obrigatorio="true"
        />

        <parametro nome="idadeParaAcompanhamentoMensal" grupo="SISVAN" tipo="java.lang.Integer"
                   observacao="Idade máxima em meses em que deve haver o registro mensal de acompanhamento no SISVAN."
                   defaultValue="6" obrigatorio="true"
        />

        <parametro nome="idadeParaAcompanhamentoSemestral" grupo="SISVAN" tipo="java.lang.Integer"
                   observacao="Idade máxima em meses em que deve haver o registro semestral de acompanhamento no SISVAN."
                   defaultValue="24" obrigatorio="true"
        />

        <parametro nome="idadeParaAcompanhamentoAnual" grupo="SISVAN" tipo="java.lang.Integer"
                   observacao="Idade máxima em meses em que deve haver o registro anual de acompanhamento no SISVAN."
                   defaultValue="120" obrigatorio="true"
        />

        <parametro nome="idadeInicialParaAcompanhamentoSemestralAdulto" grupo="SISVAN" tipo="java.lang.Integer"
                   observacao="Idade inicial de adultos para registro semestral de acompanhamento SISVAN"
                   defaultValue="17" obrigatorio="true"
        />

        <parametro nome="idadeFinalParaAcompanhamentoSemestralAdulto" grupo="SISVAN" tipo="java.lang.Integer"
                   observacao="Idade final para registro semestral de acompanhamento SISVAN"
                   defaultValue="59" obrigatorio="true"
        />

        <parametro nome="tipoAcompanhamentoGestante" grupo="SISVAN" tipo="java.lang.Long"
                   observacao="Definição da Periodicidade da coleta do registro de acompanhamento no SISVAN."
                   defaultValue="1" obrigatorio="true"
        >
            <checkValues value="1" rotulo="rotulo_mensal"/>
            <checkValues value="2" rotulo="rotulo_bimestral"/>
            <checkValues value="3" rotulo="rotulo_trimestral"/>
            <checkValues value="6" rotulo="rotulo_semestral"/>
            <checkValues value="12" rotulo="rotulo_anual"/>
        </parametro>

        <parametro nome="validarDadosClinicos" grupo="Exames" tipo="java.lang.String" defaultValue="N"
                   observacao="Validar se é obrigatório informar o campo Dados Clínicos na Solicitação do Exame."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="permiteAtendimentoParalelo" grupo="Prontuario" tipo="java.lang.String" defaultValue="N"
                   observacao="Ao selecionar a opção SIM, o sistema permitirá que seja admitido o mesmo paciente para mais de um profissional, com um mesmo Tipo de Atendimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="procedimentoInicioTratamentoOdontologico" grupo="Odonto"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Procedimento que será gerado ao criar a ficha clinica do tratamento odontológico do paciente."
                   obrigatorio="true"
        />

        <parametro nome="procedimentoConclusaoTratamentoOdontologico" grupo="Odonto"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Procedimento que será gerado ao concluir a ficha clinica do tratamento odontológico do paciente."
                   obrigatorio="true"
        />

        <parametro nome="procedimentoUrgenciaTratamentoOdontologico" grupo="Odonto"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Procedimento que será gerado ao criar um tratamento odontológico de urgência para o paciente."
                   obrigatorio="true"
        />

        <parametro nome="SituacaoProteseSuperior" grupo="Odonto"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente"
                   observacao="Situação que será aplicada quando definido que o paciente faz uso de prótese superior."
                   obrigatorio="true"
        />

        <parametro nome="SituacaoProteseInferior" grupo="Odonto"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente"
                   observacao="Situação que será aplicada quando definido que o paciente faz uso de prótese inferior."
                   obrigatorio="true"
        />

        <parametro nome="convenio" grupo="Exames" tipo="java.lang.Long" defaultValue="2"
                   observacao="Configuração do tipo do convênio que será utilizado no atendimento quando solicitar um novo exame."
        >
            <checkValues value="0" rotulo="rotulo_sus"/>
            <checkValues value="1" rotulo="rotulo_particular"/>
            <checkValues value="2" rotulo="rotulo_ambos"/>
        </parametro>

        <parametro nome="cancelaExamesAutorizadosCompetenciaAnterior" grupo="Exames" tipo="java.lang.String" defaultValue="N"
                   observacao="Permitir cancelamento de exames autorizados com competência anterior a atual.">
            <checkValues value="S" rotulo="rotulo_habilitado"/>
            <checkValues value="N" rotulo="rotulo_desabilitado"/>
        </parametro>

        <parametro nome="tipoDocumentoEstornoGasto" grupo="AtendimentoSAMU"
                   tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Tipo documento que será utilizado para fazer o estorno do produto para o estoque ao cancelar o atendimento."
                   obrigatorio="true"
        />

        <parametro nome="tipoDocumentoBaixaGasto" grupo="AtendimentoSAMU"
                   tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Tipo documento que será utilizado para fazer a baixa do produto do estoque ao salvar o atendimento."
                   obrigatorio="true"
        />

        <!--        <parametro nome="procedimento" grupo="AtendimentoSAMU" tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                           observacao="Procedimento do atendimento para geração do BPA."
                           obrigatorio="true"
                />-->

        <parametro nome="HoraInicioPrescricao" grupo="Prontuario:Prescrição Interna" tipo="java.lang.String"
                   observacao="Define a hora de início da prescrição, se informar 99 será utilizado a hora atual."
        />

        <parametro nome="DataPrescricao" grupo="Prontuario:Prescrição Interna" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se o campo da data da prescrição ficará habilitado."
        >
            <checkValues value="S" rotulo="rotulo_habilitado"/>
            <checkValues value="N" rotulo="rotulo_desabilitado"/>
        </parametro>

        <parametro nome="QuantidadeVolumeMorto" grupo="Prontuario:Prescrição Interna" tipo="java.lang.Long"
                   observacao="Quantidade em ML que será incluída na solução quando for aplicada através da Bomba de Infusão (BI)"
                   defaultValue="0"
        />

        <parametro nome="AprazamentoAgoraPadrao" grupo="Prontuario:Prescrição Interna" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se o Aprazamento Agora é definido como padrão.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="SugerirDescricaoPosologia" grupo="Prontuario:Prescrição Interna" tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Define se a Descrição da Posologia é sugerida ao alterar os campos no grupo Medicamentos.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="bloqueiaInsercaoDeMedicamentoSemSaldo" grupo="Prontuario:Prescrição Interna"
                   tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Bloqueia a inserção de um medicamento sem saldo na prescrição interna.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="mostrarSaldoDaFarmacianaPrescrIcaointerna" grupo="Prontuario:Prescrição Interna"
                   tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao="Mostra no auto complete medicamento do nó prescrição_interna o saldo da empresa citada no parâmetro"
                   obrigatorio="false">
        </parametro>

        <parametro nome="ExigeImpressaoReceituarioControlado" grupo="Prontuario:Prescrição Interna"
                   tipo="java.lang.String" defaultValue="N"
                   observacao="Define se o órgão que controla as medicações exige a Impressão do Receituário Controlado."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="modeloImpressaoAprazamento" grupo="Prontuario:Prescrição Interna" tipo="java.lang.Long"
                   observacao="Define a forma que será impressa a posologia na prescrição interna do atendimento e farmácia."
                   defaultValue="1"
        >
            <checkValues value="1" rotulo="rotulo_coluna"/>
            <checkValues value="2" rotulo="rotulo_linha"/>
        </parametro>

        <parametro nome="LimiteVisualizarUltimosLanctos" grupo="Prontuario:Ganhos/Perdas" tipo="java.lang.Integer"
                   defaultValue="12"
                   observacao="Limite(em horas) para Visualizar os Ultimos Lançamentos." obrigatorio="false"
        />

        <parametro nome="LimiteInativacaoLanctoBalanco" grupo="Prontuario:Ganhos/Perdas" tipo="java.lang.Integer"
                   defaultValue="12"
                   observacao="Limite(em horas) para Inativar um Lançamento no Balanço." obrigatorio="false"
        />

        <parametro nome="LimiteInativacaoLanctoAnalise" grupo="Prontuario:Ganhos/Perdas" tipo="java.lang.Integer"
                   defaultValue="12"
                   observacao="Limite(em horas) para Inativar um Lançamento na Análise." obrigatorio="false"
        />

        <parametro nome="LimiteLanctoRetroativo" grupo="Prontuario:Ganhos/Perdas" tipo="java.lang.Integer"
                   defaultValue="12"
                   observacao="Limite(em horas) para Registrar Lançamento Retroativo." obrigatorio="false"
        />

        <parametro nome="HoraInicioBalanco" grupo="Prontuario:Ganhos/Perdas" tipo="java.lang.Integer" defaultValue="7"
                   observacao="Hora de Início do Balanço." obrigatorio="false"
        />

        <parametro nome="imprimirEtiqueExame" grupo="Recepção:Exames" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se vai ser impresso a etiqueta na marcação do exame pela recepção."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="demonstraValorContaSaldo" grupo="Recepção:Exames" tipo="java.lang.String" defaultValue="S"
                   observacao="demonstra os campos Cota Unidade, Cota Utilizada, Saldo e coluna Saldo Prestador no nó EXAMES_AUTORIZACAO"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="agendamentoAutorizacaoExames" grupo="Recepção:Exames" tipo="java.lang.String" defaultValue="S"
                   observacao="Cria agendamento a partir da autorização de exames">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="emiteComprovanteAutorizacaoExames" grupo="Recepção:Exames" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Emite o comprovante de autorização das requisições de exame">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="validaCargaHorariaAmbulatorial" grupo="Recepção:Exames" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se o sistema irá validar a carga horária ambulatorial do profissional no momento da confirmação de presença para exames na tela da Recepção.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="quantidadeViasImpressaoLaudoAih" grupo="AIH" tipo="java.lang.Long"
                   observacao="Define a quantidade de vias para a impressão do laudo da aih." defaultValue="1"
        />

        <parametro nome="permiteGerarSolicitacaoAgendamentoLaudoAIH" grupo="AIH" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se será gerado Solicitação de Agendamento ao selecionar um Tipo de Procedimento no Laudo AIH"
        >
            <checkValues value="1" rotulo="rotulo_sim"/>
            <checkValues value="0" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="tempoEvolucaoAtendimentoConcluido" grupo="Prontuario" tipo="java.lang.Long"
                   observacao="Tempo para evoluir um atendimento concluído." defaultValue="24"
        />

        <parametro nome="obrigaDocumento" grupo="Recepção:Acompanhantes" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se pelo menos um documento é obrigatório no cadastro de acompanhantes do paciente."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="obrigaTelefone" grupo="Recepção:Acompanhantes" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se o telefone é obrigatório no cadastro de acompanhantes do paciente."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="obrigaGrauParentesco" grupo="Recepção:Acompanhantes" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se o grau de parentesco é obrigatório no cadastro de acompanhantes do paciente."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="permiteAgendarSemCadastro" grupo="Recepção" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se é permitido realizar agendamentos de pacientes não cadastrados no sistema."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ImprimeHorarioImpressao" tipo="java.lang.String" defaultValue="S" grupo="Prontuario"
                   observacao="Define se deve mostrar a hora da impressão nos formulários/documentos do atendimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="TempoLimiteCancelamentoAtendimento" tipo="java.lang.Long"
                   grupo="Prontuario:Cancelamento de Atendimento" obrigatorio="true" defaultValue="0"
                   observacao="Define o tempo em horas em que pode ser cancelado um atendimento após o mesmo ser finalizado."
        />

        <parametro nome="motivoDestinoSaidaCancelamentoAtendimento"
                   grupo="Prontuario:Cancelamento de Atendimento:Ficha de Acolhimento"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida"
                   observacao="Motivo de Destino/Saída para a ficha de acolhimento do atendimento cancelado após um atendimento finalizado."
                   obrigatorio="true"
        />

        <parametro nome="DiasVisualizacaoAgendaExame" grupo="Exames" tipo="java.lang.Long" defaultValue="30"
                   observacao="Dias para a visualização da agenda a partir do dia de agendamento disponível."
        />

        <parametro nome="enderecoEnvioSisprenatal" grupo="Integração:SISPRENATAL" tipo="java.lang.String"
                   observacao="Define o endereço para a sincronização do SISPRENATAL."
        />

        <parametro nome="ProcedimentoAdministracaoMedicamentoNaoFaturavel" grupo="BPA"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Esse procedimento não será faturado na administração de medicamentos no atendimento."
        />

        <parametro nome="planejamentoVisitaCriancaFaixaEtariaInicial" grupo="Planejamento da Visita"
                   tipo="java.lang.Long" defaultValue="0"
                   observacao="Número de meses iniciais da faixa etária da criança a ser exibida no relatório de Planejamento da Visita."
        />

        <parametro nome="planejamentoVisitaCriancaFaixaEtariaFinal" grupo="Planejamento da Visita" tipo="java.lang.Long"
                   defaultValue="144"
                   observacao="Número de meses finais da faixa etária da criança a ser exibida no relatório de Planejamento da Visita."
        />

        <parametro nome="desabilitaCamposRequisicaoExames" grupo="Exames" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se os campos Exame, Quantidade e Complemento do nó REQUISIÇÃO_EXAMES serão desabilitados."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="informaJustificativaRequisicaoExame" grupo="Exames" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se o campo Justificativa do nó REQUISIÇÃO_EXAMES é obrigatório para exames de Radiologia."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="validaJustificativaEmRequisicaoDeExames" grupo="Exames" tipo="java.lang.Long" defaultValue="0"
                   observacao="Quando em 'SIM' valida o preenchimento da justificativa ao solicitar um procedimento pelo nó Requisição de Exames"
                   obrigatorio="true">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="tipoAtendimentoAtividade" grupo="Atividades em grupo"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
                   observacao="Tipo de Atendimento que será utilizado para geração do atendimento, ao finalizar a atividade. Deve possuir cadastro do relacionamento Natureza Procura x Tipo Atendimento"
        />

        <parametro nome="procedimentoPadraoAtividadeGrupoProducao" grupo="Atividades em grupo"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Define o procedimento padrão para o item da conta do paciente, quando não for informado o procedimento na atividade em grupo."
                   obrigatorio="true"
        />

        <parametro nome="competenciaInicioGeracaoBpaConta" grupo="BPA" tipo="java.util.Date"
                   observacao="Define a competência de início para geração do BPA a partir da conta do paciente."
        />

        <parametro nome="bancoConsultaBPA" grupo="BPA" tipo="java.lang.String" defaultValue="L"
                   observacao="Define em qual o banco vai ser executada a consulta de geração do BPA. ** IMPORTANTE ** APENAS pode ser ALTERADO para o escrita mediante análise e orientação da equipe de engenharia e dba"
        >
            <checkValues value="L" rotulo="rotulo_leitura"/>
            <checkValues value="E" rotulo="rotulo_escrita"/>
        </parametro>

        <parametro nome="procedimentoDengue" tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   grupo="BPA"
                   observacao="Define o procedimento relacionado a dengue. Ao adicionar na tela Lançamento de Produção (585) o procedimento configurado, será permitido identificar se o mesmo é ou não relacionado a dengue na produção em questão."
                   obrigatorio="false"
        />


        <parametro nome="urlTelessaude" tipo="java.lang.String" grupo="Prontuario"
                   observacao="Define a URL de acesso ao site do TELESSAÚDE"
        />

        <parametro nome="urlFarmaciaEscola" tipo="java.lang.String" grupo="Receituario"
                   observacao="Define a URL de acesso ao site da Farmácia Escola."
        />

        <parametro nome="imprimirOcorrenciasProntuario" grupo="Prontuario" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será impresso no prontuário as ocorrências do paciente."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibirCotaAutorizacaoExames" grupo="Prontuario:Solicitação de Exames" tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Define se será mostrado na Autorização de Exames o campo de valor da Cota."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibirSaldoAutorizacaoExames" grupo="Prontuario:Solicitação de Exames" tipo="java.lang.String"
                   defaultValue="S"
                   observacao="Define se será mostrado na Solicitação de Exames( Autorização de Exames) o campo de valor do Saldo."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibirSaldoPrestadorAutorizacaoExames" grupo="Prontuario:Solicitação de Exames"
                   tipo="java.lang.String" defaultValue="S"
                   observacao="Define se será mostrado na Solicitação de Exames( Autorização de Exames) a coluna de valor do Saldo Prestador."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ExibirMedicamentosDispensados" grupo="Relatório" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se demostra o medicamento dispensado na impressão do relatório."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="permiteInformarPrioridadeSolicitacaoExames" grupo="Prontuario:Solicitação de Exames"
                   tipo="java.lang.String" defaultValue="S"
                   observacao="Define se será permitido informar a prioridade no nó Solicitação de Exames / Procedimentos."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="habilitaClassificacaoRiscoExame" grupo="Prontuario:Solicitação de Exames" tipo="java.lang.String" defaultValue="N"
                   observacao="Quando habilitado vai substituir o campo prioridade no nó Solicitação de Exames pela Classificação de Risco"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="obrigatorioInformarCIAP" grupo="Prontuario" tipo="java.lang.Long" defaultValue="0"
                   observacao="Torna obrigatório informar o campo CIAP no atendimento e nas telas de informação do e-SUS do Lançamento de Produção"
                   obrigatorio="true">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="exibirAnexosHistoricoClinico" grupo="Prontuario" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se os anexos do paciente serão exibidos no Node de Histórico Clínico.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="demonstrarConvenioRecepçãoAtendimento" grupo="Recepção" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Permite ao usuário visualizar no nó Atendimento, qual o convênio do paciente.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="vozPainel" tipo="java.lang.String" defaultValue="F" grupo="Painel" obrigatorio="true"
                   observacao="Define a voz do painel de chamada, masculina ou feminina."
        >
            <checkValues value="F" rotulo="rotulo_feminino"/>
            <checkValues value="M" rotulo="rotulo_masculino"/>
        </parametro>

        <parametro nome="utilizaVozPainel" grupo="Painel" tipo="java.lang.String" defaultValue="N" obrigatorio="true"
                   observacao="Define se será utilizado a voz no painel de chamada."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibeClassificaoRiscoPainel" grupo="Painel" tipo="java.lang.String" defaultValue="N"
                   obrigatorio="true"
                   observacao="Define se a classificação de riscos / hora da chamada é apresentada no painel de chamados."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="chaveAcessoIdPainel" grupo="Painel" tipo="java.lang.String" defaultValue="********************"
                   obrigatorio="true"
                   observacao="Define a chave de acesso ao painel de chamada."
        />

        <parametro nome="senhaAcessoIdPainel" grupo="Painel" tipo="java.lang.String"
                   defaultValue="aBqoV8ggtU4HCKdKf0khAAF2aX+vIX/7kBxjcG3f" obrigatorio="true"
                   observacao="Define a senha de acesso ao painel de chamada."
        />

        <parametro nome="URL_Papagaio" grupo="Painel" tipo="java.lang.String"
                   defaultValue="https://papagaio.celk.com.br"
                   observacao="Define a URL de acesso ao sistema de Sintetização de Voz da CELK (Papagaio)."
        />

        <parametro nome="TipoAnexoDigitacaoLaudo" grupo="Exames"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.TipoAnexo" obrigatorio="true"
                   observacao="Define o Tipo do Anexo gerado quando é feito a Digitação do Laudo."
        />

        <parametro nome="TipoAnexoCidResultadoExame" grupo="Exames"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.TipoAnexo" obrigatorio="true"
                   observacao="Define o Tipo do Anexo gerado quando é informado o CID do Resultado do exame."
        />

        <parametro nome="obrigatorioPreencherFormularioAquisicao" grupo="Receituario" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Torna obrigatório preencher o formulário de aquisição para medicamentos não padronizados no nó Receituário."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permiteAdicionarMecidamentoInativoReceita" grupo="Receituario" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Possibilita a inclusão de medicamentos que estejam inativados na geração ou copia da receita."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permiteInformarLocaldeTrabalhoPacienteRecepcao" grupo="Recepção" tipo="java.lang.Long"
                   defaultValue="0"
                   observacao="Define se será permitido informar o local de trabalho do paciente ao marcar ou agendar o mesmo na recepção."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="descricaoNexoTecnicoEpidemiologicoPrevidenciarioCerest" grupo="Prontuario"
                   tipo="java.lang.String"
                   observacao="Define a descrição do Nexo Técnico Epidemiológico Previdenciário - NTEP, que será exibido ao finalizar um atendimento onde foi informado um CID que tenha vínculo com o CNAE do estabelecimento que o paciente trabalha."
                   obrigatorio="true"
        />

        <parametro nome="exibirPainelClassificacaoRiscoConsultaAtendimentos" grupo="Prontuario" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se será exibido o Formulário de Aquisição para medicamentos não padronizados no nó receituário"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="validarUsuarioTemporario" grupo="Prontuario" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se será exigido a senha de autenticação de usuário ao salvar o atendimento"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="diasMinimoCancelamentoListaAtendimento" grupo="Prontuario" tipo="java.lang.Long"
                   defaultValue="3"
                   observacao="Define a quantidade mínima de dias que um atendimento deve estar aguardando para poder ser considerado no cancelamento da lista de atendimentos"
                   obrigatorio="true"
        />
        <parametro nome="diasFechamentoCancelamentoAtendimento" grupo="Fechamento Automático de Atendimento"
                   tipo="java.lang.Long" defaultValue="5"
                   observacao="Define a quantidade de dias sem movimentação de um atendimento para realizar o fechamento/cancelamento do atendimento pelo processo"
                   obrigatorio="true"
        />
        <parametro nome="defineTipoAtendimentoUtilizadoFechamentoAtendimento"
                   grupo="Fechamento Automático de Atendimento"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoAtendimento"
                   observacao="Define o Tipo de Atendimento que será utilizado como padrão para o processo de Fechamento/Cancelamento dos Atendimentos"
        />
        <parametro nome="cidUtilizadoFechamentoAtendimento" grupo="Fechamento Automático de Atendimento"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                   observacao="Define o CID que será utilizado como padrão para o processo de Fechamento/Cancelamento dos Atendimentos"
        />
        <parametro nome="descricaoLocalExameAutorizacao" grupo="Exames" tipo="java.lang.String"
                   observacao="Define a descrição do local do exame (laboratório) a ser exibido no campo Local do Exame da Requisição de Exames."
                   obrigatorio="false"
        />

        <parametro nome="acaoVencimentoValidadeRequisicao" grupo="Exames" tipo="java.lang.Long" defaultValue="2"
                   observacao="Define o que será feito com o exame caso ultrapasse a data de validade da autorização de requisição do exame."
        >
            <checkValues value="0" rotulo="rotulo_cancelar_exame"/>
            <checkValues value="1" rotulo="rotulo_cancelar_autorizacao"/>
            <checkValues value="2" rotulo="rotulo_nao_alterar"/>
        </parametro>

        <parametro nome="obrigatorioCidEncaminhamentoEspecialista" tipo="java.lang.String" defaultValue="N"
                   grupo="Prontuario"
                   observacao="Define se será obrigatório informar o CID no nó Encaminhamento Especialista."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ordenacaoConfirmacaoPresenca" grupo="Recepção" tipo="java.lang.String" defaultValue="P"
                   observacao="Define a descrição do local do exame (laboratório) a ser exibido no campo Local do Exame da Requisição de Exames.">
            <checkValues value="P" rotulo="rotulo_nome_paciente"/>
            <checkValues value="D" rotulo="rotulo_data_hora"/>
        </parametro>

        <parametro nome="tempoAvisoAtendimentoRecenteOutraUnidade" grupo="Recepção" tipo="java.lang.Long"
                   defaultValue="0" obrigatorio="true"
                   observacao="Define em horas, quando será demonstrada uma mensagem ao marcar um atendimento para o paciente na recepção, caso ele tenha sido atendido em outra unidade dentro do período em questão."
        />

        <parametro nome="TipoAnexo" grupo="Visita Domiciliar" tipo="br.com.ksisolucoes.vo.prontuario.basico.TipoAnexo"
                   obrigatorio="false"
                   observacao="Define o tipo de anexo para as fotos dos indíviduos da visita"
        />

        <parametro nome="procedimentoComunidadeTerapeutica" grupo="RAAS"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Define o procedimento de faturamento que será enviado ao RAAS quando for inserido um número de autorização na ficha de acolhimento do paciente."
        />

        <parametro nome="ImprimeTermoImpressaoProntuario" tipo="java.lang.String" grupo="Prontuario" obrigatorio="true"
                   defaultValue="N"
                   observacao="Ao definir como SIM, na tela de IMPRESSÃO DO PRONTUÁRIO (320), ao gerar a impressão do prontuário de um paciente será impresso na primeira página o TERMO DE COMPROMISSO.">

            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ImpressaoDoProntuario" tipo="java.lang.String" grupo="Prontuario" defaultValue="N"
                   observacao="Este parâmetro altera o formato de impressão realizado ao final do atendimento na tela 266, permitindo escolher entre imprimir apenas o último atendimento ou a sequência de atendimentos realizados no mesmo dia.">
            <checkValues value="N" rotulo="rotulo_sequencia_atendimento"/>
            <checkValues value="S" rotulo="rotulo_ultimo_atendimento"/>
        </parametro>

        <parametro nome="ImprimeQrCodeAtestado" tipo="java.lang.String" grupo="Prontuario" obrigatorio="true"
                   defaultValue="N"
                   observacao="Ao definir como sim, quando for emitido no atendimento um documento configurado na tela Consulta do Modelo de Documento (279) como Atestado, o documento emitido deve conter um QR Code.">

            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ImprimeQrCodeReceita" tipo="java.lang.String" grupo="Prontuario" obrigatorio="true"
                   defaultValue="N"
                   observacao="Ao definir como sim, quando imprimir um receituário, será exibido um QRCode para validação do documento.">

            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="valorMaximoUltrapassarCotaProfissionalCBO" grupo="Exames" tipo="java.lang.Double"
                   valorMinimo="0" valorMaximo="999.99"
                   observacao="Define o valor limite que o Profissional / CBO poderá ultrapassar da sua cota de exames. Se estiver com o valor 0 (zero), sistema não permitirá que o Profissional / CBO ultrapasse sua cota."
                   obrigatorio="false"
        />

        <parametro nome="ajustaSaldoCotaFinanceiraPacienteAusente"
                   grupo="Recepção"
                   tipo="java.lang.String"
                   defaultValue="N"
                   obrigatorio="true"
                   observacao="Define se ao registrar a ausência de um paciente, pela recepção (tela 318) ou via processo agendado, o sistema deverá ajustar a cota financeira do prestador com o estorno do valor do procedimento que não foi executado. Caso definido como “Sim”, o valor será ajustado na cota da competência em que a solicitação de agendamento foi criada."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="visualizaAgendadosGeral"
                   grupo="Recepção"
                   tipo="java.lang.String"
                   defaultValue="N"
                   obrigatorio="true"
                   observacao="Permite ao usuário visualizar todos os pacientes agendados na tela 318, no NÓ Confirmação de Presença, desde que o campo Estabelecimento não esteja preenchido."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ExigeAnexarLaudo"
                   grupo="Recepção"
                   tipo="java.lang.String"
                   defaultValue="N"
                   obrigatorio="true"
                   observacao="Torna obrigatório anexar Laudo (documento PDF, JPG, JPEG, JPE ou PNG), na confirmação dos exames."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="sugereClassificaçãoDeRisco" grupo="Prontuario" tipo="java.lang.String" defaultValue="N"
                   observacao=" Sugere a Classificação de Risco do atendimento após seleção do Fator de Risco e dos Sintomas"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="validaTituloEleitoral" grupo="Prontuario" tipo="java.lang.String" defaultValue="N"
                   observacao="O município será validado no cadastro do título de eleitor durante os atendimentos e agendamentos."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="habilitarAceitePesquisa"
                   grupo="Recepção"
                   tipo="java.lang.String"
                   defaultValue="N"
                   obrigatorio="true"
                   observacao="Habilita a opção para informar a autorização do cliente à participar da pesquisa de satisfação"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="editarGrupoNaAplicacaoVacinaNoAtendimento" tipo="java.lang.String" grupo="Recepção" defaultValue="S"
                   observacao="Habilita a edição do grupo de atendimento do paciente direto na aplicação da vacina no atendimento."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="obrigatorioDataSugeridaRetorno" tipo="java.lang.String" defaultValue="N" grupo="Prontuario"
                   observacao="Define se o preenchimento do campo 'Data Sugerida' será obrigatório quando o encaminhamento ao especialista a flag retorno."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibeOcorrenciaAusenciaNoEvolucao" tipo="java.lang.String" defaultValue="S" grupo="Prontuario"
                   observacao="Controla se a ocorrência da ausência do paciente na atividade em grupo será exibida no Nó Evolução."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

    </modulo>

    <modulo nome="25">
        <!-- Integração FastMedic -->
        <parametro nome="urlFastMedicProducao" tipo="java.lang.String" grupo="Integração FastMedic"
                   obrigatorio="true"
                   observacao="Define a URL de produção para o serviço de integração com a FastMedic."
        />

        <parametro nome="urlFastMedicHomologacao" tipo="java.lang.String" grupo="Integração FastMedic"
                   obrigatorio="true"
                   observacao="Define a URL de homologação para o serviço de integração com a FastMedic."
        />

        <parametro nome="urlFastMedicUtilizada"
                   tipo="java.lang.String"
                   grupo="Integração FastMedic"
                   defaultValue="H"
                   observacao="Define a URL que será utilizada no serviço de integração com a FastMedic."
                   obrigatorio="true"
        >
            <checkValues value="H" rotulo="rotulo_homologacao"/>
            <checkValues value="P" rotulo="rotulo_producao"/>
        </parametro>

        <parametro nome="senhaFastMedic" tipo="java.lang.String" grupo="Integração FastMedic"
                   obrigatorio="false"
                   observacao="Senha utilizada para a integração das unidades."
        />

        <parametro nome="tipoAtendimentoClassificacaoRiscoFastMedic" tipo="java.lang.String" grupo="Integração FastMedic"
                   obrigatorio="false"
                   observacao="Código do tipo de atendimento que deve ser filtrado. Os códigos devem ser separados por ;. Exemplo: 123;456;789"
        />
        <!-- Integração FastMedic -->

        <!-- Integração Telemedicina -->
        <parametro nome="descricaoAtendimentoTeletriagem" tipo="java.lang.String" grupo="Integração Telemedicina"
                   obrigatorio="false"
                   observacao="Nome utilizado na tela 229 para definição do tipo de atendimento Teletriagem"
        />

        <parametro nome="descricaoAtendimentoTeleconsulta" tipo="java.lang.String" grupo="Integração Telemedicina"
                   obrigatorio="false"
                   observacao="Nome utilizado na tela 229 para definição do tipo de atendimento Teleconsulta"
        />

        <parametro nome="descricaoNaturezaTipoAtendimento" tipo="java.lang.String" grupo="Integração Telemedicina"
                   obrigatorio="false"
                   observacao="Parâmetro Natureza Procura na tela 235 que deve estar cadastrado junto aos parâmetros da tela 229"
        />
        <!-- Fim Integração Telemedicina -->

        <!-- Assinatura Digital -->
        <parametro nome="habilitaAssinaturaDigital" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String" defaultValue="N"
                   observacao="Através de uma integração via API com sistema terceiro, o sistema permitirá realizar a assinatura digital de documentos emitidos dentro de um atendimento, como receitas e atestados."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="urlPortalDispensacaoCelk" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="URL utilizada para fazer os envios de receitas assinadas digitalmente para o portal de dispensação da celk.">
        </parametro>

        <parametro nome="provedorAssinaturaDigital" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String" defaultValue="0"
                   observacao="Através de uma integração via API com sistema terceiro, o sistema permitirá realizar a assinatura digital de documentos emitidos dentro de um atendimento, como receitas e atestados."
        >
            <checkValues value="0" rotulo="rotulo_globaltech"/>
            <checkValues value="1" rotulo="rotulo_bry"/>
        </parametro>

        <parametro nome="urlServicoAssinaturaDigital" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="URL utilizada para o serviço de terceiros para o envio do arquivo a ser assinado digitalmente">
        </parametro>

        <parametro nome="urlAutenticacaoAssinaturaDigital" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="URL utilizada para o serviço de terceiros para aitemtocação da assinatura digital">
        </parametro>

        <parametro nome="urlSyngular" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="URL utilizada para autenticação dos serviços da Syngular">
        </parametro>

        <parametro nome="urlVidaas" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="URL utilizada para autenticação dos serviços do VidaaS">
        </parametro>

        <parametro nome="autenticaçãoAssinaturaDigital" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="Autenticação utilizada para o serviço de terceiros para o envio do arquivo a ser assinado digitalmente">
        </parametro>

        <parametro nome="clientIdAssinaturaDigital" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="Chave utilizada para o serviço de terceiros para o envio do arquivo a ser assinado digitalmente (Bry)">
        </parametro>

        <parametro nome="clientSecretAssinaturaDigital" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="Client secret utilizado para o serviço de terceiros para o envio do arquivo a ser assinado digitalmente (Bry)">
        </parametro>

        <parametro nome="clientIdPscSyngular" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="Chave utilizado para o serviço de autenticação da Syngular">
        </parametro>

        <parametro nome="clientSecretPscSyngular" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="Client secret utilizado para o serviço de autenticação da Syngular">
        </parametro>

        <parametro nome="clientIdPscVidaas" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="Chave utilizado para o serviço de autenticação do Vidaas">
        </parametro>

        <parametro nome="clientSecretPscVidaas" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="Client secret utilizado para o serviço de autenticação do Vidaas">
        </parametro>

        <parametro nome="valorUuidAssinaturaDigital" grupo="Prontuario:Assinatura Digital" tipo="java.lang.String"
                   observacao="Valor do Uuid">
        </parametro>

        <parametro nome="duracaoLinkAssinaturaDigital" grupo="Prontuario:Assinatura Digital" tipo="java.lang.Long" defaultValue="15"
                   observacao="Tempo que o link permanece disponível para assinatura">
        </parametro>
        <!-- Assinatura Digital -->

    </modulo>

    <!--Geral-->
    <modulo nome="1">

        <parametro nome="plataformaEnvioMensagens" grupo="Comunicação" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define qual plataforma a ser usada no envio e recebimento de mensagens. O serviço GOVE.DIGITAL é apenas utilizado pelos processos de Envio dos Agendamentos pela Solicitação e Reenvio dos Agendamentos pela Solicitação"
        >
            <checkValues value="1" rotulo="rotulo_sms"/>
            <checkValues value="2" rotulo="rotulo_whatsapp"/>
            <checkValues value="3" rotulo="rotulo_twilio"/>
            <checkValues value="4" rotulo="rotulo_gove_digital"/>
        </parametro>

        <parametro nome="urlGoveDigital" grupo="Comunicação:IntegracaoWhatsGoveDigital" tipo="java.lang.String"
                   observacao="Define o endereco da API a ser utilizado para envio das mensagem."
        />

        <parametro nome="emailGoveDigital" grupo="Comunicação:IntegracaoWhatsGoveDigital" tipo="java.lang.String"
                   observacao="Define o email a ser utilizado para autenticação da API."
        />

        <parametro nome="senhaGoveDigital" grupo="Comunicação:IntegracaoWhatsGoveDigital" tipo="java.lang.String"
                   observacao="Define a senha a ser utilizada para autenticação da API."
        />

        <parametro nome="urlBaseApiWhatsApp" grupo="Comunicação" tipo="java.lang.String"
                   observacao="Define a URL base da API de comunicação com whatsapp quando o parâmetro plataformaEnvioMensagens for diferente WhatsApp. Os endpoints são definidos no código."
        />

        <parametro nome="contatoPadraoSuporteWhatsApp" grupo="Comunicação" tipo="java.lang.String"
                   observacao="Define o contato enviado para WhatsApp que irá receber duvidas dos pacientes."
        />


        <parametro nome="diasMaximoGeracaoFichaInvestigacaoAgravo" grupo="Relatórios" tipo="java.lang.Long" defaultValue="10"
                   observacao="Define o intervalo máximo em dias para gerar a impressão das fichas de agravo (967)."
        />

        <parametro nome="numeroSuporteVigilanciaExterno" grupo="Suporte" tipo="java.lang.String"
                   observacao="Define o número do telefone de suporte do sistema para vigilância externo."
        />

        <parametro nome="Numero_suporte" grupo="Suporte" tipo="java.lang.String" defaultValue="0800-642-1056"
                   observacao="Define o número do telefone de suporte do sistema."
        />
        <parametro nome="urlSistemaSuporte" grupo="Suporte" tipo="java.lang.String"
                   observacao="Define a URL para acesso de sistema de suporte. O link estará disponível no cabeçalho do sistema."
        />
        <parametro nome="Logotipo_Empresa" grupo="Relatórios" tipo="java.lang.String"
                   defaultValue="imagens/logo_documento.png"
                   observacao="Define o caminho da imagem no FTP para as impressões que possuem cabeçalho timbrado."
        />
        <parametro nome="Logotipo_Login" grupo="Configurações" tipo="java.lang.String"
                   defaultValue="imagens/logo_login.png"
                   observacao="Define o caminho da imagem no FTP para o logotipo do login."
        />

        <parametro nome="enderecoPadraoPacientesSemEndereco" grupo="Configurações"
                   tipo="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"
                   observacao="Define o endereço padrão para pacientes sem endereço informado durante a execução de processos."
                   obrigatorio="false"
        />

        <parametro nome="AlertaPainel" grupo="Configurações" tipo="java.lang.String"
                   defaultValue="audio/alerta_painel.ogg"
                   observacao="Define o caminho no FTP do arquivo de áudio para o painel de chamados. DEVE SER UM ARQUIVO .ogg"
        />
        <parametro nome="imagemFundo" grupo="Configurações" tipo="java.lang.String"
                   observacao="Define o caminho da imagem de fundo do sistema."
        />

        <parametro nome="EmailAdministrador" grupo="Configurações" tipo="java.lang.String"
                   observacao="Define o email do administrador do sistema."
        />
        <parametro nome="URL_Papaleguas" grupo="Configurações" tipo="java.lang.String"
                   defaultValue="https://papaleguas.celk.com.br"
                   observacao="Define a URL de acesso ao sistema de entrega de notificações da CELK (Papaléguas)."
        />
        <parametro nome="URL_ReceitaFederal" grupo="Configurações" tipo="java.lang.String"
                   defaultValue="https://receitafederal.celk.com.br"
                   observacao="Define a URL de acesso ao serviço de consulta de dados da Receita Federal."
        />
        <parametro nome="Tipo_de_storage" grupo="Configurações" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define em qual recurso os arquivos gerenciados pela aplicação são armazenados."
        >
            <checkValues value="0" rotulo="rotulo_ftp"/>
            <checkValues value="1" rotulo="rotulo_s3"/>
        </parametro>
        <parametro nome="EmailNotificacao" grupo="Configurações" tipo="java.lang.String"
                   defaultValue="<EMAIL>"
                   observacao="Define o email que receberá notificações de erro do sistema."
        />

        <parametro nome="utilizaControleHorarioUsuario" grupo="Configurações" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se será utilizado o controle de horário de trabalho para o usuário.."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ObrigaINEFichaProcedimento" grupo="Configurações" tipo="java.lang.Long" defaultValue="0"
                   observacao="Obrigatório o preenchimento do campo Equipe na tela Usuário para faturamento diferente do atendimento." obrigatorio="true">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="PreencherDataHoraAutomatica" grupo="Configurações" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite ao usuário definir se a data e hora da aplicação da guia será preenchido automaticamente com data e hora em que a guia foi confirmada."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="usarLogoTipoCisamrec" grupo="Configurações" tipo="java.lang.String" defaultValue="N"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="bloqueioFlagGestantePeloCBO" grupo="Cadsus" tipo="java.lang.String" defaultValue="N"
                   observacao="Quando habilitado(parametro =SIM) impede que o CBO (cadastrados no sub-nivel)desmarque a condição de gestante durante edição das fichas de Cadastro Ficha Individual Odontológica, Cadastro do Usuário / Cidadão e Visita Domiciliar mesmo que o pré-natal esteja ativo"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="descricaoEstabelecimentoImportacaoCNES"
                   grupo="Configurações" tipo="java.lang.String"
                   defaultValue="F"
                   observacao="Define o que vai ser setado na descrição do estabelecimento na importação do XML do CNES. Para SMS deve ser setado o nome fantasia, pois na razão social vem o nome da prefeitura, e para consórcios a razão social.">
            <checkValues value="F" rotulo="rotulo_nome_fantasia"/>
            <checkValues value="R" rotulo="rotulo_razao_social"/>
        </parametro>

        <parametro nome="convenioSUS" grupo="Configurações" tipo="br.com.ksisolucoes.vo.prontuario.basico.Convenio"
                   observacao="Registro de Convênio referente ao SUS, para uso em processos específicos para o SUS."
                   obrigatorio="true"
        />

        <parametro nome="idServicoNotasSistema" grupo="Configurações:Notas do Sistema" tipo="java.lang.String"
                   observacao="Id de acesso para o serviço de notas do sistema"
        />

        <parametro nome="chaveServicoNotasSistema" grupo="Configurações:Notas do Sistema" tipo="java.lang.String"
                   observacao="Chave de acesso para o serviço de notas do sistema"
        />

        <parametro nome="hostServicoNotas" grupo="Configurações:Notas do Sistema" tipo="java.lang.String"
                   observacao="host de Acesso para o serviço de notas do sistema ex: https://celk.com.br/notas/service/nota/datacadastromaiorque/dd-MM-yyyy/"
        />

        <parametro nome="ControlaEntregaCartao" grupo="Gestão do CNS" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se realiza o processo de entrega do cartão automáticamente ou manualmente."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>


        <parametro nome="Hospital" grupo="Hospital" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se o sistema será configurado como hospital."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permitePesquisarEndereçoSemFamíla" grupo="CadSUS" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se na tela Domiciliar e Territorial (396) será possível utilizar a Flag Apenas Endereços Com Família. Caso o parâmetro esteja definido como Não, as pesquisas realizadas nessa tela irão considerar apenas os endereços com famílias.">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
        <parametro nome="idadeAcompanhamento" grupo="CadSUS" tipo="java.lang.Long" defaultValue="144"
                   observacao="Idade em meses que diz se o indíviduo é criança."
        />

        <parametro nome="maxResultGeorreferenciamento" grupo="CadSUS" tipo="java.lang.Long" defaultValue="1000"
                   observacao="Quantidade máxima de pontos no mapa."
        />

        <parametro nome="cidadeEstrangeiro" grupo="CadSUS" tipo="br.com.ksisolucoes.vo.basico.Cidade"
                   observacao="Cidade para ser utilizada na criação dos endereço dos usuários estrangeiros."
                   obrigatorio="true"
        />

        <parametro nome="utilizaEnderecoEstruturado" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se o município utilizará o cadastro estruturado de endereços."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="HabilitarModoLiteConsultaPacientes" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Habilitar modo lite na consulta de pacientes.">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="MunicipioComEnderecoEstruturado" grupo="CadSUS" tipo="br.com.ksisolucoes.vo.basico.Cidade"
                   observacao="Define qual município utilizará as regras de cadastro de endereço estruturado nas telas de cadastro de pacientes e domicílios."
                   obrigatorio="false"
        />

        <parametro nome="ValidarDocumentoCadastroNovo" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Valida quando é um Cadastro Novo a digitação de pelo menos um documento."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ValidarTelefoneCadastroNovo" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Valida quando é um Cadastro Novo a digitação de pelo menos um telefone."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ValidarCnsCadastroNovo" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Valida quando é um Cadastro Novo a digitação do CNS."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="filtraPorMunicipioResidencia" tipo="java.lang.String" grupo="CadSUS" defaultValue="N"
                   observacao="Define se deve ou não ser aplicado o filtro Município de Residência na consulta de pacientes (programa 3)"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ValidarCpfCadastro" tipo="java.lang.String" grupo="CadSUS"
                   observacao="Valida no cadastro de paciente a inserção do CPF."
                   brigatorio="false" defaultValue="N">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="referencia" grupo="CadSUS" tipo="java.lang.String"
                   observacao="Utiliza o campo Referência no lugar do campo Código nas telas de cadastro de paciente"
                   obrigatorio="false" defaultValue="N">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="usaBiometria" grupo="Biometria" tipo="java.lang.String"
                   observacao="Define se habilita ou não o componente de biometria na web."
                   obrigatorio="false" defaultValue="N">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="cpfObrigatorio" grupo="Profissional" tipo="java.lang.String"
                   observacao="Flag utilizada para validar no cadastro do profissional se o CPF deve ser obrigatório na digitação."
                   obrigatorio="false" defaultValue="S">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="MotivoDesligamentoAgendador" grupo="Profissional" tipo="java.lang.String"
                   observacao="Esse motivo será utilizado quando o profissional for desligado pelo Agendador de Processos."
                   obrigatorio="true"/>

        <parametro nome="LimiteDesligamento" grupo="Profissional" tipo="java.lang.Long"
                   observacao="Limite (em meses) do último acesso do Profissional para desligá-lo automaticamente pelo Agendador de Processos."
                   defaultValue="2"
                   obrigatorio="true"/>

        <parametro nome="convenioIPE" grupo="Hospital:IPE" tipo="br.com.ksisolucoes.vo.prontuario.basico.Convenio"
                   observacao="Registro de Convênio referente ao IPE, para uso em processos específicos para o IPE."
                   obrigatorio="false"
        />

        <parametro nome="tamanhoAnexoMensagem" grupo="Configurações" tipo="java.lang.Long" defaultValue="2048"
                   obrigatorio="true"
                   observacao="Tamanho máximo total dos anexos da mensagem (KB)."/>

        <parametro nome="UnidadeCentralIntegracaoEsus" grupo="Configurações" tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao="Define a Unidade que será utilizada como remetente para enviar os dados."
                   obrigatorio="false"
        />

        <parametro nome="UnidadePadraoAgendadorProcessos" grupo="Configurações"
                   tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao="Define a Unidade que será utilizada como padrão para executar os processos configurados no Timer Service(Agendador de Processos)."
                   obrigatorio="true"
        />

        <parametro nome="tamanhoMaximoPDFComplementarIPE" tipo="java.lang.Long"
                   observacao="Define o tamanho máximo de um aquivo .pdf para que esse possa ser anexado"
                   defaultValue="0"
        />

        <parametro nome="limpaCepInvalido" grupo="Configurações" tipo="java.lang.Long"
                   observacao="Define se limpa o cep quando retornado inválido pelo webservice"
                   defaultValue="1"
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="buscarCep" grupo="Configurações" tipo="java.lang.Long"
                   observacao="Define se vai ser realizada a busca no webservice"
                   defaultValue="1"
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="habilitarNPS" grupo="Configurações" tipo="java.lang.Long"
                   observacao="Define se será habilitada a ferramenta de NPS."
                   defaultValue="0"
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ordenarAutoCompletePaciente" grupo="Configurações" tipo="java.lang.Long"
                   observacao="Define se o autocomplete de paciente será ordenado pelo nome do paciente."
                   defaultValue="1"
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="tempoTimeoutCep" grupo="Configurações" tipo="java.lang.Long"
                   observacao="Define o tempo de espera até ocorrer um timeout (em segundos)"
                   defaultValue="5"
        />

        <parametro nome="ModeloComprovanteAgendamento" tipo="java.lang.String" defaultValue="P" grupo="Relatórios"
                   observacao="Define o modelo da impressão do processo do comprovante de agendamento."
        >
            <checkValues value="P" rotulo="rotulo_padrao"/>
            <checkValues value="H" rotulo="rotulo_hospital"/>
        </parametro>

        <parametro nome="exibeLogoSusImpressaoDocumentos" tipo="java.lang.Long" grupo="Relatórios" defaultValue="1"
                   observacao="Define se será exibido a logo do SUS na impressão dos recibos do consórcio e impressão do Nó Documentos.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="procedimentoConsultaComObservacaoHospital" grupo="Hospital"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Procedimento para encaminhamento de observação."
                   obrigatorio="false"
        />

        <parametro nome="Logotipo" grupo="Relatórios:Cabeçalho Formulário Estado" tipo="java.lang.String"
                   observacao="Define o caminho do logotipo no FTP que será impresso no cabeçalho das impressões dos formulários do estado"
        />

        <parametro nome="Linha1" grupo="Relatórios:Cabeçalho Formulário Estado" tipo="java.lang.String"
                   observacao="Descrição que será impressa na 1ª linha do cabeçalho das impressões dos formulários do estado"
        />

        <parametro nome="Linha2" grupo="Relatórios:Cabeçalho Formulário Estado" tipo="java.lang.String"
                   observacao="Descrição que será impressa na 2ª linha do cabeçalho das impressões dos formulários do estado"
        />

        <parametro nome="Linha3" grupo="Relatórios:Cabeçalho Formulário Estado" tipo="java.lang.String"
                   observacao="Descrição que será impressa na 3ª linha do cabeçalho das impressões dos formulários do estado"
        />

        <parametro nome="Linha4" grupo="Relatórios:Cabeçalho Formulário Estado" tipo="java.lang.String"
                   observacao="Descrição que será impressa na 4ª linha do cabeçalho das impressões dos formulários do estado"
        />

        <parametro nome="layoutMenu" grupo="Configurações" tipo="java.lang.Long" defaultValue="2"
                   observacao="Define o layout dos menus ao logar no sistema."
                   obrigatorio="true"
        >
            <checkValues value="1" rotulo="rotulo_hospital"/>
            <checkValues value="2" rotulo="rotulo_unidade_saude"/>
        </parametro>

        <parametro nome="tipoImportacaoCNES" grupo="Configurações" tipo="java.lang.String" defaultValue="ZIP"
                   observacao="Define o tipo do arquivo que será importado no CNES"
                   obrigatorio="true"
        >
            <checkValues value="XML" rotulo="rotulo_xml"/>
            <checkValues value="ZIP" rotulo="rotulo_zip"/>
        </parametro>

        <parametro nome="despesasMedicamentos" grupo="Hospital:IPE"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                   observacao="Define a despesa para o lançamento de medicamentos do IPE."
                   obrigatorio="true"
        />

        <parametro nome="convenioParticular" grupo="Hospital:Financeiro"
                   tipo="br.com.ksisolucoes.vo.prontuario.basico.Convenio"
                   observacao="Registro de Convênio referente ao Particular, para uso em processos específicos para o Particular."
        />

        <parametro nome="adiantamento" grupo="Hospital:Financeiro"
                   tipo="br.com.ksisolucoes.vo.hospital.financeiro.FormaPagamento"
                   observacao="Registro da Forma de Pagamento referente ao Adiantamento, para uso em processos específicos para o Adiantamento"
                   obrigatorio="true"
        />

        <parametro nome="desconto" grupo="Hospital:Financeiro"
                   tipo="br.com.ksisolucoes.vo.hospital.financeiro.FormaPagamento"
                   observacao="Registro da Forma de Pagamento referente ao Desconto, para uso em processos específicos para o Desconto"
                   obrigatorio="true"
        />

        <parametro nome="usuarioMensagemPacienteDuplicado" grupo="Configurações"
                   tipo="br.com.ksisolucoes.vo.controle.Usuario"
                   observacao="Usuário que irá receber as mensagens contendo anexo com os pacientes duplicados no sistema."
                   obrigatorio="true"
        />

        <parametro nome="limitePesquisa" grupo="Pesquisa" tipo="java.lang.Long" defaultValue="3"
                   observacao="Numero que limita a quantidade, de perguntas adicionadas a pesquisa."
                   obrigatorio="true"
        />

        <parametro nome="validaCpfTelefoneUsuario" grupo="Configurações:Usuários" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Ao fazer o login e o usuário não possuir CPF ou Telefone configurado, abrir página para atualizar os dados"
                   obrigatorio="true"
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="CaminhoSigtap" grupo="Configurações" tipo="java.lang.String"
                   observacao="Define o caminho do arquivo de importação do SIGTAP para o preocesso de importação automática."
                   obrigatorio="true"
        />

        <parametro nome="motivoDestinoSaidaObito" grupo="Exclusão Paciente:Ficha de Acolhimento"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida"
                   observacao="Motivo de Destino/Saída para a ficha de acolhimento do paciente excluído por motivo de óbito."
                   obrigatorio="true"
        />

        <parametro nome="motivoDestinoSaidaOutros" grupo="Exclusão Paciente:Ficha de Acolhimento"
                   tipo="br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida"
                   observacao="Motivo de Destino/Saída para a ficha de acolhimento do paciente excluído por outros motivos."
                   obrigatorio="true"
        />

        <parametro nome="usuarioNotificacaoEmprestimo" grupo="Exclusão Paciente:Empréstimo"
                   tipo="br.com.ksisolucoes.vo.controle.Usuario"
                   observacao="Usuário que irá receber as mensagens de notificações de empréstimos do paciente excluído."
                   obrigatorio="true"
        />

        <parametro nome="mensagemNotificacaoEmprestimo" grupo="Exclusão Paciente:Empréstimo" tipo="java.lang.String"
                   observacao="Mensagem de notificação de empréstimo do paciente excluído."
                   obrigatorio="true" defaultValue="Favor avaliar os empréstimos do mesmo e baixá-los."
        />

        <parametro nome="mensagemReversaoNotificacaoEmprestimo" grupo="Exclusão Paciente:Empréstimo"
                   tipo="java.lang.String"
                   observacao="Mensagem de notificação de empréstimo do paciente situacao."
                   obrigatorio="true" defaultValue="Favor suspender a baixa dos empréstimos do mesmo."
        />

        <parametro nome="usuarioNotificacaoPedidoTFD" grupo="Exclusão Paciente:Pedido TFD"
                   tipo="br.com.ksisolucoes.vo.controle.Usuario"
                   observacao="Usuário que irá receber as mensagens de notificações dos pedidos do TFD do paciente excluído."
                   obrigatorio="true"
        />

        <parametro nome="mensagemNotificacaoPedidoTFD" grupo="Exclusão Paciente:Pedido TFD" tipo="java.lang.String"
                   observacao="Mensagem de notificação dos pedidos do TFD do paciente excluído."
                   obrigatorio="true"
                   defaultValue="Favor avaliar os pedidos que estão aguardando o retorno da regional/SISREG e efetuar a baixa."
        />

        <parametro nome="mensagemReversaoNotificacaoPedidoTFD" grupo="Exclusão Paciente:Pedido TFD"
                   tipo="java.lang.String"
                   observacao="Mensagem de notificação dos pedidos do TFD do paciente situacao."
                   obrigatorio="true"
                   defaultValue="Favor suspender a baixa dos pedidos que estão aguardando o retorno da regional/SISREG."
        />

        <parametro nome="idadeMinimaGestante" grupo="CadSUS" tipo="java.lang.Long" defaultValue="9" obrigatorio="true"
                   observacao="Define a idade mínima para ser gestante no cadastro de Usuário / Cidadão."/>

        <parametro nome="permiteAlterarEndereçoDomicilio" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Habilita a opção de alterar o endereço do paciente nos cadastros quando possuir vinculo com uma família."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permiteAlterarEndereçoDomicilioOutraUnidade" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Habilita a opção de alterar o endereço do paciente nos cadastros de outras unidades."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="obrigatorioInformarRaca" grupo="CadSUS" tipo="java.lang.Long" defaultValue="1"
                   observacao="Torna obrigatório informar o campo Raça nos cadastros de paciente."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="exibirLogoCelk" grupo="Configurações" tipo="java.lang.Long" defaultValue="1"
                   observacao="Exibir a logo da CELK no sistema" obrigatorio="true">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="exibirLinkFamilias" grupo="Configurações" tipo="java.lang.Long" defaultValue="0"
                   observacao="Exibir a link para baixar o aplicativo para android usado pelos ACS" obrigatorio="true">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="textoportaria344" grupo="Configurações" tipo="java.lang.Long" defaultValue="1"
                   observacao="Exibir mensagem na impressão da receita conforme a portaria 344/1998/ANVISA" obrigatorio="true">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permiteTransferirComponente" grupo="Configurações" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se será permitido transferir componentes de uma família para outra em microáreas diferentes"
                   obrigatorio="true">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="NumeroTentativasLogin" grupo="Configurações" tipo="java.lang.Long" defaultValue="10"
                   valorMinimo="3" valorMaximo="10"
                   observacao="Define o número de tentativas para tentar logar no sistema."/>

        <parametro nome="TempoLimiteSessaoAtiva" grupo="Configurações" tipo="java.lang.Long" defaultValue="120"
                   obrigatorio="true"
                   observacao="Tempo limite para cada sessão ativa (em minutos)."/>

        <!--############### USAR AS CONFIGURAÇÕES ABAIXO PARA TESTES LOCAIS #############-->
        <!-- UtilizaServicoIntegracaoCadsusPixPDQ: Sim -->
        <!-- AmbienteIntegracaoPixPDQ: Homologação -->
        <!-- EndPointCadsusV5: https://servicoshm.saude.gov.br/cadsus/CadsusService/v5r0?wsdl -->
        <!-- UrlWSDLPixPDQ: http://localhost:8080/cadsus/wsdl/PDQSupplier.wsdl -->
        <!-- UsuarioAutenticacaoCadsusPixPDQ: CADSUS.CNS.PDQ.PUBLICO -->
        <!-- SenhaAutenticacaoCadsusPixPDQ: kUXNmiiii#RDdlOELdoe00966 -->
        <!-- NumeroCnesCadsusV5: 6963447 -->
        <!-- UsuarioCnesCadsusV5: LEONARDO -->

        <parametro nome="UtilizaServicoIntegracaoCadsusPixPDQ" grupo="CadSUS:Integração PIX/PDQ" tipo="java.lang.String"
                   defaultValue="N"
                   observacao="Define se o sistema fará uso do recurso que busca os dados do paciente por meio dos Web Services CADSUS V5 e PIX/PDQ."
                   obrigatorio="false">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="AmbienteIntegracaoPixPDQ" grupo="CadSUS:Integração PIX/PDQ" tipo="java.lang.String"
                   obrigatorio="true" defaultValue="H"
                   observacao="Define o ambiente de integração com Web Service PIX/PDQ.">
            <checkValues value="H" rotulo="rotulo_homologacao"/>
            <checkValues value="P" rotulo="rotulo_producao"/>
        </parametro>

        <parametro nome="EndPointCadsusV5" grupo="CadSUS:Integração PIX/PDQ"
                   tipo="java.lang.String" obrigatorio="true"
                   observacao="Define o Endpoint utilizado para consulta ao Web Service do CADSUS V5."/>

        <parametro nome="UrlWSDLPixPDQ" grupo="CadSUS:Integração PIX/PDQ"
                   tipo="java.lang.String" obrigatorio="true"
                   observacao="Define a URL do WSDL utilizado para consulta ao Web Service do PIX/PDQ."/>

        <parametro nome="UsuarioAutenticacaoCadsusPixPDQ" grupo="CadSUS:Integração PIX/PDQ"
                   tipo="java.lang.String" obrigatorio="true"
                   observacao="Define o usuário utilizado para autenticação com os Web Services CADSUS V5 e PIX/PDQ."/>

        <parametro nome="SenhaAutenticacaoCadsusPixPDQ" grupo="CadSUS:Integração PIX/PDQ"
                   tipo="java.lang.String" obrigatorio="true"
                   observacao="Define a senha utilizada para autenticação com os Web Services CADSUS V5 e PIX/PDQ."/>

        <parametro nome="NumeroCnesCadsusV5" grupo="CadSUS:Integração PIX/PDQ"
                   tipo="java.lang.String" obrigatorio="true"
                   observacao="Define o Número do CNES da Unidade utilizado para consultar o Web Service do CADSUS V5."/>

        <parametro nome="UsuarioCnesCadsusV5" grupo="CadSUS:Integração PIX/PDQ"
                   tipo="java.lang.String" obrigatorio="true"
                   observacao="Define o Usuário do CNES utilizado para consultar o Web Service do CADSUS V5."/>

        <parametro nome="URL_BoletoAPI" grupo="Configurações:Boleto" tipo="java.lang.String"
                   defaultValue="https://boleto.celk.com.br"
                   observacao="Define a URL de acesso ao serviço de geração e consulta de boletos." obrigatorio="true"
        />

        <parametro nome="TipoIntegracao_BoletoAPI" grupo="Configurações:Boleto" tipo="java.lang.String"
                   observacao="Define a forma de funcionamento do serviço de geração e consulta de boletos."
                   obrigatorio="true" defaultValue="REMESSA"
        >
            <checkValues value="REMESSA" rotulo="rotulo_remessa_retorno"/>
            <checkValues value="WEBSERVICE" rotulo="rotulo_web_service"/>
        </parametro>

        <parametro nome="Ambiente_BoletoAPI" grupo="Configurações:Boleto" tipo="java.lang.String"
                   observacao="Define o ambiente que será usado pelo serviço de geração e consulta de boletos."
                   obrigatorio="true"
        >
            <checkValues value="HOMOLOGACAO" rotulo="rotulo_homologacao"/>
            <checkValues value="OFICIAL" rotulo="rotulo_oficial"/>
        </parametro>

        <parametro nome="TokenAcessoHomologacao" grupo="Configurações:Boleto" tipo="java.lang.String"
                   defaultValue="api-key_7RepQ86Oz2f4JdsxXMEQpViifo8sYp1-x_80Fo13EJ0="
                   observacao="Define o Token utilizado na autenticação com o ambiente de homologação da API Boleto Cloud."
                   obrigatorio="true"
        />

        <parametro nome="SenhaAcessoHomologacao" grupo="Configurações:Boleto" tipo="java.lang.String"
                   defaultValue="token"
                   observacao="Define a Senha utilizada na autenticação com o ambiente de homologação da API Boleto Cloud."
                   obrigatorio="true"
        />

        <parametro nome="TokenAcessoProducao" grupo="Configurações:Boleto" tipo="java.lang.String"
                   defaultValue="api-key_Qx3gZ-r4EB973tXKTMY9PIIKiHvWUfa6hk0hHsdVtuw="
                   observacao="Define o Token utilizado na autenticação com o ambiente de produção da API Boleto Cloud."
                   obrigatorio="true"
        />

        <parametro nome="SenhaAcessoProducao" grupo="Configurações:Boleto" tipo="java.lang.String" defaultValue="token"
                   observacao="Define a Senha utilizada na autenticação com o ambiente de produção da API Boleto Cloud."
                   obrigatorio="true"
        />

        <parametro nome="tituloPaginaLogin" grupo="Configurações" tipo="java.lang.String" defaultValue="Celk Saúde"
                   observacao="Define o título do sistema da aba do navegador."
        />

        <parametro nome="SecretariaMunicipaldeSaude" grupo="Configurações" tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao="Esse parâmetro é utilizado para definir os dados da Secretaria de Saúde que serão impressos no Termo de Compromisso do Prontuário."
        />

        <parametro nome="HabilitarLoginComCertificadoDigital" grupo="Login" tipo="java.lang.Long" defaultValue="0"
                   observacao="Habilita a função de logar no sistema por meio de certificado digital.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ObrigarEnderecoMac" grupo="Login" tipo="java.lang.Long" defaultValue="0"
                   observacao="Esse parâmetro define se o usuário poderá logar no sistema se o MAC Address estiver na lista de MACs liberado na tela Acessos por IP/MAC (283).">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ObrigarCertificadoParaIpNaoLiberado" grupo="Login" tipo="java.lang.Long" defaultValue="0"
                   observacao="Quando o parâmetro HabilitarLoginComCertificadoDigital estiver ativo, esse parâmetro define se o usuário poderá se autenticar somente usando um certificado,
 ou se poderá usar os campos usuário e senha. O lista de IP liberado é de acordo com a tela Acessos por IP (283).">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="MensagemAvisoLogin" grupo="Login" tipo="java.lang.String" obrigatorio="false"
                   observacao="Quando preenchido, ao realizar o login vai abrir pagina de aviso com a mensagem e obrigando o usuário confirmar a leitura sempre que realizar o login."
        />

        <parametro nome="ID_IdentificAPI" grupo="Login" tipo="java.lang.String" obrigatorio="true"
                   observacao="ID de acesso a API Identific para integração do login com certificado digital. Para criar nova chave acesse: https://identific.certificadodigital.com.br/signup"
        />

        <parametro nome="KEY_IdentificAPI" grupo="Login" tipo="java.lang.String" obrigatorio="true"
                   observacao="Chave de acesso a API Identific para integração do login com certificado digital. Para criar nova chave acesse: https://identific.certificadodigital.com.br/signup"
        />

        <parametro nome="AutenticacaoDoisFatores" grupo="Login" tipo="java.lang.Long" defaultValue="0"
                   observacao="Habilitar autenticação em dois fatores">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <!--LDAP-->
        <parametro nome="HabilitarLoginLDAP" grupo="Login:LDAP" tipo="java.lang.Long" defaultValue="0"
                   observacao="Habilita o uso do serviço Active Directory para login centralizado dos usuários.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="URL_Servico" grupo="Login:LDAP" tipo="java.lang.String" obrigatorio="true"
                   observacao="URL (FQDN) para acesso ao serviço LDAPS, deve obrigatóriamente usar a porta com acesso SSL.  Ex.: host.domain.br:636"
        />

        <parametro nome="BaseDN" grupo="Login:LDAP" tipo="java.lang.String" obrigatorio="true"
                   observacao="Deve conter o path AD base para os cadastros dos usuários. Ex.: cn=Users,dc=domain,dc=br"
        />

        <parametro nome="LdapUserAdministrador" grupo="Login:LDAP" tipo="java.lang.String" obrigatorio="true"
                   observacao="Deve conter o usuário com permissão de administrador do LDAP que permita cadastrar novos usuários no domínio."
        />

        <parametro nome="LdapSenhaAdministrador" grupo="Login:LDAP" tipo="java.lang.String" obrigatorio="true"
                   observacao="Deve conter a senha do usuário com permissão de administrador do LDAP que permita cadastrar novos usuários no domínio."
        />

        <!--Indra-->
        <parametro nome="HabilitarIntegracaoBarramentoIndra" grupo="IntegraçãoIndra" tipo="java.lang.Long"
                   defaultValue="0"
                   observacao="Habilita o envio de dados de atendimento para o barramento da indra.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="IndraAWSAccessKeyID" grupo="IntegraçãoIndra" tipo="java.lang.String" obrigatorio="true"
                   observacao="Para fazer a integração o sistema tem que ter acesso a colocar os atendimentos na fila de processamento da AWS"
        />
        <parametro nome="IndraAWSSecretAccessKey" grupo="IntegraçãoIndra" tipo="java.lang.String" obrigatorio="true"
                   observacao="Para fazer a integração o sistema tem que ter acesso a colocar os atendimentos na fila de processamento da AWS"
        />

        <parametro nome="IndraURLQueue" grupo="IntegraçãoIndra" tipo="java.lang.String" obrigatorio="true"
                   observacao="A URL da fila onde serão colocados os atendimentos."
        />

        <parametro
                grupo="Google"
                nome="googleGeocodingAPIKey"
                tipo="java.lang.String"
                obrigatorio="false"
                observacao="Número de meses padrão para o cálculo do vencimento do contrato com os prestadores para o edital."
        />
        <parametro nome="exigeCategorizacaoCPF" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Torna obrigatório marcar o motivo que gerou um cadastro sem o CPF."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>


        <parametro nome="ExigeEquipeAcompanhamento" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Tornar o campo Equipe de Acompanhamento de preenchimento obrigatório."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>


        <!--Pharos-->
        <parametro nome="HabilitarIntegracaoPharos" grupo="IntegraçãoPharos" tipo="java.lang.Long"
                   defaultValue="0"
                   observacao="Habilita o envio de dados para o Pharos.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="UrlIntegradorRnds" grupo="IntegracaoRNDS" tipo="java.lang.String"
                   observacao="URL do integrador RNDS."
        />
        <parametro nome="UsaNovoModeloIntegracaoRnds" grupo="IntegracaoRNDS" tipo="java.lang.Long"
                   defaultValue="0"
                   observacao="Habilita a nova forma de integrar os registros, usando filas.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="UrlKeyCloak" grupo="Segurança" tipo="java.lang.String"
                   observacao="Define URL para comunicação do Celk Saúde com o keycloack."
        />
    </modulo>

    <!--Vacinas-->
    <modulo nome="18">

        <parametro nome="TipoDocumentoRecebimentoVacinas" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Define o tipo de documento utilizado para movimentação de estoque no processo de recebimento de vacinas"
                   obrigatorio="true"
        />

        <parametro nome="TipoDocumentoEstornoVacinas" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Define o tipo de documento utilizado para movimentação de estoque no processo de estorno do recebimento de vacinas"
                   obrigatorio="true"
        />

        <parametro nome="controlaEstoque" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se deve fazer o controle de estoque na aplicação das vacinas no Controle de Vacinação">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ExibirApenasVacinasCalendario" tipo="java.lang.String" defaultValue="N"
                   observacao=" Define se o sistema deve exibir exclusivamente as vacinas previstas no calendário nacional de vacinação no campo Calendário de Vacinação
Valores: ">
            <checkValues value="N" rotulo="rotulo_inativo"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="PermiteIntercambialidadeVacinasCovid19" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite aplicação de vacinas para Covid de diferentes fabricantes para o mesmo paciente">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_ativo"/>
        </parametro>

        <parametro nome="usuarioVacinasACS" tipo="br.com.ksisolucoes.vo.controle.Usuario" obrigatorio="true"
                   observacao="Usuário que irá receber as mensagens internas das vacinas aplicadas pelas ACS"
        />

        <parametro nome="TipoDocumentoAplicacaoSaida" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Define o tipo de documento de saída utilizado para movimentação de estoque no processo de aplicação de vacinas"
                   obrigatorio="true"
        />

        <parametro nome="FiltrarViasDeAdministraçãoPorVacina" tipo="java.lang.String" defaultValue="N"
                   observacao="Habilita o filtro das Vias De Administracao">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="TipoDocumentoAplicacaoEntrada" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Define o tipo de documento de entrada utilizado para movimentação de estoque no processo de cancelamento de aplicação de vacinas"
                   obrigatorio="true"
        />

        <parametro nome="TipoDocumentoInsumosSaida" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Define o tipo de documento de saída utilizado para movimentação de estoque de insumos utilizados no processo de aplicação de vacinas"
                   obrigatorio="true"
        />

        <parametro nome="tipoGeracaoArquivoPni" tipo="java.lang.Long" defaultValue="0" grupo="PNI"
                   observacao="Define o tipo de geração do arquivo do PNI">
            <checkValues value="0" rotulo="rotulo_geral"/>
            <checkValues value="1" rotulo="rotulo_individual"/>
        </parametro>

        <parametro nome="DiasParaNotificacaoVacinasJaAplicadas"
                   tipo="java.lang.Integer"
                   grupo="Notificação Vacina"
                   defaultValue="90"
                   observacao="Número de dias para considerar as vacinas que o paciente já aplicou mas não foi notificado."
        />

        <parametro nome="notificarDiasAnterioresAplicacao" tipo="java.lang.Long" grupo="Notificação Vacina" defaultValue="2"
                   observacao="Define a quantidade de dias para iniciar a notificação ao usuário do app cidadão."
        />
        <parametro nome="notificarDiasAprazamentoCodigo85" tipo="java.lang.Long" grupo="Notificação Vacina" defaultValue="60"
                   observacao="Define a quantidade de dias para iniciar a notificação ao usuário do app cidadão.(COVID-19 - ChAdOx1 nCoV-19 (FioCruz/Oxford))"
        />
        <parametro nome="notificarDiasAprazamentoCodigo86" tipo="java.lang.Long" grupo="Notificação Vacina" defaultValue="21"
                   observacao="Define a quantidade de dias para iniciar a notificação ao usuário do app cidadão. (COVID-19 - Coronavac (Butantan/Sinovac))"
        />
        <parametro nome="notificarDiasAprazamentoCodigo87" tipo="java.lang.Long" grupo="Notificação Vacina" defaultValue=""
                   observacao="Define a quantidade de dias para iniciar a notificação ao usuário do app cidadão. (COVID-19 - BNT162b2 (Pfizer/BioTech))"
        />
        <parametro nome="ImprimirCadernetaDeVacinaçãoExterna" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se o paciente podera imprimir a Caderneta de vacinação de forma externa.">
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>
    </modulo>

    <!--Consórcio-->
    <modulo nome="27">

        <parametro nome="descricaoPadraoRecibo" tipo="java.lang.String" grupo="Recibo"
                   defaultValue="Taxa de Manutenção Mês |@MM|/|@AAAA|"
                   observacao="Descrição padrão para a geração dos recibos de manutenção, pode ser utilizadas as variáveis |@MM| ou |@AAAA| para serem atribuídas ao mês e ano do dia da geração. (Ex: |@MM|/|@AAAA|)."
        />

        <parametro nome="tempoValidadeGuia" grupo="Impressão Guia" tipo="java.lang.Long" defaultValue="90"
                   observacao="Define a validade em dias da guia do consórcio a partir da data da autorização (data de agendamento) da mesma."
        />

        <parametro nome="descricaoOrientacaoPacienteImpressaoGuia" tipo="java.lang.String" grupo="Impressão Guia"
                   observacao="Define a descrição para orientação ao paciente a ser impresso na guia do consórcio."
                   defaultValue="Caro usuário do SUS, este atendimento é gratuito e custeado pelo seu município, sendo proibido qualquer tipo de cobrança e em qualquer hipótese. Caso for indicado pelo médico algum exame ou procedimento complementar, o mesmo deverá encaminhá-lo a secretaria de origem ou disponibilizá-lo à regulação mediante Laudo Médico."
        />

        <parametro nome="consorcioPadrao" tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao="Define a Unidade que será utilizada como padrão para o consórcio do qual se faz parte"
        />

        <parametro nome="tipoMovimentacaoProvisionamento" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação padrão para lançamento de crédito provisionado"
        />

        <parametro nome="descricaoMovimentacaoProvisionamento" tipo="java.lang.String"
                   observacao="Define o tipo de movimentação padrão para lançamento de crédito provisionado"
        />

        <parametro nome="tipoContaProcedimentos" tipo="br.com.ksisolucoes.vo.consorcio.TipoConta"
                   observacao="Define o Tipo de Conta utilizado para o cadastramento das Guias de Procedimentos "
        />

        <parametro nome="tipoContaProcedimentosFisico" tipo="br.com.ksisolucoes.vo.consorcio.TipoConta"
                   observacao="Define o Tipo de Conta utilizado para o cadastramento das Guias de Procedimentos para prestador do Tipo de Pessoa Física"
        />

        <parametro nome="tipoMovimentoPagamentoPrestador" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Tipo de Movimentação utilizada para o pagamentos das guias de procedimentos executados pelos prestadores de serviço"
        />

        <parametro nome="aliquotaImpostoRenda" tipo="java.lang.Double" defaultValue="0"
                   observacao="Define a alíquota do imposto de renda a deduzir do pagamento das guias de procedimentos para os prestadores de serviço"
        />

        <parametro nome="DiasVencimentoContrato" grupo="Prestador" tipo="java.lang.Long" defaultValue="30"
                   observacao="Define quantos dias antes do vencimento deve ser emitido o relatório com os prestadores com contratos a vencer."
                   obrigatorio="true"
        />

        <parametro nome="aliquotaPadraoINSSPatronal" tipo="java.lang.Double" defaultValue="0"
                   observacao="Define o percentual do imposto de INSS Patronal a ser calculado sobre o valor total da guia de procedimentos."
        />

        <parametro nome="tipoContaImposto" tipo="br.com.ksisolucoes.vo.consorcio.TipoConta"
                   observacao="Define o tipo de conta que representa os valores referentes aos impostos"
        />

        <parametro nome="tipoMovimentoTransferenciaImpostoRenda" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Defino o tipo de movimentação referente a transferência de valores da conta do consorciado no momento do pagamento para conta de imposto de renda"
        />

        <parametro nome="tipoMovimentoEntradaImpostoRenda" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação de entrada na conta do imposto de renda, no momento do pagamento dos prestadores de serviço"
        />

        <parametro nome="tipoMovimentoEntradaINSSPatronal" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação de entrada na conta do INSS Patronal, no momento do pagamento das guias"
        />

        <parametro nome="tipoMovimentoTransferenciaINSSPatronal" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação referente a transferência de valores do imposto de INSS Patronal da conta do consorciado no momento do pagamento para conta de imposto"
        />

        <parametro nome="tipoMovimentoSaidaImpostoRenda" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação de saída na conta do imposto de renda, no momento do pagamento do imposto de renda"
        />

        <parametro nome="almoxarifadoPadrao" tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao=""
        />

        <parametro nome="aliquotaPadraoINSS" tipo="java.lang.Double" defaultValue="0"
                   observacao="Aliquota padrão de INSS a ser usada nos cálculos de imposto no pagamento das guias do consórcio. Será usada apenas quando no prestador estiver configurado para utilizar a Alíquota Fixa"
        />

        <parametro nome="aliquotaPadraoISS" tipo="java.lang.Double" defaultValue="0"
                   observacao="Aliquota padrão de ISS a ser usada nos cálculos de imposto no pagamento das guias do consórcio. Será usada apenas quando no prestador estiver configurado para utilizar a Alíquota Fixa"
        />

        <parametro nome="tipoMovimentoEntradaImpostoISS" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação de entrada na conta do imposto para o valor do ISS, no momento do pagamento dos prestadores de serviço"
        />

        <parametro nome="tipoMovimentoEntradaImpostoINSS" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação de entrada na conta do imposto para o valor do INSS, no momento do pagamento dos prestadores de serviço"
        />

        <parametro nome="tipoMovimentoTransferenciaINSS" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação referente a transferência de valores do imposto de INSS da conta do consorciado no momento do pagamento para conta de imposto"
        />

        <parametro nome="tipoMovimentoTransferenciaISS" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação referente a transferência de valores do imposto de ISS da conta do consorciado no momento do pagamento para conta de imposto"
        />

        <parametro nome="tipoMovimentoSaidaImpostoISS" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação de saída na conta do imposto para o ISS, no momento do pagamento do imposto"
        />

        <parametro nome="tipoMovimentoSaidaImpostoINSS" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação de saída na conta do imposto para o valor do INSS, no momento do pagamento do imposto"
        />

        <parametro nome="tipoDocumentoSaidaAlmoxarifado" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Define o tipo de documento para a movimentação de estoque da entrega de produtos do almoxarifado"
                   obrigatorio="true"
        />

        <parametro nome="tipoDocumentoEstornoEntrega" tipo="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                   observacao="Define o tipo de documento para a movimentação de estoque do estorno da entrega de produtos do almoxarifado"
                   obrigatorio="true"
        />

        <parametro nome="tipoMovimentoEstornoEntrada" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação de entrada para o estorno dos pagamentos de guias"
                   obrigatorio="true"
        />

        <parametro nome="tipoMovimentoEstornoSaida" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   observacao="Define o tipo de movimentação de saída para o estorno dos pagamentos de guias"
                   obrigatorio="true"
        />

        <parametro nome="utilizarDescontoGlosa" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será utilizado o Desconto de Glosa no momento de pagamento da guia."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="gerarContaGeracaoBPA" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se ao realizar a Geração do BPA deve ser gerado o lançamento de Conta antes."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permiteAdicionarMaisConsultaProcedimentoGuia" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será permitido adicionar mais de um procedimento na guia para os modelo de requisição de consultas e procedimentos."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="opcoesMotivoCancelamento" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se as opções de motivo de cancelamento devem aparecer em uma lista de escolha ou não."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ObrigatorioInformarDataAgendamento" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se é obrigatório informar a data de agendamento no cadastro da guia."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ObrigatorioInformarDocumentoPagamento" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se é obrigatório informar o campo Documento no momento do pagamentos das guias."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="ImpressaoGuiaProcedimentos2Vias" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se a impressão da guia de procedimentos será em 1 ou 2 vias."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="gerarBpaConsorcio" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será gerado lançamento de produção na confirmação da guia, que será utilizado para geração do BPA."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="quantidadeDiasCancelamentoGuias" tipo="java.lang.Integer" obrigatorio="true"
                   observacao="Define a quantidade de dias para o cancelamento das guias."
        />

        <parametro nome="urlIntegracaoProdutos" tipo="java.lang.String" grupo="Integração"
                   observacao="URL usada na integração dos Pedidos de Consórcio"
        />

        <parametro nome="chaveIntegracaoProdutos" tipo="java.lang.String" grupo="Integração"
                   observacao="chave usada na integração dos Pedidos de Consórcio"
        />

        <parametro nome="idIntegracaoProdutos" tipo="java.lang.String" grupo="Integração"
                   observacao="id usada na integração dos Pedidos de Consórcio"
        />

        <parametro nome="informarPacienteSistema" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se deve ser informado um paciente do sistema no cadastro da guia."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="tamanhoImpressaoGuia" tipo="java.lang.Long" defaultValue="1"
                   observacao="Define se a impressão da guia vai sair em uma folha ou em meia folha."
        >
            <checkValues value="0" rotulo="rotulo_a4"/>
            <checkValues value="1" rotulo="rotulo_a5"/>
            <checkValues value="2" rotulo="rotulo_a4_cisamosc"/>
            <checkValues value="3" rotulo="rotulo_padrao_guia_procedimento"/>
        </parametro>

        <parametro nome="ocultarValoresGuia" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se os valores da impressão da guia serão ocultos."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="observacaoGuia" tipo="java.lang.String"
                   observacao="Observação que será impressa na guia de procedimentos."
        />

        <parametro nome="quantidadeSessaoGuia" tipo="java.lang.Long" defaultValue="4"
                   observacao="Define a quantidade limite de sessões de um procedimento por guia de procedimentos."
        />

        <parametro nome="ModeloReciboPadrao" tipo="br.com.ksisolucoes.vo.consorcio.ModeloReciboConsorcio"
                   observacao="Define o modelo de recibo usado na impressão dos recibos."
                   obrigatorio="true"
        />

        <parametro nome="confirmarGuiasComDataAgendamentoPosteriorAtual" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se será permitido confirmar pelo prestador as guias de procedimento com data agendada posterior a data atual."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>


        <parametro nome="permitirGuiasProcedimentoComDataRetroativa" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se será permitido cadastrar e confirmar pelo prestador as guias de procedimento com data retroativas (Data inferior a data atual)."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="Integraçao Consórcio-Ambulatorial" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite Integração entre o ambulatorial e Consórcio"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="campoSisregObrigatorio" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se os campos do SISREG serão obrigatórios no Cadastro da Guia de Procedimentos (109)."
        >
            <checkValues value="1" rotulo="rotulo_sim"/>
            <checkValues value="0" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="validarRegrasSigtapGuia" tipo="java.lang.Long" defaultValue="0"
                   observacao="Realizar a validação da quantidade e sexo conforme regras do SIGTAP para os procedimentos informados na emissão da guia."
        >
            <checkValues value="1" rotulo="rotulo_sim"/>
            <checkValues value="0" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="utilizarEditalConsorcio" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se o consórcio utilizará editais para preços dos procedimentos no Cadastro da Guia de Procedimentos (109)."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="exibirCamposSisregGuia" tipo="java.lang.Long" defaultValue="0"
                   observacao="Define se será exibido os campos referentes ao SISREG no Cadastro da Guia de Procedimentos (109). A obrigatoriedade deve ser configurada no Parâmetro GEM campoSisregObrigatorio."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="exibeNomeAutorizadorGuiaConsorcio" tipo="java.lang.Long" grupo="Impressão Guia"
                   defaultValue="0"
                   observacao="Define se será exibido o usuário autorizador na guia de procedimentos."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="demonstrarCamposSisregNaImpressao" tipo="java.lang.Long" grupo="Impressão Guia"
                   defaultValue="0"
                   observacao="Define se irá demonstrar os campos Chave, Código e Data na impressão da guia."
        >
            <checkValues value="0" rotulo="rotulo_nao"/>
            <checkValues value="1" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="utilizarControleFinanceiroPedidoTransferenciaConsorcio" tipo="java.lang.String"
                   defaultValue="N" grupo="Medicamento"
                   observacao="Define se será utilizado controle financeiro para solicitação de medicamentos do consórcio (Pedidos de Transferência (215))."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="tipoContaMedicamento" tipo="br.com.ksisolucoes.vo.consorcio.TipoConta" grupo="Medicamento"
                   observacao="Define o tipo de conta que será utilizado para o controle financeiro no cadastramento dos pedidos de transferência do consórcio."
        />

        <parametro nome="tipoMovimentoPagamentoMedicamento" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   grupo="Medicamento" obrigatorio="true"
                   observacao="Define o tipo de movimentação utilizada para o pagamento do controle financeiro dos pedidos de transferência do consórcio."
        />
        <parametro nome="tipoMovimentacaoPadraoCancelamentoGuiaSaida"
                   tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao" grupo="Medicamento" obrigatorio="true"
                   observacao="Define o procedimento padrão para o tipo de movimentação para guias de saida canceladas."
        />

        <parametro nome="tipoMovimentoFechamentoSaldoAnual" tipo="br.com.ksisolucoes.vo.consorcio.TipoMovimentacao"
                   obrigatorio="true"
                   observacao="Tipo utilizado para geração da movimentação financeira no processo de fechamento do saldo anual."
        />

        <parametro nome="controlaSaldoPorAno" tipo="java.lang.String" defaultValue="N" grupo="Financeiro"
                   observacao="Define se o controle financeiro deve ser por ano fiscal, não transportando o saldo do ano anterior para o próximo."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="efetuarBaixaDoSaldoNoPagametoDaGuia" tipo="java.lang.String" defaultValue="N" grupo="Financeiro"
                   observacao="Define se a baixa do saldo será no pagamento ou agendamento da guia."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="vencimentoContratoMes" tipo="java.lang.Long" defaultValue="12" obrigatorio="true"
                   observacao="Número de meses padrão para o cálculo do vencimento do contrato com os prestadores para o edital."
        />

        <parametro nome="atualizaQuantidadeRecebidaPedidoLicitacaoNF" tipo="java.lang.String" defaultValue="N"
                   grupo="Suprimentos"
                   observacao="Flag para identificar se na entrada da nota fiscal com licitação deve ser atualizado a quantidade recebida do pedido de licitação. Quando esta flag estiver ativada o consórcio não deve utilizar a emissão da guia de saída do pedido de transferência."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="agrupamentoOrdemCompra"
                   tipo="java.lang.String"
                   grupo="Suprimentos"
                   defaultValue="P"
                   observacao="Define como vai ser realizado o agrupamento do pedidos selecionados para geração da Ordem de Compra. Pedido (Fornecedor e Pedido), Produto (Fornecedor e Produto)."
                   obrigatorio="true">
            <checkValues value="P" rotulo="rotulo_produto"/>
            <checkValues value="D" rotulo="rotulo_pedido"/>
        </parametro>

        <parametro nome="visualizarFiltroPedidoConsorcio"
                   tipo="java.lang.String"
                   grupo="Suprimentos"
                   defaultValue="N"
                   observacao="Define se deve visualizar na consulta da Ordem de Compra os campos do consorciado e pedido (filtro / tabela)"
                   obrigatorio="true">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="guiaDigital" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será utilizado a guia digital."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
    </modulo>

    <modulo nome="29">
        <parametro nome="tipoExameLaboratorial" tipo="br.com.ksisolucoes.vo.prontuario.basico.TipoExame"
                   observacao="Tipo de Exame que representa Exames Laboratoriais"
                   obrigatorio="true"
        />
    </modulo>

    <!--MOBILE-->
    <modulo nome="30">
        <parametro nome="diasSincronizacao" tipo="java.lang.Long"
                   observacao="Indica em quantos dias a sincronização deve ser feita. Indicando -1 o parâmetro será desconsiderado."
                   defaultValue="5"
        />

        <parametro nome="SolicitaConfirmacaoAgendamento" tipo="java.lang.Long" defaultValue="-1"
                   observacao="Permite ao Cidadão confirmar o seu agendamento por meio do App Saúde Digital Cidadão, caso não seja confirmado no dia do recebimento da notificação, o agendamento será cancelado."
        >
        </parametro>

        <parametro nome="URLComunicaçãoAuthCidadão" tipo="java.lang.String"
                   observacao="Define URL para comunicação do Celk Saúde com o servidor de autenticação do aplicativo CS Cidadão"
        />

        <parametro nome="HabilitaFotoVisitaRecusadaAusente" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite ao ACS registrar uma foto da residência quando o desfecho da visita for recusada/ausente.">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="HabilitaIntegracaoCADSUSFamilias" tipo="java.lang.String" defaultValue="N"
                   observacao="Habilita a funcionalidade de consulta do CADSUS no famílias (aplicativo tablet) para auto preenchimento do cadastro de indivíduos.">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="AlterarDadosCadastrais" tipo="java.lang.String" defaultValue="S"
                   observacao="Permite ao paciente alterar Nome e Data de Nascimento por meio do aplicativo Saúde Digital Cidadão.">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="DisponibilizarFichaDiabetico" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite ao ACS fazer registro da Ficha de Acompanhamento de Diabéticos por meio do App Saúde Digital Famílias.">
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="URLKeycloackCidadao" tipo="java.lang.String"
                   observacao="Define URL para comunicação do Celk Saúde com o keycloack do aplicativo CS Cidadão"
        />
        <parametro nome="filtrarPor" tipo="java.lang.String" defaultValue="M"
                   observacao="Indica se o filtro mobile será por área micro-área ou todos."
        >
            <checkValues value="M" rotulo="rotulo_microarea"/>
        </parametro>
        <parametro nome="HabilitarNotificacaoAppCidadao" tipo="java.lang.String" defaultValue="N"
                   observacao="Habilita as notificações do AppCidadão se o mesmo estiver disponível ao município."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
        <parametro nome="DisponibilizarFichaGestante" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite ao ACS fazer registro da Ficha de Acompanhamento de Gestante por meio do App Saúde Digital Famílias."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
        <parametro nome="DisponibilizarFichaHipertensao" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite ao ACS fazer registro da Ficha de Acompanhamento de Hipertenso por meio do App Saúde Digital Famílias."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
        <parametro nome="DisponibilizarFichaHanseniase" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite ao ACS fazer registro da Ficha de Acompanhamento de Hanseníase por meio do App Saúde Digital Famílias."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
        <parametro nome="DisponibilizarFichaTuberculose" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite ao ACS fazer registro da Ficha de Acompanhamento de Tuberculoso por meio do App Saúde Digital Famílias."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
        <parametro nome="NotificacaoAppCidadaoAgendamento" tipo="java.lang.String" obrigatorio="true"
                   observacao="Campo para descrever notificação que será enviada para o paciente através do app cidadão
                    quando for realizado um agendamento, sendo permitido no máximo 155 caracteres.
                    Variáveis das informações que podem ser incluidas na mensagem: $data$, $hora$, $local_agendamento$,
                    $tipo_procedimento$, $nome_paciente$, $nome_completo_paciente$."
        />

        <parametro nome="LiberarAutomaticoAppCidadao" grupo="CadSUS" tipo="java.lang.String" defaultValue="N"
                   observacao="Libera automaticamente o App Cidadão."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
        <parametro nome="DisponibilizaAgendaUnidadeReferencia" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite ao usuário restringir agendas do app Cidadão apenas para pacientes da área da unidade de saúde de referência."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="AgendarConsultaViaPDA" tipo="java.lang.String" defaultValue="N"
                   observacao="As agendas do tipo 'Consulta' serão exibidas para agendamento via Endpoint PDA."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
    </modulo>

    <!--PORTAL-->
    <modulo nome="31">
        <parametro nome="habilitado" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se o Portal do Cidadão está ou não disponível."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
        <parametro nome="unidadeReferencia" tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao="Define a unidade que será utilizada para os processos do Portal."
        />
    </modulo>

    <!--INTEGRACAO INOVAMFRI-->
    <modulo nome="32">
        <parametro nome="urlConexao" tipo="java.lang.String"
                   observacao="Define a URL de conexão com servidor da INOVAMFRI"
                   obrigatorio="false"
        />
        <parametro nome="clientSecret" tipo="java.lang.String"
                   observacao="Define a chave para a conexão com servidor da INOVAMFRI"
                   obrigatorio="false"
        />

        <parametro nome="clientId" tipo="java.lang.String"
                   observacao="Define o indentificador da conexão com servidor da INOVAMFRI"
                   obrigatorio="false"
        />
    </modulo>

    <!--VIGILANCIA SANITARIA-->
    <modulo nome="33">
        <parametro nome="ambienteIntegracao" tipo="java.lang.String" defaultValue="N" grupo="Integração DocsPrime"
                   observacao="Define o ambiente de integração do DocsPrime."
        >
            <checkValues value="N" rotulo="rotulo_nao_utilizado"/>
            <checkValues value="H" rotulo="rotulo_homologacao"/>
            <checkValues value="P" rotulo="rotulo_producao"/>
        </parametro>
        <parametro nome="urlManualVS" tipo="java.lang.String"
                   observacao="Define a URL do link de downlaod do manual da Vigilância Sanitária"
                   obrigatorio="false"
        />
        <parametro nome="dsLinkManualVS" tipo="java.lang.String"
                   observacao="Descrição da identificação do link para download do manual da Vigilância Sanitária"
                   obrigatorio="false"
        />
        <parametro nome="urlVidFuncFerramentaVS" tipo="java.lang.String"
                   observacao="Define a URL do link de download do vídeo instrucional sobre o funcionamento da ferramenta"
                   obrigatorio="false"
        />
        <parametro nome="dsLinkVidFuncFerramentaVS" tipo="java.lang.String"
                   observacao="Descrição da identificação do link de download do vídeo instrucional sobre o funcionamento da ferramenta"
                   obrigatorio="false"
        />
        <parametro nome="dataInicioObrigatoriedadeAnexo" tipo="java.util.Date"
                   observacao="Define a data do inicio da obrigatoriedade dos anexos nos requerimentos de alvarás."
                   obrigatorio="false"
        />
        <parametro nome="diasRetroativoConsultaRetornoBoletos" tipo="java.lang.Long" grupo="Boleto"
                   defaultValue="5"
                   observacao="Define a quantidade de dias retroativos baseados na data atual que serão consultados os retornos das remessas dos boletos."
        />
        <parametro nome="diasParaNotificarRequerimentosSemMovimentacao" tipo="java.lang.Long" grupo="Alertas"
                   defaultValue="60"
                   observacao="Define número de dias para o sistema enviar mensagem avisando que o requerimento está sem movimentação. (Exige ativação no gerenciador de processos agendados)"
        />

        <parametro nome="diasParaNotificarVencimentoValidadeAlvaraLicenca" tipo="java.lang.Long" grupo="Alertas"
                   defaultValue="30"
                   observacao="Define o número de dias precedentes a data de vencimento da Validade dos Alvarás e/ou das Licenças, em que o sistema irá notificar via e-mail. (Exige ativação no gerenciador de processos agendados)"
        />

        <parametro nome="diasParaNotificarVencimentoPrazoDefesaRecurso" tipo="java.lang.Long" grupo="Alertas"
                   defaultValue="30"
                   observacao="Define o número de dias precedentes aos Prazos das Defesas e/ou dos Recursos, em que o sistema irá notificar via e-mail. (Exige ativação no gerenciador de processos agendados)"
        />

        <parametro nome="permitirMesmoResponsavelMultiplosSetores" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se um único Responsável Técnico poderá supervisionar múltiplos Setores de um Estabelecimento."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="impressaoAlvaraSomenteDeferido" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se será permitido imprimir os alvarás somente quando o requerimento estiver deferido."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="obrigatoriedadeCpfRepresentanteLegalEstabelecimento" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se o campo CPF do Representante Legal é obrigatório no cadastro do estabelecimento."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="restricaoEstabelecimentoRequerimentoExterno" tipo="java.lang.String" defaultValue="N"
                   observacao="Restrição nos campos de 'Estabelecimento' dos Requerimentos Externos para exibir somente os estabelecimentos em que o usuário externo é responsável."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="restricaoPessoaRequerimentoExterno" tipo="java.lang.String" defaultValue="N"
                   observacao="Restrição nos campos de 'Pessoa' dos Requerimentos Externos para exibir somente as pessoas em que o usuário externo é responsável."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="permiteVisualizarAnoAnteriorAlvaraInicialERevalidacao" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se no campo Ano Base, será visível o ano anterior ao ano atual."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="visualizarCampoTipoEmpresaCompleto" tipo="java.lang.String" defaultValue="S"
                   observacao="Define se no campo Tipo de Empresa os itens 'Cozinha Terceirizada' e 'Outros' será visível."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="tamanhoAnexoPranchas" tipo="java.lang.Long" defaultValue="2048"
                   obrigatorio="true"
                   valorMinimo="0" valorMaximo="20480"
                   observacao="Tamanho máximo total dos anexos das pranchas (KB)."/>

        <parametro nome="gerarTaxaAlvaraRevalidacaoRetroativa" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se na revalidação do alvará será cobrado taxa retroativa dos anos anteriores."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="diasCobrancaTaxaAlvaraRevalidacaoRetroativa" tipo="java.lang.Long" defaultValue="90"
                   observacao="Define a quantidade de dias de diferença entre o cadastro do requerimento da data de vencimento do alvará para considerar na cobrança das taxas retroativas do alvará."
        />

        <parametro nome="diasAntecipacaoSolicitacaoAlvaraDataFixa" tipo="java.lang.Long" defaultValue="120"
                   observacao="Define a quantidade de dias que ao solicitar um alvará de atividade com data fixa, irá definir a validade, para o ano corrente ou ano seguinte. Diferença entre data atual e data fixa da atividade for menor que o valor do parâmetro - Ano seguinte, maior que o valor - Ano atual."
        />

        <parametro nome="exibirNomeResponsavel" tipo="java.lang.String" grupo="Relatórios:Autos"
                   defaultValue="S"
                   observacao="Define se deverá ser exibido/carregado o nome do responsável na impressão dos autos."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="exibirData" tipo="java.lang.String" grupo="Relatórios:Autos"
                   defaultValue="S"
                   observacao="Define se deverá ser exibida a data referente ao auto."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>
        <parametro nome="autorizacaoAutomaticaEstabelecimentoExterno" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se a criação de estabelecimento externo será permitida sem autorização."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="gerarDocumentoComAssinaturaFiscal" tipo="java.lang.String" defaultValue="S"
                   observacao="Exibir nome e matrícula do fiscal nos documentos de requerimento de vigilância">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="removerObrigatoriedadeCamposFichaCovid" tipo="java.lang.String"
                   grupo="Vigilancia Epidemiológica:Fichas" defaultValue="N"
                   observacao="Remover obrigatoriedade dos campos(Evolução do Caso, Rastreamento de contato, Profissionais de segurança e saúde) da ficha de covid 19"
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="diasAntecipacaoAlvaraEvento" tipo="java.lang.Long" defaultValue="5"
                   observacao="Quantidade de dias para antecipação do alvará de evento!"/>

        <parametro nome="habilitaIntegracaoEstabelecimentoRest" tipo="java.lang.String" defaultValue="N"
                   observacao="Habilita a integracao de estabelecimentos por meio do Endpoint."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="habilitaAplicativoFiscalNaRua" tipo="java.lang.String" grupo="Aplicativo" defaultValue="N"
                   observacao="Habilita o uso do aplicativo Fiscal na Rua."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="habilitaEstabelecimentoAtualizarObservacaoDestaqueAlvara"
                   tipo="java.lang.String"
                   grupo="Estabelecimento"
                   defaultValue="S"
                   observacao="Habilita com que a Observação de Destaque no Estabelecimento atualize a que está no Requerimento de alvará."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <!--Integração Agravos de Notificação UPAs Fortaleza-->


        <parametro nome="urlProducao" tipo="java.lang.String" grupo="Vigilancia Epidemiológica:Integração Agravos de Notificação UPAs Fortaleza"
                   obrigatorio="true"
                   observacao="Define a URL de produção para o serviço de integração de Agravos de Notificação."
        />

        <parametro nome="emailIntegracao" tipo="java.lang.String" grupo="Vigilancia Epidemiológica:Integração Agravos de Notificação UPAs Fortaleza"
                   obrigatorio="true"
                   observacao="Define o Email para request da integração de Agravos de Notificação."
        />

        <parametro nome="senhaIntegracao" tipo="java.lang.String" grupo="Vigilancia Epidemiológica:Integração Agravos de Notificação UPAs Fortaleza"
                   obrigatorio="true"
                   observacao="Define a Senha para request da integração de Agravos de Notificação."
        />

        <parametro nome="urlHomologacao" tipo="java.lang.String" grupo="Vigilancia Epidemiológica:Integração Agravos de Notificação UPAs Fortaleza"
                   obrigatorio="true"
                   observacao="Define a URL de homologação para o serviço de integração de Agravos de Notificação."
        />

        <parametro nome="urlUtilizada"
                   tipo="java.lang.String"
                   grupo="Vigilancia Epidemiológica:Integração Agravos de Notificação UPAs Fortaleza"
                   defaultValue="H"
                   observacao="Define a URL que será utilizada no serviço de Integração Agravos de Notificação UPAs Fortaleza."
                   obrigatorio="true"
        >
            <checkValues value="H" rotulo="rotulo_homologacao"/>
            <checkValues value="P" rotulo="rotulo_producao"/>
        </parametro>
        <!--final integração-->

        <!-- Integração Estabelecimento SIM -->
        <parametro nome="habilitaIntegracaoEstabelecimentoSIM" tipo="java.lang.String" defaultValue="N" grupo="Integração SIM Sistemas"
                   observacao="Habilita a integracao de estabelecimentos por meio do agendador de processos para consultar no endpoint do sistema SIM."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="urlSIMSistemas" tipo="java.lang.String" grupo="Integração SIM Sistemas"
                   observacao="URL para onde apontar a integração da SIM Sistemas."
        />

        <parametro nome="chaveAcessoEstabelecimentoSimSistemas" tipo="java.lang.String" grupo="Integração SIM Sistemas"
                   observacao="Chave de acesso ao endpoint de Estabelecimentos."
        />

        <parametro nome="chaveAcessoLancarTaxaSimSistemas" tipo="java.lang.String" grupo="Integração SIM Sistemas"
                   observacao="Chave de acesso para lançar taxas."
        />

        <parametro nome="emailEnvioLogSIM" tipo="java.lang.String" grupo="Integração SIM Sistemas"
                   observacao="Email para enviar o logs da integração SIM Sistemas."
        />
        <!-- Integração Estabelecimento SIM -->

        <parametro nome="senhaLoginUnico" tipo="java.lang.String"
                   grupo="Login Unico"
                   observacao="Senha do login unico de Goiania"
        />

        <parametro nome="estabelecimentoPadrao" tipo="br.com.ksisolucoes.vo.basico.Empresa"
                   observacao="Define o estabelecimento utilizado pela vigilancia para o processo de sincronização do aplicativo fiscal na rua"
        >
        </parametro>

        <parametro nome="certidaoBaixaResponsabilidadeTecnica" tipo="java.lang.String" defaultValue="S"
                   grupo="Assinatura Documentos VISA"
                   observacao="Define se exibirá linha para assinatura na impressão do documento da AUTORIDADE DE SAÚDE.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="utilizarQuestionarioFatorDeRisco" tipo="java.lang.String" defaultValue="N"
                   grupo="Questionario"
                   observacao="Define se sera utilizado o questionario para alterar o fator de risco">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="UtilizaNovaInspecaoSanitaria" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se sera utilizado os novos campos apara a Inspeção Sanitária">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="utilizarLicencaSanitaria" tipo="java.lang.String" defaultValue="N"
                   observacao="Define se sera utilizado licença sanitária">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="atividadeslicenciaveisaoDeferir" tipo="java.lang.String" defaultValue="N"
                   observacao="Permite que o fiscal remova CNAEs não licenciáveis na finalização do requerimento">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="desobrigacaoAlvaraSanitario" tipo="java.lang.String" defaultValue="N"
                   observacao="Controlar a ativação do processo de desobrigação no sistema, permitindo que o fluxo para gerar o requerimento de desobrigação seja acionado.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="DispensaLicençaSanitáriaAtividadesBaixoRisco" tipo="java.lang.String" defaultValue="N"
                   observacao="Controlar a ativação do processo de Dispensa de Licença Sanitária para Atividades de Baixo Risco.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="permitirParcelamentoBoletosVigilancia" tipo="java.lang.String" defaultValue="N" grupo="Parcelamento Financeiro Vigilancia"
                   observacao="Define se o parcelamento de boletos é permitido!"
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="ordenacaoPorEdicaoRequerimento" tipo="java.lang.String" defaultValue="N"
                   observacao="Ordena os requerimentos nas telas 245 e 764 pela data de edição ou retorno.">
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="valorMinimoParcelaBoletoVigilancia" tipo="java.lang.Double" grupo="Parcelamento Financeiro Vigilancia"
                   valorMinimo="0"
                   defaultValue="50.00"
                   valorMaximo="999.99"
                   observacao="Define o valor mínimo que cada boleto deve possuir para permitir o parcelamento!"
                   obrigatorio="false"
        />

        <parametro nome="quantidadeMaximaParcelasBoletosVigilancia" tipo="java.lang.Long"
                   defaultValue="1" grupo="Parcelamento Financeiro Vigilancia"
                   observacao="Define a quantidade máxima de parcelas que podem ser geradas!"
        >
            <checkValues value="1" rotulo="rotulo_uma"/>
            <checkValues value="2" rotulo="rotulo_duas"/>
            <checkValues value="3" rotulo="rotulo_tres_maisculo"/>
            <checkValues value="4" rotulo="rotulo_quatro_maiusculo"/>
            <checkValues value="5" rotulo="rotulo_cinco_maiusculo"/>
            <checkValues value="6" rotulo="rotulo_seis_maiusculo"/>
            <checkValues value="7" rotulo="rotulo_sete_maiusculo"/>
            <checkValues value="8" rotulo="rotulo_oito_maiusculo"/>
            <checkValues value="9" rotulo="rotulo_nove_maiusculo"/>
            <checkValues value="10" rotulo="rotulo_dez_maiusculo"/>
        </parametro>

        <parametro nome="ambienteIntegracaoFinanceiroSmart" tipo="java.lang.String" defaultValue="N" grupo="Integração Financeira Smart"
                   observacao="Define o ambiente de integração do Financeiro da Celk com sistema da Smart."
        >
            <checkValues value="N" rotulo="rotulo_nao_utilizado"/>
            <checkValues value="H" rotulo="rotulo_homologacao"/>
            <checkValues value="P" rotulo="rotulo_producao"/>
        </parametro>

        <parametro nome="integracaoFinanceiroSmartUrlHomologacao" tipo="java.lang.String" grupo="Integração Financeira Smart"
                   observacao="Define a url do ambiente de homologação para a integração do Financeiro da Celk com sistema da Smart."
        />
        <parametro nome="integracaoFinanceiroSmartUrlProducao" tipo="java.lang.String" grupo="Integração Financeira Smart"
                   observacao="Define a url do ambiente de produção para a integração do Financeiro da Celk com sistema da Smart."
        />
        <parametro nome="integracaoFinanceiroSmartUsuario" tipo="java.lang.String" grupo="Integração Financeira Smart"
                   observacao="Define o usuário do ambiente de integração do Financeiro da Celk com sistema da Smart."
        />
        <parametro nome="integracaoFinanceiroSmartSenha" tipo="java.lang.String" grupo="Integração Financeira Smart"
                   observacao="Define a senha do ambiente de integração do Financeiro da Celk com sistema da Smart."
        />
        <parametro nome="integracaoFinanceiroSmartScope" tipo="java.lang.String" grupo="Integração Financeira Smart"
                   observacao="Define o escopo do ambiente de integração do Financeiro da Celk com sistema da Smart."
        />
        <parametro nome="integracaoFinanceiroSmartGrantType" tipo="java.lang.String" grupo="Integração Financeira Smart"
                   observacao="Define o tipo de autorização do ambiente de integração do Financeiro da Celk com sistema da Smart."
        />




        <parametro nome="ambienteIntegracaoFinanceiroGoiania" tipo="java.lang.String" defaultValue="N" grupo="Integração Financeira Goiânia"
                   observacao="Define o ambiente de integração do Financeiro da Celk com sistema de Goiania."
        >
            <checkValues value="N" rotulo="rotulo_nao_utilizado"/>
            <checkValues value="H" rotulo="rotulo_homologacao"/>
            <checkValues value="P" rotulo="rotulo_producao"/>
        </parametro>

        <parametro nome="integracaoFinanceiroGoianiaUrlHomologacao" tipo="java.lang.String" grupo="Integração Financeira Goiânia"
                   observacao="Define a url do ambiente de homologação para a integração do Financeiro da Celk com sistema de Goiania."
        />
        <parametro nome="integracaoFinanceiroGoianiaUrlProducao" tipo="java.lang.String" grupo="Integração Financeira Goiânia"
                   observacao="Define a url do ambiente de produção para a integração do Financeiro da Celk com sistema de Goiania"
        />
        <parametro nome="integracaoFinanceiroGoianiaNumeroServico" tipo="java.lang.String" grupo="Integração Financeira Goiânia"
                   observacao="Define o usuário do ambiente de integração do Financeiro da Celk com sistema de Goiania"
        />
        <parametro nome="integracaoFinanceiroGoianiaNumeroCliente" tipo="java.lang.String" grupo="Integração Financeira Goiânia"
                   observacao="Define o escopo do ambiente de integração do Financeiro da Celk com sistema de Goiania"
        />
        <parametro nome="integracaoFinanceiroGoianiaSenha" tipo="java.lang.String" grupo="Integração Financeira Goiânia"
                   observacao="Define a senha do ambiente de integração do Financeiro da Celk com sistema de Goiania"
        />

        <parametro nome="exiberpopuppdeautospendentes" tipo="java.lang.String" defaultValue="N" grupo="Alertas"
                   observacao="Habilita a exibição de pop-up de alerta no login do usuário externo quando houver autos pendentes (Intimação, Infração, Penalidade ou Multa)"
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>



    </modulo>

    <!--FROTAS-->
    <modulo nome="34">
        <parametro nome="gerarBpaSemTfd" tipo="java.lang.String" defaultValue="N" grupo="BPA"
                   observacao="Define se será gerada conta do paciente para faturamento do BPA para viagens que não possuem TFD."
        >
            <checkValues value="S" rotulo="rotulo_sim"/>
            <checkValues value="N" rotulo="rotulo_nao"/>
        </parametro>

        <parametro nome="permiteEditarVeiculoMotorista" tipo="java.lang.String" defaultValue="N"
                   observacao="Permitir editar Veículo e Motorista no Roteiro de Viagem."
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="imprimeEnderecoRoteiroViagem" tipo="java.lang.String" defaultValue="N"
                   observacao="Permitir impressão do endereço do paciente no relatório Roteiro de Viagem"
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>

        <parametro nome="forneceAlimentacaoNasViagens" tipo="java.lang.String" defaultValue="N"
                   observacao="Quando ativo insere na Impressão do Roteiro Viagem campo para coletar a assinatura do passageiro"
        >
            <checkValues value="N" rotulo="rotulo_nao"/>
            <checkValues value="S" rotulo="rotulo_sim"/>
        </parametro>
    </modulo>
</estrutura>
